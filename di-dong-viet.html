<!DOCTYPE html>
<html lang="en" dir="">

<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

  <!-- Title -->
  <title>Di Động Việt - Customer success story</title>

  <!-- Favicon -->
  <link rel="shortcut icon" href="./assets/img/logo/favicon.png" type="image/x-icon" />

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">

  <!-- CSS Implementing Plugins -->
  <link rel="stylesheet" href="./assets/vendor/bootstrap-icons/font/bootstrap-icons.css">
  <link rel="stylesheet" href="./assets/vendor/hs-mega-menu/dist/hs-mega-menu.min.css">
  

  <!-- CSS Front Template -->
  <link rel="stylesheet" href="./assets/css/theme.min.css">
<!-- meta tag -->
<meta property="og:locale" content="vi_VN" />
<link rel="canonical" href="https://sepay.vn/faq.html" />
<meta name="description" content="Di Động Việt sử dụng SePay để đem đến trải nghiệm tuyệt vời cho khách hàng của mình khi thanh toán."/>
<meta property="og:type" content="article" />
<meta property="og:url" content="https://sepay.vn/faq.html" />
<meta property="og:title" content="Di Động Việt - Customer success story" />
<meta property="og:description" content="Di Động Việt sử dụng SePay để đem đến trải nghiệm tuyệt vời cho khách hàng của mình khi thanh toán." />

<meta property="og:site_name" content="Di Động Việt - Customer success story" />
<meta property="og:image" content="https://sepay.vn/assets/img/others/cua-hang-di-dong-viet.jpeg" />
<meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/cua-hang-di-dong-viet.jpeg" />
<!-- meta tag -->


  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-J8DLMQTKSQ');
  </script>
</head>

<body>
  <!-- ========== HEADER ========== -->
  <header id="header" class="navbar navbar-expand-lg navbar-end navbar-light bg-white">


    <div class="container">
      <nav class="js-mega-menu navbar-nav-wrap">
        <!-- Default Logo -->
        <a class="navbar-brand" href="https://sepay.vn" aria-label="Front">
          <img class="navbar-brand-logo" src="./assets/img/logo/sepay-blue-154x50.png" alt="Logo">
        </a>
        <!-- End Default Logo -->

        <!-- Toggler -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown"
          aria-controls="navbarNavDropdown" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-default">
            <i class="bi-list"></i>
          </span>
          <span class="navbar-toggler-toggled">
            <i class="bi-x"></i>
          </span>
        </button>
        <!-- End Toggler -->

        <!-- Collapse -->
        <div class="collapse navbar-collapse" id="navbarNavDropdown">
          <div class="navbar-absolute-top-scroller">
            <ul class="navbar-nav">
            

              <!-- Features -->
              <li class="hs-has-sub-menu nav-item">
                <a id="coursesMegaMenu" class="hs-mega-menu-invoker nav-link dropdown-toggle" href="#" role="button"> Tính năng</a>

                <!-- Mega Menu -->
                <div class="hs-sub-menu dropdown-menu" aria-labelledby="coursesMegaMenu" style="min-width: 20rem;">


                  <a class="navbar-dropdown-menu-media-link" href="chia-se-bien-dong-so-du.html">
                    <div class="d-flex">
                      <div class="flex-shrink-0">
                        <span class="svg-icon svg-icon-sm text-primary">
                          <span class="svg-icon svg-icon-sm text-primary">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M22.1671 18.1421C22.4827 18.4577 23.0222 18.2331 23.0206 17.7868L23.0039 13.1053V5.52632C23.0039 4.13107 21.8729 3 20.4776 3H8.68815C7.2929 3 6.16183 4.13107 6.16183 5.52632V9H13C14.6568 9 16 10.3431 16 12V15.6316H19.6565L22.1671 18.1421Z"
                                fill="#035A4B" />
                              <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd"
                                d="M1.98508 18V13C1.98508 11.8954 2.88051 11 3.98508 11H11.9851C13.0896 11 13.9851 11.8954 13.9851 13V18C13.9851 19.1046 13.0896 20 11.9851 20H4.10081L2.85695 21.1905C2.53895 21.4949 2.01123 21.2695 2.01123 20.8293V18.3243C1.99402 18.2187 1.98508 18.1104 1.98508 18ZM5.99999 14.5C5.99999 14.2239 6.22385 14 6.49999 14H11.5C11.7761 14 12 14.2239 12 14.5C12 14.7761 11.7761 15 11.5 15H6.49999C6.22385 15 5.99999 14.7761 5.99999 14.5ZM9.49999 16C9.22385 16 8.99999 16.2239 8.99999 16.5C8.99999 16.7761 9.22385 17 9.49999 17H11.5C11.7761 17 12 16.7761 12 16.5C12 16.2239 11.7761 16 11.5 16H9.49999Z"
                                fill="#035A4B" />
                            </svg>

                          </span>

                        </span>
                      </div>

                      <div class="flex-grow-1 ms-3">
                        <span class="navbar-dropdown-menu-media-title">Chia sẻ biến động số dư</span>
                        <p class="navbar-dropdown-menu-media-desc">Báo lên group chat khi nhận chuyển khoản</p>
                      </div>
                    </div>
                  </a>

                  <div class="dropdown-divider"></div>

                  <a class="navbar-dropdown-menu-media-link" href="cong-thanh-toan-truc-tuyen.html">
                    <div class="d-flex">
                      <div class="flex-shrink-0">
                        <span class="svg-icon svg-icon-sm text-primary">
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path opacity="0.3" d="M18 10V20C18 20.6 18.4 21 19 21C19.6 21 20 20.6 20 20V10H18Z"
                              fill="#035A4B" />
                            <path opacity="0.3"
                              d="M11 10V17H6V10H4V20C4 20.6 4.4 21 5 21H12C12.6 21 13 20.6 13 20V10H11Z"
                              fill="#035A4B" />
                            <path opacity="0.3" d="M10 10C10 11.1 9.1 12 8 12C6.9 12 6 11.1 6 10H10Z" fill="#035A4B" />
                            <path opacity="0.3" d="M18 10C18 11.1 17.1 12 16 12C14.9 12 14 11.1 14 10H18Z"
                              fill="#035A4B" />
                            <path opacity="0.3" d="M14 4H10V10H14V4Z" fill="#035A4B" />
                            <path opacity="0.3" d="M17 4H20L22 10H18L17 4Z" fill="#035A4B" />
                            <path opacity="0.3" d="M7 4H4L2 10H6L7 4Z" fill="#035A4B" />
                            <path
                              d="M6 10C6 11.1 5.1 12 4 12C2.9 12 2 11.1 2 10H6ZM10 10C10 11.1 10.9 12 12 12C13.1 12 14 11.1 14 10H10ZM18 10C18 11.1 18.9 12 20 12C21.1 12 22 11.1 22 10H18ZM19 2H5C4.4 2 4 2.4 4 3V4H20V3C20 2.4 19.6 2 19 2ZM12 17C12 16.4 11.6 16 11 16H6C5.4 16 5 16.4 5 17C5 17.6 5.4 18 6 18H11C11.6 18 12 17.6 12 17Z"
                              fill="#035A4B" />
                          </svg>

                        </span>
                      </div>

                      <div class="flex-grow-1 ms-3">
                        <span class="navbar-dropdown-menu-media-title">Xác thực thanh toán</span>
                        <p class="navbar-dropdown-menu-media-desc">Tự động xác nhận thanh toán khi khách hàng chuyển
                          tiền</p>
                      </div>
                    </div>
                  </a>

                  <div class="dropdown-divider"></div>

                  <a class="navbar-dropdown-menu-media-link" href="thong-ke-dong-tien.html">
                    <div class="d-flex">
                      <div class="flex-shrink-0">
                        <span class="svg-icon svg-icon-sm text-primary">
                          <span class="svg-icon svg-icon-sm text-primary">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd"
                                d="M12 17C16.4183 17 20 13.4183 20 9C20 4.58172 16.4183 1 12 1C7.58172 1 4 4.58172 4 9C4 13.4183 7.58172 17 12 17Z"
                                fill="#035A4B" />
                              <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd"
                                d="M6.53819 9L10.568 19.3624L11.976 18.8149L13.3745 19.3674L17.4703 9H6.53819ZM9.46188 11H14.5298L11.9759 17.4645L9.46188 11Z"
                                fill="#035A4B" />
                              <path opacity="0.3"
                                d="M10 22H14V22C14 23.1046 13.1046 24 12 24V24C10.8954 24 10 23.1046 10 22V22Z"
                                fill="#035A4B" />
                              <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M8 17C8 16.4477 8.44772 16 9 16H15C15.5523 16 16 16.4477 16 17C16 17.5523 15.5523 18 15 18C15.5523 18 16 18.4477 16 19C16 19.5523 15.5523 20 15 20C15.5523 20 16 20.4477 16 21C16 21.5523 15.5523 22 15 22H9C8.44772 22 8 21.5523 8 21C8 20.4477 8.44772 20 9 20C8.44772 20 8 19.5523 8 19C8 18.4477 8.44772 18 9 18C8.44772 18 8 17.5523 8 17Z"
                                fill="#035A4B" />
                            </svg>

                          </span>

                        </span>
                      </div>

                      <div class="flex-grow-1 ms-3">
                        <span class="navbar-dropdown-menu-media-title">Thống kê dòng tiền</span>
                        <p class="navbar-dropdown-menu-media-desc">Nắm bắt được dòng tiền tất cả tài khoản</p>
                      </div>
                    </div>
                  </a>

                </div>

              </li>
              <!-- End Features -->



              <li class="nav-item">
                <a class="nav-link" href="why-sepay.html">Why SePay?</a>
              </li>

 

              <li class="nav-item">
                <a class="nav-link" href="bang-gia.html">Bảng giá</a>
              </li>
             
              <li class="nav-item">
                <a class="nav-link" href="https://docs.sepay.vn" target="_blank">Hướng dẫn</a>
              </li>

              <li class="nav-item">
                <a class="nav-link" href="https://sepay.vn/blog">Blog</a>
              </li>
<li class="nav-item">
                <a class="nav-link" href="https://sepay.vn/affiliate.html">Đối tác</a>
              </li>

               <!-- Company -->
               <li class="hs-has-sub-menu nav-item">
                <a id="accountMegaMenu" class="hs-mega-menu-invoker nav-link dropdown-toggle " href="#" role="button" aria-expanded="false">Công ty</a>

                <!-- Mega Menu -->
                <div class="hs-sub-menu dropdown-menu" aria-labelledby="accountMegaMenu" style="min-width: 14rem;">
                    <a class="dropdown-item " href="./gioi-thieu.html">Giới thiệu</a>
                    <a class="dropdown-item " href="./chung-nhan.html">Chứng nhận</a>

                  <!-- Authentication -->
                  <div class="hs-has-sub-menu nav-item">
                    <a id="authenticationMegaMenu" class="hs-mega-menu-invoker dropdown-item dropdown-toggle " href="#" role="button" aria-expanded="false">Ký kết hợp tác</a>

                    <div class="hs-sub-menu dropdown-menu" aria-labelledby="authenticationMegaMenu" style="min-width: 14rem;">
                      <a class="dropdown-item " href="./kien-long-bank.html">Ngân hàng Kiên Long</a>
                      <a class="dropdown-item " href="./ocb.html">Ngân hàng OCB</a>
                      <a class="dropdown-item " href="./msb.html">Ngân hàng MSB</a>
                     </div>
                  </div>
                  <!-- End Authentication -->

                  <a class="dropdown-item " href="./ngan-hang.html">Ngân hàng kết nối (18)</a>
                   
                </div>
                <!-- End Mega Menu -->
              </li>
              <!-- End Company -->
              <!-- Button -->
              <li class="nav-item mt-5 mt-lg-0">
                <a class="btn btn-light btn-transition"
                href="https://my.sepay.vn/login"
                target="_blank">Đăng nhập</a>
                <a class="btn btn-primary btn-transition"
                  href="https://my.sepay.vn/register"
                  target="_blank">Đăng ký</a>
              </li>
              <!-- End Button -->
            </ul>
          </div>
        </div>
        <!-- End Collapse -->
      </nav>
    </div>
  </header>

  <!-- ========== END HEADER ========== -->

   <!-- ========== MAIN CONTENT ========== -->
   <main id="content" role="main">
    <!-- Hero -->
    <div class="bg-dark" style="background-image: url(./assets/svg/components/wave-pattern-light.svg);">
      <div class="container content-space-2 content-space-lg-3">
        <div class="w-lg-65 text-center mx-lg-auto">
          <span class="badge bg-warning text-dark rounded-pill mb-3">Customer success story</span>
          <h1 class="text-white mb-0">Di Động Việt</h1>
        </div>
      </div>
    </div>
    <!-- End Hero -->

    <!-- Content -->
    <div class="container content-space-2 content-space-lg-0">
      <div class="row">
        <div class="col-lg-4 mt-lg-n10 mb-7 mb-lg-0">
          <!-- Sticky Block -->
          <div id="stickyBlockStartPoint">
            <div class="js-sticky-block"
                  data-hs-sticky-block-options='{
                   "parentSelector": "#stickyBlockStartPoint",
                   "targetSelector": "#header",
                   "breakpoint": "lg",
                   "startPoint": "#stickyBlockStartPoint",
                   "endPoint": "#stickyBlockEndPoint",
                   "stickyOffsetTop": 20
                 }'>
              <!-- Card -->
              <div class="card">
                <div class="card-header border-bottom text-center">
                  <img class="avatar avatar-xxl avatar-4x3" src="./assets/img/others/didongviet-logo-150.png" alt="Logo">
                </div>

                <div class="card-body">
                  <dl class="row">
                    <dt class="col-sm-4">Ngành</dt>
                    <dd class="col-sm-8">Bán lẻ</dd>
                  </dl>
                  <!-- End Row -->

                  <dl class="row">
                    <dt class="col-sm-4">Quy mô</dt>
                    <dd class="col-sm-8">Hơn 60 cửa hàng<br>Hơn 1000 nhân sự</dd>
                  </dl>
                  <!-- End Row -->

                  <dl class="row">
                    <dt class="col-sm-4">Giới thiệu</dt>
                    <dd class="col-sm-8">Di Động Việt là hệ thống bán lẻ điện thoại uy tín, là đại lý ủy quyền của Apple (AAR) tại Việt Nam.</dd>
                  </dl>
                  <!-- End Row -->


                  <dl class="row">
                    <dt class="col-sm-4">Hình thức</dt>
                    <dd class="col-sm-8">Online và Offline</dd>
                  </dl>
                  <!-- End Row -->


                  <hr class="my-4">

                  <div class="mb-3">
                    <h4>Tính năng đã sử dụng</h4>
                  </div>

                  <dl class="row mt-5">
                    <dt class="col-sm-4 mb-2 mb-sm-0">
                        <span class="svg-icon text-primary">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-qr-code" viewBox="0 0 16 16">
                              <path d="M2 2h2v2H2V2Z"></path>
                              <path d="M6 0v6H0V0h6ZM5 1H1v4h4V1ZM4 12H2v2h2v-2Z"></path>
                              <path d="M6 10v6H0v-6h6Zm-5 1v4h4v-4H1Zm11-9h2v2h-2V2Z"></path>
                              <path d="M10 0v6h6V0h-6Zm5 1v4h-4V1h4ZM8 1V0h1v2H8v2H7V1h1Zm0 5V4h1v2H8ZM6 8V7h1V6h1v2h1V7h5v1h-4v1H7V8H6Zm0 0v1H2V8H1v1H0V7h3v1h3Zm10 1h-1V7h1v2Zm-1 0h-1v2h2v-1h-1V9Zm-4 0h2v1h-1v1h-1V9Zm2 3v-1h-1v1h-1v1H9v1h3v-2h1Zm0 0h3v1h-2v1h-1v-2Zm-4-1v1h1v-2H7v1h2Z"></path>
                              <path d="M7 12h1v3h4v1H7v-4Zm9 2v2h-3v-1h2v-1h1Z"></path>
                            </svg>
            
                          </span>
                    </dt>
                    <!-- End Col -->

                    <dd class="col-sm-8">
                      <h5 class="mb-0">QR Code thanh toán</h5>
                      <p class="mb-0">Thanh toán chuyển khoản qua QR Code</p>
                    </dd>
                    <!-- End Col -->
                  </dl>
                  <!-- End Row -->

                  <dl class="row mt-3">
                    <dt class="col-sm-4 mb-2 mb-sm-0">
                        <span class="svg-icon text-primary mb-3">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M4 4L11.6314 2.56911C11.875 2.52343 12.125 2.52343 12.3686 2.56911L20 4V11.9033C20 15.696 18.0462 19.2211 14.83 21.2313L12.53 22.6687C12.2057 22.8714 11.7943 22.8714 11.47 22.6687L9.17001 21.2313C5.95382 19.2211 4 15.696 4 11.9033L4 4Z" fill="#035A4B"></path>
                              <path fill-rule="evenodd" clip-rule="evenodd" d="M11.175 14.75C10.9354 14.75 10.6958 14.6542 10.5042 14.4625L8.58749 12.5458C8.20415 12.1625 8.20415 11.5875 8.58749 11.2042C8.97082 10.8208 9.59374 10.8208 9.92915 11.2042L11.175 12.45L14.3375 9.2875C14.7208 8.90417 15.2958 8.90417 15.6792 9.2875C16.0625 9.67083 16.0625 10.2458 15.6792 10.6292L11.8458 14.4625C11.6542 14.6542 11.4146 14.75 11.175 14.75Z" fill="#035A4B"></path>
                            </svg>
            
                          </span>
                    </dt>
                    <!-- End Col -->

                    <dd class="col-sm-8">
                      <h5 class="mb-0">Xác thực thanh toán</h5>
                      <p class="mb-0">Tự động xác nhận thanh toán, tích hợp vào POS</p>
                    </dd>
                    <!-- End Col -->
                  </dl>
                  <!-- End Row -->

                  <dl class="row mt-3">
                    <dt class="col-sm-4 mb-2 mb-sm-0">
                      <div class="svg-icon text-primary">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-telegram" viewBox="0 0 16 16">
                            <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM8.287 5.906c-.778.324-2.334.994-4.666 2.01-.378.15-.577.298-.595.442-.03.243.275.339.69.47l.175.055c.408.133.958.288 1.243.294.26.006.549-.1.868-.32 2.179-1.471 3.304-2.214 3.374-2.23.05-.012.12-.026.166.016.047.041.042.12.037.141-.03.129-1.227 1.241-1.846 1.817-.193.18-.33.307-.358.336a8.154 8.154 0 0 1-.188.186c-.38.366-.664.64.015 1.088.327.216.589.393.85.571.284.194.568.387.936.629.093.06.183.125.27.187.331.236.63.448.997.414.214-.02.435-.22.547-.82.265-1.417.786-4.486.906-5.751a1.426 1.426 0 0 0-.013-.315.337.337 0 0 0-.114-.217.526.526 0 0 0-.31-.093c-.3.005-.763.166-2.984 1.09z"/>
                          </svg>

                      </div>
                    </dt>
                    <!-- End Col -->

                    <dd class="col-sm-8">
                      <h5 class="mb-0">Chia sẻ biến động số dư</h5>
                      <p class="mb-0">Nhận biết có khách thanh toán qua Telegram</p>
                    </dd>
                    <!-- End Col -->
                  </dl>
                  <!-- End Row -->
                </div>
              </div>
              <!-- End Card -->
            </div>
          </div>
          <!-- Sticky Block -->
        </div>
        <!-- End Col -->

        <div class="col-lg-8 content-space-t-lg-2">
          <div class="ps-lg-4">
            <h2 class="h3 py-3">Giới thiệu</h2>
            <p>Công ty TNHH Công Nghệ Di Động Việt vận hành chuỗi cửa hàng Didongviet.vn, được thành lập năm 2009 với tư cách một hệ thống uy tín chuyên kinh doanh các mặt hàng công nghệ chính hãng, nổi bật là điện thoại di động, máy tính bảng, laptop, phụ kiện, linh kiện...</p>

            <p>Di Động Việt đang có hơn 60 cửa hàng trên khắp cả nước. Top 3 công ty bán lẻ Điện Thoại tại Việt Nam. Là đại lý ủy quyền chính thức của Apple (AAR).</p>
            <div class="my-4">
                <img class="img-fluid rounded-2" src="assets/img/others/cua-hang-di-dong-viet.jpeg" alt="Cửa hàng Di Động Việt">
              </div>
            <h2 class="h3 py-3">Bài toán cần giải</h2>

            <p>Khi khách hàng mua hàng tại cửa hàng và thực hiện thanh toán, Di Động Việt hỗ trợ nhiều phương thức thanh toán khác nhau, trong đó có <b>chuyển khoản ngân hàng</b>. Và đây là những vấn đề phát sinh:</p>

            <p>
                <ul class="lh-lg">
                    <li><b>Khách hàng phải tự điền thông tin chuyển khoản:</b> Chọn ngân hàng, số tài khoản, số tiền, nội dung thanh toán. Việc này tốn thời gian, dễ dẫn đến sai sót.</li>
                    <li><b>Thời gian kiểm tra thanh toán lâu, tốn nhiều nhân lực:</b> Nhân viên bán hàng phải nhờ kế toán kiểm tra giao dịch xem đã nhận tiền chưa, và có nhận đủ không. Quá trình này thường tốn thời gian từ 3 đến 15 phút.</li>
                    <li><b>Khách hàng phải đợi lâu</b> để được xác nhận đã thanh toán.</li>
                </ul>
            </p>
            <p>Nếu sử dụng <b>cổng thanh toán</b>, các vấn đề trên được giải quyết. Tuy nhiên phát sinh các vấn đề khác như:</p>
            <ul class="lh-lg">
                <li><b>Phí cổng thanh toán quá cao:</b> Trung bình từ 1 đến 2% trên mỗi giao dịch. Ví dụ nếu bán một đơn hàng điện thoại 20 triệu, bạn sẽ tốn 400,000đ cho cổng thanh toán.</li>
                <li><b>Tiền bị giữ ở cổng thanh toán:</b> Sau thanh toán, tiền không về về tài khoản ngay mà phải chờ từ 1 đến 3 ngày. </li>
                <li><b>Rất khó khăn trong việc đối soát</b>.</li>
            </ul>

            <h2 class="h3 py-3">SePay đã giải quyết bài toán cho Di Động Việt</h2>

            <p class="fw-bold">Trải nghiệm của khách hàng khi thanh toán chuyển khoản tại Di Động Việt:</p>
            <p>
                <ul class="lh-lg">
                    <li>Nhân viên bán hàng tạo đơn trên app POS trên điện thoại, mã QR Code tự động tạo và hiện trên màn hình.</li>
                    <li>Khách hàng mở app ngân hàng và chọn quét QR code. Tất cả thông tin chuyển khoản tự động điền. Khách chỉ cần bấm tiếp tục và chuyển khoản.</li>
                    <li>Sau 0 đến 15 giây, app bán hàng tự động xác nhận khách đã thanh toán thành công.</li>
                    <li>Tin nhắn thông báo có khách thanh toán cũng được bắn lên nhóm Telegram của chi nhánh.</li>
                </ul>
            </p>
            <div class="position-relative mx-auto">
                <!-- Mobile Device -->
                <figure class="device-mobile mx-auto">
                  <div class="device-mobile-frame">
                    <img class="device-mobile-img" src="./assets/img/others/sepay-qrcode.jpg?v=1" alt="Image Description">
                  </div>
                </figure>
                <!-- End Mobile Device -->
  
                <!-- SVG Shape -->
                <div class="position-absolute top-50 start-50 translate-middle zi-n1" style="width: 25rem;">
                  <img class="img-fluid" src="./assets/svg/components/shape-2.svg" alt="SVG">
                </div>
                <!-- End SVG Shape -->
              </div>
            <p class="fw-bold mt-3">Lợi ích mà SePay đem lại</p>
            
            <p>
                <ul class="lh-lg">
                    <li>Việc thanh toán chuyển khoản đã tiện lợi tương đương với cổng thanh toán. Khách hàng chỉ cần mở app ngân hàng và quét mã QR để chuyển khoản. Sau 0 đến 15 giây đã thanh toán thành công.</li>
                    <li>Nhờ đẩy mạnh thanh toán qua QR Code. Di Động Việt đã giảm được rất nhiều chi phí cho cổng thanh toán.</li>
                    <li>Giảm workload cho kế toán. Bây giờ bộ phận kế toán không còn phải kiểm tra giao dịch. Nhân viên bán hàng tự biết giao dịch qua Telegram.</li>
                    <li>Đối soát dễ dàng.</li>
                </ul>
            </p>

            <h2 class="h3 py-3">Các tính năng của SePay mà Di Động Việt đã sử dụng</h2>
            <p>
                <ul class="lh-lg">
                    <li><b><a href="https://qr.sepay.vn">Tạo QR Code động</a> chuẩn Việt QR</b>: Bởi qr.sepay.vn</li>
                    <li><b><a href="cong-thanh-toan-truc-tuyen.html">Xác thực thanh toán</a>:</b> Tích hợp webhooks để SePay bắn thông tin qua POS khi có giao dịch: Giúp tự động xác nhận thanh toán.</li>
                    <li><b><a href="chia-se-bien-dong-so-du.html">Chia sẻ biến động số dư</a>:</b> SePay báo có giao dịch qua nhóm Telegram. Báo chính xác từng chi nhánh bán hàng. Nhân viên bán hàng dễ dàng nắm được thông tin thanh toán của khách.</li>
                </ul>
            </p>
            <!-- End Sticky End Point -->
            <div id="stickyBlockEndPoint"></div>
          </div>
        </div>
        <!-- End Col -->
      </div>
      <!-- End Row -->
    </div>
    <!-- End Content -->

    <!-- Testimonials -->
    <div class="container content-space-b-2 content-space-lg-3">
      <div class="text-center mb-5">
        <img class="avatar avatar-lg avatar-4x3" src="./assets/svg/illustrations/oc-person-2.svg" alt="Illustration">
      </div>
      
      <!-- Blockquote -->
      <figure class="w-md-75 text-center mx-md-auto">
        <blockquote class="blockquote">“Bán một cái laptop 20 triệu, cổng thanh toán thu ngay 400.000đ tiền phí, quá cao! SePay giúp chúng tôi tiết kiệm hàng trăm triệu phí cổng thanh toán mỗi tháng.”
        </blockquote>

        <figcaption class="blockquote-footer text-center">
          Mr. Đạt Nguyễn
          <span class="blockquote-footer-source">CEO Di Động Việt</span>
        </figcaption>
      </figure>
      <!-- End Blockquote -->
    </div>
    <!-- End Testimonials -->

    
  </main>
  <!-- ========== END MAIN CONTENT ========== -->


  
  <!-- ========== FOOTER ========== -->
  <footer class="container content-space-2 content-space-t-lg-3">
    <!-- Links -->
    <div class="row mb-5">
      <div class="col-md-3 mb-5 mb-md-0">
          <!-- Logo -->
          <div class="mb-3">
            <a class="navbar-brand" href="./index.html" aria-label="Space">
              <img class="navbar-brand-logo" src="./assets/img/logo/sepay-blue-154x50.png" alt="Image Description">
            </a>
          </div>
          <!-- End Logo -->

          <!-- List -->
          <ul class="list-unstyled list-py-1">  
            <li><b>Công Ty Cổ Phần SePay</b></li>
            <li><a class="link-sm link-secondary" href="https://goo.gl/maps/HGZVhsbv3NhR7kan8" target="_blank"><i class="bi-geo-alt-fill me-1"></i> 168 Đường số 2, Vạn Phúc City, P. Hiệp Bình Phước, Thủ Đức, TP. Hồ Chí Minh, Việt Nam</a></li>
            <li><a class="link-sm link-secondary" href="tel:0903252427"><i class="bi-telephone-inbound-fill me-1"></i> +84 0903 25 24 27</a></li>
            <li><span class="text-secondary fs-6"><i class="bi bi-envelope-fill me-2"></i> <EMAIL></span></li>
            <li><span class="text-secondary fs-6">MST:  0317887567, cấp ngày 19/06/2023 tại Sở Kế hoạch và Đầu tư TPHCM</span></li>

          </ul>
          <!-- End List -->
          <div class="col-sm-auto">
            <!-- Socials -->
            <ul class="list-inline mb-0">
              <li class="list-inline-item">
                <a class="btn btn-soft-secondary btn-xs btn-icon" href="https://www.facebook.com/sepay.vn">
                  <i class="bi-facebook"></i>
                </a>
              </li>
              <li class="list-inline-item">
                <a class="btn btn-soft-secondary btn-xs btn-icon" href="https://www.youtube.com/@SePayVN">
                  <i class="bi-youtube"></i>
                </a>
              </li>
              <li class="list-inline-item">
                <a class="btn btn-soft-secondary btn-xs btn-icon" href="https://www.pinterest.com/sepayvn/">
                  <i class="bi-pinterest"></i>
                </a>
              </li>
              

              
    
            </ul>
            <!-- End Socials -->
          </div>
      </div>

      <div class="col-sm-4 col-md-3 col-6 mb-3 mb-sm-0">
        <h5 class=" mb-3">THÔNG TIN</h5>

        <!-- List -->
        <ul class="list-unstyled list-py-1">
          <li><a class="text-body" href="gioi-thieu.html">Giới thiệu</a></li>
          <li><a class="text-body" href="bang-gia.html">Bảng giá</a></li>
        
          <li><a class="text-body" href="https://sepay.vn/blog">Blog</a></li>
          <li><a class="text-body" href="lien-he.html">Liên hệ</a></li>
          <li><a class="text-body" target="_blank" href="https://docs.sepay.vn/tich-hop-webhooks.html">WebHooks</a></li>

          <li><a class="text-body" target="_blank" href="https://docs.sepay.vn/gioi-thieu-api.html">API</a></li>

          <li><a class="text-body" href="faq.html">FAQ</a></li>
<li><a class="text-body" href="changelog.html">Changelog</a></li>

        </ul>
        <div><img loading="lazy"  src="/assets/img/others/RapidSSL-SEAL.gif" class="img-fluid"> <a href="https://secure.sitelock.com/public/verify/sepay.vn" target="_blank" rel=nofollow><img src="https://shield.sitelock.com/shield/sepay.vn" loading="lazy" class="img-fluid" style="max-width: 90px;"></a> <a href="http://online.gov.vn/Home/WebDetails/107586" target="_blank" rel=nofollow><img src="https://sepay.vn/assets/img/others/ttbct.png" loading="lazy" class="img-fluid" style="max-width: 150px;"></a></div>
        <!-- End List -->
      </div>

      <div class="col-sm-4 col-md-3 col-6 mb-3 mb-sm-0">
        <h5 class=" mb-3">HƯỚNG DẪN & TÍNH NĂNG</h5>

        <!-- List -->
        <ul class="list-unstyled list-py-1">
          <li>
            <a class="text-body" href="https://docs.sepay.vn/">
              Hướng dẫn sử dụng
            </a>
          </li>
          <li><a class="text-body" href="thanh-toan.html">Hướng dẫn thanh toán</a></li>
          <li><a class="text-body" href="terms-of-service.html">Quy định sử dụng</a></li>
          <li><a class="text-body" href="hoan-tien.html">Quy định hoàn tiền</a></li>
          <li><a class="text-body" href="privacy.html">Chính sách bảo mật</a></li>
          <li>
            <a class="text-body" href="ngan-hang.html">
              Ngân hàng kết nối
            </a>
          </li>
        
         
          <li>
            <a class="text-body" href="affiliate.html">
             Chương trình đối tác
            </a>
          </li>
          <li>
            <a class="text-body" href="chia-se-bien-dong-so-du.html">
             Chia sẻ biến động số dư
            </a>
          </li>
          <li>
            <a class="text-body" href="cong-thanh-toan-truc-tuyen.html">
             Xác thực thanh toán
            </a>
          </li>
          <li>
            <a class="text-body" href="thong-ke-dong-tien.html">
             Thống kê dòng tiền
            </a>
          </li>
                </ul>
        <!-- End List -->
      </div>

      <div class="col-sm-4 col-md-3 col-6 mb-3 mb-sm-0 mx-auto">
        <h5 class=" mb-3">TÍCH HỢP</h5>
        <!-- List -->
        <ul class="list-unstyled list-py-1">

          <li>
            <a class="text-body" href="https://docs.sepay.vn/tich-hop-haravan.html">
              <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/haravan-icon.png" style="width:22px; height: 22px;"> Haravan
            </a>
          </li>

          <li>
            <a class="text-body" href="https://docs.sepay.vn/tich-hop-sapo.html">
              <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/sapo-icon.png" style="width:22px; height: 22px;"> Sapo
            </a>
          </li>
          <li>
            <a class="text-body" href="https://docs.sepay.vn/woocommerce.html">
              <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/woocommerce-icon.png" style="width:22px; height: 22px;"> WooCommerce
            </a>
          </li>
          <li>
            <a class="text-body" href="https://docs.sepay.vn/tich-hop-google-sheets.html">
              <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/google-sheets-icon.png" style="width:22px; height: 22px;"> Google Sheets
            </a>
          </li>
          <li>
            <a class="text-body" href="https://docs.sepay.vn/tich-hop-lark-messenger.html">
              <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/lark/lark-icon.png" style="width:22px; height: 22px;"> Lark
            </a>
          </li>
          <li>
            <a class="text-body" href="https://docs.sepay.vn/tich-hop-telegram.html">
              <i class="bi bi-telegram me-1 text-info fs-3"></i> Telegram
            </a>
          </li>
          
          
         
         
        <h5 class="mt-3 mb-3">KẾT NỐI VỚI SEPAY</h5>
        <!-- List -->
        <ul class="list-unstyled list-py-1">

          <li>
            <a class="text-body" href="https://www.facebook.com/sepay.vn">
              <i class="bi bi-facebook me-1"></i> Facebook
            </a>
          </li>
          <li>
            <a class="text-body" href="https://t.me/s/sepaychannel">
              <i class="bi bi-telegram me-1"></i> Telegram
            </a>
          </li>
          <li>
            <a class="text-body" href="https://www.youtube.com/@SePayVN">
              <i class="bi bi-youtube me-1"></i> Youtube
            </a>
          </li>
         
          <li>
            <a class="text-body" href="https://www.pinterest.com/sepayvn/">
              <i class="bi bi-pinterest me-1"></i> Pinterest
            </a>
          </li>
          <li>
            <a class="text-body" href="https://github.com/sepayvn">
              <i class="bi bi-github me-1"></i> GitHub
            </a>
          </li>
         
           
        </ul>
        <!-- End List -->
      </div>
    </div>
    <!-- End Links -->
    <div class="border-top border-10 my-5"></div>
    <div class="row align-items-sm-center">
      <div class="col-12 mb-4 mb-sm-0">
        <p class="small mb-0 text-center">&copy; SePay 2023. All rights reserved.</p>
      </div>
      <!-- End Col -->

     
      <!-- End Col -->
    </div>
    <!-- End Row -->
  </footer>
  <!-- ========== END FOOTER ========== -->
  
  <!-- JS Global Compulsory  -->
  <script src="./assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

  <!-- JS Implementing Plugins -->
  <script src="./assets/vendor/hs-header/dist/hs-header.min.js"></script>
  <script src="./assets/vendor/hs-mega-menu/dist/hs-mega-menu.min.js"></script>
  <script src="./assets/vendor/hs-sticky-block/dist/hs-sticky-block.min.js"></script>

  <!-- JS Front -->
  <script src="./assets/js/theme.min.js"></script>

  <!-- JS Plugins Init. -->
  <script>
    (function () {
      // INITIALIZATION OF HEADER
      // =======================================================
      new HSHeader('#header').init()


      // INITIALIZATION OF MEGA MENU
      // =======================================================
      new HSMegaMenu('.js-mega-menu', {
        desktop: {
          position: 'left'
        }
      })
  
      // INITIALIZATION OF BOOTSTRAP DROPDOWN
      // =======================================================
      HSBsDropdown.init()

    // INITIALIZATION OF STICKY BLOCKS
    // =======================================================
    new HSStickyBlock('.js-sticky-block', {
    targetSelector: document.getElementById('header').classList.contains('navbar-fixed') ? '#header' : null
    })
 
    })()
    
  </script>


  <!-- Messenger Plugin chat Code -->
  <div id="fb-root"></div>

  <!-- Your Plugin chat code -->
  <div id="fb-customer-chat" class="fb-customerchat">
  </div>

  <script>
    var chatbox = document.getElementById('fb-customer-chat');
    chatbox.setAttribute("page_id", "117903214582465");
    chatbox.setAttribute("attribution", "biz_inbox");
  </script>

  <!-- Your SDK code -->
  <script>
    window.fbAsyncInit = function() {
      FB.init({
        xfbml            : true,
        version          : 'v19.0'
      });
    };

    (function(d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s); js.id = id;
      js.src = 'https://connect.facebook.net/vi_VN/sdk/xfbml.customerchat.js';
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));
  </script>
</body>

</html>