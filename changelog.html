<!DOCTYPE html>
<html lang="en" dir="">

<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

  <!-- Title -->
  <title>Changelog - Những cập nhật mới của SePay</title>

  <!-- Favicon -->
  <link rel="shortcut icon" href="./assets/img/logo/favicon.png" type="image/x-icon" />

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">

  <!-- CSS Implementing Plugins -->
  <link rel="stylesheet" href="./assets/vendor/bootstrap-icons/font/bootstrap-icons.css">
  <link rel="stylesheet" href="./assets/vendor/hs-mega-menu/dist/hs-mega-menu.min.css">


  <!-- CSS Front Template -->
  <link rel="stylesheet" href="./assets/css/theme.min.css">

  <!--contact-box-css-->
  <link rel="stylesheet" href="./assets/css/contact-box.css">
  <!--/contact-box-css-->

  <!-- meta tag -->
  <meta property="og:locale" content="vi_VN" />
  <link rel="canonical" href="https://sepay.vn/chia-se-bien-dong-so-du.html" />
  <meta name="description" content="SePay changelog - Cập nhật những tính thay đổi và tính năng mới của SePay" />
  <meta property="og:type" content="article" />
  <meta property="og:url" content="https://sepay.vn/chia-se-bien-dong-so-du.html" />
  <meta property="og:title" content="Changelog - Những cập nhật mới của SePay" />
  <meta property="og:description" content="SePay changelog - Cập nhật những tính thay đổi và tính năng mới của SePay" />

  <meta property="og:site_name" content="Changelog - Những cập nhật mới của SePay" />
  <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
  <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
  <!-- meta tag -->


  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());

    gtag('config', 'G-J8DLMQTKSQ');
  </script>

</head>

<body>
  <!-- ========== HEADER ========== -->
  <header id="header"
    class="navbar navbar-expand-lg navbar-end navbar-absolute-top navbar-light navbar-show-hide navbar-scrolled"
    data-hs-header-options='{
            "fixMoment": 1000,
            "fixEffect": "slide"
          }'>


    <div class="container">
      <nav class="js-mega-menu navbar-nav-wrap">
        <!-- Default Logo -->
        <a class="navbar-brand" href="https://sepay.vn" aria-label="Front">
          <img class="navbar-brand-logo" src="./assets/img/logo/sepay-blue-154x50.png" alt="Logo">
        </a>
        <!-- End Default Logo -->

        <div class="ms-auto">
          <!-- Dropdown -->
          <div class="dropdown">
            <button type="button" class="btn btn-primary dropdown-toggle" id="navbarUpdatesDropdown"
              data-bs-toggle="dropdown" aria-expanded="false" data-bs-dropdown-animation="">Get updates</button>

            <div id="signUpDropdown" class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarUpdatesDropdown"
              style="min-width: 17rem;">
              <!-- Link -->
              <a class="navbar-dropdown-menu-media-link" href="https://t.me/s/sepaychannel">
                <div class="d-flex">
                  <div class="flex-shrink-0">
                    <i class="bi-telegram fs-3"></i>
                  </div>

                  <div class="flex-grow-1 ms-3">
                    <span class="navbar-dropdown-menu-media-title">Telegram</span>
                    <p class="navbar-dropdown-menu-media-desc">Subscribe kênh Telegram của SePay</p>
                  </div>
                </div>
              </a>
              <!-- End Link -->

              <div class="dropdown-divider"></div>

              <!-- Link -->
              <a class="navbar-dropdown-menu-media-link" href="https://www.facebook.com/sepay.vn">
                <div class="d-flex">
                  <div class="flex-shrink-0">
                    <i class="bi-facebook fs-3"></i>
                  </div>

                  <div class="flex-grow-1 ms-3">
                    <span class="navbar-dropdown-menu-media-title">Facebook</span>
                    <p class="navbar-dropdown-menu-media-desc">Like Fanpage để cập nhật bài post của SePay</p>
                  </div>
                </div>
              </a>
              <!-- End Link -->

              <!-- Link -->
              <a class="navbar-dropdown-menu-media-link" href="https://www.youtube.com/@SePayVN">
                <div class="d-flex">
                  <div class="flex-shrink-0">
                    <i class="bi-youtube fs-3"></i>
                  </div>

                  <div class="flex-grow-1 ms-3">
                    <span class="navbar-dropdown-menu-media-title">Youtube</span>
                    <p class="navbar-dropdown-menu-media-desc">Subscribe kênh Youtube của SePay</p>
                  </div>
                </div>
              </a>
              <!-- End Link -->

            </div>
          </div>
          <!-- End Dropdown -->
        </div>
      </nav>
    </div>


    <script type="application/ld+json"> {
    "@context": "https://schema.org",
    "@graph": [{
        "@type": "Organization",
        "@id": "https://sepay.vn/#organization",
        "name": "SePay",
        "url": "https://sepay.vn/",
        "logo": {
            "@type": "ImageObject",
            "@id": "https://sepay.vn/#logo",
            "inLanguage": "vi_VN",
            "url": "https://sepay.vn/assets/img/logo/sepay-820x820-blue-icon.png",
            "contentUrl": "https://sepay.vn/assets/img/logo/sepay-820x820-blue-icon.png",
            "width": 820,
            "height": 820,
            "caption": "SePay"
        },
        "image": {
            "@id": "https://sepay.vn/#logo"
        }
    }, {
        "@type": "WebSite",
        "@id": "https://sepay.vn/#website",
        "url": "https://sepay.vn/",
        "name": "SePay",
        "description": "SePay cung cấp công cụ giúp chia sẻ biến động số dư ngân hàng, xác thực thanh toán chuyển khoản, thống kê dòng tiền ngân hàng một cách tự động",
        "publisher": {
            "@id": "https://sepay.vn/#organization"
        },
        "inLanguage": "vi_VN"
    }, {
        "@type": "WebPage",
        "@id": "https://sepay.vn/chia-se-bien-dong-so-du.html#webpage",
        "url": "https://sepay.vn/chia-se-bien-dong-so-du.html",
        "name": "Chia sẻ biến động số dư - SePay",
        "isPartOf": {
            "@id": "https://sepay.vn/#website"
        },
        "primaryImageOfPage": {
            "@id": "https://sepay.vn/chia-se-bien-dong-so-du.html#primaryimage"
        },
        "datePublished": "2023-03-19T15:56:22+00:00",
        "dateModified": "2023-03-20T11:06:48+00:00",
        "description": "SePay Bot báo có giao dịch lên group Telegram, Lark Messenger. Nhanh chóng, đúng group, đúng người.",
        "breadcrumb": {
            "@id": "https://sepay.vn/chia-se-bien-dong-so-du.html#breadcrumb"
        },
        "inLanguage": "vi_VN",
        "potentialAction": [{
            "@type": "ReadAction",
            "target": ["https://sepay.vn/chia-se-bien-dong-so-du.html"]
        }]
    }, {
        "@type": "BreadcrumbList",
        "@id": "https://sepay.vn/chia-se-bien-dong-so-du.html#breadcrumb",
        "itemListElement": [{
            "@type": "ListItem",
            "position": 1,
            "name": "Trang chủ",
            "item": "https://sepay.vn/"
        }, {
            "@type": "ListItem",
            "position": 2,
            "name": "Chia sẻ biến động số dư"
        }]
    }]
  } </script>
  </header>

  <!-- ========== END HEADER ========== -->

  <!-- ========== MAIN CONTENT ========== -->
  <main id="content" role="main">
    <div class="container content-space-1 mt-5">
      <!-- Alert -->
      <div class="alert alert-primary mb-5 mb-md-7" role="alert">
        <div class="d-flex">


          <div class="flex-grow-1 ms-3">
            <h4 class="alert-heading">Changelog</h4>
            <hr>
            <p>Những cập nhật mới của SePay.</p>
          </div>
        </div>
      </div>
      <!-- End Alert -->


      <!-- Step Timeline -->
      <ul class="step">
        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2024-07-25</span>

              <ul>
                <li>[Added] SePay phát hành Mobile App trên hai nền tảng iOS và Android. Hỗ trợ <a href="https://docs.sepay.vn/mobile-app.html" target="_blank">chia sẻ biến động số dư qua Mobile App</a> theo thời gian thực.</li>
              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2024-07-23</span>

              <ul>
                <li>[Added] Tích hợp <a href="https://docs.sepay.vn/tich-hop-shopify.html" target="_blank">Shopify</a>, giúp tự động xác thực thanh toán qua chuyển khoản ngân hàng.</li>
              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2024-07-10</span>

              <ul>
                <li>[Added] Hỗ trợ SeABank.</li>
                <li>[Updated] Hỗ trợ đơn vị tiền tệ USD cho MSB qua liên kết SMS Banking.</li>
              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2024-07-04</span>

              <ul>
                <li>[Added] Liên kết với tài khoản ngân hàng BIDV dành cho doanh nghiệp qua API.</li>
              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2024-06-28</span>

              <ul>
                <li>[Added] Hỗ trợ BAC A BANK.</li>
                <li>[Added] Hỗ trợ LPBank.</li>
              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2024-05-08</span>

              <ul>
                <li>[Added] Liên kết với tài khoản ngân hàng MB qua API.</li>
                <li>[Fixed] Sửa lỗi cập nhật biến động số dư tài khoản ngân hàng OCB.</li>
              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2024-03-29</span>

              <ul>
                <li>[Updated] Hỗ trợ Cart & Checkout blocks cho <a href="https://docs.sepay.vn/woocommerce.html" target="_blank">SePay WooCommerce Payment plugin</a>.</li>
              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2024-03-21</span>

              <ul>
                <li>[Added] Thêm <a href="https://my.sepay.vn/company/change_plan">tính năng</a> cho phép người dùng tự nâng cấp/hạ cấp gói dịch vụ.</li>
              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2024-03-18</span>

              <ul>
                <li>[Added] Thêm <a href="https://docs.sepay.vn/ket-noi-ocb-api.html#ho-tro-nhan-sms-tien-ra" target=""_blank>tính năng nhận biến động số dư các giao dịch tiền ra bằng SMS Banking</a> cho tài khoản ngân hàng OCB kết nối thông qua API.</li>
              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->
        
        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2024-03-12</span>

              <ul>
                <li>[Added] Thêm tính năng tùy chỉnh cấu hình tham số của Sapo webhook.</li>
              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2024-03-07</span>

              <ul>
                <li>[Added] Thêm tùy chọn <a href="https://docs.sepay.vn/tich-hop-webhooks.html#retry" target="_blank">gọi lại webhook</a> theo điều kiện trạng thái phản hồi.</li>
              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2024-03-05</span>

              <ul>
                <li>[Added] Hỗ trợ ABBank.</li>
                <li>[Updated] Cải thiện giao diện ở trang đăng nhập, trang đăng ký và trang lấy lại mật khẩu.</li>
                <li>[Updated] Cải thiện xác thực người dùng ở trang đăng nhập, trang đăng ký và trang lấy lại mật khẩu.</li>
              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2024-02-06</span>

              <ul>
                <li>[Fixed] Sửa lỗi không gửi được thông báo mã nhóm chat khi thêm SePay Bot vào nhóm Telegram.</li>
              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2024-01-24</span>

              <ul>
                <li>[Added] Liên kết với tài khoản ngân hàng OCB doanh nghiệp qua API.</li>
              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2024-01-17</span>

              <ul>
                <li>[Fixed] Sửa lỗi không tích hợp được các ngân hàng sử dụng cùng số tài khoản ngân hàng.</li>
                <li>[Fixed] Sửa lỗi hiển thị lịch sử giao dịch khi trùng số tài khoản ngân hàng.</li>
              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-12-18</span>

              <ul>
                <li>[Updated] Cập nhật thông tin hướng dẫn liên kết tài khoản ngân hàng OCB qua API</li>
                <li>[Fixed] Sửa lỗi không làm mới biểu mẫu sau khi tạo tài khoản ảo ở trang chi tiết tài khoản ngân hàng
                  OCB.</li>
                <li>[Fixed] Sửa lỗi trường tự động tạo mã tài khoản ảo ngân hàng OCB.</li>
              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-12-13</span>

              <ul>
                <li>[Added] Chính thức kết nối với Ngân hàng TMCP Phương Đông OCB qua API.</li>
                <li>[Added] Liên kết với tài khoản ngân hàng OCB cá nhân qua API.</li>
                <li>Thông tin về hợp tác giữa SePay và OCB:

                  <ul>
                    <li><a
                        href="https://sepay.vn/blog/sepay-hop-tac-cung-ngan-hang-tmcp-phuong-dong-ocb-trien-khai-dich-vu-api-ngan-hang/">
                        Thông báo hợp tác giữa SePay và Ngân hàng TMCP Phương Đông OCB</a>.</li>
                    <li><a href="https://docs.sepay.vn/ket-noi-ocb-api.html"> Hướng dẫn liên kết tài khoản ngân hàng OCB
                        vào SePay</a>.</li>
                  </ul>
                </li>
                <li>[Updated] Giao diện <a href="https://my.sepay.vn/bankaccount/connect">kết nối ngân hàng mới</a>.</li>
              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-11-27</span>

              <ul>
                <li>[Added] Chính thức kết nối với Kiên Long Bank qua API</li>
                <li>[Added] Liên kết với tài khoản ngân hàng Kiên Long Bank qua API :
                <li>Thông tin về hợp tác giữa SePay và Kiên Long Bank:

                  <ul>
                    <li><a
                        href="https://sepay.vn/blog/sepay-kienlongbank-thong-bao-hop-tac-cung-cap-open-banking-toan-dien/">
                        Thông báo hợp tác giữa SePay và Kiên Long Bank</a>.</li>
                    <li><a href="https://docs.sepay.vn/ket-noi-kienlongbank-api.html"> Hướng dẫn liên kết tài khoản ngân
                        hàng Kiên Long Bank vào SePay</a>.</li>
                  </ul>
                </li>
              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-11-07</span>

              <ul>
                <li>[Updated] Hiện thông tin độ ổn định, tốc độ gửi SMS, phí SMS khi <a
                    href="https://docs.sepay.vn/them-tai-khoan-ngan-hang.html">thêm tài khoản ngân hàng</a>. Hiện hướng
                  dẫn đăng ký SMS biến động số dư các ngân hàng.</li>
                <li><a href="https://docs.sepay.vn/woocommerce.html">Plugin WooCommerce</a>:
                  <ul>
                    <li>[Updated] Tối ưu giao diện CSS.</li>
                    <li>[Updated] Hỗ trợ Digital/Downloadable product. Cho phép download sau khi thanh toán.</li>
                    <li>[Fixed] Fix lỗi json response.</li>
                  </ul>
                </li>
              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-10-27</span>

              <ul>
                <li>[Updated] Tích hợp Telegram: Hỗ trợ Topic. Chỉ cần điền ID topic của nhóm Telegram, SePay sẽ gửi đến
                  đúng topic mà bạn cần.</li>
                <li>[Fixed] Sửa lỗi nhận diện sai số dư tài khoản ngân hàng nếu giao dịch có đơn vị tiền tệ là USD.</li>

              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->


        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-10-19</span>

              <ul>
                <li>[Updated] Tích hợp Haravan: Tối ưu truy vấn API đến Haravan để fix một số lỗi không tìm thấy Order
                  ID.</li>
              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-10-18</span>

              <ul>
                <li>[Added] Hiện thông tin người chuyển khoản (Tên, Số tài khoản) đối với các giao dịch sử dụng MSB API.
                </li>
                <li>[Updated] Tối ưu UI/UX.</li>
              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-10-16</span>

              <ul>
                <li>[Added] Giao diện <a href="https://my.sepay.vn/bankaccount/connect">kết nối ngân hàng mới</a>.</li>
                <li>[Added] Hỗ trợ MSB API. Quý khách có thể liên hệ SePay để được mở API ngân hàng MSB.</li>
                <li>[Fixed] Sửa lỗi tooltip tại giao diện xem giao dịch.</li>

              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-10-04</span>

              <ul>
                <li>[Updated] Cập nhật plugin WordPress WooCommerce. Xem thông tin cập nhật tại <a
                    href="https://docs.sepay.vn/woocommerce.html">Hướng dẫn tích hợp WooCommerce </a>.</li>

              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-09-11</span>

              <ul>
                <li>[Added] SePay phát hành plugin cho WordPress WooCommerce Xem <a
                    href="https://docs.sepay.vn/woocommerce.html">Hướng dẫn tích hợp WooCommerce </a>.</li>
              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-08-08</span>

              <ul>
                <li>[Added] Hỗ trợ <a
                    href="https://sepay.vn/blog/sepay-chinh-thuc-ho-tro-ngan-hang-public-bank-viet-nam/">Public Bank
                    Việt Nam</a>.</li>
                <li>[Updated] Telegram và Lark Messenger: Hỗ trợ điều kiện chỉ báo tin chat <b>Khi tiền vào lớn hơn hoặc
                    bằng</b> hoặc <b>Khi tiền vào nhỏ hơn hoặc bằng</b>.</li>

              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-07-27</span>

              <ul>
                <li>[Updated] SePay + Haravan: Bổ sung gửi email xác nhận đã thanh toán sau khi thanh toán thành công.
                </li>
              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-07-07</span>

              <ul>
                <li>[Added] Cho phép tùy chỉnh nội dung tin chat tại Telegram và Lark Messenger.</li>

              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-06-29</span>

              <ul>
                <li>[Added] Cho phép xem tổng quan dòng tiền theo ngày tại <a
                    href="https://my.sepay.vn/statistics/daily_cashflow">Tổng quan dòng tiền</a>.</li>
              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-06-28</span>

              <ul>
                <li>[Added] Cho phép gọi lại webhook bằng tay tại giao diện Xem chi tiết giao dịch và giao diện Nhật ký
                  Webhooks.</li>
              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-06-22</span>

              <ul>
                <li>[Added] Tùy chọn cấu hình <b>Thời gian lưu dữ liệu</b> tại phần <b>Cấu hình chung</b>. Xem hướng dẫn
                  <a href="https://docs.sepay.vn/cau-hinh-chung.html#storage-time" target="_blank">tại đây</a>.</li>
              </ul>
            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-06-15</span>

              <ul>
                <li>[Added] Tích hợp <a href="https://docs.sepay.vn/tich-hop-google-sheets.html" target="_blank">Google
                    Sheets</a>.</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-06-14</span>

              <ul>
                <li>[Added] Tích hợp Agribank.</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-06-11</span>

              <ul>
                <li>[Added] Tích hợp thông báo giao dịch qua <a
                    href="https://docs.sepay.vn/tich-hop-lark-messenger.html" target="_blank">Lark Messenger</a>.</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-05-15</span>

              <ul>
                <li>[Added] Giới thiệu <a href="https://docs.sepay.vn/gioi-thieu-api.html" target="_blank">SePay
                    API</a>.</li>
                <li>[Updated] Bỏ dấu tên chủ tài khoản ngân hàng khi thêm/ sửa Tài khoản ngân hàng.</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-04-25</span>

              <ul>
                <li>[Added] Phân quyền người dùng theo tài khoản phụ.</li>
                <li>[Updated] Nâng cấp giao diện Xem giao dịch.</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-04-14</span>

              <ul>
                <li>[Added] Tích hợp <a href="https://docs.sepay.vn/tich-hop-haravan.html" target="_blank">Haravan</a>,
                  giúp tự động xác thực thanh toán qua chuyển khoản ngân hàng.</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-04-11</span>

              <ul>
                <li>[Added] Tích hợp <a href="https://docs.sepay.vn/tich-hop-sapo.html" target="_blank">Sapo</a>, giúp
                  tự động xác thực thanh toán qua chuyển khoản ngân hàng.</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-04-10</span>

              <ul>
                <li>[Added] Hỗ trợ ngân hàng VIB.</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-03-30</span>

              <ul>
                <li>[Added] Hỗ trợ Eximbank.</li>
                <li>[Fixed] Fix lỗi không lấy được giao dịch Techcombank.</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-03-26</span>

              <ul>
                <li>[Updated] Tối ưu phân tích giao dịch cho BIDV.</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-03-25</span>

              <ul>
                <li>[Added] In ảnh QR tại giao diện xem tài khoản ngân hàng.</li>
                <li>[Added] Nhúng link ảnh QR code.</li>
                <li>[Added] Ra mắt <a href="https://qr.sepay.vn/" target="_blank">qr.sepay.vn</a>.</li>
                <li>[Added] Thêm phương thức xác thự API Key tại webhooks.</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-03-23</span>

              <ul>
                <li>[Added] Bổ sung tính năng tạo mã Viet QR cho tài khoản ngân hàng chính và phụ.</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-03-21</span>

              <ul>
                <li>[Added] Tính năng Gói dịch vụ để quản lý gói dịch vụ của công ty.</li>
                <li>[Added] Tính năng Hóa đơn để quản lý hóa đơn.</li>
                <li>[Added] Người dùng có thể chọn gói và thanh toán.</li>
                <li>[Added] Hỗ trợ quét QR code để thanh toán và xác thực thanh toán tự động.</li>
                <li>[Updated] Thay đổi cấu trúc menu để tiện lợi hơn.</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->


        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-03-10</span>

              <ul>
                <li>[Added] Hỗ trợ MBBank, BIDV</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-02-24</span>

              <ul>
                <li>[Added] Thống kê: Thống kê Tổng quan dòng tiền và Thống kê số lượng giao dịch</li>
                <li>[Added] Retry webhooks khi webhooks gọi thất bại do network connect timeout.</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-02-21</span>

              <ul>
                <li>[Added] Báo cáo: Báo cáo số dư tài khoản và Báo cáo tiền vào theo tài khoản phụ</li>
                <li>[Fixed] Phân quyền người dùng: Lỗi hiển thị không đúng khi phân quyền</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-02-16</span>

              <ul>
                <li>[Updated] Hỗ trợ ngân hàng ACB, VPBank, Sacombank, Techcombank, HDBank, VietinBank</li>
                <li>[Updated] Thêm thống kê tài khoản phụ tại Dashboard</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->
        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-02-14</span>

              <ul>
                <li>[Added] Company Configuration: Cấu hình các tùy chọn cho công ty như bật/ tắt tài khoản
                  phụ, bật/ tắt xác thực thanh toán tự động, định nghĩa Cấu trúc mã thanh toán</li>
                <li>[Updated] Tối ưu cơ chế xác định tài khoản phụ ngân hàng tại mỗi giao dịch</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->
        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-02-12</span>

              <ul>
                <li>[Added] Company Profile: Hồ sơ công ty</li>
                <li>[Added] Company Log: Nhật ký người dùng</li>
                <li>[Updated] Tối ưu giao diện</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->
        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-02-11</span>

              <ul>
                <li>[Updated] Phân quyền: Thay Company Owner bằng Super Admin. Thêm đối tượng Admin. Admin có
                  toàn quyền, trừ quyền chỉnh sửa Super Admin.</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->
        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-02-08</span>

              <ul>
                <li>[Updated] Transactions: Tối ưu cho giao diện mobile</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->


        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-02-07</span>

              <ul>
                <li>[Updated] Bank Account: Hiển thị số lượng tài khoản phụ</li>
                <li>[Updated] Telegram: Thêm emoji vào nội dung tin nhắn</li>
                <li>[Added] Hỗ trợ giao diện tối (dark mode)</li>
                <li>[Added] Hỗ trợ Quick Menu, sidebar toggle, sidebar behavior</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-02-05</span>

              <ul>
                <li>[Fixed] Transaction: Không lấy được Code thanh toán tại tại một số trường hợp</li>
                <li>[Fixed] WebHooks Log: Sai link Transaction ID</li>
                <li>[Updated] Transaction: Tối ưu cơ chế lấy code thanh toán, tài khoản phụ</li>
                <li>[Added] Telegram: Thêm thông tin trạng thái xác thực thanh toán</li>

              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-02-02</span>

              <ul>
                <li>[Added] Telegram Filter: Bỏ thông báo nếu nếu có từ/ cụm từ được chỉ định</li>
                <li>[Added] Hiển thị thống kê cho WebHooks </li>
                <li>[Added] WebHooks: Định nghĩa loại webhooks là xác thực thanh toán, bỏ qua webhooks nếu
                  không có mã code thanh toán</li>
                <li>[Added] Transaction: Thêm tooltip chú thích các cột dữ liệu</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-02-01</span>

              <ul>
                <li>[Added] Xem chi tiết từng giao dịch</li>
                <li>[Fixed] Telegram gửi tin nhắn lỗi một số trường hợp </li>
                <li>[Added] Telegram bot tự gửi tin nhắn báo chat id khi được thêm vào group </li>
                <li>[Added] Tài khoản phụ: Bổ sung nhận diện qua nội dung thanh toán</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-01-30</span>

              <ul>
                <li>[Added] Tích hợp thông báo qua Telegram</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->

        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-01-18</span>

              <ul>
                <li>[Added] Tài khoản phụ ngân hàng</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->
        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-01-17</span>

              <ul>
                <li>[Added] Phân quyền cho người dùng theo tài khoản ngân hàng</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->
        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-01-16</span>

              <ul>
                <li>[Fixed ] Fix một số lỗi liên quan OAuthen 2</li>
                <li>[Added] Phân quyền cho người dùng</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->
        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-01-12</span>

              <ul>
                <li>[Added] Webhooks: Hỗ trợ OAuthen 2</li>
                <li>[Added] Thêm filter cho transactions</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->
        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-01-11</span>

              <ul>
                <li>[Added] WebHooks</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->
        <!-- Timeline Item -->
        <li class="step-item">
          <div class="step-content-wrapper">
            <span class="step-icon step-icon-soft-dark">
              <i class="bi-calendar3"></i>
            </span>

            <div class="step-content">
              <span class="step-title">2023-01-10</span>

              <ul>
                <li>Phát hành phiên bản đầu tiên với các tính năng cơ bản</li>
              </ul>

            </div>
          </div>
        </li>
        <!-- End Timeline Item -->




      </ul>
      <!-- End Step Timeline -->
    </div>
  </main>
  <!-- ========== END MAIN CONTENT ========== -->

  
   <!-- ========== FOOTER ========== -->
   <footer class="container content-space-2 content-space-t-lg-3">
    <!-- Links -->
    <div class="row mb-5">
      <div class="col-md-3 mb-5 mb-md-0">
        <!-- Logo -->
        <div class="mb-3">
          <a class="navbar-brand" href="./index.html" aria-label="Space">
            <img class="navbar-brand-logo" src="./assets/img/logo/sepay-blue-154x50.png" alt="Image Description">
          </a>
        </div>
        <!-- End Logo -->

        <!-- List -->
        <ul class="list-unstyled list-py-1">
          <li><b>Công Ty Cổ Phần SePay</b></li>
          <li><a class="link-sm link-secondary" href="https://goo.gl/maps/HGZVhsbv3NhR7kan8" target="_blank"><i
                class="bi-geo-alt-fill me-1"></i> 168 Đường số 2, Vạn Phúc City, P. Hiệp Bình Phước, Thủ Đức, TP. Hồ Chí
              Minh, Việt Nam</a></li>
          <li><a class="link-sm link-secondary" href="tel:02873059589"><i class="bi-telephone-inbound-fill me-1"></i>
              02873.059.589</a></li>
          <li><span class="text-secondary fs-6"><i class="bi bi-envelope-fill me-2"></i> <EMAIL></span></li>
          <li><span class="text-secondary fs-6">MST: 0317887567, cấp ngày 19/06/2023 tại Sở Kế hoạch và Đầu tư
              TPHCM</span></li>

        </ul>
        <!-- End List -->
        <div class="col-sm-auto">
          <!-- Socials -->
          <ul class="list-inline mb-0">
            <li class="list-inline-item">
              <a class="btn btn-soft-secondary btn-xs btn-icon" href="https://www.facebook.com/sepay.vn">
                <i class="bi-facebook"></i>
              </a>
            </li>
            <li class="list-inline-item">
              <a class="btn btn-soft-secondary btn-xs btn-icon" href="https://www.youtube.com/@SePayVN">
                <i class="bi-youtube"></i>
              </a>
            </li>
            <li class="list-inline-item">
              <a class="btn btn-soft-secondary btn-xs btn-icon" href="https://www.pinterest.com/sepayvn/">
                <i class="bi-pinterest"></i>
              </a>
            </li>




          </ul>
          <!-- End Socials -->
        </div>
      </div>

      <div class="col-sm-2 col-md-2 col-6 mb-3 mb-sm-0">
        <h5 class=" mb-3">THÔNG TIN</h5>

        <!-- List -->
        <ul class="list-unstyled list-py-1">
          <li><a class="text-body" href="gioi-thieu.html">Giới thiệu</a></li>
          <li><a class="text-body" href="bang-gia.html">Bảng giá</a></li>

          <li><a class="text-body" href="https://sepay.vn/blog">Blog</a></li>
          <li><a class="text-body" href="lien-he.html">Liên hệ</a></li>
          <li><a class="text-body" target="_blank" href="https://docs.sepay.vn/tich-hop-webhooks.html">WebHooks</a></li>

          <li><a class="text-body" target="_blank" href="https://docs.sepay.vn/gioi-thieu-api.html">API</a></li>

          <li><a class="text-body" href="faq.html">FAQ</a></li>
          <li><a class="text-body" href="changelog.html">Changelog</a></li>

        </ul>
        <div><img loading="lazy" src="/assets/img/others/RapidSSL-SEAL.gif" class="img-fluid"> <a
            href="https://secure.sitelock.com/public/verify/sepay.vn" target="_blank" rel=nofollow><img
              src="https://shield.sitelock.com/shield/sepay.vn" loading="lazy" class="img-fluid"
              style="max-width: 90px;"></a> <a href="http://online.gov.vn/Home/WebDetails/107586" target="_blank"
            rel=nofollow><img src="https://sepay.vn/assets/img/others/ttbct.png" loading="lazy" class="img-fluid"
              style="max-width: 150px;"></a></div>
        <!-- End List -->
      </div>

      <div class="col-sm-3 col-md-3 col-6 mb-3 mb-sm-0">
        <h5 class=" mb-3">HƯỚNG DẪN & TÍNH NĂNG</h5>

        <!-- List -->
        <ul class="list-unstyled list-py-1">
          <li>
            <a class="text-body" href="https://docs.sepay.vn/">
              Hướng dẫn sử dụng
            </a>
          </li>
          <li><a class="text-body" href="thanh-toan.html">Hướng dẫn thanh toán</a></li>
          <li><a class="text-body" href="terms-of-service.html">Quy định sử dụng</a></li>
          <li><a class="text-body" href="hoan-tien.html">Quy định hoàn tiền</a></li>
          <li><a class="text-body" href="privacy.html">Chính sách bảo mật</a></li>
          <li>
            <a class="text-body" href="ngan-hang.html">
              Ngân hàng kết nối
            </a>
          </li>


          <li>
            <a class="text-body" href="affiliate.html">
              Tiếp thị liên kết
            </a>
          </li>
          <li>
            <a class="text-body" href="chia-se-bien-dong-so-du.html">
              Chia sẻ biến động số dư
            </a>
          </li>
          <li>
            <a class="text-body" href="cong-thanh-toan-truc-tuyen.html">
              Cổng thanh toán trực tuyến
            </a>
          </li>
          <li>
            <a class="text-body" href="thong-ke-dong-tien.html">
              Thống kê dòng tiền
            </a>
          </li>
        </ul>
        <!-- End List -->
      </div>

      <div class="col-sm-4 row">
        <div class="col-6 mb-3 mb-sm-0 mx-auto">
          <h5 class=" mb-3">TÍCH HỢP</h5>
          <!-- List -->
          <ul class="list-unstyled list-py-1">
  
            <li>
              <a class="text-body" href="https://docs.sepay.vn/tich-hop-haravan.html">
                <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/haravan-icon.png"
                  style="width:22px; height: 22px;"> Haravan
              </a>
            </li>
  
            <li>
              <a class="text-body" href="https://docs.sepay.vn/tich-hop-sapo.html">
                <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/sapo-icon.png"
                  style="width:22px; height: 22px;"> Sapo
              </a>
            </li>

            <li>
              <a class="text-body" href="https://docs.sepay.vn/tich-hop-shopify.html">
                <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/shopify-icon.png"
                  style="width:22px; height: 22px;"> Shopify
              </a>
            </li>
            
            <li>
              <a class="text-body" href="https://docs.sepay.vn/woocommerce.html">
                <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/woocommerce-icon.png"
                  style="width:22px; height: 22px;"> WooCommerce
              </a>
            </li>
            <li>
              <a class="text-body" href="https://docs.sepay.vn/tich-hop-google-sheets.html">
                <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/google-sheets-icon.png"
                  style="width:22px; height: 22px;"> Google Sheets
              </a>
            </li>
            <li>
              <a class="text-body" href="https://docs.sepay.vn/tich-hop-lark-messenger.html">
                <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/lark/lark-icon.png"
                  style="width:22px; height: 22px;"> Lark
              </a>
            </li>
            <li>
              <a class="text-body" href="https://docs.sepay.vn/tich-hop-telegram.html">
                <i class="bi bi-telegram me-1 text-info fs-3"></i> Telegram
              </a>
            </li>
  
  
  
  
            
        </div>
        <div class="col-6 mb-3 mb-sm-0 mx-auto">
          <h5 class=" mb-3">KẾT NỐI VỚI SEPAY</h5>
           <!-- List -->
           <ul class="list-unstyled list-py-1">
  
            <li>
              <a class="text-body" href="https://www.facebook.com/sepay.vn">
                <i class="bi bi-facebook me-1"></i> Facebook
              </a>
            </li>
            <li>
              <a class="text-body" href="https://t.me/s/sepaychannel">
                <i class="bi bi-telegram me-1"></i> Telegram
              </a>
            </li>
            <li>
              <a class="text-body" href="https://www.youtube.com/@SePayVN">
                <i class="bi bi-youtube me-1"></i> Youtube
              </a>
            </li>
  
            <li>
              <a class="text-body" href="https://www.pinterest.com/sepayvn/">
                <i class="bi bi-pinterest me-1"></i> Pinterest
              </a>
            </li>
            <li>
              <a class="text-body" href="https://github.com/sepayvn">
                <i class="bi bi-github me-1"></i> GitHub
              </a>
            </li>
  
  
  
          </ul>
          <!-- End List -->
        </div>
        <div class="col-12">
          <span class="text-cap mt-1">Hợp tác chiến lược:</span>
          <div class="row">
            <div class="col-3 py-3">
              <a href="https://sepay.vn/mb.html"><img class="avatar avatar-lg avatar-4x3" src="https://qr.sepay.vn/assets/img/banklogo/MB.png" alt="MBBank"></a>
            </div>
            <!-- End Col -->
  
            <div class="col-3 py-3">
              <a href="https://sepay.vn/ocb.html"><img class="avatar avatar-lg avatar-4x3" src="https://qr.sepay.vn/assets/img/banklogo/OCB.png" alt="OCB"></a>
            </div>
            <!-- End Col -->
            <div class="col-3 py-3">
              <a href="https://sepay.vn/kien-long-bank.html"><img class="avatar avatar-lg avatar-4x3" src="https://sepay.vn/blog/wp-content/uploads/2024/05/kienlongbank-logo.png" alt="KienlongBank"></a>
            </div>
            <!-- End Col -->
  
            <div class="col-3 py-3">
              <a href="https://sepay.vn/msb.html"><img class="avatar avatar-lg avatar-4x3" src="https://qr.sepay.vn/assets/img/banklogo/MSB.png" alt="MSB"></a>
            </div>
            <!-- End Col -->
          </div>
        </div>
      </div>
    </div>
    <!-- End Links -->
    <div class="border-top border-10 my-5"></div>
    <div class="row align-items-sm-center">
      <div class="col-12 mb-4 mb-sm-0">
        <p class="small mb-0 text-center">&copy; SePay 2023. All rights reserved.</p>
      </div>
      <!-- End Col -->


      <!-- End Col -->
    </div>
    <!-- End Row -->
  </footer>
  <!-- ========== END FOOTER ========== -->

  <!--contact-box-html-->
  <div id="contact-box-overlay"></div>
  <div id="contact-box">
      <div class="popup">
          <a href="https://m.me/***************" target="_blank" class="item">
              <div class="logo">
                  <img src="assets/img/others/fb-messenger.png" width="50%" />
              </div>
              <div class="meta">
                  <p class="title">Facebook Messenger</p>
                  <small class="description">Hỗ trợ live chat 24/7</small>
              </div>
          </a>
          <div class="divide"></div>
          <a href="tel:02873059589" class="item">
              <div class="icon" style="background-color: #22c55e; color: #fff;">
                  <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;"><path d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" fill="transparent"></path></svg>
              </div>
              <div class="meta">
                  <p class="title">Hotline</p>
                  <small class="description">Điện thoại hỗ trợ</small>
              </div>
          </a>
          <div class="divide"></div>
          <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
              <div class="logo">
                  <img src="assets/img/others/youtube-social.png" />
              </div>
              <div class="meta">
                  <p class="title">Youtube</p>
                  <small class="description">Theo dõi video mới nhất của SePay</small>
              </div>
          </a>
          <div class="divide"></div>
          <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
              <div class="logo">
                  <img src="assets/img/others/telegram-social.png" />
              </div>
              <div class="meta">
                  <p class="title">Telegram</p>
                  <small class="description">Nhận thông tin mới nhất từ SePay</small>
              </div>
          </a>
      </div>
      <div class="container">
          <div class="dot-ping">
              <div class="ping"></div>
              <div class="dot"></div>
          </div>
          <div class="contact-icon">
              <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;"><path fill-rule="evenodd" clip-rule="evenodd" d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z" fill="currentColor"></path></svg>
          </div>
          <span style="font-weight: bold;">Liên hệ chúng tôi</span>
      </div>
  </div>
  <!--/contact-box-html-->

  <!-- JS Global Compulsory  -->
  <script src="./assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

  <!-- JS Implementing Plugins -->
  <script src="./assets/vendor/hs-header/dist/hs-header.min.js"></script>
  <script src="./assets/vendor/hs-mega-menu/dist/hs-mega-menu.min.js"></script>
  <!-- JS Front -->
  <script src="./assets/js/theme.min.js"></script>

  <!-- contact-box-js -->
  <script src="./assets/js/contact-box.js"></script>
  <!-- /contact-box-js -->

  <!-- JS Plugins Init. -->
  <script>
    (function () {
      // INITIALIZATION OF HEADER
      // =======================================================
      new HSHeader('#header').init()


      // INITIALIZATION OF MEGA MENU
      // =======================================================
      new HSMegaMenu('.js-mega-menu', {
        desktop: {
          position: 'left'
        }
      })

      // INITIALIZATION OF BOOTSTRAP DROPDOWN
      // =======================================================
      HSBsDropdown.init()

    })()

  </script>
</body>

</html>