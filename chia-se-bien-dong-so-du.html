<!DOCTYPE html>
<html lang="en" dir="">

<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

  <!-- Title -->
  <title>Chia sẻ biến động số dư ngân hàng</title>

  <!-- Favicon -->
  <link rel="shortcut icon" href="./assets/img/logo/favicon.png" type="image/x-icon" />

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">

  <!-- CSS Implementing Plugins -->
  <link rel="stylesheet" href="./assets/vendor/bootstrap-icons/font/bootstrap-icons.css">
  <link rel="stylesheet" href="./assets/vendor/hs-mega-menu/dist/hs-mega-menu.min.css">
  

  <!-- CSS Front Template -->
  <link rel="stylesheet" href="./assets/css/theme.min.css">

  <!--contact-box-css-->
  <link rel="stylesheet" href="./assets/css/contact-box.css">
  <!--/contact-box-css-->

  <!-- meta tag -->
  <meta property="og:locale" content="vi_VN" />
  <link rel="canonical" href="https://sepay.vn/chia-se-bien-dong-so-du.html" />
  <meta name="description" content="SePay Bot báo có giao dịch lên group Telegram, Lark Messenger, giúp nhân viên của bạn kịp thời nắm bắt giao dịch từ khách hàng."/>
  <meta property="og:type" content="article" />
  <meta property="og:url" content="https://sepay.vn/chia-se-bien-dong-so-du.html" />
  <meta property="og:title" content="Chia sẻ biến động số dư ngân hàng" />
  <meta property="og:description" content="SePay Bot báo có giao dịch lên group Telegram, Lark Messenger, giúp nhân viên của bạn kịp thời nắm bắt giao dịch từ khách hàng." />

  <meta property="og:site_name" content="Chia sẻ biến động số dư ngân hàng" />
  <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
  <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
  <!-- meta tag -->
  

  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-J8DLMQTKSQ');
  </script>

</head>

<body>
  <!-- ========== HEADER ========== -->
  <header id="header" class="navbar navbar-expand-lg navbar-end navbar-absolute-top navbar-light navbar-show-hide navbar-scrolled"
    data-hs-header-options='{
            "fixMoment": 1000,
            "fixEffect": "slide"
          }'>


    <div class="container">
      <nav class="js-mega-menu navbar-nav-wrap">
        <!-- Default Logo -->
        <a class="navbar-brand" href="https://sepay.vn" aria-label="Front">
          <img class="navbar-brand-logo" src="./assets/img/logo/sepay-blue-154x50.png" alt="Logo">
        </a>
        <!-- End Default Logo -->

        <!-- Toggler -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown"
          aria-controls="navbarNavDropdown" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-default">
            <i class="bi-list"></i>
          </span>
          <span class="navbar-toggler-toggled">
            <i class="bi-x"></i>
          </span>
        </button>
        <!-- End Toggler -->

        <!-- Collapse -->
        <div class="collapse navbar-collapse" id="navbarNavDropdown">
          <div class="navbar-absolute-top-scroller">
            <ul class="navbar-nav">
              

              <!-- Features -->
              <li class="hs-has-sub-menu nav-item">
                <a id="coursesMegaMenu" class="hs-mega-menu-invoker nav-link dropdown-toggle" href="#" role="button"> Tính năng</a>

                <!-- Mega Menu -->
                <div class="hs-sub-menu dropdown-menu" aria-labelledby="coursesMegaMenu" style="min-width: 20rem;">


                  <a class="navbar-dropdown-menu-media-link" href="chia-se-bien-dong-so-du.html">
                    <div class="d-flex">
                      <div class="flex-shrink-0">
                        <span class="svg-icon svg-icon-sm text-primary">
                          <span class="svg-icon svg-icon-sm text-primary">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M22.1671 18.1421C22.4827 18.4577 23.0222 18.2331 23.0206 17.7868L23.0039 13.1053V5.52632C23.0039 4.13107 21.8729 3 20.4776 3H8.68815C7.2929 3 6.16183 4.13107 6.16183 5.52632V9H13C14.6568 9 16 10.3431 16 12V15.6316H19.6565L22.1671 18.1421Z"
                                fill="#035A4B" />
                              <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd"
                                d="M1.98508 18V13C1.98508 11.8954 2.88051 11 3.98508 11H11.9851C13.0896 11 13.9851 11.8954 13.9851 13V18C13.9851 19.1046 13.0896 20 11.9851 20H4.10081L2.85695 21.1905C2.53895 21.4949 2.01123 21.2695 2.01123 20.8293V18.3243C1.99402 18.2187 1.98508 18.1104 1.98508 18ZM5.99999 14.5C5.99999 14.2239 6.22385 14 6.49999 14H11.5C11.7761 14 12 14.2239 12 14.5C12 14.7761 11.7761 15 11.5 15H6.49999C6.22385 15 5.99999 14.7761 5.99999 14.5ZM9.49999 16C9.22385 16 8.99999 16.2239 8.99999 16.5C8.99999 16.7761 9.22385 17 9.49999 17H11.5C11.7761 17 12 16.7761 12 16.5C12 16.2239 11.7761 16 11.5 16H9.49999Z"
                                fill="#035A4B" />
                            </svg>

                          </span>

                        </span>
                      </div>

                      <div class="flex-grow-1 ms-3">
                        <span class="navbar-dropdown-menu-media-title">Chia sẻ biến động số dư</span>
                        <p class="navbar-dropdown-menu-media-desc">Báo lên nhóm chat khi nhận chuyển khoản</p>
                      </div>
                    </div>
                  </a>

                  <div class="dropdown-divider"></div>

                  <a class="navbar-dropdown-menu-media-link" href="cong-thanh-toan-truc-tuyen.html">
                    <div class="d-flex">
                      <div class="flex-shrink-0">
                        <span class="svg-icon svg-icon-sm text-primary">
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path opacity="0.3" d="M18 10V20C18 20.6 18.4 21 19 21C19.6 21 20 20.6 20 20V10H18Z"
                              fill="#035A4B" />
                            <path opacity="0.3"
                              d="M11 10V17H6V10H4V20C4 20.6 4.4 21 5 21H12C12.6 21 13 20.6 13 20V10H11Z"
                              fill="#035A4B" />
                            <path opacity="0.3" d="M10 10C10 11.1 9.1 12 8 12C6.9 12 6 11.1 6 10H10Z" fill="#035A4B" />
                            <path opacity="0.3" d="M18 10C18 11.1 17.1 12 16 12C14.9 12 14 11.1 14 10H18Z"
                              fill="#035A4B" />
                            <path opacity="0.3" d="M14 4H10V10H14V4Z" fill="#035A4B" />
                            <path opacity="0.3" d="M17 4H20L22 10H18L17 4Z" fill="#035A4B" />
                            <path opacity="0.3" d="M7 4H4L2 10H6L7 4Z" fill="#035A4B" />
                            <path
                              d="M6 10C6 11.1 5.1 12 4 12C2.9 12 2 11.1 2 10H6ZM10 10C10 11.1 10.9 12 12 12C13.1 12 14 11.1 14 10H10ZM18 10C18 11.1 18.9 12 20 12C21.1 12 22 11.1 22 10H18ZM19 2H5C4.4 2 4 2.4 4 3V4H20V3C20 2.4 19.6 2 19 2ZM12 17C12 16.4 11.6 16 11 16H6C5.4 16 5 16.4 5 17C5 17.6 5.4 18 6 18H11C11.6 18 12 17.6 12 17Z"
                              fill="#035A4B" />
                          </svg>

                        </span>
                      </div>

                      <div class="flex-grow-1 ms-3">
                        <span class="navbar-dropdown-menu-media-title">Cổng thanh toán trực tuyến</span>
                        <p class="navbar-dropdown-menu-media-desc">Cổng thanh toán QR miễn phí</p>
                      </div>
                    </div>
                  </a>

                  <div class="dropdown-divider"></div>

                  <a class="navbar-dropdown-menu-media-link" href="thong-ke-dong-tien.html">
                    <div class="d-flex">
                      <div class="flex-shrink-0">
                        <span class="svg-icon svg-icon-sm text-primary">
                          <span class="svg-icon svg-icon-sm text-primary">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd"
                                d="M12 17C16.4183 17 20 13.4183 20 9C20 4.58172 16.4183 1 12 1C7.58172 1 4 4.58172 4 9C4 13.4183 7.58172 17 12 17Z"
                                fill="#035A4B" />
                              <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd"
                                d="M6.53819 9L10.568 19.3624L11.976 18.8149L13.3745 19.3674L17.4703 9H6.53819ZM9.46188 11H14.5298L11.9759 17.4645L9.46188 11Z"
                                fill="#035A4B" />
                              <path opacity="0.3"
                                d="M10 22H14V22C14 23.1046 13.1046 24 12 24V24C10.8954 24 10 23.1046 10 22V22Z"
                                fill="#035A4B" />
                              <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M8 17C8 16.4477 8.44772 16 9 16H15C15.5523 16 16 16.4477 16 17C16 17.5523 15.5523 18 15 18C15.5523 18 16 18.4477 16 19C16 19.5523 15.5523 20 15 20C15.5523 20 16 20.4477 16 21C16 21.5523 15.5523 22 15 22H9C8.44772 22 8 21.5523 8 21C8 20.4477 8.44772 20 9 20C8.44772 20 8 19.5523 8 19C8 18.4477 8.44772 18 9 18C8.44772 18 8 17.5523 8 17Z"
                                fill="#035A4B" />
                            </svg>

                          </span>

                        </span>
                      </div>

                      <div class="flex-grow-1 ms-3">
                        <span class="navbar-dropdown-menu-media-title">Thống kê dòng tiền</span>
                        <p class="navbar-dropdown-menu-media-desc">Nắm bắt được dòng tiền tất cả tài khoản</p>
                      </div>
                    </div>
                  </a>

                </div>

              </li>
              <!-- End Features -->



              <li class="nav-item">
                <a class="nav-link" href="why-sepay.html">Why SePay?</a>
              </li>

 

              <li class="nav-item">
                <a class="nav-link" href="bang-gia.html">Bảng giá</a>
              </li>

              <li class="nav-item">
                <a class="nav-link" href="https://docs.sepay.vn" target="_blank">Hướng dẫn</a>
              </li>
             
              <li class="nav-item">
                <a class="nav-link" href="https://sepay.vn/blog">Blog</a>
              </li>

                <!-- Cooperate -->
                <li class="hs-has-sub-menu nav-item">
                  <a id="cooperateMegaMenu" class="hs-mega-menu-invoker nav-link dropdown-toggle" href="#"
                      role="button"> Hợp tác</a>

                  <!-- Mega Menu -->
                  <div class="hs-sub-menu dropdown-menu" aria-labelledby="cooperateMegaMenu"
                      style="min-width: 20rem;">


                      <a class="navbar-dropdown-menu-media-link" href="sepay-bank-hub.html">
                          <div class="d-flex">
                              <div class="flex-shrink-0">
                                  <span class="svg-icon svg-icon-sm text-primary">
                                      <span class="svg-icon svg-icon-sm text-primary">
                                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                              xmlns="http://www.w3.org/2000/svg">
                                              <path fill-rule="evenodd" clip-rule="evenodd"
                                                  d="M15 19.5229C15 20.265 15.9624 20.5564 16.374 19.9389L22.2227 11.166C22.5549 10.6676 22.1976 10 21.5986 10H17V4.47708C17 3.73503 16.0376 3.44363 15.626 4.06106L9.77735 12.834C9.44507 13.3324 9.80237 14 10.4014 14H15V19.5229Z"
                                                  fill="#035A4B"></path>
                                              <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd"
                                                  d="M3 6.5C3 5.67157 3.67157 5 4.5 5H9.5C10.3284 5 11 5.67157 11 6.5C11 7.32843 10.3284 8 9.5 8H4.5C3.67157 8 3 7.32843 3 6.5ZM3 18.5C3 17.6716 3.67157 17 4.5 17H9.5C10.3284 17 11 17.6716 11 18.5C11 19.3284 10.3284 20 9.5 20H4.5C3.67157 20 3 19.3284 3 18.5ZM2.5 11C1.67157 11 1 11.6716 1 12.5C1 13.3284 1.67157 14 2.5 14H6.5C7.32843 14 8 13.3284 8 12.5C8 11.6716 7.32843 11 6.5 11H2.5Z"
                                                  fill="#035A4B"></path>
                                          </svg>

                                      </span>

                                  </span>
                              </div>

                              <div class="flex-grow-1 ms-3">
                                <span class="navbar-dropdown-menu-media-title">SePay Bank Hub</span>
                                <p class="navbar-dropdown-menu-media-desc"> Hợp nhất API ngân hàng</p>
                              </div>
                          </div>
                      </a>

                      <div class="dropdown-divider"></div>

                      <a class="navbar-dropdown-menu-media-link" href="affiliate.html">
                          <div class="d-flex">
                              <div class="flex-shrink-0">
                                  <span class="svg-icon svg-icon-sm text-primary">
                                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                          xmlns="http://www.w3.org/2000/svg">
                                          <path opacity="0.3"
                                              d="M12.5 22C11.9 22 11.5 21.6 11.5 21V3C11.5 2.4 11.9 2 12.5 2C13.1 2 13.5 2.4 13.5 3V21C13.5 21.6 13.1 22 12.5 22Z"
                                              fill="#035A4B"></path>
                                          <path
                                              d="M17.8 14.7C17.8 15.5 17.6 16.3 17.2 16.9C16.8 17.6 16.2 18.1 15.3 18.4C14.5 18.8 13.5 19 12.4 19C11.1 19 10 18.7 9.10001 18.2C8.50001 17.8 8.00001 17.4 7.60001 16.7C7.20001 16.1 7 15.5 7 14.9C7 14.6 7.09999 14.3 7.29999 14C7.49999 13.8 7.80001 13.6 8.20001 13.6C8.50001 13.6 8.69999 13.7 8.89999 13.9C9.09999 14.1 9.29999 14.4 9.39999 14.7C9.59999 15.1 9.8 15.5 10 15.8C10.2 16.1 10.5 16.3 10.8 16.5C11.2 16.7 11.6 16.8 12.2 16.8C13 16.8 13.7 16.6 14.2 16.2C14.7 15.8 15 15.3 15 14.8C15 14.4 14.9 14 14.6 13.7C14.3 13.4 14 13.2 13.5 13.1C13.1 13 12.5 12.8 11.8 12.6C10.8 12.4 9.99999 12.1 9.39999 11.8C8.69999 11.5 8.19999 11.1 7.79999 10.6C7.39999 10.1 7.20001 9.39998 7.20001 8.59998C7.20001 7.89998 7.39999 7.19998 7.79999 6.59998C8.19999 5.99998 8.80001 5.60005 9.60001 5.30005C10.4 5.00005 11.3 4.80005 12.3 4.80005C13.1 4.80005 13.8 4.89998 14.5 5.09998C15.1 5.29998 15.6 5.60002 16 5.90002C16.4 6.20002 16.7 6.6 16.9 7C17.1 7.4 17.2 7.69998 17.2 8.09998C17.2 8.39998 17.1 8.7 16.9 9C16.7 9.3 16.4 9.40002 16 9.40002C15.7 9.40002 15.4 9.29995 15.3 9.19995C15.2 9.09995 15 8.80002 14.8 8.40002C14.6 7.90002 14.3 7.49995 13.9 7.19995C13.5 6.89995 13 6.80005 12.2 6.80005C11.5 6.80005 10.9 7.00005 10.5 7.30005C10.1 7.60005 9.79999 8.00002 9.79999 8.40002C9.79999 8.70002 9.9 8.89998 10 9.09998C10.1 9.29998 10.4 9.49998 10.6 9.59998C10.8 9.69998 11.1 9.90002 11.4 9.90002C11.7 10 12.1 10.1 12.7 10.3C13.5 10.5 14.2 10.7 14.8 10.9C15.4 11.1 15.9 11.4 16.4 11.7C16.8 12 17.2 12.4 17.4 12.9C17.6 13.4 17.8 14 17.8 14.7Z"
                                              fill="#035A4B"></path>
                                      </svg>
                                  </span>
                              </div>

                              <div class="flex-grow-1 ms-3">
                                  <span class="navbar-dropdown-menu-media-title">Tiếp thị liên kết -
                                      Affiliate</span>
                                  <p class="navbar-dropdown-menu-media-desc"> Hoa hồng lên đến 30%</p>
                              </div>
                          </div>
                      </a>



                  </div>

              </li>
              <!-- End Cooperate -->

              <!-- Company -->
               <li class="hs-has-sub-menu nav-item">
                <a id="accountMegaMenu" class="hs-mega-menu-invoker nav-link dropdown-toggle " href="#" role="button" aria-expanded="false">Công ty</a>

                <!-- Mega Menu -->
                <div class="hs-sub-menu dropdown-menu" aria-labelledby="accountMegaMenu" style="min-width: 14rem;">
                    <a class="dropdown-item " href="./gioi-thieu.html">Giới thiệu</a>
                    <a class="dropdown-item " href="./chung-nhan.html">Chứng nhận</a>

                  <!-- Authentication -->
                  <div class="hs-has-sub-menu nav-item">
                    <a id="authenticationMegaMenu" class="hs-mega-menu-invoker dropdown-item dropdown-toggle " href="#" role="button" aria-expanded="false">Ký kết hợp tác</a>

                    <div class="hs-sub-menu dropdown-menu" aria-labelledby="authenticationMegaMenu" style="min-width: 14rem;">
                      <a class="dropdown-item " href="./kien-long-bank.html">Ngân hàng Kiên Long</a>
                      <a class="dropdown-item " href="./ocb.html">Ngân hàng OCB</a>
                      <a class="dropdown-item " href="./msb.html">Ngân hàng MSB</a>
                      <a class="dropdown-item " href="./mb.html">Ngân hàng MB</a>
                     </div>
                  </div>
                  <!-- End Authentication -->

                  <a class="dropdown-item " href="./ngan-hang.html">Ngân hàng kết nối (22)</a>
                   
                </div>
                <!-- End Mega Menu -->
              </li>
              <!-- End Company -->
              <!-- Button -->
              <li class="nav-item mt-5 mt-lg-0">

                <a class="btn btn-light btn-transition"
                href="https://my.sepay.vn/login"
                target="_blank">Đăng nhập</a>
                
                <a class="btn btn-primary btn-transition"
                  href="https://my.sepay.vn/register"
                  target="_blank">Đăng ký</a>
              </li>
              <!-- End Button -->
            </ul>
          </div>
        </div>
        <!-- End Collapse -->
      </nav>
    </div>


  <script type = "application/ld+json"> {
    "@context": "https://schema.org",
    "@graph": [{
        "@type": "Organization",
        "@id": "https://sepay.vn/#organization",
        "name": "SePay",
        "url": "https://sepay.vn/",
        "logo": {
            "@type": "ImageObject",
            "@id": "https://sepay.vn/#logo",
            "inLanguage": "vi_VN",
            "url": "https://sepay.vn/assets/img/logo/sepay-820x820-blue-icon.png",
            "contentUrl": "https://sepay.vn/assets/img/logo/sepay-820x820-blue-icon.png",
            "width": 820,
            "height": 820,
            "caption": "SePay"
        },
        "image": {
            "@id": "https://sepay.vn/#logo"
        }
    }, {
        "@type": "WebSite",
        "@id": "https://sepay.vn/#website",
        "url": "https://sepay.vn/",
        "name": "SePay",
        "description": "SePay cung cấp công cụ giúp chia sẻ biến động số dư ngân hàng, xác thực thanh toán chuyển khoản, thống kê dòng tiền ngân hàng một cách tự động",
        "publisher": {
            "@id": "https://sepay.vn/#organization"
        },
        "inLanguage": "vi_VN"
    }, {
        "@type": "WebPage",
        "@id": "https://sepay.vn/chia-se-bien-dong-so-du.html#webpage",
        "url": "https://sepay.vn/chia-se-bien-dong-so-du.html",
        "name": "Chia sẻ biến động số dư - SePay",
        "isPartOf": {
            "@id": "https://sepay.vn/#website"
        },
        "primaryImageOfPage": {
            "@id": "https://sepay.vn/chia-se-bien-dong-so-du.html#primaryimage"
        },
        "datePublished": "2023-03-19T15:56:22+00:00",
        "dateModified": "2023-03-20T11:06:48+00:00",
        "description": "SePay Bot báo có giao dịch lên group Telegram, Lark Messenger. Nhanh chóng, đúng group, đúng người.",
        "breadcrumb": {
            "@id": "https://sepay.vn/chia-se-bien-dong-so-du.html#breadcrumb"
        },
        "inLanguage": "vi_VN",
        "potentialAction": [{
            "@type": "ReadAction",
            "target": ["https://sepay.vn/chia-se-bien-dong-so-du.html"]
        }]
    }, {
        "@type": "BreadcrumbList",
        "@id": "https://sepay.vn/chia-se-bien-dong-so-du.html#breadcrumb",
        "itemListElement": [{
            "@type": "ListItem",
            "position": 1,
            "name": "Trang chủ",
            "item": "https://sepay.vn/"
        }, {
            "@type": "ListItem",
            "position": 2,
            "name": "Chia sẻ biến động số dư"
        }]
    }]
  } </script>
  </header>

  <!-- ========== END HEADER ========== -->

  <!-- ========== MAIN CONTENT ========== -->
  <main id="content" role="main">
    <!-- Hero -->
   <!-- <div class="container content-space-t-4 content-space-t-lg-5 content-space-b-2 content-space-b-lg-3">--> 
    <div class="position-relative overflow-hidden">
      <div class="container content-space-t-4 content-space-b-lg-1">
      <div class="row justify-content-lg-between align-items-lg-center mb-10">
        <div class="col-md-6 col-lg-5">
          <!-- Heading -->
          <div class="mb-5">
            <h1>Nhận biết thanh toán trong vài giây</h1>
            <p>SePay Bot báo có giao dịch lên group Telegram, Lark Messenger. Nhanh chóng, đúng group, đúng người.</p>
          </div>
          <!-- End Title & Description -->

          <div class="d-grid d-sm-flex gap-3">
            <a class="btn btn-primary btn-transition" href="https://my.sepay.vn/register">Đăng ký ngay</a>
            <a class="btn btn-link" href="lien-he.html">Tư vấn <i class="bi-chevron-right small ms-1"></i></a>
          </div>
        </div>
        <!-- End Col -->

        <div class="col-sm-7 col-md-6 d-none d-md-block">
          <img class="img-fluid" src="./assets/svg/illustrations/oc-relaxing.svg" alt="Image Description">
        </div>
        <!-- End Col -->
      </div>
      <!-- End Row -->
    </div>
 
    </div>
    <!-- End Hero -->

    <!-- Feature Nav -->
    <div class="container content-space-1">
      <!-- Heading -->
      <div class="w-md-75 w-lg-50 text-center mx-md-auto mb-5 mb-md-9">

        <h2>Cách nhanh nhất để biết được<br> có khách thanh toán</h2>
      </div>
      <!-- End Heading -->

      <div class="row align-items-lg-center">
        <div class="col-lg-5 order-lg-2 mb-7 mb-lg-0">
          <!-- Nav Scroller -->
          <div class="js-nav-scroller hs-nav-scroller-horizontal">
            <span class="hs-nav-scroller-arrow-prev" style="display: none;">
              <a class="hs-nav-scroller-arrow-link" href="javascript:;">
                <i class="bi-chevron-left"></i>
              </a>
            </span>

            <span class="hs-nav-scroller-arrow-next" style="display: none;">
              <a class="hs-nav-scroller-arrow-link" href="javascript:;">
                <i class="bi-chevron-right"></i>
              </a>
            </span>
            
            <!-- Nav Pills -->
            <ul class="nav nav-lg nav-pills nav-pills-shadow flex-lg-column gap-lg-1 p-3" id="featuresTab" role="tablist">
              <li class="nav-item" role="presentation">
                <a class="nav-link active" href="#featuresOne" id="featuresOne-tab" data-bs-toggle="tab" data-bs-target="#featuresOne" role="tab" aria-controls="featuresOne" aria-selected="true" style="min-width: 20rem;">
                  <!-- Media -->
                  <div class="d-flex align-items-center align-items-lg-start">
                    <span class="svg-icon svg-icon-sm text-primary">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path opacity="0.3" d="M22 19V17C22 16.4 21.6 16 21 16H8V3C8 2.4 7.6 2 7 2H5C4.4 2 4 2.4 4 3V19C4 19.6 4.4 20 5 20H21C21.6 20 22 19.6 22 19Z" fill="#035A4B"/>
                      <path d="M20 5V21C20 21.6 19.6 22 19 22H17C16.4 22 16 21.6 16 21V8H8V4H19C19.6 4 20 4.4 20 5ZM3 8H4V4H3C2.4 4 2 4.4 2 5V7C2 7.6 2.4 8 3 8Z" fill="#035A4B"/>
                      </svg>

                    </span>

                    <div class="flex-grow-1 ms-3">
                      <h4 class="mb-1">Làm việc cùng nhau</h4>
                      <p class="text-body text-wrap mb-0">SePay Bot báo biến động số dư lên nhóm Telegram, Lark Messenger. Mọi thành viên trong nhóm đều biết được.</p>
                    </div>
                  </div>
                  <!-- End Media -->
                </a>
              </li>

              <li class="nav-item" role="presentation">
                <a class="nav-link" href="#featuresTwo" id="featuresTwo-tab" data-bs-toggle="tab" data-bs-target="#featuresTwo" role="tab" aria-controls="featuresTwo" aria-selected="false" style="min-width: 20rem;">
                  <!-- Media -->
                  <div class="d-flex align-items-center align-items-lg-start">
                    <span class="svg-icon svg-icon-sm text-primary">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M15 19.5229C15 20.265 15.9624 20.5564 16.374 19.9389L22.2227 11.166C22.5549 10.6676 22.1976 10 21.5986 10H17V4.47708C17 3.73503 16.0376 3.44363 15.626 4.06106L9.77735 12.834C9.44507 13.3324 9.80237 14 10.4014 14H15V19.5229Z" fill="#035A4B"/>
                      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M3 6.5C3 5.67157 3.67157 5 4.5 5H9.5C10.3284 5 11 5.67157 11 6.5C11 7.32843 10.3284 8 9.5 8H4.5C3.67157 8 3 7.32843 3 6.5ZM3 18.5C3 17.6716 3.67157 17 4.5 17H9.5C10.3284 17 11 17.6716 11 18.5C11 19.3284 10.3284 20 9.5 20H4.5C3.67157 20 3 19.3284 3 18.5ZM2.5 11C1.67157 11 1 11.6716 1 12.5C1 13.3284 1.67157 14 2.5 14H6.5C7.32843 14 8 13.3284 8 12.5C8 11.6716 7.32843 11 6.5 11H2.5Z" fill="#035A4B"/>
                      </svg>

                    </span>

                    <div class="flex-grow-1 ms-3">
                      <h4 class="mb-1">Nhanh chóng & không giới hạn</h4>
                      <p class="text-body text-wrap mb-0">Nhận tin nhắn báo số dư chỉ vài giây sau khi khách hàng chuyển khoản. Không giới hạn số tin.</p>
                    </div>
                  </div>
                  <!-- End Media -->
                </a>
              </li>

              <li class="nav-item" role="presentation">
                <a class="nav-link" href="#featuresThree" id="featuresThree-tab" data-bs-toggle="tab" data-bs-target="#featuresThree" role="tab" aria-controls="featuresThree" aria-selected="false" style="min-width: 20rem;">
                  <!-- Media -->
                  <div class="d-flex align-items-center align-items-lg-start">
                    <span class="svg-icon svg-icon-sm text-primary">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M17.2718 8.68537C16.8933 8.28319 16.9125 7.65032 17.3146 7.2718C17.7168 6.89329 18.3497 6.91246 18.7282 7.31464L22.7282 11.5646C23.0906 11.9497 23.0906 12.5503 22.7282 12.9354L18.7282 17.1854C18.3497 17.5875 17.7168 17.6067 17.3146 17.2282C16.9125 16.8497 16.8933 16.2168 17.2718 15.8146L20.6268 12.25L17.2718 8.68537Z" fill="#035A4B"/>
                      <path d="M6.7282 8.68537C7.10671 8.28319 7.08754 7.65032 6.68536 7.2718C6.28319 6.89329 5.65031 6.91246 5.2718 7.31464L1.2718 11.5646C0.909397 11.9497 0.909397 12.5503 1.2718 12.9354L5.2718 17.1854C5.65031 17.5875 6.28319 17.6067 6.68536 17.2282C7.08754 16.8497 7.10671 16.2168 6.7282 15.8146L3.37325 12.25L6.7282 8.68537Z" fill="#035A4B"/>
                      <rect opacity="0.3" x="12.7388" y="3.97168" width="2" height="16" rx="1" transform="rotate(12.3829 12.7388 3.97168)" fill="#035A4B"/>
                      </svg>

                    </span>

                    <div class="flex-grow-1 ms-3">
                      <h4 class="mb-1">Nhiều tùy chọn nâng cao</h4>
                      <p class="text-body text-wrap mb-0">Ẩn số dư, không gửi tin nếu nội dung thanh toán có các từ chỉ định và nhiều tùy chọn khác.</p>
                    </div>
                  </div>
                  <!-- End Media -->
                </a>
              </li>
            </ul>
            <!-- End Nav Pills -->
          </div>
          <!-- End Nav Scroller -->
        </div>
        <!-- End Col -->

        <div class="col-lg-7 order-lg-1">
          <!-- Tab Content -->
          <div class="tab-content" id="featuresTabContent">
            <div class="tab-pane fade show active" id="featuresOne" role="tabpanel" aria-labelledby="featuresOne-tab">
              <!-- Browser Device -->
              <figure class="device-browser">
                

                <div class="device-browser-frame">
                  <img class="device-browser-img" src="./assets/img/others/telegram-app-desktop.png" alt="Image Description">
                </div>
              </figure>
              <!-- End Browser Device -->
            </div>

            <div class="tab-pane fade" id="featuresTwo" role="tabpanel" aria-labelledby="featuresTwo-tab">
              <!-- Browser Device -->
              <figure class="device-browser">
                

                <div class="device-browser-frame">
                  <img class="device-browser-img" src="https://docs.sepay.vn/assets/img/lark/lark-intergrated-sepay-09.png?v=1" alt="Image Description">
                </div>
              </figure>
              <!-- End Browser Device -->
            </div>

            <div class="tab-pane fade" id="featuresThree" role="tabpanel" aria-labelledby="featuresThree-tab">
              <!-- Browser Device -->
              <figure class="device-browser">
                

                <div class="device-browser-frame">
                  <img class="device-browser-img" src="./assets/img/others/sepay-new-telegram.png" alt="Image Description">
                </div>
              </figure>
              <!-- End Browser Device -->
            </div>
          </div>
          <!-- End Tab Content -->
        </div>
        <!-- End Col -->
      </div>
      <!-- End Row -->
    </div>
    <!-- End Feature Nav -->

    <!-- Features Nav -->
    <div class="container content-space-2 content-space-lg-3">
      <div class="row align-items-lg-center">
        <div class="col-lg-8 col-12 mb-7 mb-lg-0">
          <!-- Heading -->
          <div class="mb-5">
            <h2>Nhiều tùy chọn cấu hình</h2>
            <p>Khi tạo mới tích hợp để báo tin vào nhóm chat, có nhiều tùy chọn cấu hình để đáp ứng yêu cầu của bạn.</p>
          </div>
          <!-- End Heading -->

          <div class="row mb-4">
            <div class="col-6">
              <!-- List Checked -->
              <ul class="list-checked list-checked-success mb-0">
                <li class="list-checked-item">Sự kiện có tiền vào hoặc tiền ra hoặc cả hai</li>
                <li class="list-checked-item">Cấu hình theo tài khoản ngân hàng chỉ định</li>
                <li class="list-checked-item">Chỉ gửi nếu trạng thái WebHooks thành công/ thất bại</li>

              </ul>
              <!-- End List Checked -->
            </div>
            <!-- End Col -->

            <div class="col-6">
              <!-- List Checked -->
              <ul class="list-checked list-checked-success mb-0">
                <li class="list-checked-item">Gửi tin nếu nội dung thanh toán chứa các từ</li>
                <li class="list-checked-item">Tùy chọn ẩn số dư tài khoản</li>
                <li class="list-checked-item">Không gửi tin nếu nội dung thanh toán chứa các từ, cụm từ </li>

              </ul>
              <!-- End List Checked -->
            </div>
            <!-- End Col -->
          </div>
          <!-- End Row -->

          <a class="link" href="https://docs.sepay.vn/tich-hop-telegram.html">Đọc thêm hướng dẫn <i class="bi-chevron-right small ms-1"></i></a>
        </div>
        <!-- End Col -->

       
      </div>
      <!-- End Row -->
    </div>
    <!-- End Features Nav -->

    <!-- Icon Blocks -->
    <div class="container" style="max-width:960px">
      <!-- Heading -->
      <div class="w-md-75 w-lg-50 text-center mx-md-auto mb-5 mb-md-9">
        <span class="text-cap">Lợi ích</span>
        <h2>Những lợi ích mà tính năng báo số dư của SePay đem lại</h2>
      </div>
      <!-- End Heading -->

      <div class="row mb-5 mb-md-9">
        <div class="col-sm-6 col-md-6 mb-3 mb-sm-7">
          <!-- Icon Block -->
          <div class="d-flex align-items-center mb-2">
            <div class="flex-shrink-0">
              <span class="svg-icon svg-icon-sm text-primary">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M17.302 11.35L12.002 20.55H21.202C21.802 20.55 22.202 19.85 21.902 19.35L17.302 11.35Z" fill="#035A4B"/>
                <path opacity="0.3" d="M12.002 20.55H2.802C2.202 20.55 1.80202 19.85 2.10202 19.35L6.70203 11.45L12.002 20.55ZM11.302 3.45L6.70203 11.35H17.302L12.702 3.45C12.402 2.85 11.602 2.85 11.302 3.45Z" fill="#035A4B"/>
                </svg>

              </span>
            </div>

            <div class="flex-grow-1 ms-3">
              <h4 class="mb-0">Giảm nhân lực kế toán</h4>
            </div>
          </div>
          <!-- End Icon Block -->

          <p>Ngay bây giờ, nhân viên bán hàng của bạn cũng nắm được thông tin khi có khách hàng thanh toán.</p>
        </div>
        <!-- End Col -->

        <div class="col-sm-6 col-md-6 mb-3 mb-sm-7">
          <!-- Icon Block -->
          <div class="d-flex align-items-center mb-2">
            <div class="flex-shrink-0">
              <span class="svg-icon svg-icon-sm text-primary">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path opacity="0.3" d="M21 2H13C12.4 2 12 2.4 12 3V13C12 13.6 12.4 14 13 14H21C21.6 14 22 13.6 22 13V3C22 2.4 21.6 2 21 2ZM15.7 8L14 10.1V5.80005L15.7 8ZM15.1 4H18.9L17 6.40002L15.1 4ZM17 9.59998L18.9 12H15.1L17 9.59998ZM18.3 8L20 5.90002V10.2L18.3 8ZM9 2H3C2.4 2 2 2.4 2 3V21C2 21.6 2.4 22 3 22H9C9.6 22 10 21.6 10 21V3C10 2.4 9.6 2 9 2ZM4.89999 12L4 14.8V9.09998L4.89999 12ZM4.39999 4H7.60001L6 8.80005L4.39999 4ZM6 15.2L7.60001 20H4.39999L6 15.2ZM7.10001 12L8 9.19995V14.9L7.10001 12Z" fill="#035A4B"/>
                <path d="M21 18H13C12.4 18 12 17.6 12 17C12 16.4 12.4 16 13 16H21C21.6 16 22 16.4 22 17C22 17.6 21.6 18 21 18ZM19 21C19 20.4 18.6 20 18 20H13C12.4 20 12 20.4 12 21C12 21.6 12.4 22 13 22H18C18.6 22 19 21.6 19 21Z" fill="#035A4B"/>
                </svg>

              </span>
            </div>

            <div class="flex-grow-1 ms-3">
              <h4 class="mb-0">Phá bỏ giới hạn Internet Banking</h4>
            </div>
          </div>
          <!-- End Icon Block -->

          <p>Không lo internet banking chậm, không cần đăng nhập nhiều lần. Kiểm tra thanh toán dễ dàng hơn bao giờ hết.</p>
        </div>
        <!-- End Col -->

        <div class="col-sm-6 col-md-6 mb-3 mb-sm-7 mb-md-0">
          <!-- Icon Block -->
          <div class="d-flex align-items-center mb-2">
            <div class="flex-shrink-0">
              <span class="svg-icon svg-icon-sm text-primary">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M4.85714 1H11.7364C12.0911 1 12.4343 1.12568 12.7051 1.35474L17.4687 5.38394C17.8057 5.66895 18 6.08788 18 6.5292V19.0833C18 20.8739 17.9796 21 16.1429 21H4.85714C3.02045 21 3 20.8739 3 19.0833V2.91667C3 1.12612 3.02045 1 4.85714 1ZM7 13C7 12.4477 7.44772 12 8 12H15C15.5523 12 16 12.4477 16 13C16 13.5523 15.5523 14 15 14H8C7.44772 14 7 13.5523 7 13ZM8 16C7.44772 16 7 16.4477 7 17C7 17.5523 7.44772 18 8 18H11C11.5523 18 12 17.5523 12 17C12 16.4477 11.5523 16 11 16H8Z" fill="#035A4B"/>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M6.85714 3H14.7364C15.0911 3 15.4343 3.12568 15.7051 3.35474L20.4687 7.38394C20.8057 7.66895 21 8.08788 21 8.5292V21.0833C21 22.8739 20.9796 23 19.1429 23H6.85714C5.02045 23 5 22.8739 5 21.0833V4.91667C5 3.12612 5.02045 3 6.85714 3ZM7 13C7 12.4477 7.44772 12 8 12H15C15.5523 12 16 12.4477 16 13C16 13.5523 15.5523 14 15 14H8C7.44772 14 7 13.5523 7 13ZM8 16C7.44772 16 7 16.4477 7 17C7 17.5523 7.44772 18 8 18H11C11.5523 18 12 17.5523 12 17C12 16.4477 11.5523 16 11 16H8Z" fill="#035A4B"/>
                </svg>

              </span>
            </div>

            <div class="flex-grow-1 ms-3">
              <h4 class="mb-0">Lên đơn hàng nhanh chóng</h4>
            </div>
          </div>
          <!-- End Icon Block -->

          <p> Nhân viên nhanh chóng nắm bắt được có khách hàng thanh toán, thực hiện các tác vụ khác như lên đơn, xác nhận, giao hàng.</p>
        </div>
        <!-- End Col -->

       

        <div class="col-sm-6 col-md-6 mb-3 mb-sm-7 mb-md-0">
          <!-- Icon Block -->
          <div class="d-flex align-items-center mb-2">
            <div class="flex-shrink-0">
              <span class="svg-icon svg-icon-sm text-primary">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M15 19.5229C15 20.265 15.9624 20.5564 16.374 19.9389L22.2227 11.166C22.5549 10.6676 22.1976 10 21.5986 10H17V4.47708C17 3.73503 16.0376 3.44363 15.626 4.06106L9.77735 12.834C9.44507 13.3324 9.80237 14 10.4014 14H15V19.5229Z" fill="#035A4B"/>
                <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M3 6.5C3 5.67157 3.67157 5 4.5 5H9.5C10.3284 5 11 5.67157 11 6.5C11 7.32843 10.3284 8 9.5 8H4.5C3.67157 8 3 7.32843 3 6.5ZM3 18.5C3 17.6716 3.67157 17 4.5 17H9.5C10.3284 17 11 17.6716 11 18.5C11 19.3284 10.3284 20 9.5 20H4.5C3.67157 20 3 19.3284 3 18.5ZM2.5 11C1.67157 11 1 11.6716 1 12.5C1 13.3284 1.67157 14 2.5 14H6.5C7.32843 14 8 13.3284 8 12.5C8 11.6716 7.32843 11 6.5 11H2.5Z" fill="#035A4B"/>
                </svg>

              </span>
            </div>

            <div class="flex-grow-1 ms-3">
              <h4 class="mb-0">Khách hàng không phải chờ lâu</h4>
            </div>
          </div>
          <!-- End Icon Block -->

          <p>Tăng sự hài lòng của khách hàng nhờ việc xác nhận đơn hàng đã thanh toán chỉ trong 15 giây.</p>
        </div>
        <!-- End Col -->

       
      </div>
      <!-- End Row -->

      <div class="text-center">
        <div class="d-grid d-sm-flex justify-content-center gap-2 mb-3">
          <a class="btn btn-primary btn-transition" href="https://my.sepay.vn/register">Đăng ký ngay</a>
          <a class="btn btn-link" href="lien-he.html#contact">Liên hệ tư vấn <i class="bi-chevron-right small ms-1"></i></a>
        </div>
      </div>
    </div>
    <!-- End Icon Blocks -->

    <!-- Testimonials -->
    <div class="container content-space-2 content-space-lg-3">
      <div class="text-center mb-5">
        <img class="avatar avatar-lg avatar-4x3" src="./assets/svg/illustrations/oc-person-2.svg" alt="Illustration">
      </div>
      
      <!-- Blockquote -->
      <figure class="w-md-75 text-center mx-md-auto">
        <blockquote class="blockquote">“Hơn 60 cửa hàng của Di Động Việt đã không còn phụ thuộc vào kế toán để kiểm tra chuyển khoản nữa, tất cả nhân viên bán hàng đều nắm bắt qua Telegram.”</blockquote>

        <figcaption class="blockquote-footer text-center">
          Mr. Đạt Nguyễn
          <span class="blockquote-footer-source">CEO | Di Động Việt</span>
        </figcaption>
      </figure>
      <!-- End Blockquote -->
    </div>
    <!-- End Testimonials -->

     

      
    </div>
    <!-- End Contacts -->
  </main>
  <!-- ========== END MAIN CONTENT ========== -->

   <!-- ========== FOOTER ========== -->
   <footer class="container content-space-2 content-space-t-lg-3">
    <!-- Links -->
    <div class="row mb-5">
      <div class="col-md-3 mb-5 mb-md-0">
        <!-- Logo -->
        <div class="mb-3">
          <a class="navbar-brand" href="./index.html" aria-label="Space">
            <img class="navbar-brand-logo" src="./assets/img/logo/sepay-blue-154x50.png" alt="Image Description">
          </a>
        </div>
        <!-- End Logo -->

        <!-- List -->
        <ul class="list-unstyled list-py-1">
          <li><b>Công Ty Cổ Phần SePay</b></li>
          <li><a class="link-sm link-secondary" href="https://goo.gl/maps/HGZVhsbv3NhR7kan8" target="_blank"><i
                class="bi-geo-alt-fill me-1"></i> 168 Đường số 2, Vạn Phúc City, P. Hiệp Bình Phước, Thủ Đức, TP. Hồ Chí
              Minh, Việt Nam</a></li>
          <li><a class="link-sm link-secondary" href="tel:02873059589"><i class="bi-telephone-inbound-fill me-1"></i>
              02873.059.589</a></li>
          <li><span class="text-secondary fs-6"><i class="bi bi-envelope-fill me-2"></i> <EMAIL></span></li>
          <li><span class="text-secondary fs-6">MST: 0317887567, cấp ngày 19/06/2023 tại Sở Kế hoạch và Đầu tư
              TPHCM</span></li>

        </ul>
        <!-- End List -->
        <div class="col-sm-auto">
          <!-- Socials -->
          <ul class="list-inline mb-0">
            <li class="list-inline-item">
              <a class="btn btn-soft-secondary btn-xs btn-icon" href="https://www.facebook.com/sepay.vn">
                <i class="bi-facebook"></i>
              </a>
            </li>
            <li class="list-inline-item">
              <a class="btn btn-soft-secondary btn-xs btn-icon" href="https://www.youtube.com/@SePayVN">
                <i class="bi-youtube"></i>
              </a>
            </li>
            <li class="list-inline-item">
              <a class="btn btn-soft-secondary btn-xs btn-icon" href="https://www.pinterest.com/sepayvn/">
                <i class="bi-pinterest"></i>
              </a>
            </li>




          </ul>
          <!-- End Socials -->
        </div>
      </div>

      <div class="col-sm-2 col-md-2 col-6 mb-3 mb-sm-0">
        <h5 class=" mb-3">THÔNG TIN</h5>

        <!-- List -->
        <ul class="list-unstyled list-py-1">
          <li><a class="text-body" href="gioi-thieu.html">Giới thiệu</a></li>
          <li><a class="text-body" href="bang-gia.html">Bảng giá</a></li>

          <li><a class="text-body" href="https://sepay.vn/blog">Blog</a></li>
          <li><a class="text-body" href="lien-he.html">Liên hệ</a></li>
          <li><a class="text-body" target="_blank" href="https://docs.sepay.vn/tich-hop-webhooks.html">WebHooks</a></li>

          <li><a class="text-body" target="_blank" href="https://docs.sepay.vn/gioi-thieu-api.html">API</a></li>

          <li><a class="text-body" href="faq.html">FAQ</a></li>
          <li><a class="text-body" href="changelog.html">Changelog</a></li>

        </ul>
        <div><img loading="lazy" src="/assets/img/others/RapidSSL-SEAL.gif" class="img-fluid"> <a
            href="https://secure.sitelock.com/public/verify/sepay.vn" target="_blank" rel=nofollow><img
              src="https://shield.sitelock.com/shield/sepay.vn" loading="lazy" class="img-fluid"
              style="max-width: 90px;"></a> <a href="http://online.gov.vn/Home/WebDetails/107586" target="_blank"
            rel=nofollow><img src="https://sepay.vn/assets/img/others/ttbct.png" loading="lazy" class="img-fluid"
              style="max-width: 150px;"></a></div>
        <!-- End List -->
      </div>

      <div class="col-sm-3 col-md-3 col-6 mb-3 mb-sm-0">
        <h5 class=" mb-3">HƯỚNG DẪN & TÍNH NĂNG</h5>

        <!-- List -->
        <ul class="list-unstyled list-py-1">
          <li>
            <a class="text-body" href="https://docs.sepay.vn/">
              Hướng dẫn sử dụng
            </a>
          </li>
          <li><a class="text-body" href="thanh-toan.html">Hướng dẫn thanh toán</a></li>
          <li><a class="text-body" href="terms-of-service.html">Quy định sử dụng</a></li>
          <li><a class="text-body" href="hoan-tien.html">Quy định hoàn tiền</a></li>
          <li><a class="text-body" href="privacy.html">Chính sách bảo mật</a></li>
          <li>
            <a class="text-body" href="ngan-hang.html">
              Ngân hàng kết nối
            </a>
          </li>


          <li>
            <a class="text-body" href="affiliate.html">
              Tiếp thị liên kết
            </a>
          </li>
          <li>
            <a class="text-body" href="chia-se-bien-dong-so-du.html">
              Chia sẻ biến động số dư
            </a>
          </li>
          <li>
            <a class="text-body" href="cong-thanh-toan-truc-tuyen.html">
              Cổng thanh toán trực tuyến
            </a>
          </li>
          <li>
            <a class="text-body" href="thong-ke-dong-tien.html">
              Thống kê dòng tiền
            </a>
          </li>
        </ul>
        <!-- End List -->
      </div>

      <div class="col-sm-4 row">
        <div class="col-6 mb-3 mb-sm-0 mx-auto">
          <h5 class=" mb-3">TÍCH HỢP</h5>
          <!-- List -->
          <ul class="list-unstyled list-py-1">
  
            <li>
              <a class="text-body" href="https://docs.sepay.vn/tich-hop-haravan.html">
                <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/haravan-icon.png"
                  style="width:22px; height: 22px;"> Haravan
              </a>
            </li>
  
            <li>
              <a class="text-body" href="https://docs.sepay.vn/tich-hop-sapo.html">
                <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/sapo-icon.png"
                  style="width:22px; height: 22px;"> Sapo
              </a>
            </li>

            <li>
              <a class="text-body" href="https://docs.sepay.vn/tich-hop-shopify.html">
                <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/shopify-icon.png"
                  style="width:22px; height: 22px;"> Shopify
              </a>
            </li>
            
            <li>
              <a class="text-body" href="https://docs.sepay.vn/woocommerce.html">
                <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/woocommerce-icon.png"
                  style="width:22px; height: 22px;"> WooCommerce
              </a>
            </li>
            <li>
              <a class="text-body" href="https://docs.sepay.vn/tich-hop-google-sheets.html">
                <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/google-sheets-icon.png"
                  style="width:22px; height: 22px;"> Google Sheets
              </a>
            </li>
            <li>
              <a class="text-body" href="https://docs.sepay.vn/tich-hop-lark-messenger.html">
                <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/lark/lark-icon.png"
                  style="width:22px; height: 22px;"> Lark
              </a>
            </li>
            <li>
              <a class="text-body" href="https://docs.sepay.vn/tich-hop-telegram.html">
                <i class="bi bi-telegram me-1 text-info fs-3"></i> Telegram
              </a>
            </li>
  
  
  
  
            
        </div>
        <div class="col-6 mb-3 mb-sm-0 mx-auto">
          <h5 class=" mb-3">KẾT NỐI VỚI SEPAY</h5>
           <!-- List -->
           <ul class="list-unstyled list-py-1">
  
            <li>
              <a class="text-body" href="https://www.facebook.com/sepay.vn">
                <i class="bi bi-facebook me-1"></i> Facebook
              </a>
            </li>
            <li>
              <a class="text-body" href="https://t.me/s/sepaychannel">
                <i class="bi bi-telegram me-1"></i> Telegram
              </a>
            </li>
            <li>
              <a class="text-body" href="https://www.youtube.com/@SePayVN">
                <i class="bi bi-youtube me-1"></i> Youtube
              </a>
            </li>
  
            <li>
              <a class="text-body" href="https://www.pinterest.com/sepayvn/">
                <i class="bi bi-pinterest me-1"></i> Pinterest
              </a>
            </li>
            <li>
              <a class="text-body" href="https://github.com/sepayvn">
                <i class="bi bi-github me-1"></i> GitHub
              </a>
            </li>
  
  
  
          </ul>
          <!-- End List -->
        </div>
        <div class="col-12">
          <span class="text-cap mt-1">Hợp tác chiến lược:</span>
          <div class="row">
            <div class="col-3 py-3">
              <a href="https://sepay.vn/mb.html"><img class="avatar avatar-lg avatar-4x3" src="https://qr.sepay.vn/assets/img/banklogo/MB.png" alt="MBBank"></a>
            </div>
            <!-- End Col -->
  
            <div class="col-3 py-3">
              <a href="https://sepay.vn/ocb.html"><img class="avatar avatar-lg avatar-4x3" src="https://qr.sepay.vn/assets/img/banklogo/OCB.png" alt="OCB"></a>
            </div>
            <!-- End Col -->
            <div class="col-3 py-3">
              <a href="https://sepay.vn/kien-long-bank.html"><img class="avatar avatar-lg avatar-4x3" src="https://sepay.vn/blog/wp-content/uploads/2024/05/kienlongbank-logo.png" alt="KienlongBank"></a>
            </div>
            <!-- End Col -->
  
            <div class="col-3 py-3">
              <a href="https://sepay.vn/msb.html"><img class="avatar avatar-lg avatar-4x3" src="https://qr.sepay.vn/assets/img/banklogo/MSB.png" alt="MSB"></a>
            </div>
            <!-- End Col -->
          </div>
        </div>
      </div>
    </div>
    <!-- End Links -->
    <div class="border-top border-10 my-5"></div>
    <div class="row align-items-sm-center">
      <div class="col-12 mb-4 mb-sm-0">
        <p class="small mb-0 text-center">&copy; SePay 2023. All rights reserved.</p>
      </div>
      <!-- End Col -->


      <!-- End Col -->
    </div>
    <!-- End Row -->
  </footer>
  <!-- ========== END FOOTER ========== -->
  
  <!--contact-box-html-->
  <div id="contact-box-overlay"></div>
  <div id="contact-box">
      <div class="popup">
          <a href="https://m.me/***************" target="_blank" class="item">
              <div class="logo">
                  <img src="assets/img/others/fb-messenger.png" width="50%" />
              </div>
              <div class="meta">
                  <p class="title">Facebook Messenger</p>
                  <small class="description">Hỗ trợ live chat 24/7</small>
              </div>
          </a>
          <div class="divide"></div>
          <a href="tel:02873059589" class="item">
              <div class="icon" style="background-color: #22c55e; color: #fff;">
                  <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;"><path d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" fill="transparent"></path></svg>
              </div>
              <div class="meta">
                  <p class="title">Hotline</p>
                  <small class="description">Điện thoại hỗ trợ</small>
              </div>
          </a>
          <div class="divide"></div>
          <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
              <div class="logo">
                  <img src="assets/img/others/youtube-social.png" />
              </div>
              <div class="meta">
                  <p class="title">Youtube</p>
                  <small class="description">Theo dõi video mới nhất của SePay</small>
              </div>
          </a>
          <div class="divide"></div>
          <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
              <div class="logo">
                  <img src="assets/img/others/telegram-social.png" />
              </div>
              <div class="meta">
                  <p class="title">Telegram</p>
                  <small class="description">Nhận thông tin mới nhất từ SePay</small>
              </div>
          </a>
      </div>
      <div class="container">
          <div class="dot-ping">
              <div class="ping"></div>
              <div class="dot"></div>
          </div>
          <div class="contact-icon">
              <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;"><path fill-rule="evenodd" clip-rule="evenodd" d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z" fill="currentColor"></path></svg>
          </div>
          <span style="font-weight: bold;">Liên hệ chúng tôi</span>
      </div>
  </div>
  <!--/contact-box-html-->

  <!-- JS Global Compulsory  -->
  <script src="./assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

  <!-- JS Implementing Plugins -->
  <script src="./assets/vendor/hs-header/dist/hs-header.min.js"></script>
  <script src="./assets/vendor/hs-mega-menu/dist/hs-mega-menu.min.js"></script>
  <!-- JS Front -->
  <script src="./assets/js/theme.min.js"></script>

  <!-- contact-box-js -->
  <script src="./assets/js/contact-box.js"></script>
  <!-- /contact-box-js -->

  <!-- JS Plugins Init. -->
  <script>
    (function () {
      // INITIALIZATION OF HEADER
      // =======================================================
      new HSHeader('#header').init()


      // INITIALIZATION OF MEGA MENU
      // =======================================================
      new HSMegaMenu('.js-mega-menu', {
        desktop: {
          position: 'left'
        }
      })
  
      // INITIALIZATION OF BOOTSTRAP DROPDOWN
      // =======================================================
      HSBsDropdown.init()
 
    })()
    
  </script>
</body>

</html>