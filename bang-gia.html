<!DOCTYPE html>
<html lang="en" dir="">

<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

  <!-- Title -->
  <title>Bảng giá Se<PERSON>ay</title>

  <!-- Favicon -->
  <link rel="shortcut icon" href="./assets/img/logo/favicon.png" type="image/x-icon" />

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">

  <!-- CSS Implementing Plugins -->
  <link rel="stylesheet" href="./assets/vendor/bootstrap-icons/font/bootstrap-icons.css">
  <link rel="stylesheet" href="./assets/vendor/hs-mega-menu/dist/hs-mega-menu.min.css">


  <!-- CSS Front Template -->
  <link rel="stylesheet" href="./assets/css/theme.min.css">

  <!--contact-box-css-->
  <link rel="stylesheet" href="./assets/css/contact-box.css">
  <!--/contact-box-css-->

  <!-- meta tag -->
  <meta property="og:locale" content="vi_VN" />
  <link rel="canonical" href="https://sepay.vn/bang-gia.html" />
  <meta name="description"
    content="Bảng giá SePay, bắt đầu chỉ với 350,000đ/ tháng. Hỗ trợ chia sẻ số dư ngân hàng, tự động xác thực thanh toán chuyển khoản, thống kê dòng tiền chuyên nghiệp." />
  <meta property="og:type" content="article" />
  <meta property="og:url" content="https://sepay.vn/bang-gia.html" />
  <meta property="og:title" content="Bảng giá SePay" />
  <meta property="og:description"
    content="Bảng giá SePay, bắt đầu chỉ với 350,000đ/ tháng. Hỗ trợ chia sẻ số dư ngân hàng, tự động xác thực thanh toán chuyển khoản, thống kê dòng tiền chuyên nghiệp." />

  <meta property="og:site_name" content="Bảng giá SePay" />
  <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
  <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
  <!-- meta tag -->


  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
  <script>
    window.dataLayer = window.dataLayer || [];

    function gtag() {
      dataLayer.push(arguments);
    }
    gtag('js', new Date());

    gtag('config', 'G-J8DLMQTKSQ');
  </script>
</head>

<body>
  <!-- ========== HEADER ========== -->
  <header id="header"
    class="navbar navbar-expand-lg navbar-end navbar-absolute-top navbar-light navbar-show-hide navbar-scrolled"
    data-hs-header-options='{
            "fixMoment": 1000,
            "fixEffect": "slide"
          }'>


    <div class="container">
      <nav class="js-mega-menu navbar-nav-wrap">
        <!-- Default Logo -->
        <a class="navbar-brand" href="https://sepay.vn" aria-label="Front">
          <img class="navbar-brand-logo" src="./assets/img/logo/sepay-blue-154x50.png" alt="Logo">
        </a>
        <!-- End Default Logo -->

        <!-- Toggler -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown"
          aria-controls="navbarNavDropdown" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-default">
            <i class="bi-list"></i>
          </span>
          <span class="navbar-toggler-toggled">
            <i class="bi-x"></i>
          </span>
        </button>
        <!-- End Toggler -->

        <!-- Collapse -->
        <div class="collapse navbar-collapse" id="navbarNavDropdown">
          <div class="navbar-absolute-top-scroller">
            <ul class="navbar-nav">


              <!-- Features -->
              <li class="hs-has-sub-menu nav-item">
                <a id="coursesMegaMenu" class="hs-mega-menu-invoker nav-link dropdown-toggle" href="#" role="button">
                  Tính năng</a>

                <!-- Mega Menu -->
                <div class="hs-sub-menu dropdown-menu" aria-labelledby="coursesMegaMenu" style="min-width: 20rem;">


                  <a class="navbar-dropdown-menu-media-link" href="chia-se-bien-dong-so-du.html">
                    <div class="d-flex">
                      <div class="flex-shrink-0">
                        <span class="svg-icon svg-icon-sm text-primary">
                          <span class="svg-icon svg-icon-sm text-primary">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M22.1671 18.1421C22.4827 18.4577 23.0222 18.2331 23.0206 17.7868L23.0039 13.1053V5.52632C23.0039 4.13107 21.8729 3 20.4776 3H8.68815C7.2929 3 6.16183 4.13107 6.16183 5.52632V9H13C14.6568 9 16 10.3431 16 12V15.6316H19.6565L22.1671 18.1421Z"
                                fill="#035A4B" />
                              <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd"
                                d="M1.98508 18V13C1.98508 11.8954 2.88051 11 3.98508 11H11.9851C13.0896 11 13.9851 11.8954 13.9851 13V18C13.9851 19.1046 13.0896 20 11.9851 20H4.10081L2.85695 21.1905C2.53895 21.4949 2.01123 21.2695 2.01123 20.8293V18.3243C1.99402 18.2187 1.98508 18.1104 1.98508 18ZM5.99999 14.5C5.99999 14.2239 6.22385 14 6.49999 14H11.5C11.7761 14 12 14.2239 12 14.5C12 14.7761 11.7761 15 11.5 15H6.49999C6.22385 15 5.99999 14.7761 5.99999 14.5ZM9.49999 16C9.22385 16 8.99999 16.2239 8.99999 16.5C8.99999 16.7761 9.22385 17 9.49999 17H11.5C11.7761 17 12 16.7761 12 16.5C12 16.2239 11.7761 16 11.5 16H9.49999Z"
                                fill="#035A4B" />
                            </svg>

                          </span>

                        </span>
                      </div>

                      <div class="flex-grow-1 ms-3">
                        <span class="navbar-dropdown-menu-media-title">Chia sẻ biến động số dư</span>
                        <p class="navbar-dropdown-menu-media-desc">Báo lên group chat khi nhận chuyển khoản</p>
                      </div>
                    </div>
                  </a>

                  <div class="dropdown-divider"></div>

                  <a class="navbar-dropdown-menu-media-link" href="cong-thanh-toan-truc-tuyen.html">
                    <div class="d-flex">
                      <div class="flex-shrink-0">
                        <span class="svg-icon svg-icon-sm text-primary">
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path opacity="0.3" d="M18 10V20C18 20.6 18.4 21 19 21C19.6 21 20 20.6 20 20V10H18Z"
                              fill="#035A4B" />
                            <path opacity="0.3"
                              d="M11 10V17H6V10H4V20C4 20.6 4.4 21 5 21H12C12.6 21 13 20.6 13 20V10H11Z"
                              fill="#035A4B" />
                            <path opacity="0.3" d="M10 10C10 11.1 9.1 12 8 12C6.9 12 6 11.1 6 10H10Z" fill="#035A4B" />
                            <path opacity="0.3" d="M18 10C18 11.1 17.1 12 16 12C14.9 12 14 11.1 14 10H18Z"
                              fill="#035A4B" />
                            <path opacity="0.3" d="M14 4H10V10H14V4Z" fill="#035A4B" />
                            <path opacity="0.3" d="M17 4H20L22 10H18L17 4Z" fill="#035A4B" />
                            <path opacity="0.3" d="M7 4H4L2 10H6L7 4Z" fill="#035A4B" />
                            <path
                              d="M6 10C6 11.1 5.1 12 4 12C2.9 12 2 11.1 2 10H6ZM10 10C10 11.1 10.9 12 12 12C13.1 12 14 11.1 14 10H10ZM18 10C18 11.1 18.9 12 20 12C21.1 12 22 11.1 22 10H18ZM19 2H5C4.4 2 4 2.4 4 3V4H20V3C20 2.4 19.6 2 19 2ZM12 17C12 16.4 11.6 16 11 16H6C5.4 16 5 16.4 5 17C5 17.6 5.4 18 6 18H11C11.6 18 12 17.6 12 17Z"
                              fill="#035A4B" />
                          </svg>

                        </span>
                      </div>

                      <div class="flex-grow-1 ms-3">
                        <span class="navbar-dropdown-menu-media-title">Cổng thanh toán trực tuyến</span>
                        <p class="navbar-dropdown-menu-media-desc">Cổng thanh toán QR miễn phí</p>
                      </div>
                    </div>
                  </a>

                  <div class="dropdown-divider"></div>

                  <a class="navbar-dropdown-menu-media-link" href="thong-ke-dong-tien.html">
                    <div class="d-flex">
                      <div class="flex-shrink-0">
                        <span class="svg-icon svg-icon-sm text-primary">
                          <span class="svg-icon svg-icon-sm text-primary">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd"
                                d="M12 17C16.4183 17 20 13.4183 20 9C20 4.58172 16.4183 1 12 1C7.58172 1 4 4.58172 4 9C4 13.4183 7.58172 17 12 17Z"
                                fill="#035A4B" />
                              <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd"
                                d="M6.53819 9L10.568 19.3624L11.976 18.8149L13.3745 19.3674L17.4703 9H6.53819ZM9.46188 11H14.5298L11.9759 17.4645L9.46188 11Z"
                                fill="#035A4B" />
                              <path opacity="0.3"
                                d="M10 22H14V22C14 23.1046 13.1046 24 12 24V24C10.8954 24 10 23.1046 10 22V22Z"
                                fill="#035A4B" />
                              <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M8 17C8 16.4477 8.44772 16 9 16H15C15.5523 16 16 16.4477 16 17C16 17.5523 15.5523 18 15 18C15.5523 18 16 18.4477 16 19C16 19.5523 15.5523 20 15 20C15.5523 20 16 20.4477 16 21C16 21.5523 15.5523 22 15 22H9C8.44772 22 8 21.5523 8 21C8 20.4477 8.44772 20 9 20C8.44772 20 8 19.5523 8 19C8 18.4477 8.44772 18 9 18C8.44772 18 8 17.5523 8 17Z"
                                fill="#035A4B" />
                            </svg>

                          </span>

                        </span>
                      </div>

                      <div class="flex-grow-1 ms-3">
                        <span class="navbar-dropdown-menu-media-title">Thống kê dòng tiền</span>
                        <p class="navbar-dropdown-menu-media-desc">Nắm bắt được dòng tiền tất cả tài khoản</p>
                      </div>
                    </div>
                  </a>

                </div>

              </li>
              <!-- End Features -->



              <li class="nav-item">
                <a class="nav-link" href="why-sepay.html">Why SePay?</a>
              </li>



              <li class="nav-item">
                <a class="nav-link active" href="bang-gia.html">Bảng giá</a>
              </li>



              <li class="nav-item">
                <a class="nav-link" href="https://docs.sepay.vn" target="_blank">Hướng dẫn</a>
              </li>


              <li class="nav-item">
                <a class="nav-link" href="https://sepay.vn/blog">Blog</a>
              </li>
             
                <!-- Cooperate -->
                <li class="hs-has-sub-menu nav-item">
                  <a id="cooperateMegaMenu" class="hs-mega-menu-invoker nav-link dropdown-toggle" href="#"
                      role="button"> Hợp tác</a>

                  <!-- Mega Menu -->
                  <div class="hs-sub-menu dropdown-menu" aria-labelledby="cooperateMegaMenu"
                      style="min-width: 20rem;">


                      <a class="navbar-dropdown-menu-media-link" href="sepay-bank-hub.html">
                          <div class="d-flex">
                              <div class="flex-shrink-0">
                                  <span class="svg-icon svg-icon-sm text-primary">
                                      <span class="svg-icon svg-icon-sm text-primary">
                                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                              xmlns="http://www.w3.org/2000/svg">
                                              <path fill-rule="evenodd" clip-rule="evenodd"
                                                  d="M15 19.5229C15 20.265 15.9624 20.5564 16.374 19.9389L22.2227 11.166C22.5549 10.6676 22.1976 10 21.5986 10H17V4.47708C17 3.73503 16.0376 3.44363 15.626 4.06106L9.77735 12.834C9.44507 13.3324 9.80237 14 10.4014 14H15V19.5229Z"
                                                  fill="#035A4B"></path>
                                              <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd"
                                                  d="M3 6.5C3 5.67157 3.67157 5 4.5 5H9.5C10.3284 5 11 5.67157 11 6.5C11 7.32843 10.3284 8 9.5 8H4.5C3.67157 8 3 7.32843 3 6.5ZM3 18.5C3 17.6716 3.67157 17 4.5 17H9.5C10.3284 17 11 17.6716 11 18.5C11 19.3284 10.3284 20 9.5 20H4.5C3.67157 20 3 19.3284 3 18.5ZM2.5 11C1.67157 11 1 11.6716 1 12.5C1 13.3284 1.67157 14 2.5 14H6.5C7.32843 14 8 13.3284 8 12.5C8 11.6716 7.32843 11 6.5 11H2.5Z"
                                                  fill="#035A4B"></path>
                                          </svg>

                                      </span>

                                  </span>
                              </div>

                              <div class="flex-grow-1 ms-3">
                                <span class="navbar-dropdown-menu-media-title">SePay Bank Hub</span>
                                <p class="navbar-dropdown-menu-media-desc"> Hợp nhất API ngân hàng</p>
                              </div>
                          </div>
                      </a>

                      <div class="dropdown-divider"></div>

                      <a class="navbar-dropdown-menu-media-link" href="affiliate.html">
                          <div class="d-flex">
                              <div class="flex-shrink-0">
                                  <span class="svg-icon svg-icon-sm text-primary">
                                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                          xmlns="http://www.w3.org/2000/svg">
                                          <path opacity="0.3"
                                              d="M12.5 22C11.9 22 11.5 21.6 11.5 21V3C11.5 2.4 11.9 2 12.5 2C13.1 2 13.5 2.4 13.5 3V21C13.5 21.6 13.1 22 12.5 22Z"
                                              fill="#035A4B"></path>
                                          <path
                                              d="M17.8 14.7C17.8 15.5 17.6 16.3 17.2 16.9C16.8 17.6 16.2 18.1 15.3 18.4C14.5 18.8 13.5 19 12.4 19C11.1 19 10 18.7 9.10001 18.2C8.50001 17.8 8.00001 17.4 7.60001 16.7C7.20001 16.1 7 15.5 7 14.9C7 14.6 7.09999 14.3 7.29999 14C7.49999 13.8 7.80001 13.6 8.20001 13.6C8.50001 13.6 8.69999 13.7 8.89999 13.9C9.09999 14.1 9.29999 14.4 9.39999 14.7C9.59999 15.1 9.8 15.5 10 15.8C10.2 16.1 10.5 16.3 10.8 16.5C11.2 16.7 11.6 16.8 12.2 16.8C13 16.8 13.7 16.6 14.2 16.2C14.7 15.8 15 15.3 15 14.8C15 14.4 14.9 14 14.6 13.7C14.3 13.4 14 13.2 13.5 13.1C13.1 13 12.5 12.8 11.8 12.6C10.8 12.4 9.99999 12.1 9.39999 11.8C8.69999 11.5 8.19999 11.1 7.79999 10.6C7.39999 10.1 7.20001 9.39998 7.20001 8.59998C7.20001 7.89998 7.39999 7.19998 7.79999 6.59998C8.19999 5.99998 8.80001 5.60005 9.60001 5.30005C10.4 5.00005 11.3 4.80005 12.3 4.80005C13.1 4.80005 13.8 4.89998 14.5 5.09998C15.1 5.29998 15.6 5.60002 16 5.90002C16.4 6.20002 16.7 6.6 16.9 7C17.1 7.4 17.2 7.69998 17.2 8.09998C17.2 8.39998 17.1 8.7 16.9 9C16.7 9.3 16.4 9.40002 16 9.40002C15.7 9.40002 15.4 9.29995 15.3 9.19995C15.2 9.09995 15 8.80002 14.8 8.40002C14.6 7.90002 14.3 7.49995 13.9 7.19995C13.5 6.89995 13 6.80005 12.2 6.80005C11.5 6.80005 10.9 7.00005 10.5 7.30005C10.1 7.60005 9.79999 8.00002 9.79999 8.40002C9.79999 8.70002 9.9 8.89998 10 9.09998C10.1 9.29998 10.4 9.49998 10.6 9.59998C10.8 9.69998 11.1 9.90002 11.4 9.90002C11.7 10 12.1 10.1 12.7 10.3C13.5 10.5 14.2 10.7 14.8 10.9C15.4 11.1 15.9 11.4 16.4 11.7C16.8 12 17.2 12.4 17.4 12.9C17.6 13.4 17.8 14 17.8 14.7Z"
                                              fill="#035A4B"></path>
                                      </svg>
                                  </span>
                              </div>

                              <div class="flex-grow-1 ms-3">
                                  <span class="navbar-dropdown-menu-media-title">Tiếp thị liên kết -
                                      Affiliate</span>
                                  <p class="navbar-dropdown-menu-media-desc"> Hoa hồng lên đến 30%</p>
                              </div>
                          </div>
                      </a>



                  </div>

              </li>
              <!-- End Cooperate -->

              <!-- Company -->
              <li class="hs-has-sub-menu nav-item">
                <a id="accountMegaMenu" class="hs-mega-menu-invoker nav-link dropdown-toggle " href="#" role="button"
                  aria-expanded="false">Công ty</a>

                <!-- Mega Menu -->
                <div class="hs-sub-menu dropdown-menu" aria-labelledby="accountMegaMenu" style="min-width: 14rem;">
                  <a class="dropdown-item " href="./gioi-thieu.html">Giới thiệu</a>
                  <a class="dropdown-item " href="./chung-nhan.html">Chứng nhận</a>

                  <!-- Authentication -->
                  <div class="hs-has-sub-menu nav-item">
                    <a id="authenticationMegaMenu" class="hs-mega-menu-invoker dropdown-item dropdown-toggle " href="#"
                      role="button" aria-expanded="false">Ký kết hợp tác</a>

                    <div class="hs-sub-menu dropdown-menu" aria-labelledby="authenticationMegaMenu"
                      style="min-width: 14rem;">
                      <a class="dropdown-item " href="./kien-long-bank.html">Ngân hàng Kiên Long</a>
                      <a class="dropdown-item " href="./ocb.html">Ngân hàng OCB</a>
                      <a class="dropdown-item " href="./msb.html">Ngân hàng MSB</a>
                      <a class="dropdown-item " href="./mb.html">Ngân hàng MB</a>
                    </div>
                  </div>
                  <!-- End Authentication -->

                  <a class="dropdown-item " href="./ngan-hang.html">Ngân hàng kết nối (22)</a>

                </div>
                <!-- End Mega Menu -->
              </li>
              <!-- End Company -->

              <!-- Button -->
              <li class="nav-item mt-5 mt-lg-0">
                <a class="btn btn-light btn-transition" href="https://my.sepay.vn/login" target="_blank">Đăng nhập</a>

                <a class="btn btn-primary btn-transition" href="https://my.sepay.vn/register" target="_blank">Đăng
                  ký</a>
              </li>
              <!-- End Button -->
            </ul>
          </div>
        </div>
        <!-- End Collapse -->
      </nav>
    </div>

    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@graph": [{
          "@type": "Organization",
          "@id": "https://sepay.vn/#organization",
          "name": "SePay",
          "url": "https://sepay.vn/",
          "logo": {
            "@type": "ImageObject",
            "@id": "https://sepay.vn/#logo",
            "inLanguage": "vi_VN",
            "url": "https://sepay.vn/assets/img/logo/sepay-820x820-blue-icon.png",
            "contentUrl": "https://sepay.vn/assets/img/logo/sepay-820x820-blue-icon.png",
            "width": 820,
            "height": 820,
            "caption": "SePay"
          },
          "image": {
            "@id": "https://sepay.vn/#logo"
          }
        }, {
          "@type": "WebSite",
          "@id": "https://sepay.vn/#website",
          "url": "https://sepay.vn/",
          "name": "SePay",
          "description": "SePay cung cấp công cụ giúp chia sẻ biến động số dư ngân hàng, xác thực thanh toán chuyển khoản, thống kê dòng tiền ngân hàng một cách tự động",
          "publisher": {
            "@id": "https://sepay.vn/#organization"
          },
          "inLanguage": "vi_VN"
        }, {
          "@type": "ImageObject",
          "@id": "https://sepay.vn/bang-gia.html#primaryimage",
          "inLanguage": "vi_VN",
          "url": "https://sepay.vn/assets/img/others/sepay-pricing.png",
          "contentUrl": "https://sepay.vn/assets/img/others/sepay-pricing.png",
          "width": 1511,
          "height": 796
        }, {
          "@type": "WebPage",
          "@id": "https://sepay.vn/bang-gia.html#webpage",
          "url": "https://sepay.vn/bang-gia.html",
          "name": "Bảng giá - SePay",
          "isPartOf": {
            "@id": "https://sepay.vn/#website"
          },
          "primaryImageOfPage": {
            "@id": "https://sepay.vn/bang-gia.html#primaryimage"
          },
          "datePublished": "2023-03-19T15:56:22+00:00",
          "dateModified": "2023-03-20T11:06:48+00:00",
          "description": "Khám phá bảng giá của SePay. Lựa chọn gói phù hợp với bạn.",
          "breadcrumb": {
            "@id": "https://sepay.vn/bang-gia.html#breadcrumb"
          },
          "inLanguage": "vi_VN",
          "potentialAction": [{
            "@type": "ReadAction",
            "target": ["https://sepay.vn/bang-gia.html"]
          }]
        }, {
          "@type": "BreadcrumbList",
          "@id": "https://sepay.vn/bang-gia.html#breadcrumb",
          "itemListElement": [{
            "@type": "ListItem",
            "position": 1,
            "name": "Trang chủ",
            "item": "https://sepay.vn/"
          }, {
            "@type": "ListItem",
            "position": 2,
            "name": "Bảng giá"
          }]
        }]
      }
    </script>

  </header>

  <!-- ========== END HEADER ========== -->

  <!-- ========== MAIN CONTENT ========== -->
  <main id="content" role="main">
    <div class="overflow-hidden">
      <!-- Hero -->
      <div class="position-relative bg-img-start" style="background-image: url(./assets/svg/components/card-11.svg);">
        <div class="container content-space-t-3 content-space-t-lg-5 content-space-b-2 content-space-b-lg-3">
          <!-- Heading -->
          <div class="w-md-75 w-lg-50 text-center mx-auto mb-9">
            <h1>Chọn gói phù hợp với bạn</h1>
            <p>Số tiền trả cho SePay ít hơn rất nhiều so với chi phí cổng thanh toán</p>
          </div>
          <!-- End Heading -->

          <!-- Form Switch -->
          <div class="row justify-content-center mt-3 mb-2">
            <div class="col-auto">
              <div class="btn-group" role="group" aria-label="Basic radio toggle button group">
                <input type="radio" class="btn-check" name="btnradio" id="btnradio1" autocomplete="off" checked
                  onclick="updatePriceBox('billing_select', 1, this)" data-discount="0">
                <label class="btn btn-outline-primary" for="btnradio1">Theo tháng</label>

                <input type="radio" class="btn-check" name="btnradio" id="btnBillYearly" autocomplete="off"
                  onclick="updatePriceBox('billing_select', 12, this)" data-discount="0.3">
                <label class="btn btn-outline-primary" for="btnBillYearly">Theo năm <span
                    class="form-switch-promotion-container">
                    <span class="form-switch-promotion-body">
                      <svg class="form-switch-promotion-arrow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px"
                        viewBox="0 0 99.3 57" width="48">
                        <path fill="none" stroke="#bdc5d1" stroke-width="4" stroke-linecap="round"
                          stroke-miterlimit="10" d="M2,39.5l7.7,14.8c0.4,0.7,1.3,0.9,2,0.4L27.9,42"></path>
                        <path fill="none" stroke="#bdc5d1" stroke-width="4" stroke-linecap="round"
                          stroke-miterlimit="10" d="M11,54.3c0,0,10.3-65.2,86.3-50"></path>
                      </svg>
                      <span class="form-switch-promotion-text">
                        <span class="badge bg-danger rounded-pill ms-1">Giảm tới 30%</span>
                      </span>
                    </span>
                  </span></label>

              </div>
            </div>
          </div>
          <!-- End Form Switch -->
        </div>
      </div>
      <!-- End Hero -->
      <style>
        .card-top-price {
          border-top-left-radius: 7% 3% !important;
          border-top-right-radius: 7% 3% !important;
          overflow: hidden;
        }
      </style>
      <!-- Pricing -->
      <div class="mt-n5 mt-lg-n10 mx-auto" style="max-width:1200px">
        <div class="position-relative zi-1 mb-10">
          <div class="row justify-content-center">




            <!-- plans api only -->
            <div class="col-md-4 mb-3 mb-md-0 mt-3">
              <div class="card h-100 rounded-4 py-0 card-top-price border border-success">
                <div class="card-header py-2 fw-bold text-center card-top-price bg-success text-white">NGÂN HÀNG HỢP TÁC
                  <br><span><i class="bi bi-check-circle-fill"></i> Tiết kiệm thêm 15%</span></div>
                <div class="card-body d-flex flex-column p-0">
                  <div class="px-4 pt-4 pb-2">
                    <h5 class="card-title" id="box-api-name">STARTUP</h5>
                    <p class="fw-bold h2"><span class="price_value" data-originalprice="120000"
                        id="box-api-price">120,000đ</span> <span class="fw-normal fs-5 text-muted">/tháng</span></p>

                    <div class="d-flex">
                      <span class="form-switch-promotion-body ms-auto">
                        <svg class="form-switch-promotion-arrow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 99.3 57" width="48">
                          <path fill="none" stroke="#bdc5d1" stroke-width="4" stroke-linecap="round" stroke-miterlimit="10" d="M2,39.5l7.7,14.8c0.4,0.7,1.3,0.9,2,0.4L27.9,42"></path>
                          <path fill="none" stroke="#bdc5d1" stroke-width="4" stroke-linecap="round" stroke-miterlimit="10" d="M11,54.3c0,0,10.3-65.2,86.3-50"></path>
                        </svg>
                        <span class="form-switch-promotion-text">
                          <span class="badge bg-danger rounded-pill ms-1">Bạn cần gói cao hơn?</span>
                        </span>
                      </span>
                    </div>

                    <div class="mt-3" style="font-size: 14px;">

                      <div class="my-2"> <select name="api_plan_select" id="api_plan_select" class="form-select"
                          onchange="updatePriceBox('api')">

                          <option value="10" data-price="120000" data-transactions="180" selected data-plan='Startup'>
                            180 giao dịch/ tháng</option>
                          <option value="11" data-price="368000" data-transactions="600" data-plan='Basic'>
                            600 giao dịch/ tháng</option>
                          <option value="12" data-price="589000" data-transactions="1000" data-plan='VIP'>
                            1,000 giao dịch/ tháng</option>
                          <option value="13" data-price="850000" data-transactions="1500" data-plan='Growth'>
                            1,500 giao dịch/ tháng</option>
                          <option value="14" data-price="1190000" data-transactions="2300" data-plan='Stellar'>
                            2,300 giao dịch/ tháng</option>
                          <option value="15" data-price="1680000" data-transactions="3400" data-plan='Premium'>
                            3,400 giao dịch/ tháng</option>
                          <option value="16" data-price="2480000" data-transactions="5100" data-plan='Elite'>
                            5,100 giao dịch/ tháng</option>
                          <option value="17" data-price="3320000" data-transactions="7600" data-plan='Platinum'>
                            7,600 giao dịch/ tháng</option>
                          <option value="18" data-price="4980000" data-transactions="12000" data-plan='Titanium'>
                            12,000 giao dịch/ tháng</option>
                          <option value="19" data-price="6980000" data-transactions="18000" data-plan='Diamond'>
                            18,000 giao dịch/ tháng</option>
                          <option value="20" data-price="9200000" data-transactions="26000" data-plan='Galaxy'>
                            26,000 giao dịch/ tháng</option>
                          <option value="21" data-price="13580000" data-transactions="39000" data-plan='Universe'>
                            39,000 giao dịch/ tháng</option>
                          <option value="22" data-price="19680000" data-transactions="58000" data-plan='Supreme'>
                            58,000 giao dịch/ tháng</option>
                          <option value="23" data-price="27200000" data-transactions="87000" data-plan='Ultimate'>
                            87,000 giao dịch/ tháng</option>
                        </select></div>

                      <div class="my-2 mt-4">
                        <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"
                          class="me-2 text-success">
                          <path
                            d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z"
                            fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                          </path>
                        </svg> <a href="javascript:;" class="link-secondary" data-bs-toggle="modal"
                          data-bs-target="#modalBDSD">Chia sẻ biến động số dư <i
                            class="bi bi-eye float-end text-primary"></i></a>
                      </div>
                      <div class="my-2">
                        <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"
                          class="me-2 text-success">
                          <path
                            d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z"
                            fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                          </path>
                        </svg><a href="javascript:;" class="link-secondary" data-bs-toggle="modal"
                          data-bs-target="#modalQRCODE"> Cổng thanh toán trực tuyến <i
                            class="bi bi-eye float-end text-primary"></i></a>
                      </div>
                      <div class="my-2">
                        <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"
                          class="me-2 text-success">
                          <path
                            d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z"
                            fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                          </path>
                        </svg><a href="javascript:;" class="link-secondary" data-bs-toggle="modal"
                          data-bs-target="#modalAPINH"> Hỗ trợ Webhook, API <i
                            class="bi bi-eye text-primary float-end"></i></a></div>
                      <div class="my-2">
                        <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"
                          class="me-2 text-success">
                          <path
                            d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z"
                            fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                          </path>
                        </svg> <a href="javascript:;" class="link-secondary" data-bs-toggle="modal"
                          data-bs-target="#modalTCTN"> Tất cả tính năng của SePay <i
                            class="bi bi-eye text-primary float-end"></i></a>
                      </div>


                    </div>
                  </div>
                  <div class="text-center mt-2 pb-4 d-grid d-grid px-3">
                    <a id="box-api-order"
                      href="https://my.sepay.vn/company/cart?product_id=10&amp;billing_cycle=monthly"
                      class="btn btn-lg btn-primary btn-order">Chọn</a>
                  </div>
                  <div class="border-top">
                    <div class="px-4 py-2">
                      <p class="fw-4 h5 mt-2 mb-3">Ngân hàng hỗ trợ
                        (4)</p>
                      <div>

                        <div class="position-relative d-inline me-2 mb-1">
                          <img data-bs-toggle="tooltip" data-bs-title="MB Bank" class="rounded-circle border p-1 mt-1"
                            src="https://my.sepay.vn/assets/images/banklogo/mbbank-icon.png"
                            style="height:35px; width:35px" data-bs-original-title="" title="">
                          <span class="position-absolute top-0 start-100 translate-middle" data-bs-toggle="tooltip"
                            data-bs-title="Đã ký hợp tác chính thức" data-bs-original-title="" title="">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                              class="bi bi-patch-check-fill text-info" viewBox="0 0 16 16">
                              <path
                                d="M10.067.87a2.89 2.89 0 0 0-4.134 0l-.622.638-.89-.011a2.89 2.89 0 0 0-2.924 2.924l.01.89-.636.622a2.89 2.89 0 0 0 0 4.134l.637.622-.011.89a2.89 2.89 0 0 0 2.924 2.924l.89-.01.622.636a2.89 2.89 0 0 0 4.134 0l.622-.637.89.011a2.89 2.89 0 0 0 2.924-2.924l-.01-.89.636-.622a2.89 2.89 0 0 0 0-4.134l-.637-.622.011-.89a2.89 2.89 0 0 0-2.924-2.924l-.89.01zm.287 5.984-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7 8.793l2.646-2.647a.5.5 0 0 1 .708.708">
                              </path>
                            </svg>
                          </span>

                        </div>

                        <div class="position-relative d-inline me-2 mb-1">
                          <img data-bs-toggle="tooltip" data-bs-title="KienlongBank"
                            class="rounded-circle border p-1 mt-1"
                            src="https://my.sepay.vn/assets/images/banklogo/kienlongbank-icon.png"
                            style="height:35px; width:35px" data-bs-original-title="" title="">
                          <span class="position-absolute top-0 start-100 translate-middle" data-bs-toggle="tooltip"
                            data-bs-title="Đã ký hợp tác chính thức" data-bs-original-title="" title="">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                              class="bi bi-patch-check-fill text-info" viewBox="0 0 16 16">
                              <path
                                d="M10.067.87a2.89 2.89 0 0 0-4.134 0l-.622.638-.89-.011a2.89 2.89 0 0 0-2.924 2.924l.01.89-.636.622a2.89 2.89 0 0 0 0 4.134l.637.622-.011.89a2.89 2.89 0 0 0 2.924 2.924l.89-.01.622.636a2.89 2.89 0 0 0 4.134 0l.622-.637.89.011a2.89 2.89 0 0 0 2.924-2.924l-.01-.89.636-.622a2.89 2.89 0 0 0 0-4.134l-.637-.622.011-.89a2.89 2.89 0 0 0-2.924-2.924l-.89.01zm.287 5.984-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7 8.793l2.646-2.647a.5.5 0 0 1 .708.708">
                              </path>
                            </svg>
                          </span>

                        </div>
                        <div class="position-relative d-inline me-2 mb-1">
                          <img data-bs-toggle="tooltip" data-bs-title="OCB" class="rounded-circle border p-1 mt-1"
                            src="https://my.sepay.vn/assets/images/banklogo/ocb-icon.png"
                            style="height:35px; width:35px" data-bs-original-title="" title="">
                          <span class="position-absolute top-0 start-100 translate-middle" data-bs-toggle="tooltip"
                            data-bs-title="Đã ký hợp tác chính thức" data-bs-original-title="" title="">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                              class="bi bi-patch-check-fill text-info" viewBox="0 0 16 16">
                              <path
                                d="M10.067.87a2.89 2.89 0 0 0-4.134 0l-.622.638-.89-.011a2.89 2.89 0 0 0-2.924 2.924l.01.89-.636.622a2.89 2.89 0 0 0 0 4.134l.637.622-.011.89a2.89 2.89 0 0 0 2.924 2.924l.89-.01.622.636a2.89 2.89 0 0 0 4.134 0l.622-.637.89.011a2.89 2.89 0 0 0 2.924-2.924l-.01-.89.636-.622a2.89 2.89 0 0 0 0-4.134l-.637-.622.011-.89a2.89 2.89 0 0 0-2.924-2.924l-.89.01zm.287 5.984-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7 8.793l2.646-2.647a.5.5 0 0 1 .708.708">
                              </path>
                            </svg>
                          </span>
                        </div>

                        <div class="position-relative d-inline me-2 mb-1">
                          <img data-bs-toggle="tooltip" data-bs-title="BIDV" class="rounded-circle border p-1 mt-1"
                            src="https://my.sepay.vn/assets/images/banklogo/bidv-icon.png"
                            style="height:35px; width:35px" data-bs-original-title="" title="">
                          <span class="position-absolute top-0 start-100 translate-middle" data-bs-toggle="tooltip"
                            data-bs-title="Đã ký hợp tác chính thức cho KH doanh nghiệp" data-bs-original-title="" title="">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                              class="bi bi-patch-check-fill text-info" viewBox="0 0 16 16">
                              <path
                                d="M10.067.87a2.89 2.89 0 0 0-4.134 0l-.622.638-.89-.011a2.89 2.89 0 0 0-2.924 2.924l.01.89-.636.622a2.89 2.89 0 0 0 0 4.134l.637.622-.011.89a2.89 2.89 0 0 0 2.924 2.924l.89-.01.622.636a2.89 2.89 0 0 0 4.134 0l.622-.637.89.011a2.89 2.89 0 0 0 2.924-2.924l-.01-.89.636-.622a2.89 2.89 0 0 0 0-4.134l-.637-.622.011-.89a2.89 2.89 0 0 0-2.924-2.924l-.89.01zm.287 5.984-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7 8.793l2.646-2.647a.5.5 0 0 1 .708.708">
                              </path>
                            </svg>
                          </span>
                        </div>


                      </div>
                    </div>
                  </div>


                </div>


                <div class="card-footer border-top p-3" style="background-color: #0096880f;">
                  <span class="text-danger fw-bold"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14"
                      fill="currentColor" class="bi bi-gift" viewBox="0 0 16 16">
                      <path
                        d="M3 2.5a2.5 2.5 0 0 1 5 0 2.5 2.5 0 0 1 5 0v.006c0 .07 0 .27-.038.494H15a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1v7.5a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 14.5V7a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h2.038A3 3 0 0 1 3 2.506zm1.068.5H7v-.5a1.5 1.5 0 1 0-3 0c0 .085.002.274.045.43zM9 3h2.932l.023-.07c.043-.156.045-.345.045-.43a1.5 1.5 0 0 0-3 0zM1 4v2h6V4zm8 0v2h6V4zm5 3H9v8h4.5a.5.5 0 0 0 .5-.5zm-7 8V7H2v7.5a.5.5 0 0 0 .5.5z" />
                    </svg> Quà tặng từ ngân hàng:</span>

                  <table class="table p-0 mb-0 table-borderless fs-6 mt-2">
                    <tbody class="p-0">
                      <tr class="p-0">
                        <td class="p-1 fw-bold">MBBank</td>
                        <td class="py-1">Tặng voucher <b>tài khoản số đẹp 6</b> số trị giá 25.000.000 VND, tặng thẻ cung hoàn đạo <a data-bs-toggle="modal" data-bs-target="#modalBANKPROMO"
                            href="javascript:;">Xem<i class="bi bi-chevron-right"></i></a></td>
                      </tr>
                    </tbody>
                  </table>

                </div>


              </div>
            </div>
            <!-- plans api only -->


            <!-- plans sms -->
            <div class="col-md-4 mb-3 mb-md-0 mt-3">
              <div class="card h-100 rounded-4 py-0 card-top-price border border-primary">
                <div class="card-header py-3 fw-bold text-center card-top-price bg-info text-white">TẤT CẢ NGÂN HÀNG
                </div>
                <div class="card-body d-flex flex-column p-0">
                  <div class="px-4 pt-4 pb-2">
                    <h5 class="card-title" id="box-sms-name">STARTER</h5>
                    <p class="fw-bold h2"><span id="box-sms-price" class="price_value"
                        data-originalprice="689000">689,000đ</span> <span
                        class="fw-normal fs-5 text-muted">/tháng</span></p>

                    <div class="d-flex">
                      <span class="form-switch-promotion-body ms-auto">
                        <svg class="form-switch-promotion-arrow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 99.3 57" width="48">
                          <path fill="none" stroke="#bdc5d1" stroke-width="4" stroke-linecap="round" stroke-miterlimit="10" d="M2,39.5l7.7,14.8c0.4,0.7,1.3,0.9,2,0.4L27.9,42"></path>
                          <path fill="none" stroke="#bdc5d1" stroke-width="4" stroke-linecap="round" stroke-miterlimit="10" d="M11,54.3c0,0,10.3-65.2,86.3-50"></path>
                        </svg>
                        <span class="form-switch-promotion-text">
                          <span class="badge bg-danger rounded-pill ms-1">Bạn cần gói cao hơn?</span>
                        </span>
                      </span>
                    </div>

                    <div class="mt-3" style="font-size: 14px;">

                      <div class="my-2">
                        <select class="form-select" name="sms_plan_select" id="sms_plan_select"
                          onchange="updatePriceBox('sms')">

                          <option value="1" data-price="689000" data-transactions="1000" selected data-plan='Starter'>
                            1,000 giao dịch/ tháng</option>
                          <option value="24" data-price="998000" data-transactions="1500" data-plan='Ascend'>
                            1,500 giao dịch/ tháng</option>
                          <option value="25" data-price="1430000" data-transactions="2300" data-plan='Pinnacle'>
                            2,300 giao dịch/ tháng</option>
                          <option value="26" data-price="1990000" data-transactions="3400" data-plan='Blaze'>
                            3,400 giao dịch/ tháng</option>
                          <option value="27" data-price="2880000" data-transactions="5100" data-plan='Zenith'>
                            5,100 giao dịch/ tháng</option>
                          <option value="28" data-price="3900000" data-transactions="7600" data-plan='Luminary'>
                            7,600 giao dịch/ tháng</option>
                          <option value="29" data-price="5800000" data-transactions="12000" data-plan='Titan'>
                            12,000 giao dịch/ tháng</option>
                          <option value="30" data-price="8200000" data-transactions="18000" data-plan='Nebula'>
                            18,000 giao dịch/ tháng</option>
                          <option value="31" data-price="10900000" data-transactions="26000" data-plan='Infinity'>
                            26,000 giao dịch/ tháng</option>
                          <option value="32" data-price="15900000" data-transactions="39000" data-plan='Transcendent'>
                            39,000 giao dịch/ tháng</option>
                          <option value="33" data-price="23100000" data-transactions="58000" data-plan='King'>
                            58,000 giao dịch/ tháng</option>
                          <option value="34" data-price="32200000" data-transactions="87000" data-plan='Limitless'>
                            87,000 giao dịch/ tháng</option>
                        </select></div>


                      <div class="my-2 mt-4">
                        <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"
                          class="me-2 text-success">
                          <path
                            d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z"
                            fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                          </path>
                        </svg> <a href="javascript:;" class="link-secondary" data-bs-toggle="modal"
                          data-bs-target="#modalBDSD">Chia sẻ biến động số dư <i
                            class="bi bi-eye float-end text-primary"></i></a>
                      </div>
                      <div class="my-2">
                        <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"
                          class="me-2 text-success">
                          <path
                            d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z"
                            fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                          </path>
                        </svg><a href="javascript:;" class="link-secondary" data-bs-toggle="modal"
                          data-bs-target="#modalQRCODE"> Cổng thanh toán trực tuyến <i
                            class="bi bi-eye float-end text-primary"></i></a>
                      </div>
                      <div class="my-2">
                        <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"
                          class="me-2 text-success">
                          <path
                            d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z"
                            fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                          </path>
                        </svg><a href="javascript:;" class="link-secondary" data-bs-toggle="modal"
                          data-bs-target="#modalAPINH"> Hỗ trợ Webhook, API <i
                            class="bi bi-eye text-primary float-end"></i></a></div>
                      <div class="my-2">
                        <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"
                          class="me-2 text-success">
                          <path
                            d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z"
                            fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                          </path>
                        </svg> <a href="javascript:;" class="link-secondary" data-bs-toggle="modal"
                          data-bs-target="#modalTCTN"> Tất cả tính năng của SePay <i
                            class="bi bi-eye text-primary float-end"></i></a>
                      </div>

                    </div>
                  </div>
                  <div class="text-center mt-2 pb-4 d-grid d-grid px-3">
                    <a id="box-sms-order" href="https://my.sepay.vn/company/cart?product_id=1&amp;billing_cycle=monthly"
                      class="btn btn-lg btn-primary btn-order">Chọn</a>
                  </div>
                  <div class="border-top">
                    <div class="px-4 py-2">
                      <p class="fw-4 h5 mt-2 mb-3">Ngân hàng hỗ trợ (21)</p>
                      <div>

                        <div class="position-relative d-inline me-2 mb-1">
                          <img data-bs-toggle="tooltip" data-bs-title="MB Bank" class="rounded-circle border p-1 mt-1"
                            src="https://my.sepay.vn/assets/images/banklogo/mbbank-icon.png"
                            style="height:35px; width:35px" data-bs-original-title="" title="">
                          <span class="position-absolute top-0 start-100 translate-middle" data-bs-toggle="tooltip"
                            data-bs-title="Đã ký hợp tác chính thức" data-bs-original-title="" title="">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                              class="bi bi-patch-check-fill text-info" viewBox="0 0 16 16">
                              <path
                                d="M10.067.87a2.89 2.89 0 0 0-4.134 0l-.622.638-.89-.011a2.89 2.89 0 0 0-2.924 2.924l.01.89-.636.622a2.89 2.89 0 0 0 0 4.134l.637.622-.011.89a2.89 2.89 0 0 0 2.924 2.924l.89-.01.622.636a2.89 2.89 0 0 0 4.134 0l.622-.637.89.011a2.89 2.89 0 0 0 2.924-2.924l-.01-.89.636-.622a2.89 2.89 0 0 0 0-4.134l-.637-.622.011-.89a2.89 2.89 0 0 0-2.924-2.924l-.89.01zm.287 5.984-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7 8.793l2.646-2.647a.5.5 0 0 1 .708.708">
                              </path>
                            </svg>
                          </span>

                        </div>

                        <div class="position-relative d-inline me-2 mb-1">
                          <img data-bs-toggle="tooltip" data-bs-title="KienlongBank"
                            class="rounded-circle border p-1 mt-1"
                            src="https://my.sepay.vn/assets/images/banklogo/kienlongbank-icon.png"
                            style="height:35px; width:35px" data-bs-original-title="" title="">
                          <span class="position-absolute top-0 start-100 translate-middle" data-bs-toggle="tooltip"
                            data-bs-title="Đã ký hợp tác chính thức" data-bs-original-title="" title="">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                              class="bi bi-patch-check-fill text-info" viewBox="0 0 16 16">
                              <path
                                d="M10.067.87a2.89 2.89 0 0 0-4.134 0l-.622.638-.89-.011a2.89 2.89 0 0 0-2.924 2.924l.01.89-.636.622a2.89 2.89 0 0 0 0 4.134l.637.622-.011.89a2.89 2.89 0 0 0 2.924 2.924l.89-.01.622.636a2.89 2.89 0 0 0 4.134 0l.622-.637.89.011a2.89 2.89 0 0 0 2.924-2.924l-.01-.89.636-.622a2.89 2.89 0 0 0 0-4.134l-.637-.622.011-.89a2.89 2.89 0 0 0-2.924-2.924l-.89.01zm.287 5.984-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7 8.793l2.646-2.647a.5.5 0 0 1 .708.708">
                              </path>
                            </svg>
                          </span>

                        </div>
                        <div class="position-relative d-inline me-2 mb-1">
                          <img data-bs-toggle="tooltip" data-bs-title="OCB" class="rounded-circle border p-1 mt-1"
                            src="https://my.sepay.vn/assets/images/banklogo/ocb-icon.png"
                            style="height:35px; width:35px" data-bs-original-title="" title="">
                          <span class="position-absolute top-0 start-100 translate-middle" data-bs-toggle="tooltip"
                            data-bs-title="Đã ký hợp tác chính thức" data-bs-original-title="" title="">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                              class="bi bi-patch-check-fill text-info" viewBox="0 0 16 16">
                              <path
                                d="M10.067.87a2.89 2.89 0 0 0-4.134 0l-.622.638-.89-.011a2.89 2.89 0 0 0-2.924 2.924l.01.89-.636.622a2.89 2.89 0 0 0 0 4.134l.637.622-.011.89a2.89 2.89 0 0 0 2.924 2.924l.89-.01.622.636a2.89 2.89 0 0 0 4.134 0l.622-.637.89.011a2.89 2.89 0 0 0 2.924-2.924l-.01-.89.636-.622a2.89 2.89 0 0 0 0-4.134l-.637-.622.011-.89a2.89 2.89 0 0 0-2.924-2.924l-.89.01zm.287 5.984-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7 8.793l2.646-2.647a.5.5 0 0 1 .708.708">
                              </path>
                            </svg>
                          </span>
                        </div>

                        <div class="position-relative d-inline me-2 mb-1">
                          <img data-bs-toggle="tooltip" data-bs-title="BIDV" class="rounded-circle border p-1 mt-1"
                            src="https://my.sepay.vn/assets/images/banklogo/bidv-icon.png"
                            style="height:35px; width:35px" data-bs-original-title="" title="">
                          <span class="position-absolute top-0 start-100 translate-middle" data-bs-toggle="tooltip"
                            data-bs-title="Đã ký hợp tác chính thức cho KH doanh nghiệp" data-bs-original-title="" title="">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                              class="bi bi-patch-check-fill text-info" viewBox="0 0 16 16">
                              <path
                                d="M10.067.87a2.89 2.89 0 0 0-4.134 0l-.622.638-.89-.011a2.89 2.89 0 0 0-2.924 2.924l.01.89-.636.622a2.89 2.89 0 0 0 0 4.134l.637.622-.011.89a2.89 2.89 0 0 0 2.924 2.924l.89-.01.622.636a2.89 2.89 0 0 0 4.134 0l.622-.637.89.011a2.89 2.89 0 0 0 2.924-2.924l-.01-.89.636-.622a2.89 2.89 0 0 0 0-4.134l-.637-.622.011-.89a2.89 2.89 0 0 0-2.924-2.924l-.89.01zm.287 5.984-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7 8.793l2.646-2.647a.5.5 0 0 1 .708.708">
                              </path>
                            </svg>
                          </span>
                        </div>

                        <img data-bs-toggle="tooltip" data-bs-title="Vietcombank" class="rounded-circle border p-1 mt-1"
                          src="https://my.sepay.vn/assets/images/banklogo/vietcombank-icon.png"
                          style="height:35px; width:35px" data-bs-original-title="" title="">
                        <img data-bs-toggle="tooltip" data-bs-title="VPBank" class="rounded-circle border p-1 mt-1"
                          src="https://my.sepay.vn/assets/images/banklogo/vpbank-icon.png"
                          style="height:35px; width:35px" data-bs-original-title="" title="">
                        <img data-bs-toggle="tooltip" data-bs-title="ACB" class="rounded-circle border p-1 mt-1"
                          src="https://my.sepay.vn/assets/images/banklogo/acb-icon.png" style="height:35px; width:35px"
                          data-bs-original-title="" title="">
                        <img data-bs-toggle="tooltip" data-bs-title="Sacombank" class="rounded-circle border p-1 mt-1"
                          src="https://my.sepay.vn/assets/images/banklogo/sacombank-icon.png"
                          style="height:35px; width:35px" data-bs-original-title="" title="">
                        <img data-bs-toggle="tooltip" data-bs-title="HDBank" class="rounded-circle border p-1 mt-1"
                          src="https://my.sepay.vn/assets/images/banklogo/hdbank-icon.png"
                          style="height:35px; width:35px" data-bs-original-title="" title="">
                        <img data-bs-toggle="tooltip" data-bs-title="VietinBank" class="rounded-circle border p-1 mt-1"
                          src="https://my.sepay.vn/assets/images/banklogo/vietinbank-icon.png"
                          style="height:35px; width:35px" data-bs-original-title="" title="">
                        <img data-bs-toggle="tooltip" data-bs-title="Techcombank" class="rounded-circle border p-1 mt-1"
                          src="https://my.sepay.vn/assets/images/banklogo/techcombank-icon.png"
                          style="height:35px; width:35px" data-bs-original-title="" title="">
                        <img data-bs-toggle="tooltip" data-bs-title="MSB" class="rounded-circle border p-1 mt-1"
                          src="https://my.sepay.vn/assets/images/banklogo/msb-icon.png" style="height:35px; width:35px"
                          data-bs-original-title="" title="">
                        <img data-bs-toggle="tooltip" data-bs-title="ShinhanBank" class="rounded-circle border p-1 mt-1"
                          src="https://my.sepay.vn/assets/images/banklogo/shinhan-icon.png"
                          style="height:35px; width:35px" data-bs-original-title="" title="">
                        <img data-bs-toggle="tooltip" data-bs-title="TPBank" class="rounded-circle border p-1 mt-1"
                          src="https://my.sepay.vn/assets/images/banklogo/tpbank-icon.png"
                          style="height:35px; width:35px" data-bs-original-title="" title="">
                        <img data-bs-toggle="tooltip" data-bs-title="Eximbank" class="rounded-circle border p-1 mt-1"
                          src="https://my.sepay.vn/assets/images/banklogo/eximbank-icon.png"
                          style="height:35px; width:35px" data-bs-original-title="" title="">
                        <img data-bs-toggle="tooltip" data-bs-title="VIB" class="rounded-circle border p-1 mt-1"
                          src="https://my.sepay.vn/assets/images/banklogo/vib-icon.png" style="height:35px; width:35px"
                          data-bs-original-title="" title="">
                        <img data-bs-toggle="tooltip" data-bs-title="Agribank" class="rounded-circle border p-1 mt-1"
                          src="https://my.sepay.vn/assets/images/banklogo/agribank-icon.png"
                          style="height:35px; width:35px" data-bs-original-title="" title="">
                        <img data-bs-toggle="tooltip" data-bs-title="PBVN" class="rounded-circle border p-1 mt-1"
                          src="https://my.sepay.vn/assets/images/banklogo/publicbank-icon.png"
                          style="height:35px; width:35px" data-bs-original-title="" title="">
                        <img data-bs-toggle="tooltip" data-bs-title="ABBank" class="rounded-circle border p-1 mt-1"
                          src="https://my.sepay.vn/assets/images/banklogo/abbank-icon.png"
                          style="height:35px; width:35px" data-bs-original-title="" title="">
                        <img data-bs-toggle="tooltip" data-bs-title="BAC A BANK" class="rounded-circle border p-1 mt-1"
                          src="https://my.sepay.vn/assets/images/banklogo/bacabank-icon.png"
                          style="height:35px; width:35px" data-bs-original-title="" title="">
                        <img data-bs-toggle="tooltip" data-bs-title="LPBank" class="rounded-circle border p-1 mt-1"
                          src="https://my.sepay.vn/assets/images/banklogo/lienvietpostbank-icon.png"
                          style="height:35px; width:35px" data-bs-original-title="" title="">
                        <img data-bs-toggle="tooltip" data-bs-title="SeABank" class="rounded-circle border p-1 mt-1"
                          src="https://my.sepay.vn/assets/images/banklogo/seabank-icon.png"
                          style="height:35px; width:35px" data-bs-original-title="" title="">

                      </div>
                    </div>
                  </div>


                </div>

                <div class="card-footer border-top p-3" style="background-color: #0096880f;">
                  <span class="text-danger fw-bold"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14"
                      fill="currentColor" class="bi bi-gift" viewBox="0 0 16 16">
                      <path
                        d="M3 2.5a2.5 2.5 0 0 1 5 0 2.5 2.5 0 0 1 5 0v.006c0 .07 0 .27-.038.494H15a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1v7.5a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 14.5V7a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h2.038A3 3 0 0 1 3 2.506zm1.068.5H7v-.5a1.5 1.5 0 1 0-3 0c0 .085.002.274.045.43zM9 3h2.932l.023-.07c.043-.156.045-.345.045-.43a1.5 1.5 0 0 0-3 0zM1 4v2h6V4zm8 0v2h6V4zm5 3H9v8h4.5a.5.5 0 0 0 .5-.5zm-7 8V7H2v7.5a.5.5 0 0 0 .5.5z" />
                    </svg> Quà tặng từ ngân hàng:</span>

                  <table class="table p-0 mb-0 table-borderless fs-6 mt-2">
                    <tbody class="p-0">
                      <tr class="p-0">
                        <td class="p-1 fw-bold">MBBank</td>
                        <td class="py-1">Tặng voucher <b>tài khoản số đẹp 6</b> số trị giá 25.000.000 VND, tặng thẻ cung hoàn đạo <a data-bs-toggle="modal" data-bs-target="#modalBANKPROMO"
                            href="javascript:;">Xem<i class="bi bi-chevron-right"></i></a></td>
                      </tr>
                    </tbody>
                  </table>

                </div>




              </div>
            </div>
            <!-- plans sms -->


            <!-- plans free -->


            <div class="col-md-4 mb-3 mb-md-0 mt-3">
              <div class="card h-100 rounded-4 py-0 card-top-price border">
                <div class="card-header py-3 fw-bold text-center card-top-price" style="background-color: #2196f32e;">
                  MIỄN
                  PHÍ</div>
                <div class="card-body d-flex flex-column p-0">
                  <div class="px-4 pt-4 pb-2">
                    <h5 class="card-title">FREE</h5>
                    <p class="fw-bold h2">0đ <span class="fw-normal fs-5 text-muted">/tháng</span></p>

                    <div class="mt-3" style="font-size: 14px;">
                      <div class="my-2"><svg width="15" height="15" viewBox="0 0 15 15" fill="none"
                          xmlns="http://www.w3.org/2000/svg" class="me-2 text-success">
                          <path
                            d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z"
                            fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                          </path>
                        </svg> <b>35</b>
                        giao dịch/tháng</div>

                      <div class="my-2 mt-4">
                        <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"
                          class="me-2 text-success">
                          <path
                            d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z"
                            fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                          </path>
                        </svg> <a href="javascript:;" class="link-secondary" data-bs-toggle="modal"
                          data-bs-target="#modalBDSD">Chia sẻ biến động số dư <i
                            class="bi bi-eye float-end text-primary"></i></a>
                      </div>
                      <div class="my-2">
                        <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"
                          class="me-2 text-success">
                          <path
                            d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z"
                            fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                          </path>
                        </svg><a href="javascript:;" class="link-secondary" data-bs-toggle="modal"
                          data-bs-target="#modalQRCODE"> Cổng thanh toán trực tuyến <i
                            class="bi bi-eye float-end text-primary"></i></a>
                      </div>
                      <div class="my-2">
                        <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"
                          class="me-2 text-success">
                          <path
                            d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z"
                            fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                          </path>
                        </svg><a href="javascript:;" class="link-secondary" data-bs-toggle="modal"
                          data-bs-target="#modalAPINH"> Hỗ trợ Webhook, API <i
                            class="bi bi-eye text-primary float-end"></i></a></div>
                      <div class="my-2">
                        <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"
                          class="me-2 text-success">
                          <path
                            d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z"
                            fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                          </path>
                        </svg> <a href="javascript:;" class="link-secondary" data-bs-toggle="modal"
                          data-bs-target="#modalTCTN"> Tất cả tính năng của SePay <i
                            class="bi bi-eye text-primary float-end"></i></a>
                      </div>


                    </div>
                  </div>
                  <div class="text-center mt-2 pb-4 d-grid d-grid px-3">
                    <a href="https://my.sepay.vn/company/cart?product_id=7&amp;billing_cycle=monthly"
                      class="btn btn-lg btn-outline-primary">Chọn</a>
                  </div>
                  <div class="border-top">
                    <div class="px-4 py-2">
                      <p class="fw-4 h5 mt-2 mb-3">Ngân hàng hỗ trợ
                        (4)</p>
                      <div>
                        <div class="position-relative d-inline me-2 mb-1">
                          <img data-bs-toggle="tooltip" data-bs-title="MB Bank" class="rounded-circle border p-1 mt-1"
                            src="https://my.sepay.vn/assets/images/banklogo/mbbank-icon.png"
                            style="height:35px; width:35px" data-bs-original-title="" title="">
                          <span class="position-absolute top-0 start-100 translate-middle" data-bs-toggle="tooltip"
                            data-bs-title="Đã ký hợp tác chính thức" data-bs-original-title="" title="">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                              class="bi bi-patch-check-fill text-info" viewBox="0 0 16 16">
                              <path
                                d="M10.067.87a2.89 2.89 0 0 0-4.134 0l-.622.638-.89-.011a2.89 2.89 0 0 0-2.924 2.924l.01.89-.636.622a2.89 2.89 0 0 0 0 4.134l.637.622-.011.89a2.89 2.89 0 0 0 2.924 2.924l.89-.01.622.636a2.89 2.89 0 0 0 4.134 0l.622-.637.89.011a2.89 2.89 0 0 0 2.924-2.924l-.01-.89.636-.622a2.89 2.89 0 0 0 0-4.134l-.637-.622.011-.89a2.89 2.89 0 0 0-2.924-2.924l-.89.01zm.287 5.984-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7 8.793l2.646-2.647a.5.5 0 0 1 .708.708">
                              </path>
                            </svg>
                          </span>

                        </div>
                        <div class="position-relative d-inline me-2 mb-1">
                          <img data-bs-toggle="tooltip" data-bs-title="KienlongBank"
                            class="rounded-circle border p-1 mt-1"
                            src="https://my.sepay.vn/assets/images/banklogo/kienlongbank-icon.png"
                            style="height:35px; width:35px" data-bs-original-title="" title="">
                          <span class="position-absolute top-0 start-100 translate-middle" data-bs-toggle="tooltip"
                            data-bs-title="Đã ký hợp tác chính thức" data-bs-original-title="" title="">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                              class="bi bi-patch-check-fill text-info" viewBox="0 0 16 16">
                              <path
                                d="M10.067.87a2.89 2.89 0 0 0-4.134 0l-.622.638-.89-.011a2.89 2.89 0 0 0-2.924 2.924l.01.89-.636.622a2.89 2.89 0 0 0 0 4.134l.637.622-.011.89a2.89 2.89 0 0 0 2.924 2.924l.89-.01.622.636a2.89 2.89 0 0 0 4.134 0l.622-.637.89.011a2.89 2.89 0 0 0 2.924-2.924l-.01-.89.636-.622a2.89 2.89 0 0 0 0-4.134l-.637-.622.011-.89a2.89 2.89 0 0 0-2.924-2.924l-.89.01zm.287 5.984-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7 8.793l2.646-2.647a.5.5 0 0 1 .708.708">
                              </path>
                            </svg>
                          </span>

                        </div>
                        <div class="position-relative d-inline me-2 mb-1">
                          <img data-bs-toggle="tooltip" data-bs-title="OCB" class="rounded-circle border p-1 mt-1"
                            src="https://my.sepay.vn/assets/images/banklogo/ocb-icon.png"
                            style="height:35px; width:35px" data-bs-original-title="" title="">
                          <span class="position-absolute top-0 start-100 translate-middle" data-bs-toggle="tooltip"
                            data-bs-title="Đã ký hợp tác chính thức" data-bs-original-title="" title="">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                              class="bi bi-patch-check-fill text-info" viewBox="0 0 16 16">
                              <path
                                d="M10.067.87a2.89 2.89 0 0 0-4.134 0l-.622.638-.89-.011a2.89 2.89 0 0 0-2.924 2.924l.01.89-.636.622a2.89 2.89 0 0 0 0 4.134l.637.622-.011.89a2.89 2.89 0 0 0 2.924 2.924l.89-.01.622.636a2.89 2.89 0 0 0 4.134 0l.622-.637.89.011a2.89 2.89 0 0 0 2.924-2.924l-.01-.89.636-.622a2.89 2.89 0 0 0 0-4.134l-.637-.622.011-.89a2.89 2.89 0 0 0-2.924-2.924l-.89.01zm.287 5.984-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7 8.793l2.646-2.647a.5.5 0 0 1 .708.708">
                              </path>
                            </svg>
                          </span>
                        </div>
                        <div class="position-relative d-inline me-2 mb-1">
                          <img data-bs-toggle="tooltip" data-bs-title="BIDV" class="rounded-circle border p-1 mt-1"
                            src="https://my.sepay.vn/assets/images/banklogo/bidv-icon.png"
                            style="height:35px; width:35px" data-bs-original-title="" title="">
                          <span class="position-absolute top-0 start-100 translate-middle" data-bs-toggle="tooltip"
                            data-bs-title="Đã ký hợp tác chính thức cho KH doanh nghiệp" data-bs-original-title="" title="">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                              class="bi bi-patch-check-fill text-info" viewBox="0 0 16 16">
                              <path
                                d="M10.067.87a2.89 2.89 0 0 0-4.134 0l-.622.638-.89-.011a2.89 2.89 0 0 0-2.924 2.924l.01.89-.636.622a2.89 2.89 0 0 0 0 4.134l.637.622-.011.89a2.89 2.89 0 0 0 2.924 2.924l.89-.01.622.636a2.89 2.89 0 0 0 4.134 0l.622-.637.89.011a2.89 2.89 0 0 0 2.924-2.924l-.01-.89.636-.622a2.89 2.89 0 0 0 0-4.134l-.637-.622.011-.89a2.89 2.89 0 0 0-2.924-2.924l-.89.01zm.287 5.984-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7 8.793l2.646-2.647a.5.5 0 0 1 .708.708">
                              </path>
                            </svg>
                          </span>
                        </div>

                      </div>
                    </div>
                  </div>


                </div>


                <div class="card-footer border-top p-3" style="background-color: #0096880f;">
                  <span class="text-danger fw-bold"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14"
                      fill="currentColor" class="bi bi-gift" viewBox="0 0 16 16">
                      <path
                        d="M3 2.5a2.5 2.5 0 0 1 5 0 2.5 2.5 0 0 1 5 0v.006c0 .07 0 .27-.038.494H15a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1v7.5a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 14.5V7a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h2.038A3 3 0 0 1 3 2.506zm1.068.5H7v-.5a1.5 1.5 0 1 0-3 0c0 .085.002.274.045.43zM9 3h2.932l.023-.07c.043-.156.045-.345.045-.43a1.5 1.5 0 0 0-3 0zM1 4v2h6V4zm8 0v2h6V4zm5 3H9v8h4.5a.5.5 0 0 0 .5-.5zm-7 8V7H2v7.5a.5.5 0 0 0 .5.5z" />
                    </svg> Quà tặng từ ngân hàng:</span>

                  <table class="table p-0 mb-0 table-borderless fs-6 mt-2">
                    <tbody class="p-0">
                      <tr class="p-0">
                        <td class="p-1 fw-bold">MBBank</td>
                        <td class="py-1">Tặng voucher <b>tài khoản số đẹp 6</b> số trị giá 25.000.000 VND, tặng thẻ cung hoàn đạo <a data-bs-toggle="modal" data-bs-target="#modalBANKPROMO"
                            href="javascript:;">Xem<i class="bi bi-chevron-right"></i></a></td>
                      </tr>
                    </tbody>
                  </table>

                </div>



              </div>
            </div>
            <!-- plans free -->


          </div>
          <!-- End Row -->
          <!-- End Row -->



          <figure class="position-absolute top-0 end-0 mt-n8 me-n8 d-none d-sm-block" style="width: 10rem;">
            <img class="img-fluid" src="./assets/svg/components/dots-lg.svg" alt="Image Description">
          </figure>
          <!-- End SVG Elements -->
        </div>


      </div>
      <!-- End Pricing -->
    </div>

    <div class="py-3">
      <p class="text-center h4">Bạn muốn tư vấn? Hãy liên hệ ngay với chúng tôi</p>
      <p>
      </p>
      <p class="text-center fs-4"><i class="bi bi-telephone"></i> <a href="tel:**********">02873.059.589</a> | Chat <i
          class="bi bi-messenger"></i> <a target="_blank" href="m.me/***************">fb.me/sepay.vn</a>

      </p>
    </div>

    <!-- Clients -->
    <div class="container content-space-2 content-space-lg-2">
      <!-- Heading -->
      <div class="w-md-75 w-lg-50 text-center mx-md-auto mb-5 mb-md-9">
        <h2>Được tin tưởng bởi nhiều doanh nghiệp công nghệ, bán lẻ tại Việt Nam</h2>
      </div>
      <!-- End Heading -->

      <div class="row">

        <div class="col-4 col-md-3 text-center py-3 m-auto">
          <img class="img-fluid" src="./assets/img/others/didongviet-logo-150.png" style="max-height: 100px;"
            alt="Di Động Việt Logo">
        </div>
        <!-- End Col -->
        <div class="col-4 col-md-3 text-center py-3 m-auto">
          <img class="img-fluid" src="./assets/img/others/logo-Hai-Trieu-Ngang.png" style="max-height: 100px;"
            alt="Dong Ho Hai Trieu Logo">
        </div>
        <!-- End Col -->
        <div class="col-4 col-md-3 text-center py-3 m-auto">
          <img class="img-fluid" src="./assets/img/others/clickbuy-logo.png" style="max-height: 100px;"
            alt="ClickBuy Logo">
        </div>
        <!-- End Col -->
        <div class="col-4 col-md-3 text-center py-3 m-auto">
          <img class="img-fluid" src="./assets/img/others/fuji-fruit-logo.jpg" style="max-height: 100px;"
            alt="Fuji Fruit Logo">
        </div>
        <!-- End Col -->

        <div class="col-4 col-md-3 text-center py-3 m-auto">
          <img class="img-fluid" src="./assets/img/others/levents-logo.jpg" style="max-height: 100px;"
            alt="Levents Logo">
        </div>
        <!-- End Col -->

        <div class="col-4 col-md-3 text-center py-3 m-auto">
          <img class="img-fluid" src="./assets/img/others/tiem-banh-coi-xay-gio-logo.png" style="max-height: 100px;"
            alt="Tiem Banh Coi Xay Gio Logo">
        </div>
        <!-- End Col -->
        <div class="col-4 col-md-3 text-center py-3 m-auto">
          <img class="img-fluid" src="./assets/img/others/giaybq-logo.png" style="max-height: 100px;"
            alt="Giay BQ Logo">
        </div>
        <!-- End Col -->

        <div class="col-4 col-md-3 text-center py-3 m-auto">
          <img class="img-fluid" src="./assets/img/others/jemmia-logo.jpg" style="max-height: 100px;" alt="Jemmia Logo">
        </div>
        <!-- End Col -->

        <div class="col-4 col-md-3 text-center py-3 m-auto">
          <img class="img-fluid" src="./assets/img/others/anhlee-spa-logo.jpg" style="max-height: 100px;"
            alt="AnhLee Spa Logo">
        </div>
        <!-- End Col -->


        <div class="col-4 col-md-3 text-center py-3 m-auto">
          <img class="img-fluid" src="./assets/img/others/123host-logo-120x58.png" style="max-height: 100px;"
            alt="123HOST Logo">
        </div>
        <!-- End Col -->



        <div class="col-4 col-md-3 text-center py-3 m-auto">
          <img class="img-fluid" src="./assets/img/others/logo-guland.png" style="max-height: 100px;" alt="Guland Logo">
        </div>
        <!-- End Col -->


        <div class="col-4 col-md-3 text-center py-3 m-auto">
          <img class="img-fluid" src="./assets/img/others/thai-yen-cafe-logo.jpeg" alt="Thai Yen Cafe Logo">
        </div>
        <!-- End Col -->

        <div class="col-4 col-md-3 text-center py-3 m-auto">
          <img class="img-fluid" src="./assets/img/others/vaithuhay-logo2.png" style="max-height: 100px;"
            alt="Vaithuhay Logo">
        </div>
        <!-- End Col -->



        <div class="col-4 col-md-3 text-center py-3 m-auto">
          <img class="img-fluid" src="./assets/img/others/oro-logo.jpeg" style="max-height: 100px;"
            alt="Anh Ngu Oro Logo">
        </div>
        <!-- End Col -->

        <div class="col-4 col-md-3 text-center py-3 m-auto">
          <img class="img-fluid" src="./assets/img/others/friendshipcakes-logo.png" style="max-height: 100px;"
            alt="Friendshipcakes Logo">
        </div>
        <!-- End Col -->


        <div class="col-4 col-md-3 text-center py-3 m-auto">
          <img class="img-fluid" src="./assets/img/others/logo-healingresort.png" style="max-height: 100px;"
            alt="Healing Resort Logo">
        </div>
        <!-- End Col -->




      </div>

      <div class="row mt-5">
        <div class="col-sm-6 col-lg-4">
          <!-- Card -->
          <div class="card shadow-none">
            <div class="card-body">
              <!-- Rating -->
              <div class="d-flex gap-1 mb-2">
                <img src="./assets/svg/illustrations/star.svg" alt="Review rating" width="16">
                <img src="./assets/svg/illustrations/star.svg" alt="Review rating" width="16">
                <img src="./assets/svg/illustrations/star.svg" alt="Review rating" width="16">
                <img src="./assets/svg/illustrations/star.svg" alt="Review rating" width="16">
                <img src="./assets/svg/illustrations/star.svg" alt="Review rating" width="16">
              </div>
              <!-- End Rating -->

              <div class="mb-auto">
                <p class="card-text">Với SePay và QR Code, thanh toán chuyển khoản bây giờ còn nhanh và tiện lợi hơn qua
                  cổng thanh toán.</p>
              </div>
            </div>

            <div class="card-footer pt-0">
              <!-- Media -->
              <div class="d-flex align-items-center">
                <div class="flex-shrink-0">
                  <img class="avatar avatar-circle" src="./assets/img/others/mr-nicky.jpeg" alt="Mr Nicky">
                </div>
                <div class="flex-grow-1 ms-3">
                  <h5 class="card-title mb-0">Mr. Nicky</h5>
                  <p class="card-text small">Ashtray Cocktail Factory</p>
                </div>
              </div>
              <!-- End Media -->
            </div>
          </div>
          <!-- End Card -->
        </div>
        <!-- End Col -->

        <div class="col-sm-6 col-lg-4">
          <!-- Card -->
          <div class="card bg-primary">
            <div class="card-body">
              <!-- Rating -->
              <div class="d-flex gap-1 mb-2">
                <img src="./assets/svg/illustrations/star.svg" alt="Review rating" width="16">
                <img src="./assets/svg/illustrations/star.svg" alt="Review rating" width="16">
                <img src="./assets/svg/illustrations/star.svg" alt="Review rating" width="16">
                <img src="./assets/svg/illustrations/star.svg" alt="Review rating" width="16">
                <img src="./assets/svg/illustrations/star-half.svg" alt="Review rating" width="16">
              </div>
              <!-- End Rating -->

              <div class="mb-auto">
                <p class="card-text text-white">SePay giúp chúng tôi tiết kiệm hàng trăm triệu phí cổng thanh toán mỗi
                  tháng.</p>
              </div>
            </div>

            <div class="card-footer pt-0">
              <!-- Media -->
              <div class="d-flex align-items-center">
                <div class="flex-shrink-0">
                  <img class="avatar avatar-circle" src="./assets/img/others/mr-dat-nguyen.jpeg" alt="Mr Đạt Nguyễn">
                </div>
                <div class="flex-grow-1 ms-3">
                  <h5 class="card-title text-white mb-0">Mr. Đạt Nguyễn</h5>
                  <p class="card-text small text-white-70">CEO Di Động Việt</p>
                </div>
              </div>
              <!-- End Media -->
            </div>
          </div>
          <!-- End Card -->
        </div>
        <!-- End Col -->

        <div class="col-sm-6 col-lg-4">
          <!-- Card -->
          <div class="card shadow-none">
            <div class="card-body">
              <!-- Rating -->
              <div class="d-flex gap-1 mb-2">
                <img src="./assets/svg/illustrations/star.svg" alt="Review rating" width="16">
                <img src="./assets/svg/illustrations/star.svg" alt="Review rating" width="16">
                <img src="./assets/svg/illustrations/star.svg" alt="Review rating" width="16">
                <img src="./assets/svg/illustrations/star.svg" alt="Review rating" width="16">
                <img src="./assets/svg/illustrations/star.svg" alt="Review rating" width="16">
              </div>
              <!-- End Rating -->

              <div class="mb-auto">
                <p class="card-text">Kế toán của tôi không còn phải trực để kiểm tra giao dịch nữa. Các bạn nhân viên
                  bán hàng đã chủ động xem được biến động số dư qua Telegram.</p>
              </div>
            </div>

            <div class="card-footer pt-0">
              <!-- Media -->
              <div class="d-flex align-items-center">
                <div class="flex-shrink-0">
                  <img class="avatar avatar-circle" src="./assets/img/others/mr-hien-phan.jpeg" alt="Bùi Tấn Việt">
                </div>
                <div class="flex-grow-1 ms-3">
                  <h5 class="card-title mb-0">Mr. Hiên Phan</h5>
                  <p class="card-text small">CTO 123HOST</p>
                </div>
              </div>
              <!-- End Media -->
            </div>
          </div>
          <!-- End Card -->
        </div>
        <!-- End Col -->
      </div>
      <!-- End Row -->
    </div>
    <!-- End Clients -->

    <!-- Pricing Table -->
    <div>
      <div class="text-center">
        <h2 class="mt-2 mb-5">SePay sẽ giúp bạn</h2>

        <div class="">
          <div class="row mx-auto" style="max-width:1200px">
            <div class="col-md-4">
              <div class="card">
                <img src="https://my.sepay.vn/assets/images/plans/chia-se-bien-dong-so-du.jpg" class="card-img-top"
                  alt="...">
                <div class="card-body">
                  <h5 class="card-title">Chia sẻ biến động số dư</h5>
                  <p class="card-text">Chia sẻ thông tin giao dịch lên nhóm chat, nhân viên bán hàng nắm bắt thanh toán
                    kịp thời.</p>
                  <a class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#modalBDSD">Xem
                    thêm</a>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="card">
                <img src="https://my.sepay.vn/assets/images/plans/cong-thanh-toan-qr.jpg" class="card-img-top">
                <div class="card-body">
                  <h5 class="card-title">Tích hợp cổng thanh toán trực tuyến</h5>
                  <p class="card-text">Tích hợp cổng thanh toán vào website bán hàng của bạn trong 15 phút.</p>
                  <a data-bs-toggle="modal" data-bs-target="#modalQRCODE" class="btn btn-outline-primary btn-sm">Xem
                    thêm</a>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="card">
                <img src="https://my.sepay.vn/assets/images/plans/thong-ke-dong-tien.jpg" class="card-img-top"
                  alt="...">
                <div class="card-body">
                  <h5 class="card-title">Thống kê dòng tiền</h5>
                  <p class="card-text">Xem danh sách giao dịch. Báo cáo dòng tiền vào ra. Tất cả ngân hàng trong 1 giao
                    diện.</p>
                  <a data-bs-toggle="modal" data-bs-target="#modalTKDT" class="btn btn-outline-primary btn-sm">Xem
                    thêm</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="text-center container content-space-2 content-space-lg-2">
        <h2 class="my-5">SePay phù hợp với</h2>

        <div class="">
          <div class="row mx-auto" style="max-width:1200px">
            <div class="col-md-4">
              <div class="card">
                <img src="https://my.sepay.vn/assets/images/plans/chuoi-cua-hang.jpg" class="card-img-top" alt="...">
                <div class="card-body">
                  <h5 class="card-title">Chuỗi cửa hàng</h5>
                  <p class="card-text">Gửi thông báo thanh toán tức thì đến nhóm chat bán hàng. Phân biệt doanh thu từng
                    chi nhánh. Ẩn thông tin số dư tài khoản.</p>
                  <a data-bs-toggle="modal" data-bs-target="#modalCCH" class="btn btn-outline-primary btn-sm">Xem
                    thêm</a>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="card">
                <img src="https://my.sepay.vn/assets/images/plans/web-app-tich-hop.jpg" class="card-img-top" alt="...">
                <div class="card-body">
                  <h5 class="card-title">Website &amp; app bán hàng</h5>
                  <p class="card-text">Tích hợp SePay làm cổng thanh toán chuyển khoản, tự động hoá nạp tiền, tăng trải
                    nghiệm khách hàng.</p>
                  <a data-bs-toggle="modal" data-bs-target="#modalWEBAPPBH" class="btn btn-outline-primary btn-sm">Xem
                    thêm</a>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="card">
                <img src="https://my.sepay.vn/assets/images/plans/api-ngan-hang.jpg" class="card-img-top" alt="...">
                <div class="card-body">
                  <h5 class="card-title">Cần API Ngân hàng</h5>
                  <p class="card-text">Đồng bộ giao dịch ngân hàng đến ứng dụng của bạn thông qua API và Webhook của
                    SePay. Đầy đủ tài liệu và sandbox.</p>
                  <a data-bs-toggle="modal" data-bs-target="#modalAPINH" class="btn btn-outline-primary btn-sm">Xem
                    thêm</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>

    <!-- End Pricing Table -->

    <div class="text-center mt-5">
      <div class="card card-info-link card-sm">
        <div class="card-body">
          Bạn cần tư vấn từ chuyên gia? <a class="card-link ms-2" href="https://sepay.vn/lien-he.html"> Liên hệ chúng
            tôi<span class="bi-chevron-right small ms-1"></span></a>
        </div>
      </div>
    </div>

    <!-- FAQ -->
    <div class="container content-space-2 content-space-lg-3">
      <!-- Heading -->
      <div class="w-md-75 w-lg-50 text-center mx-lg-auto mb-5 mb-md-9">
        <h2>Các câu hỏi thường gặp</h2>
      </div>
      <!-- End Heading -->

      <div class="row justify-content-md-center">
        <div class="col-sm-6 col-lg-5 mb-3 mb-md-5">
          <h4>Số lượng giao dịch là gì?</h4>
          <p>Số lượng giao dịch được tính = giao dịch tiền vào + giao dịch tiền ra.</p>
        </div>
        <!-- End Col -->

        <div class="col-sm-6 col-lg-5 mb-3 mb-md-5">
          <h4>API Banking là gì?</h4>
          <p>API Banking là công nghệ giúp kết nối và đồng bộ các dữ liệu tài khoản ngân hàng như lịch sử giao dịch,
            biến động số dư cho bên thứ 3. SePay đã ký kết và hợp tác trực tiếp với một số ngân hàng như OCB,
            KienLongBank,
            MSB để kết nối qua API. Giao dịch được đồng bộ tức thì.</p>
        </div>
        <!-- End Col -->

        <div class="col-sm-6 col-lg-5 mb-3 mb-md-5">
          <h4>Tôi được thêm bao nhiêu tài khoản ngân hàng?</h4>

          <p>SePay không giới hạn số lượng tài khoản ngân hàng.</p>
        </div>
        <!-- End Col -->

        <div class="col-sm-6 col-lg-5 mb-3 mb-md-5">
          <h4>Tôi muốn dùng hơn giới hạn của gói đang sử dụng thì sao?</h4>
          <p>Bạn có thể nâng cấp gói cao hơn hoặc trả tiền thêm theo số lượng giao dịch vượt mức so với gói đang sử
            dụng.</p>
        </div>
        <!-- End Col -->

        <div class="col-sm-6 col-lg-5 mb-3 mb-md-5">
          <h4>Tính năng có giới hạn theo gói không?</h4>
          <p>Hiện tại, tất cả các gói đều dùng được tất cả tính năng của SePay.</p>
        </div>
        <!-- End Col -->

        <div class="col-sm-6 col-lg-5 mb-3 mb-md-5">
          <h4>SePay có hỗ trợ hoàn tiền không?</h4>
          <p>Hiện tại SePay hỗ trợ hoàn tiền trong 15 ngày đầu tiên nếu khách hàng không hài lòng về dịch vụ. Xem thêm
            <a href="hoan-tien.html">Quy định hoàn tiền</a></p>
        </div>
        <!-- End Col -->

        <div class="col-sm-6 col-lg-5 mb-3 mb-md-5">
          <h4>SePay đã kết nối API được với những ngân hàng nào?</h4>
          <p>SePay đã kết nối API trực tiếp đến ngân hàng MB, OCB, KienLongBank và MSB. Các ngân hàng còn lại kết nối
            qua SMS
            Banking. <a href="ngan-hang.html">Xem các ngân hàng kết nối</a></p>
        </div>
        <!-- End Col -->

        <div class="col-sm-6 col-lg-5 mb-3 mb-md-5">
          <h4>SePay hỗ trợ kết nối tài khoản ngân hàng cá nhân hay doanh nghiệp?</h4>
          <p>SePay kết nối cả tài khoản ngân hàng cá nhân và doanh nghiệp.</p>
        </div>
        <!-- End Col -->
        <div class="col-sm-6 col-lg-5 mb-3 mb-md-5">
          <h4>Tôi không muốn SePay lưu giao dịch của mình thì phải làm sao?</h4>
          <p>Bạn có thể cấu hình thời gian lưu giao dịch tại phần <a
              href="https://docs.sepay.vn/cau-hinh-chung.html#storage-time" target="_blank">Cấu hình chung</a>.</p>
        </div>
        <!-- End Col -->


      </div>
      <!-- End Row -->
    </div>
    <!-- End FAQ -->

    <div class="text-center">
      <div class="card card-info-link card-sm">
        <div class="card-body fs-5">
          Bạn muốn gói cao hơn? hoặc cần tư vấn triển khai? Liên hệ SePay ngay nhé! <a class="card-link ms-2"
            href="https://sepay.vn/lien-he.html">Liên hệ <span class="bi-chevron-right small ms-1"></span></a>
        </div>
      </div>
    </div>
    </div>

  </main>
  <!-- ========== END MAIN CONTENT ========== -->


   <!-- ========== FOOTER ========== -->
   <footer class="container content-space-2 content-space-t-lg-3">
    <!-- Links -->
    <div class="row mb-5">
      <div class="col-md-3 mb-5 mb-md-0">
        <!-- Logo -->
        <div class="mb-3">
          <a class="navbar-brand" href="./index.html" aria-label="Space">
            <img class="navbar-brand-logo" src="./assets/img/logo/sepay-blue-154x50.png" alt="Image Description">
          </a>
        </div>
        <!-- End Logo -->

        <!-- List -->
        <ul class="list-unstyled list-py-1">
          <li><b>Công Ty Cổ Phần SePay</b></li>
          <li><a class="link-sm link-secondary" href="https://goo.gl/maps/HGZVhsbv3NhR7kan8" target="_blank"><i
                class="bi-geo-alt-fill me-1"></i> 168 Đường số 2, Vạn Phúc City, P. Hiệp Bình Phước, Thủ Đức, TP. Hồ Chí
              Minh, Việt Nam</a></li>
          <li><a class="link-sm link-secondary" href="tel:***********"><i class="bi-telephone-inbound-fill me-1"></i>
              02873.059.589</a></li>
          <li><span class="text-secondary fs-6"><i class="bi bi-envelope-fill me-2"></i> <EMAIL></span></li>
          <li><span class="text-secondary fs-6">MST: 0317887567, cấp ngày 19/06/2023 tại Sở Kế hoạch và Đầu tư
              TPHCM</span></li>

        </ul>
        <!-- End List -->
        <div class="col-sm-auto">
          <!-- Socials -->
          <ul class="list-inline mb-0">
            <li class="list-inline-item">
              <a class="btn btn-soft-secondary btn-xs btn-icon" href="https://www.facebook.com/sepay.vn">
                <i class="bi-facebook"></i>
              </a>
            </li>
            <li class="list-inline-item">
              <a class="btn btn-soft-secondary btn-xs btn-icon" href="https://www.youtube.com/@SePayVN">
                <i class="bi-youtube"></i>
              </a>
            </li>
            <li class="list-inline-item">
              <a class="btn btn-soft-secondary btn-xs btn-icon" href="https://www.pinterest.com/sepayvn/">
                <i class="bi-pinterest"></i>
              </a>
            </li>




          </ul>
          <!-- End Socials -->
        </div>
      </div>

      <div class="col-sm-2 col-md-2 col-6 mb-3 mb-sm-0">
        <h5 class=" mb-3">THÔNG TIN</h5>

        <!-- List -->
        <ul class="list-unstyled list-py-1">
          <li><a class="text-body" href="gioi-thieu.html">Giới thiệu</a></li>
          <li><a class="text-body" href="bang-gia.html">Bảng giá</a></li>

          <li><a class="text-body" href="https://sepay.vn/blog">Blog</a></li>
          <li><a class="text-body" href="lien-he.html">Liên hệ</a></li>
          <li><a class="text-body" target="_blank" href="https://docs.sepay.vn/tich-hop-webhooks.html">WebHooks</a></li>

          <li><a class="text-body" target="_blank" href="https://docs.sepay.vn/gioi-thieu-api.html">API</a></li>

          <li><a class="text-body" href="faq.html">FAQ</a></li>
          <li><a class="text-body" href="changelog.html">Changelog</a></li>

        </ul>
        <div><img loading="lazy" src="/assets/img/others/RapidSSL-SEAL.gif" class="img-fluid"> <a
            href="https://secure.sitelock.com/public/verify/sepay.vn" target="_blank" rel=nofollow><img
              src="https://shield.sitelock.com/shield/sepay.vn" loading="lazy" class="img-fluid"
              style="max-width: 90px;"></a> <a href="http://online.gov.vn/Home/WebDetails/107586" target="_blank"
            rel=nofollow><img src="https://sepay.vn/assets/img/others/ttbct.png" loading="lazy" class="img-fluid"
              style="max-width: 150px;"></a></div>
        <!-- End List -->
      </div>

      <div class="col-sm-3 col-md-3 col-6 mb-3 mb-sm-0">
        <h5 class=" mb-3">HƯỚNG DẪN & TÍNH NĂNG</h5>

        <!-- List -->
        <ul class="list-unstyled list-py-1">
          <li>
            <a class="text-body" href="https://docs.sepay.vn/">
              Hướng dẫn sử dụng
            </a>
          </li>
          <li><a class="text-body" href="thanh-toan.html">Hướng dẫn thanh toán</a></li>
          <li><a class="text-body" href="terms-of-service.html">Quy định sử dụng</a></li>
          <li><a class="text-body" href="hoan-tien.html">Quy định hoàn tiền</a></li>
          <li><a class="text-body" href="privacy.html">Chính sách bảo mật</a></li>
          <li>
            <a class="text-body" href="ngan-hang.html">
              Ngân hàng kết nối
            </a>
          </li>


          <li>
            <a class="text-body" href="affiliate.html">
              Tiếp thị liên kết
            </a>
          </li>
          <li>
            <a class="text-body" href="chia-se-bien-dong-so-du.html">
              Chia sẻ biến động số dư
            </a>
          </li>
          <li>
            <a class="text-body" href="cong-thanh-toan-truc-tuyen.html">
              Cổng thanh toán trực tuyến
            </a>
          </li>
          <li>
            <a class="text-body" href="thong-ke-dong-tien.html">
              Thống kê dòng tiền
            </a>
          </li>
        </ul>
        <!-- End List -->
      </div>

      <div class="col-sm-4 row">
        <div class="col-6 mb-3 mb-sm-0 mx-auto">
          <h5 class=" mb-3">TÍCH HỢP</h5>
          <!-- List -->
          <ul class="list-unstyled list-py-1">
  
            <li>
              <a class="text-body" href="https://docs.sepay.vn/tich-hop-haravan.html">
                <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/haravan-icon.png"
                  style="width:22px; height: 22px;"> Haravan
              </a>
            </li>
  
            <li>
              <a class="text-body" href="https://docs.sepay.vn/tich-hop-sapo.html">
                <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/sapo-icon.png"
                  style="width:22px; height: 22px;"> Sapo
              </a>
            </li>

            <li>
              <a class="text-body" href="https://docs.sepay.vn/tich-hop-shopify.html">
                <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/shopify-icon.png"
                  style="width:22px; height: 22px;"> Shopify
              </a>
            </li>
            
            <li>
              <a class="text-body" href="https://docs.sepay.vn/woocommerce.html">
                <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/woocommerce-icon.png"
                  style="width:22px; height: 22px;"> WooCommerce
              </a>
            </li>
            <li>
              <a class="text-body" href="https://docs.sepay.vn/tich-hop-google-sheets.html">
                <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/google-sheets-icon.png"
                  style="width:22px; height: 22px;"> Google Sheets
              </a>
            </li>
            <li>
              <a class="text-body" href="https://docs.sepay.vn/tich-hop-lark-messenger.html">
                <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/lark/lark-icon.png"
                  style="width:22px; height: 22px;"> Lark
              </a>
            </li>
            <li>
              <a class="text-body" href="https://docs.sepay.vn/tich-hop-telegram.html">
                <i class="bi bi-telegram me-1 text-info fs-3"></i> Telegram
              </a>
            </li>
  
  
  
  
            
        </div>
        <div class="col-6 mb-3 mb-sm-0 mx-auto">
          <h5 class=" mb-3">KẾT NỐI VỚI SEPAY</h5>
           <!-- List -->
           <ul class="list-unstyled list-py-1">
  
            <li>
              <a class="text-body" href="https://www.facebook.com/sepay.vn">
                <i class="bi bi-facebook me-1"></i> Facebook
              </a>
            </li>
            <li>
              <a class="text-body" href="https://t.me/s/sepaychannel">
                <i class="bi bi-telegram me-1"></i> Telegram
              </a>
            </li>
            <li>
              <a class="text-body" href="https://www.youtube.com/@SePayVN">
                <i class="bi bi-youtube me-1"></i> Youtube
              </a>
            </li>
  
            <li>
              <a class="text-body" href="https://www.pinterest.com/sepayvn/">
                <i class="bi bi-pinterest me-1"></i> Pinterest
              </a>
            </li>
            <li>
              <a class="text-body" href="https://github.com/sepayvn">
                <i class="bi bi-github me-1"></i> GitHub
              </a>
            </li>
  
  
  
          </ul>
          <!-- End List -->
        </div>
        <div class="col-12">
          <span class="text-cap mt-1">Hợp tác chiến lược:</span>
          <div class="row">
            <div class="col-3 py-3">
              <a href="https://sepay.vn/mb.html"><img class="avatar avatar-lg avatar-4x3" src="https://qr.sepay.vn/assets/img/banklogo/MB.png" alt="MBBank"></a>
            </div>
            <!-- End Col -->
  
            <div class="col-3 py-3">
              <a href="https://sepay.vn/ocb.html"><img class="avatar avatar-lg avatar-4x3" src="https://qr.sepay.vn/assets/img/banklogo/OCB.png" alt="OCB"></a>
            </div>
            <!-- End Col -->
            <div class="col-3 py-3">
              <a href="https://sepay.vn/kien-long-bank.html"><img class="avatar avatar-lg avatar-4x3" src="https://sepay.vn/blog/wp-content/uploads/2024/05/kienlongbank-logo.png" alt="KienlongBank"></a>
            </div>
            <!-- End Col -->
  
            <div class="col-3 py-3">
              <a href="https://sepay.vn/msb.html"><img class="avatar avatar-lg avatar-4x3" src="https://qr.sepay.vn/assets/img/banklogo/MSB.png" alt="MSB"></a>
            </div>
            <!-- End Col -->
          </div>
        </div>
      </div>
    </div>
    <!-- End Links -->
    <div class="border-top border-10 my-5"></div>
    <div class="row align-items-sm-center">
      <div class="col-12 mb-4 mb-sm-0">
        <p class="small mb-0 text-center">&copy; SePay 2023. All rights reserved.</p>
      </div>
      <!-- End Col -->


      <!-- End Col -->
    </div>
    <!-- End Row -->
  </footer>
  <!-- ========== END FOOTER ========== -->

  <!--contact-box-html-->
  <div id="contact-box-overlay"></div>
  <div id="contact-box">
    <div class="popup">
      <a href="https://m.me/***************" target="_blank" class="item">
        <div class="logo">
          <img src="assets/img/others/fb-messenger.png" width="50%" />
        </div>
        <div class="meta">
          <p class="title">Facebook Messenger</p>
          <small class="description">Hỗ trợ live chat 24/7</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="tel:***********" class="item">
        <div class="icon" style="background-color: #22c55e; color: #fff;">
          <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20"
            style="color: currentcolor;">
            <path
              d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z"
              stroke="currentColor" stroke-width="1.5" stroke-linecap="round" fill="transparent"></path>
          </svg>
        </div>
        <div class="meta">
          <p class="title">Hotline</p>
          <small class="description">Điện thoại hỗ trợ</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
        <div class="logo">
          <img src="assets/img/others/youtube-social.png" />
        </div>
        <div class="meta">
          <p class="title">Youtube</p>
          <small class="description">Theo dõi video mới nhất của SePay</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
        <div class="logo">
          <img src="assets/img/others/telegram-social.png" />
        </div>
        <div class="meta">
          <p class="title">Telegram</p>
          <small class="description">Nhận thông tin mới nhất từ SePay</small>
        </div>
      </a>
    </div>
    <div class="container">
      <div class="dot-ping">
        <div class="ping"></div>
        <div class="dot"></div>
      </div>
      <div class="contact-icon">
        <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20"
          style="color: currentcolor;">
          <path fill-rule="evenodd" clip-rule="evenodd"
            d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z"
            fill="currentColor"></path>
        </svg>
      </div>
      <span style="font-weight: bold;">Liên hệ chúng tôi</span>
    </div>
  </div>
  <!--/contact-box-html-->


  <style>
    .dialog__header {
      padding: 1rem clamp(2rem, 3vw, 6rem) 0;
      border-radius: 16px 16px 0 0;
      min-height: 80px;
      text-align: left;
      background-color: #DFECFC;
    }

    .dialog__header__title {
      margin-bottom: 1rem;
      font-size: 1rem !important;
      line-height: 4rem;
    }
  </style>

  <!-- modal BDSD-->
  <div class="modal fade" id="modalBDSD" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header dialog__header">
          <h1 class="modal-title fs-5 dialog__header__title" id="exampleModalLabel">Chia sẻ biến động số dư ngân hàng
          </h1>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row mt-3">
            <div class="col-md-9">
              <h4>Nhận biến động số dư tức thì</h4>
              <p class="font-16">SePay bắn thông tin giao dịch lên nhóm chat, điện thoại của nhân viên bán hàng một cách
                tức thì khi có giao dịch.</p>
            </div>
            <div class="col-md-3 text-center"><img
                src="https://my.sepay.vn/assets/images/plans/chat-iphone-communication.svg" class="img-fluid"
                style="max-height:120px" loading="lazy"></div>

          </div>

          <div class="row my-4">

            <div class="col-md-3 text-center"><img src="https://my.sepay.vn/assets/images/plans/macbook-notes.svg"
                class="img-fluid d-none d-sm-block" style="max-height:120px" loading="lazy"></div>
            <div class="col-md-9">
              <h4>Đa dạng nền tảng thông báo</h4>
              <p class="font-16">Báo giao dịch lên nhóm chat Telegram, Lark, Google Sheets, Mobile App hoặc website của
                SePay. Hỗ trợ nhiều tuỳ chọn như ẩn thông tin số dư tài khoản, chỉ báo khi có tiền vào, phân quyền người
                dùng đầy đủ.</p>
            </div>
          </div>

          <div class="row mt-3">
            <div class="col-md-9">
              <h4>Thông báo đúng người, đúng chi nhánh</h4>
              <p class="font-16">Giao dịch tại điểm bán nào sẽ được thông báo lên nhóm chat của chính điểm bán đó. Áp
                dụng công nghệ tài khoản ảo VA để tách tài khoản nhận tiền từng chi nhánh. Hỗ trợ bắn thông tin giao
                dịch khi khớp nội dung chuyển khoản và nhiều tuỳ chọn khác.</p>
            </div>
            <div class="col-md-3 text-center"><img src="https://my.sepay.vn/assets/images/plans/cloud-manager.svg"
                class="img-fluid" style="max-height:120px" loading="lazy"></div>

          </div>

          <div class="text-center mt-3">
            <p class="font-16 fw-bold">Giảm nhân lực kế toán, tăng trải nghiệm khách hàng khi thanh toán chuyển
              khoản.</p>

          </div>
        </div>

      </div>
    </div>
  </div>
  <!-- modal BDSD-->


  <!-- modal TT QRCode -->
  <div class="modal fade" id="modalQRCODE" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header dialog__header">
          <h1 class="modal-title fs-5 dialog__header__title" id="exampleModalLabel">Tích hợp cổng thanh toán trực tuyến
          </h1>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row mt-3">
            <div class="col-md-9">
              <h4>Cổng thanh toán không tốn phí</h4>
              <p class="font-16">Tích hợp SePay như một cổng thanh toán tại website hoặc ứng dụng của bạn. Không phát
                sinh phí theo giao dịch, tiết kiệm rất nhiều tiền so với việc dùng cổng thanh toán khác.</p>
            </div>
            <div class="col-md-3 text-center"><img
                src="https://my.sepay.vn/assets/images/plans/ecommerce-online-shopping-app.svg" class="img-fluid"
                style="max-height:120px" loading="lazy"></div>

          </div>

          <div class="row my-4">

            <div class="col-md-3 text-center"><img
                src="https://my.sepay.vn/assets/images/plans/shopping-app-ecommerce.svg"
                class="img-fluid d-none d-sm-block" style="max-height:120px" loading="lazy"></div>
            <div class="col-md-9">
              <h4>Xác nhận thanh toán tự động</h4>
              <p class="font-16">Chỉ sau vài giây thanh toán, website sẽ hiện thông báo "Bạn đã thanh toán thành công".
                Khách hàng yên tâm biết rằng đơn hàng đã hoàn tất thanh toán.</p>
            </div>
          </div>

          <div class="row mt-3">
            <div class="col-md-9">
              <h4>Tự động hoá quy trình đơn hàng</h4>
              <p class="font-16">Đơn hàng sẽ tự động chuyển sang trạng thái đã thanh toán. Số tiền thanh toán/ nạp của
                khách hàng sẽ được ghi nhận. Nhân viên xử lý đơn hàng một cách dễ dàng.</p>
            </div>
            <div class="col-md-3 text-center"><img src="https://my.sepay.vn/assets/images/plans/wish-list-checklist.svg"
                class="img-fluid" alt="WebP Image Convert" style="max-height:120px" loading="lazy"></div>

          </div>

          <div class="text-center mt-3">
            <p class="font-16 fw-bold">Tính năng tương tự cổng thanh toán, nhưng không mất phí!</p>

          </div>
        </div>

      </div>
    </div>
  </div>
  <!-- modal TT QR code-->


  <!-- modal TK Dong Tien -->
  <div class="modal fade" id="modalTKDT" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header dialog__header">
          <h1 class="modal-title fs-5 dialog__header__title" id="exampleModalLabel">Thống kê dòng tiền</h1>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row mt-3">
            <div class="col-md-9">
              <h4>Xem giao dịch nhiều ngân hàng tại một giao diện</h4>
              <p class="font-16">Nếu bạn có nhiều tài khoản ngân hàng, việc theo dõi giao dịch ngân hàng dễ dàng hơn bao
                giờ hết với SePay. Tất cả giao dịch được thể hiện tại một giao diện.</p>
            </div>
            <div class="col-md-3 text-center"><img src="https://my.sepay.vn/assets/images/plans/browser-seo-and-web.svg"
                class="img-fluid" style="max-height:120px" loading="lazy"></div>

          </div>

          <div class="row my-4">

            <div class="col-md-3 text-center"><img src="https://my.sepay.vn/assets/images/plans/browser-web-page.svg"
                class="img-fluid d-none d-sm-block" style="max-height:120px" loading="lazy"></div>
            <div class="col-md-9">
              <h4>Quản lý số dư hiệu quả</h4>
              <p class="font-16">Xem nhanh danh sách ngân hàng, số dư chi tiết, tổng tiền đang có. Bạn không cần phải
                hỏi kế toán mình đang có bao nhiêu tiền. Chỉ cần vào SePay và 1 click chuột.</p>
            </div>
          </div>

          <div class="row mt-3">
            <div class="col-md-9">
              <h4>Báo cáo đầy đủ</h4>
              <p class="font-16">Báo cáo tiền vào tiền ra theo ngày, theo tháng với biểu đồ trực quan. Hỗ trợ thống kê
                doanh thu điểm bán.</p>
            </div>
            <div class="col-md-3 text-center"><img src="https://my.sepay.vn/assets/images/plans/browser.svg"
                class="img-fluid" style="max-height:120px" loading="lazy"></div>

          </div>

          <div class="text-center mt-3">
            <p class="font-16 fw-bold">Quản lý dòng tiền dễ dàng với SePay</p>

          </div>
        </div>

      </div>
    </div>
  </div>
  <!-- modal TT QR code-->


  <!-- modal Chuoi Cua Hang-->
  <div class="modal fade" id="modalCCH" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header dialog__header">
          <h1 class="modal-title fs-5 dialog__header__title" id="exampleModalLabel">SePay cho chuỗi cửa hàng</h1>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row mt-3">
            <div class="col-md-9">
              <h4>Quét QR Code và chuyển khoản</h4>
              <p class="font-16">Khách hàng dùng ví điện tử hoặc mọi ngân hàng để chuyển khoản thanh toán. SePay gửi
                thông báo có giao dịch tức thì đến nhóm chat, app mobile của nhân viên bán hàng.</p>
            </div>
            <div class="col-md-3 text-center"><img src="https://my.sepay.vn/assets/images/plans/pay-success-bill.png"
                class="img-fluid" style="max-height:120px" loading="lazy"></div>

          </div>

          <div class="row my-4">

            <div class="col-md-3 text-center"><img src="https://my.sepay.vn/assets/images/plans/stores.png"
                class="img-fluid d-none d-sm-block" style="max-height:120px" loading="lazy"></div>
            <div class="col-md-9">
              <h4>Thông báo đúng người, đúng chi nhánh</h4>
              <p class="font-16">Giao dịch tại điểm bán nào sẽ được thông báo lên nhóm chat của chính điểm bán đó. Áp
                dụng công nghệ tài khoản ảo VA để tách tài khoản nhận tiền từng chi nhánh. Hỗ trợ bắn thông tin giao
                dịch khi khớp nội dung chuyển khoản và nhiều tuỳ chọn khác.</p>
            </div>
          </div>

          <div class="row mt-3">
            <div class="col-md-9">
              <h4>Nhận tiền ngay, đối soát dễ</h4>
              <p class="font-16">Vì là hình thức nhận chuyển khoản ngân hàng, chủ cửa hàng sẽ nhận tiền ngay, không tốn
                phí, đối soát dễ dàng.</p>
            </div>
            <div class="col-md-3 text-center"><img src="https://my.sepay.vn/assets/images/plans/pay-success.png"
                class="img-fluid" alt="WebP Image Convert" style="max-height:120px" loading="lazy"></div>

          </div>

          <div class="text-center mt-3">
            <p class="font-16 fw-bold">SePay được thiết kế để phù hợp với chuỗi cửa hàng bán lẻ!</p>

          </div>
        </div>

      </div>
    </div>
  </div>
  <!-- modal Chuoi Cua Hang-->


  <!-- modal Website/ App Ban Hang-->
  <div class="modal fade" id="modalWEBAPPBH" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header dialog__header">
          <h1 class="modal-title fs-5 dialog__header__title" id="exampleModalLabel">SePay cho Website & App bán hàng
          </h1>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row mt-3">
            <div class="col-md-9">
              <h4>Thanh toán & xác nhận tự thanh toán tức thì</h4>
              <p class="font-16">Tích hợp SePay, website của bạn sẽ có thêm cổng thanh toán miễn phí. Nhận thanh toán và
                xác nhận thanh toán tức thì. Phù hợp website thương mại điện tử, app bán hàng, app chung cư, POS.</p>
            </div>
            <div class="col-md-3 text-center"><img src="https://my.sepay.vn/assets/images/plans/pay-success-bill.png"
                class="img-fluid" style="max-height:120px" loading="lazy"></div>

          </div>

          <div class="row my-4">

            <div class="col-md-3 text-center"><img src="https://my.sepay.vn/assets/images/plans/ecommerce-platform.png"
                class="img-fluid d-none d-sm-block" style="max-height:120px" loading="lazy"></div>
            <div class="col-md-9">
              <h4>Hỗ trợ sẵn nhiều nền tảng</h4>
              <p class="font-16">SePay tích hợp sẵn WordPress Woo, Haravan, Sapo. Ngoài ra khách hàng có thể tích hợp
                SePay làm cổng thanh toán thông qua Webhook.</p>
            </div>
          </div>



          <div class="text-center mt-3">
            <p class="font-16 fw-bold">Dùng SePay như một cổng thanh toán, dễ dàng, tiện lợi!</p>

          </div>
        </div>

      </div>
    </div>
  </div>
  <!-- modal Website/ App Ban Hang-->



  <!-- modal Website/ App Ban Hang-->
  <div class="modal fade" id="modalAPINH" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header dialog__header">
          <h1 class="modal-title fs-5 dialog__header__title" id="exampleModalLabel">SePay mở API Ngân hàng</h1>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row mt-3">
            <div class="col-md-9">
              <h4>Đồng bộ giao dịch</h4>
              <p class="font-16">Dễ dàng đồng bộ giao dịch ngân hàng của bạn đến ứng dụng bên thứ ba thông qua API và
                Webhook của SePay.</p>
            </div>
            <div class="col-md-3 text-center"><img src="https://my.sepay.vn/assets/images/plans/bank-sync.png"
                class="img-fluid" style="max-height:120px" loading="lazy"></div>

          </div>

          <div class="row my-4">

            <div class="col-md-3 text-center"><img src="https://my.sepay.vn/assets/images/plans/webhook.png"
                class="img-fluid d-none d-sm-block" style="max-height:120px" loading="lazy"></div>
            <div class="col-md-9">
              <h4>Tính năng webhook</h4>
              <p class="font-16">SePay chủ động bắn thông tin giao dịch đến ứng dụng của khách hàng. Tốc độ bắn gần như
                tức thì.</p>
            </div>
          </div>

          <div class="row my-4">

            <div class="col-md-9">
              <h4>Tính năng API</h4>
              <p class="font-16">Khách hàng chủ động gọi API đến SePay để lấy danh sách giao dịch, tra cứu chi tiết giao
                dịch.</p>
            </div>
            <div class="col-md-3 text-center"><img src="https://my.sepay.vn/assets/images/plans/api.png"
                class="img-fluid" style="max-height:120px" loading="lazy"></div>

          </div>

          <div class="text-center mt-3">
            <p class="font-16 fw-bold">Bạn là lập trình viên? hãy liên hệ với SePay để được hướng dẫn tích hợp
              nhé!</p>

          </div>
        </div>

      </div>
    </div>
  </div>
  <!-- modal Website/ App Ban Hang-->

  <!-- modal Tat ca tinh nang-->
  <div class="modal fade" id="modalTCTN" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header dialog__header">
          <h1 class="modal-title fs-5 dialog__header__title" id="exampleModalLabel">Tất cả tính năng SePay</h1>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <p>SePay <b>không giới hạn</b> tính năng giữa các gói. Tất cả các gói đều:</p>
          <ul class="lh-lg">
            <li>Không giới hạn tính năng sử dụng</li>
            <li>Không giới hạn số lượng tích hợp (Telegram, Lark, Sapo, Haravan, Webhook, API...)</li>
            <li>Không giới hạn số lượng tài khoản ngân hàng kết nối</li>
            <li>Hỗ trợ tài khoản ngân hàng cá nhân lẫn doanh nghiệp</li>
            <li>Không giới hạn số lượng người dùng (nhân viên), hỗ trợ phân quyền đầy đủ</li>

          </ul>
          <p>Giữa các gói chỉ khác nhau ở các thông số sau:<p>
              <ul class="lh-lg">
                <li>Số lượng giao dịch tối đa hằng tháng</li>
                <li>Các ngân hàng hỗ trợ</li>
                <li>Giá tiền</li>

              </ul>
              <div class="alert alert-primary alert-dismissible" role="alert">

                <div class="alert-message">
                  <p>Trường hợp quý khách dùng vượt số lượng giao dịch cho phép của gói, mọi chức năng vẫn hoạt động
                    bình thường. Tuy nhiên khi đến kỳ thanh toán, hệ thống sẽ tạo thêm hoá đơn tính phí vượt dựa trên số
                    lượng giao dịch vượt và giá tiền trên mỗi giao dịch của gói.</p>
                </div>
              </div>
        </div>

      </div>
    </div>
  </div>
  <!-- modal Tat ca tinh nang-->


  <!-- modal Bank Promo-->
  <div class="modal fade" id="modalBANKPROMO" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header dialog__header">
          <h1 class="modal-title fs-5 dialog__header__title" id="exampleModalLabel">Quà tặng từ ngân hàng hợp tác
          </h1>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">

          <h4>Giới thiệu ưu đãi</h4>
          <p>SePay là đối tác chiến lược của nhiều ngân hàng, vì vậy khách hàng của SePay sẽ được nhiều ưu đãi từ phía
            ngân hàng đã hợp tác.</p>

          <ul class="nav nav-tabs" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active  link-secondary" id="home-tab" data-bs-toggle="tab"
                data-bs-target="#home-tab-pane" type="button" role="tab" aria-controls="home-tab-pane"
                aria-selected="true"><img class="rounded-circle p-1 mt-1"
                  src="https://my.sepay.vn/assets/images/banklogo/mbbank-icon.png" style="height:35px; width:35px"
                  data-bs-original-title="" title=""> MB Bank(3)</button>
            </li>
          </ul>
          <div class="tab-content" id="myTabContent">
            <div class="tab-pane fade show active" id="home-tab-pane" role="tabpanel" aria-labelledby="home-tab"
              tabindex="0">
              <h4 class="mt-3">Ưu đãi từ MB Bank</h4>
              <div>
                <ul class="lh-lg">
                  <li>Tặng voucher <b>tài khoản số đẹp</b> 6 số trị giá 25.000.000 VND.</li>
                  <li>Tặng <b>thẻ cung hoàng đạo</b> khi thực hiện 5 giao trở lên trong tháng và duy trì tài khoản trên
                    100,000đ</li>
                  <li>Vay online không cần đến quầy giao dịch, được miễn lãi lên đến 15 ngày</li>
                </ul>
              </div>
              <h4 class="mt-3">Hướng dẫn nhận ưu đãi</h4>
              <div>
                <h5>1. Trường hợp khách hàng CHƯA có tài khoản MB Bank</h5>
                <p><b>Bước 1:</b> Quét QR bên dưới và tải app MB Bank. <span class="d-block d-sm-none">Hoặc click <a
                      href="https://mbbank.onelink.me/QPF5?pid=SF%20Email%20Warmup&c=SF_Email_WarmUP_AppInstall&af_force_deeplink=true&af_dp=mbbank%3A%2F%2F&referral_code=FNS6XEOXIX73UM2X7UPZW">vào
                      đây</a> để tải App</span></p>
                <div class="text-center"><img src="https://my.sepay.vn/assets/images/other/qr-app-mb.png"
                    class="img-fluid" style="max-height:180px"></div>
                <p><b>Bước 2:</b> Mở app MB Bank, thực hiện đăng ký tài khoản và định danh eKYC. </p>
                <p class="text-danger">Đến bước <span class="fw-bold">Thông tin hoàn thiện hồ sơ</span>, vui lòng chọn
                  Thành phố là <span class="fw-bold">Hồ Chí Minh</span>, Chọn chi nhánh là <span class="fw-bold">MB Đông
                    Sài Gòn</span>. Mã giới thiệu là <b>O905</b> (Chữ cái o)</p>
                <p><b>Lưu ý</b>: Nếu bạn không chọn chi nhánh là MB Đông Sài Gòn, bạn sẽ không nhận được ưu đãi trên.
                </p>
                <p class="text-center"><img src="https://my.sepay.vn/assets/images/other/mb-chon-chi-nhanh.png"
                    class="img-fluid" style="max-height:450px"></p>
                <p><b>Bước 3:</b> MB Đông Sài Gòn sẽ liên hệ với bạn để tặng tài khoản số đẹp 6 số và các ưu đãi nêu
                  trên. Hoặc bạn có thể liên hệ với SePay qua kênh chat để SePay chuyển thông tin đến MB Bank, giúp quá
                  trình nhận ưu đãi nhanh hơn.</p>

                <h5>2. Trường hợp khách hàng ĐÃ có tài khoản MB Bank</h5>
                <p>Để nhận ưu đãi trên, quý khách cần liên hệ với SePay để cung cấp thông tin. Chúng tôi sẽ chuyển tiếp
                  thông tin của quý khách đến MB Đông Sài Gòn, nhân viên MB Bank sẽ liên hệ với quý khách để tặng tài
                  khoản số đẹp 6 số cũng như các ưu đãi khác. Quá trình chỉ tốn 1 ngày làm việc (giờ hành chính)</p>
                <p>Liên hệ SePay để nhận ưu đãi từ MB Bank</p>
                <ul>
                  <li><a href="https://my.sepay.vn/ticket">Gửi yêu cầu</a></li>
                  <li><a href="https://m.me/***************">Chat với SePay</a></li>
                  <li><a href="tel:***********">Hotline: 02873.059.589</a></li>

                </ul>

              </div>
            </div>
            <div class="tab-pane fade" id="profile-tab-pane" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">


              <h4 class="mt-3">Ưu đãi từ OCB</h4>
              <div>
                <p>Tặng <b>30.000đ</b> vào tài khoản, áp dụng cho khách hàng mở mới tài khoản OCB.</p>
              </div>
              <h4 class="mt-3">Hướng dẫn nhận ưu đãi</h4>
              <div>
                <h5>Bước 1: Quét mã QR sau để tải app và đăng ký tài khoản. <span class="d-block d-sm-none">Hoặc click
                    <a href="https://newomni.onelink.me/sMc7/SEPAY">vào đây</a> để tải App</span></h5>
                <p>(QR đã bao gồm mã giới thiệu của SePay để nhận ưu đãi)</p>
                <div class="text-center"><img src="https://my.sepay.vn/assets/images/other/ocb-qr-app.png"
                    class="img-fluid" style="max-height:200px"></div>
                <h5>Bước 2: Mở tài khoản OCB qua App OCB OMNI vừa cài đặt</h5>
                <h5>Bước 3: Chuyển vào tài khoản OCB vừa tạo 60.000đ</h5>
                <h5>Bước 4: Chuyển ra lần 1 giao dịch 30.000đ</h5>
                <h5>Bước 5: Chuyển ra lần 2 giao dịch 30.000đ</h5>

                <p>Trong vòng 45 ngày, OCB sẽ hoàn tiền vào tài khoản của quý khách 30.000đ</p>
                <p>Nếu quý khách cần tư vấn, vui lòng liên hệ SePay qua các kênh sau:</p>
                <ul>
                  <li><a href="https://my.sepay.vn/ticket">Gửi yêu cầu</a></li>
                  <li><a href="https://m.me/***************">Chat với SePay</a></li>
                  <li><a href="tel:***********">Hotline: 02873.059.589</a></li>

                </ul>

              </div>

            </div>
          </div>



        </div>
      </div>
    </div>
    <!-- modal Bank Promo-->

    <!-- JS Global Compulsory  -->
    <script src="./assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

    <!-- JS Implementing Plugins -->
    <script src="./assets/vendor/hs-header/dist/hs-header.min.js"></script>
    <script src="./assets/vendor/hs-mega-menu/dist/hs-mega-menu.min.js"></script>

    <script src="./assets/vendor/hs-toggle-switch/dist/hs-toggle-switch.min.js"></script>

    <!-- JS Front -->
    <script src="./assets/js/theme.min.js"></script>

    <!-- contact-box-js -->
    <script src="./assets/js/contact-box.js"></script>
    <!-- /contact-box-js -->

    <!-- JS Plugins Init. -->
    <script>
      (function () {
        // INITIALIZATION OF HEADER
        // =======================================================
        new HSHeader('#header').init()


        // INITIALIZATION OF MEGA MENU
        // =======================================================
        new HSMegaMenu('.js-mega-menu', {
          desktop: {
            position: 'left'
          }
        })


        // INITIALIZATION OF BOOTSTRAP DROPDOWN
        // =======================================================
        HSBsDropdown.init()




        // INITIALIZATION OF TOGGLE SWITCH
        // =======================================================
        new HSToggleSwitch('.js-toggle-switch');


      })()
    </script>



    <script>
      var b_discount = 0;
      var b_cycle = 1;
      var billing_cycle_map = {
        1: 'monthly',
        12: 'annually',
      };

      function updatePriceBox(box, m_cycle = false, element = false) {

        if (box == 'sms' || box == 'api') {
          if (box == 'sms') {
            var e = document.getElementById("sms_plan_select");
            var option = e.options[e.selectedIndex];
            var plan_id = e.value;

          } else if (box == 'api') {
            var e = document.getElementById("api_plan_select");
            var option = e.options[e.selectedIndex];
            var plan_id = e.value;

          }

          var plan_price = option.getAttribute("data-price");
          var plan_name = option.getAttribute("data-plan");
          var plan_transactions = option.getAttribute("data-transactions");
          var order_url = "https://my.sepay.vn/company/cart";

          if (box == 'sms') {


            document.getElementById("box-sms-price").innerText = Intl.NumberFormat('en-US').format(plan_price -
              b_discount * plan_price) + 'đ';

            document.getElementById("box-sms-price").dataset['originalprice'] = plan_price;

            document.getElementById("box-sms-name").innerText = plan_name.toUpperCase();

            var btnOrder = document.getElementById("box-sms-order");


            var order_url = new URL(btnOrder.href);
            order_url.searchParams.set('billing_cycle', billing_cycle_map[b_cycle]);
            order_url.searchParams.set('product_id', plan_id);
            btnOrder.href = order_url.toString();


          } else {
            document.getElementById("box-api-price").innerText = Intl.NumberFormat('en-US').format(plan_price -
              b_discount * plan_price) + 'đ';
            document.getElementById("box-api-price").dataset['originalprice'] = plan_price;

            document.getElementById("box-api-name").innerText = plan_name.toUpperCase();

            var btnOrder = document.getElementById("box-api-order");


            var order_url = new URL(btnOrder.href);
            order_url.searchParams.set('billing_cycle', billing_cycle_map[b_cycle]);
            order_url.searchParams.set('product_id', plan_id);

            btnOrder.href = order_url.toString();

          }


        } else if (box == 'billing_select') {

          var discount = element.dataset.discount;
          b_discount = discount;
          b_cycle = m_cycle;

          var priceElms = document.querySelectorAll(".price_value");
          [].forEach.call(priceElms, function (el) {
            var before_discount = el.dataset.originalprice;

            var after_discount = before_discount - discount * before_discount;

            el.innerHTML = Intl.NumberFormat('en-US').format(after_discount) + 'đ';
          });

          var priceElms = document.querySelectorAll(".btn-order");
          [].forEach.call(priceElms, function (el) {
            var order_url = new URL(el.href);
            order_url.searchParams.set('billing_cycle', billing_cycle_map[m_cycle]);
            el.href = order_url.toString();
          });



        }




      }
      document.getElementById("btnBillYearly").click();


    </script>
</body>

</html>