<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <title>Hướng dẫn cấu hình tài khoản ngân hàng | SePay</title>
        <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet" />
        <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
        <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
        <link rel="stylesheet" href="../assets/vendor/bootstrap-icons/font/bootstrap-icons.css" />
        <link rel="stylesheet" href="../assets/css/theme.min.css" />
        <link rel="stylesheet" href="../assets/css/docs.min.css" />
        <link rel="stylesheet" href="../assets/css/contact-box.css" />
        <style>
            .docs-navbar-sidebar-aside-body {
                padding-top: 3.5rem !important;
            }
        </style>
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                dataLayer.push(arguments);
            }
            gtag("js", new Date());

            gtag("config", "G-J8DLMQTKSQ");
        </script>
    </head>
    <body class="navbar-sidebar-aside-lg">
        <header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
            <div class="container-fluid">
                <nav class="navbar-nav-wrap">
                    <div class="row flex-grow-1">
                        <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
                            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                                <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo" />
                            </a>
                        </div>
                        <div class="col-md px-lg-0">
                            <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
                                <div class="d-none d-md-block">
                                    <div class="d-none">
                                        <div id="searchTemplate" class="dropdown-item">
                                            <a class="d-block link" href="#">
                                                <span class="category d-block fw-normal text-muted mb-1"></span>
                                                <span class="component text-dark"></span>
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <ul class="navbar-nav p-0">
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html" target="_blank"> Support <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN" target="_blank"> Youtube <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://www.facebook.com/messages/t/sepay.vn" target="_blank"> Facebook <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel" target="_blank"> Telegram <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </nav>
            </div>
        </header>
        <main id="content" role="main">
            <nav
                class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
                data-hs-nav-scroller-options='{
                    "type": "vertical",
                    "target": ".navbar-nav .active",
                    "offset": 80
                }'
            >
                <button
                    type="button"
                    class="navbar-toggler btn btn-white d-grid w-100"
                    data-bs-toggle="collapse"
                    data-bs-target="#navbarVerticalNavMenu"
                    aria-label="Toggle navigation"
                    aria-expanded="false"
                    aria-controls="navbarVerticalNavMenu"
                >
                    <span class="d-flex justify-content-between align-items-center">
                        <span class="h3 mb-0">Nav menu</span>

                        <span class="navbar-toggler-default">
                            <i class="bi-list"></i>
                        </span>

                        <span class="navbar-toggler-toggled">
                            <i class="bi-x"></i>
                        </span>
                    </span>
                </button>

                <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
                    <div class="navbar-brand-wrapper border-end" style="height: auto;">
                        <div class="d-flex align-items-center mb-3">
                            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                                <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo" />
                            </a>
                        </div>
                    </div>

                    <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
                        <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
                            <li class="nav-item">
                                <span class="nav-subtitle">Giới thiệu</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="sepay-la-gi.html">SePay là gì?</a>
                            </li>
                            <li class="nav-item my-2 mt-lg-5">
                                <span class="nav-subtitle">Gói dịch vụ</span>
                            </li>
    
                            <li class="nav-item">
                                <a class="nav-link" href="goi-theo-diem-ban">Gói theo điểm bán</a>
                            </li>
                            <li class="nav-item my-2 my-lg-5"></li>

                            <li class="nav-item">
                                <span class="nav-subtitle">Hướng dẫn chung</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="dang-ky-sepay.html">Đăng ký SePay</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="them-tai-khoan-ngan-hang.html">Thêm tài khoản ngân hàng</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="xem-giao-dich.html">Xem giao dịch</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="nguoi-dung-va-phan-quyen.html">Người dùng & Phân quyền</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="tai-khoan-phu.html">Tài khoản phụ</a>
                            </li>
                
                            <li class="nav-item">
                                <a class="nav-link active" href="cau-hinh-tai-khoan-ngan-hang.html">Cấu hình TK ngân hàng</a>
                            </li>

                            <li class="nav-item my-2 my-lg-5"></li>

                            <li class="nav-item">
                                <span class="nav-subtitle">Cấu hình công ty</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="goi-dich-vu.html">Gói dịch vụ</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="hoa-don-va-thanh-toan.html">Hóa đơn & thanh toán</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="cau-hinh-chung.html">Cấu hình chung</a>
                            </li>

                            <li class="nav-item my-2 my-lg-5"></li>

                            <li class="nav-item">
                                <span class="nav-subtitle">Chia sẻ biến động số dư</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="tich-hop-telegram.html">Tích hợp Telegram <i class="bi bi-telegram ms-2 text-info"></i></a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="tich-hop-lark-messenger.html">Tích hợp Lark Messenger <img src="assets/img/lark/lark-icon.png" class="img-fluid" style="max-height: 25px;" /></a>
                            </li>

            <li class="nav-item">
                <a class="nav-link" href="tich-hop-viber.html">Tích hợp Viber <img src="assets/img/viber/viber-icon.png" class="img-fluid rounded ms-2" style="height:25px;width:25px;"></a>
            </li>

                            <li class="nav-item">
                                <a class="nav-link d-flex align-items-center" href="mobile-app.html">
                                    Mobile App
                                    <i class="bi bi-phone-vibrate ms-2 fs-3 text-info"></i>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link " href="tich-hop-loa-thanh-toan.html">Tích hợp Loa thanh toán 
                                    <svg class="mb-0 ms-1" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#377dff" class="bi bi-speaker" viewBox="0 0 16 16">
                                        <path d="M12 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1zM4 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2z"></path>
                                        <path d="M8 4.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5M8 6a2 2 0 1 0 0-4 2 2 0 0 0 0 4m0 3a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3m-3.5 1.5a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0"></path>
                                      </svg>
                                    </a>
                              </li>
                            <li class="nav-item my-2 my-lg-5"></li>

                            <li class="nav-item">
                                <span class="nav-subtitle">Tích hợp web</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="tich-hop-shopify.html">Tích hợp Shopify <img class="ms-2" src="assets/img/others/shopify-icon.png" style="width: 22px; height: 22px;" /></a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="tich-hop-sapo.html">Tích hợp Sapo <img class="ms-2" src="assets/img/others/sapo-icon.png" style="width: 18px; height: 18px;" /></a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="tich-hop-haravan.html">Tích hợp Haravan <img class="ms-2" src="assets/img/others/haravan-icon.png" style="width: 22px; height: 22px;" /></a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="tich-hop-gohighlevel.html">Tích hợp GoHighLevel <img class="ms-2" src="assets/img/others/gohighlevel-icon.png" style="width:22px; height: 22px;"></a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="woocommerce.html">Tích hợp WooCommerce <img class="ms-2" src="assets/img/others/woocommerce-icon.png" style="width: 22px; height: 22px;" /></a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="tich-hop-google-sheets.html">Tích hợp Google Sheets <img class="ms-2" src="assets/img/others/google-sheets-icon.png" style="width: 22px; height: 22px;" /></a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="tich-hop-hostbill.html">Tích hợp HostBill <img class="ms-2" src="assets/img/others/hostbill-icon.png" style="height: 22px;" /></a>
                            </li>
                            <li class="nav-item my-2 my-lg-5"></li>

                            <li class="nav-item">
                                <span class="nav-subtitle">Lập trình & Tích hợp</span>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="tich-hop-webhooks.html">Tích hợp WebHooks</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="lap-trinh-webhooks.html">Lập trình WebHooks</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="gia-lap-giao-dich.html">Giả lập giao dịch</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="tao-qr-code-vietqr-dong.html">Tạo & nhúng QR Code</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2">OAuth2</a>
                            </li>

                            <li class="nav-item my-2 my-lg-5"></li>

                            <li class="nav-item">
                                <span class="nav-subtitle">SePay API</span>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="gioi-thieu-api.html">Giới thiệu API</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="tao-api-token.html">Tạo API Token</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="api-giao-dich.html">API Giao dịch</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="api-tai-khoan-ngan-hang.html">API Tài khoản ngân hàng</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="api-va-theo-don-hang.html">API VA theo Đơn hàng</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>

            <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">
                <div style="max-width: 1200px;">
                    <div class="docs-page-header">
                        <div class="row align-items-center">
                            <div class="col-sm">
                                <h1 class="docs-page-header-title">Hướng dẫn cấu hình tài khoản ngân hàng</h1>
                                <p class="docs-page-header-text">
                                    Cấu hình tài khoản ngân hàng là bước quan trọng sau khi bạn đã thêm tài khoản vào SePay. Việc cấu hình chính xác sẽ giúp hệ thống hoạt động hiệu quả và phù hợp với nhu cầu sử dụng của bạn.
                                </p>
                            </div>
                        </div>
                    </div>
                    <p>
                        Vào phần <strong><i class="bi bi-gear"></i> Cấu hình chung</strong> trong trang chi tiết tài khoản ngân hàng để thực hiện các thao tác cấu hình tài khoản ngân hàng.
                    </p>
                    <img src="assets/img/cau-hinh-ngan-hang/image1.png" alt="Hướng dẫn cấu hình tài khoản ngân hàng" class="img-fluid rounded-1" />
                    <h2 class="hs-docs-heading" id="dong-bo-giao-dich">
                        Đồng bộ giao dịch
                        <a class="anchorjs-link" href="#dong-bo-giao-dich" aria-label="Anchor link">
                            <span class="anchorjs-icon"></span>
                        </a>
                    </h2>
                    <p>Khi bật tính năng này, SePay sẽ tự động cập nhật các giao dịch mới từ tài khoản ngân hàng của bạn. Các tính năng đồng bộ này được bật mặc định khi bạn thêm tài khoản ngân hàng vào SePay.</p>
                    <img src="assets/img/cau-hinh-ngan-hang/image2.png" alt="Hướng dẫn cấu hình tài khoản ngân hàng" class="img-fluid rounded-1 mb-4" />
                    <p>Bạn có thể tắt tính năng này nếu không muốn SePay tự động cập nhật giao dịch mới như tiền vào, tiền ra từ tài khoản ngân hàng của bạn.</p>
                    <h4 class="mb-3">1. Đồng bộ giao dịch tiền vào</h4>
                    <p>Đồng bộ các giao dịch tiền vào (nhận tiền) vào tài khoản của bạn, phù hợp với các tài khoản nhận tiền từ khách hàng. Nếu bạn không muốn SePay đồng bộ giao dịch tiền vào, bạn có thể tắt tính năng này.</p>
                    <h4 class="mb-3">2. Đồng bộ giao dịch tiền ra</h4>
                    <p>Đồng bộ các giao dịch tiền ra (chuyển tiền) từ tài khoản của bạn. Tính năng này giúp bạn theo dõi chính xác các khoản chi tiêu và chuyển khoản từ tài khoản.</p>
                    <div class="alert alert-soft-info mb-3">
                        <div class="d-flex align-items-baseline">
                            <div class="flex-shrink-0">
                                <i class="bi-info-circle me-2"></i>
                            </div>
                            <div class="flex-grow-1">
                                Hỗ trợ cho các ngân hàng:
                                <ul class="mb-0 mt-1">
                                    <li>Phương thức kết nối API với các ngân hàng: <a href="ket-noi-tpbank.html">TPBank</a>, <a href="ket-noi-vietinbank.html">VietinBank</a>, <a href="ket-noi-acb.html">ACB</a>, <a href="ket-noi-mb-api.html">MBBank</a></li>
                                    <li>Tất cả ngân hàng đều hỗ trợ khi kết nối qua SMS</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <h4 class="mb-3">3. Đồng bộ số dư</h4>
                    <div class="alert alert-soft-info mb-3">
                        <div class="d-flex align-items-baseline">
                            <div class="flex-shrink-0">
                                <i class="bi-info-circle me-2"></i>
                            </div>
                            <div class="flex-grow-1">
                                Hỗ trợ cho các ngân hàng:
                                <ul class="mb-0 mt-1">
                                    <li>Phương thức kết nối API với ngân hàng <a href="ket-noi-tpbank.html">TPBank</a> và <a href="ket-noi-vietinbank.html">VietinBank</a></li>
                                    <li>Tất cả ngân hàng đều hỗ trợ khi kết nối qua SMS</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <p>Nếu bạn không muốn hiển thị số dư tài khoản ngân hàng trên SePay, bạn có thể tắt tính năng này.</p>
                    <h4 class="mb-3">4. Lọc giao dịch theo từ khóa</h4>
                    <p>Tính năng lọc từ khóa cho phép bạn:</p>
                    <ul class="mb-4">
                        <li>Chỉ đồng bộ các giao dịch có nội dung chứa từ khóa đã cấu hình</li>
                        <li>Bỏ qua các giao dịch không liên quan</li>
                        <li>Quản lý giao dịch hiệu quả hơn</li>
                    </ul>
                    <div class="mb-4">
                        <img src="assets/img/cau-hinh-ngan-hang/image3.png" alt="Cấu hình lọc từ khóa" class="img-fluid rounded-1" />
                    </div>
                    <h2 class="hs-docs-heading" id="xoa-du-lieu-giao-dich">
                        Xóa dữ liệu giao dịch
                    </h2>
                    <p>Nếu bạn muốn xóa tất cả dữ liệu giao dịch đã đồng bộ từ tài khoản ngân hàng, bạn có thể thực hiện thao tác này. Sau khi xóa, tất cả dữ liệu giao dịch sẽ bị xóa vĩnh viễn trên SePay và không thể khôi phục lại.</p>
                    <img src="assets/img/cau-hinh-ngan-hang/image4.png" alt="Xóa dữ liệu giao dịch" class="img-fluid rounded-1 mb-4" />
                    <p>Để xóa dữ liệu giao dịch, nhấn vào nút <strong>Xóa tất cả giao dịch</strong> và nhập <strong>số tài khoản</strong> của bạn để xác nhận.</p>
                    <img src="assets/img/cau-hinh-ngan-hang/image5.png" alt="Xóa dữ liệu giao dịch" class="img-fluid rounded-1 mb-4" />
                    <div class="alert alert-soft-info mb-3">
                        <div class="d-flex align-items-baseline">
                            <div class="flex-shrink-0">
                                <i class="bi-info-circle me-2"></i>
                            </div>
                            <div class="flex-grow-1">
                                <strong class="me-1">Lưu ý:</strong>Tính năng này chỉ xóa dữ liệu giao dịch của tài khoản đó trên SePay, không ảnh hưởng đến tài khoản ngân hàng của bạn.
                            </div>
                        </div>
                    </div>
                    <h2 class="hs-docs-heading" id="cau-hoi-thuong-gap">
                        Câu hỏi thường gặp
                        <a class="anchorjs-link" href="#cau-hoi-thuong-gap" aria-label="Anchor link">
                            <span class="anchorjs-icon"></span>
                        </a>
                    </h2>
                    <div class="accordion accordion-lg accordion-flush" id="accordionLGExample">
                        <div class="accordion-item">
                            <div class="accordion-header" id="accordion-lg-headingOne">
                                <a class="accordion-button collapsed" role="button" data-bs-toggle="collapse" data-bs-target="#accordion-lg-collapseOne" aria-expanded="false" aria-controls="accordion-lg-collapseOne">
                                    Tại sao một số tính năng không khả dụng?
                                </a>
                            </div>
                            <div id="accordion-lg-collapseOne" class="accordion-collapse collapse" aria-labelledby="accordion-lg-headingOne" data-bs-parent="#accordionLGExample">
                                <div class="accordion-body">Mỗi ngân hàng hỗ trợ các tính năng khác nhau, một số tính năng có thể cần chuyển sang kết nối SMS để sử dụng.</div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <div class="accordion-header" id="accordion-lg-headingTwo">
                                <a class="accordion-button collapsed" role="button" data-bs-toggle="collapse" data-bs-target="#accordion-lg-collapseTwo" aria-expanded="false" aria-controls="accordion-lg-collapseTwo">
                                    Việc xóa dữ liệu có ảnh hưởng đến tài khoản ngân hàng không?
                                </a>
                            </div>
                            <div id="accordion-lg-collapseTwo" class="accordion-collapse collapse" aria-labelledby="accordion-lg-headingTwo" data-bs-parent="#accordionLGExample">
                                <div class="accordion-body">
                                    Không, việc xóa dữ liệu chỉ xóa dữ liệu trên SePay, không ảnh hưởng đến tài khoản thật của bạn.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <div class="accordion-header" id="accordion-lg-headingThree">
                                <a class="accordion-button collapsed" role="button" data-bs-toggle="collapse" data-bs-target="#accordion-lg-collapseThree" aria-expanded="false" aria-controls="accordion-lg-collapseThree">
                                    Tại sao không thấy giao dịch mới?
                                </a>
                            </div>
                            <div id="accordion-lg-collapseThree" class="accordion-collapse collapse" aria-labelledby="accordion-lg-headingThree" data-bs-parent="#accordionLGExample">
                                <div class="accordion-body">Có thể bạn đã tắt tính năng đồng bộ giao dịch hoặc chưa bật tính năng này.</div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-5">
                        <p>
                            Đọc tiếp:
                            <a href="goi-dich-vu.html">Gói dịch vụ<i class="bi bi-chevron-right"></i></a>
                        </p>
                    </div>
                </div>
            </div>
        </main>
        <div id="contact-box-overlay"></div>
        <div id="contact-box">
            <div class="popup">
                <a href="https://m.me/117903214582465" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/fb-messenger.png" width="50%" />
                    </div>
                    <div class="meta">
                        <p class="title">Facebook Messenger</p>
                        <small class="description">Hỗ trợ live chat 24/7</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="tel:02873059589" class="item">
                    <div class="icon" style="background-color: #22c55e; color: #fff;">
                        <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;">
                            <path
                                d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z"
                                stroke="currentColor"
                                stroke-width="1.5"
                                stroke-linecap="round"
                                fill="transparent"
                            ></path>
                        </svg>
                    </div>
                    <div class="meta">
                        <p class="title">Hotline</p>
                        <small class="description">Điện thoại hỗ trợ</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/youtube-social.png" />
                    </div>
                    <div class="meta">
                        <p class="title">Youtube</p>
                        <small class="description">Theo dõi video mới nhất của SePay</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/telegram-social.png" />
                    </div>
                    <div class="meta">
                        <p class="title">Telegram</p>
                        <small class="description">Nhận thông tin mới nhất từ SePay</small>
                    </div>
                </a>
            </div>
            <div class="container">
                <div class="dot-ping">
                    <div class="ping"></div>
                    <div class="dot"></div>
                </div>
                <div class="contact-icon">
                    <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;">
                        <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z"
                            fill="currentColor"
                        ></path>
                    </svg>
                </div>
                <span style="font-weight: bold;">Liên hệ chúng tôi</span>
            </div>
        </div>

        <a
            class="js-go-to go-to position-fixed"
            href="javascript:;"
            style="visibility: hidden;"
            data-hs-go-to-options='{
                "offsetTop": 700,
                "position": {
                    "init": {
                        "right": "2rem"
                    },
                    "show": {
                        "bottom": "2rem"
                    },
                    "hide": {
                        "bottom": "-2rem"
                    }
                }
            }'
        >
            <i class="bi-chevron-up"></i>
        </a>

        <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
        <script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
        <script src="../assets/vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js"></script>
        <script src="../assets/vendor/list.js/dist/list.min.js"></script>
        <script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>
        <script src="../assets/js/theme.min.js"></script>
        <script src="../assets/js/contact-box.js"></script>
        <script>
            (function () {
                new HSHeader("#header").init();

                new HsNavScroller(".js-nav-scroller", {
                    delay: 400,
                    offset: 140,
                });

                const docsSearch = HSCore.components.HSList.init("#docsSearch");

                new HSGoTo(".js-go-to");
            })();

            function setCookie(cname, cvalue, exdays) {
                var d = new Date();
                d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000);
                var expires = "expires=" + d.toUTCString();
                document.cookie = cname + "=" + cvalue + "; " + expires + "; path=/; domain=.sepay.vn";
            }

            let urlParams = new URLSearchParams(document.location.search);

            ["utm_source", "utm_medium", "utm_campaign", "utm_term", "utm_content"].forEach(function (param) {
                let value = urlParams.get(param);

                if (value) {
                    setCookie(param, value, 90);
                }
            });

            if (document.referrer != "" && document.cookie.indexOf("referer") === -1) {
                setCookie("referer", document.referrer, 90);
            }
        </script>
    </body>
</html>
