<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Required Meta Tags Always Come First -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- Title -->
    <title>Hướng dẫn tích hợp SePay v<PERSON><PERSON> | SePay</title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />

    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">

    <!-- CSS Implementing Plugins -->
    <link rel="stylesheet" href="../assets/vendor/bootstrap-icons/font/bootstrap-icons.css">
    <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
    <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
  
    <!-- CSS Front Template -->
    <link rel="stylesheet" href="../assets/css/theme.min.css">
    <link rel="stylesheet" href="../assets/css/docs.min.css">

    <!--contact-box-css-->
    <link rel="stylesheet" href="../assets/css/contact-box.css">
    <!--/contact-box-css-->

    <link rel="stylesheet" href="../assets/vendor/hs-video-bg/dist/hs-video-bg.min.css">
    <style>
        .docs-navbar-sidebar-aside-body {
            padding-top: 3.5rem !important;
        }
    </style>
      <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-J8DLMQTKSQ');
  </script>
</head>

<body class="navbar-sidebar-aside-lg">
    <!-- ========== HEADER ========== -->
    <header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
        <div class="container-fluid">
            <nav class="navbar-nav-wrap">
                <div class="row flex-grow-1">
                    <!-- Default Logo -->
                    <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
                        <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                            <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png"
                                alt="Logo">
                        </a>

                    </div>
                    <!-- End Default Logo -->

                    <div class="col-md px-lg-0">
                        <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
                            <div class="d-none d-md-block">


                                <!-- List Item Template -->
                                <div class="d-none">
                                    <div id="searchTemplate" class="dropdown-item">
                                        <a class="d-block link" href="#">
                                            <span class="category d-block fw-normal text-muted mb-1"></span>
                                            <span class="component text-dark"></span>
                                        </a>
                                    </div>
                                </div>
                                <!-- End List Item Template -->
                            </div>

                            <!-- Navbar -->
                            <ul class="navbar-nav p-0">
                                <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html"
                                        target="_blank">
                                        Support <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN" target="_blank">
                                      Youtube <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                  </li>
                                  <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://www.facebook.com/messages/t/sepay.vn" target="_blank">
                                      Facebook <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                  </li>
                                  <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel" target="_blank">
                                      Telegram <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                  </li>

                            </ul>
                            <!-- End Navbar -->
                        </div>
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Row -->
            </nav>
        </div>
    </header>
    <!-- ========== END HEADER ========== -->

    <!-- ========== MAIN CONTENT ========== -->
    <main id="content" role="main">
        <!-- Navbar -->
        <nav class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
            data-hs-nav-scroller-options='{
            "type": "vertical",
            "target": ".navbar-nav .active",
            "offset": 80
           }'>
            <!-- Navbar Toggle -->
            <button type="button" class="navbar-toggler btn btn-white d-grid w-100" data-bs-toggle="collapse"
                data-bs-target="#navbarVerticalNavMenu" aria-label="Toggle navigation" aria-expanded="false"
                aria-controls="navbarVerticalNavMenu">
                <span class="d-flex justify-content-between align-items-center">
                    <span class="h3 mb-0">Nav menu</span>

                    <span class="navbar-toggler-default">
                        <i class="bi-list"></i>
                    </span>

                    <span class="navbar-toggler-toggled">
                        <i class="bi-x"></i>
                    </span>
                </span>
            </button>
            <!-- End Navbar Toggle -->

            <!-- Navbar Collapse -->
            <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
                <div class="navbar-brand-wrapper border-end" style="height: auto;">
                    <!-- Default Logo -->
                    <div class="d-flex align-items-center mb-3">
                        <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                            <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png"
                                alt="Logo">
                        </a>

                    </div>
                    <!-- End Default Logo -->


                </div>

                <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
                    <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
                        <li class="nav-item">
                            <span class="nav-subtitle">Giới thiệu</span>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="sepay-la-gi.html">SePay là gì?</a>
                        </li>
                        <li class="nav-item my-2 mt-lg-5">
                            <span class="nav-subtitle">Gói dịch vụ</span>
                        </li>
              
                        <li class="nav-item">
                            <a class="nav-link" href="goi-theo-diem-ban">Gói theo điểm bán</a>
                        </li>
                        <li class="nav-item my-2 my-lg-5"></li>

                        <li class="nav-item">
                            <span class="nav-subtitle">Hướng dẫn chung</span>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="dang-ky-sepay.html">Đăng ký SePay</a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="them-tai-khoan-ngan-hang.html">Thêm tài khoản ngân hàng</a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link " href="xem-giao-dich.html">Xem giao dịch</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="nguoi-dung-va-phan-quyen.html">Người dùng & Phân quyền</a>
                        </li>
 
                        <li class="nav-item">
                            <a class="nav-link" href="tai-khoan-phu.html">Tài khoản phụ</a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="cau-hinh-tai-khoan-ngan-hang.html">Cấu hình TK ngân hàng</a>
                        </li>

                        
                        <li class="nav-item my-2 my-lg-5"></li>

                        <li class="nav-item">
                            <span class="nav-subtitle">Cấu hình công ty</span>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link " href="goi-dich-vu.html">Gói dịch vụ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="hoa-don-va-thanh-toan.html">Hóa đơn & thanh toán</a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link " href="cau-hinh-chung.html">Cấu hình chung</a>
                        </li>

                        <li class="nav-item my-2 my-lg-5"></li>

                        <li class="nav-item">
                            <span class="nav-subtitle">Chia sẻ biến động số dư</span>
                        </li>
              
                        <li class="nav-item">
                            <a class="nav-link " href="tich-hop-telegram.html">Tích hợp Telegram <i class="bi bi-telegram ms-2 text-info"></i></a>
                          </li>
              
            <li class="nav-item">
              <a class="nav-link" href="tich-hop-lark-messenger.html">Tích hợp Lark Messenger <img src="assets/img/lark/lark-icon.png" class="img-fluid" style="max-height:25px"></a>
          </li>

                        <li class="nav-item">
                            <a class="nav-link" href="tich-hop-viber.html">Tích hợp Viber <img src="assets/img/viber/viber-icon.png" class="img-fluid rounded ms-2" style="height:25px;width:25px;"></a>
                        </li>
              
                        <li class="nav-item">
                            <a class="nav-link d-flex align-items-center" href="mobile-app.html">Mobile App <i class="bi bi-phone-vibrate ms-2 fs-3 text-info"></i> </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="tich-hop-loa-thanh-toan.html">Tích hợp Loa thanh toán 
                                <svg class="mb-0 ms-1" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#377dff" class="bi bi-speaker" viewBox="0 0 16 16">
                                    <path d="M12 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1zM4 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2z"></path>
                                    <path d="M8 4.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5M8 6a2 2 0 1 0 0-4 2 2 0 0 0 0 4m0 3a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3m-3.5 1.5a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0"></path>
                                  </svg>
                                </a>
                          </li>
                <li class="nav-item my-2 my-lg-5"></li>

                <li class="nav-item">
                    <span class="nav-subtitle">Tích hợp web</span>
                </li>

                <li class="nav-item">
                    <a class="nav-link" href="tich-hop-shopify.html">Tích hợp Shopify <img class="ms-2" src="assets/img/others/shopify-icon.png" style="width:22px; height: 22px;"></a>
                  </li>
                  
                <li class="nav-item">
                    <a class="nav-link" href="tich-hop-sapo.html">Tích hợp Sapo <img class="ms-2" src="assets/img/others/sapo-icon.png" style="width:18px; height: 18px;"></a>
                </li>
               
    
                <li class="nav-item">
                    <a class="nav-link active" href="tich-hop-haravan.html">Tích hợp Haravan <img class="ms-2" src="assets/img/others/haravan-icon.png" style="width:22px; height: 22px;"></a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="tich-hop-gohighlevel.html">Tích hợp GoHighLevel <img class="ms-2" src="assets/img/others/gohighlevel-icon.png" style="width:22px; height: 22px;"></a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="woocommerce.html">Tích hợp WooCommerce <img class="ms-2" src="assets/img/others/woocommerce-icon.png" style="width:22px; height: 22px;"></a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="tich-hop-google-sheets.html">Tích hợp Google Sheets <img class="ms-2" src="assets/img/others/google-sheets-icon.png" style="width:22px; height: 22px;"></a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="tich-hop-hostbill.html">Tích hợp HostBill <img class="ms-2"
                            src="assets/img/others/hostbill-icon.png" style="height: 22px;"></a>
                </li>

            <li class="nav-item my-2 my-lg-5"></li>

            <li class="nav-item">
                <span class="nav-subtitle">Lập trình & Tích hợp</span>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="tich-hop-webhooks.html">Tích hợp WebHooks</a>
            </li>

            <li class="nav-item ">
                <a class="nav-link" href="lap-trinh-webhooks.html">Lập trình WebHooks</a>
            </li>
            <li class="nav-item ">
              <a class="nav-link" href="gia-lap-giao-dich.html">Giả lập giao dịch</a>
          </li>
          <li class="nav-item ">
            <a class="nav-link" href="tao-qr-code-vietqr-dong.html">Tạo & nhúng QR Code</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="/oauth2">OAuth2</a>
        </li>


        

        <li class="nav-item my-2 my-lg-5"></li>

      <li class="nav-item">
          <span class="nav-subtitle">SePay API</span>
      </li>
      <li class="nav-item">
          <a class="nav-link" href="gioi-thieu-api.html">Giới thiệu API</a>
      </li>
      <li class="nav-item">
          <a class="nav-link " href="tao-api-token.html">Tạo API Token</a>
      </li>
      <li class="nav-item">
          <a class="nav-link " href="api-giao-dich.html">API Giao dịch</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="api-tai-khoan-ngan-hang.html">API Tài khoản ngân hàng</a>
    </li>
      <li class="nav-item">
          <a class="nav-link" href="api-va-theo-don-hang.html">API VA theo Đơn hàng</a>
      </li>


                    </ul>
                </div>
            </div>
            <!-- End Navbar Collapse -->
        </nav>
        <!-- End Navbar -->

        <!-- Content -->
        <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">

            <div class="docs-page-header">
                <div class="row align-items-center">
                    <div class="col-sm">
                        <h1 class="docs-page-header-title">Hướng dẫn tích hợp Haravan <img src="assets/img/others/haravan-icon.png" style="width:30px"></h1>
                        <p class="docs-page-header-text">Tích hợp SePay vào Haravan giúp xác nhận thanh toán ngay sau khi khách hàng chuyển khoản. Đơn hàng cũng sẽ chuyển sang trạng thái đã thanh toán.</p>
                    </div>
                </div>
            </div>

            <div class="alert alert-soft-danger mt-2">

                <div class="flex-grow-1 ms-2 mb-3 custom-alert-content">
                    Lưu ý: Từ phiên bản cập nhật giao diện mới MyHaravan, Haravan không còn hỗ trợ add script vô website, với các tích hợp phía SePay đang sử dụng phiên bản cũ trước đó , quý khách vẫn có thể tích hợp bình thường
                    
                </div>
            </div>

            <p>Nếu bạn đang sử dụng website bán hàng tại <a href="https://www.haravan.com/haraweb" target="_blank">Haravan</a>, bài viết này sẽ hướng dẫn bạn tích hợp SePay vào Haravan giúp tự động hóa việc xác nhận thanh toán qua chuyển khoản. </p>
            <p class="fw-bold">Kết quả sau khi tích hợp:</p>
            <p class="ms-2 fw-bold">1. Phía người dùng mua hàng trực tuyến</p>
                <ul class="lh-lg">
                    <li>Khi khách đặt hàng và thanh toán, sẽ có thêm tùy chọn <b>Chuyển khoản ngân hàng</b></li>
                    <li>Mã QR code kèm thông tin thanh toán sẽ hiện lên sau khi chọn phương thức thanh toán trên.</li>
                    <li>Website sẽ hiện <b class="text-success">Bạn đã thánh toán thành công</b> sau khi khách hàng thanh toán chỉ vài giây. </li>
                    <li>Email xác nhận đã thanh toán sẽ được gửi cho khách hàng (từ Haravan).</li>
                </ul>
                <div style="max-width: 800px;">
                    <div class="ratio ratio-16x9">
                        <iframe src="https://www.youtube.com/embed/0VXRZECEPdI" title="YouTube video" allowfullscreen></iframe>
                      </div>
                      <p class="text-muted text-center mt-2">Video demo kết quả tích hợp Haravan</p>                   
                </div>
                
            <p class="ms-2 mt-4 fw-bold">2. Phía giao diện Admin</p>
            <p>Đơn hàng sẽ tự động ghi nhận thanh toán. Và tự chuyển sang trạng thái đã thanh toán (nếu khách thanh toán đủ).</p>

            <h2 class="mb-3">Hướng dẫn tích hợp</h2>

            <div style="max-width: 800px;">

                <div class="ratio ratio-16x9">
                       <iframe src="https://www.youtube.com/embed/_tENP5j3Y50?si=UWhhPnkHd4iVKVP3" title="YouTube video" allowfullscreen></iframe>
                     </div>
                     <p class="text-muted text-center mt-2">Video hướng dẫn tích hợp Haravan</p>
            </div>
               

            <p  class="h3 my-3"><b>Bước 1: Tạo Ứng dụng riêng tại Haravan để lấy thông tin API</b></p>
            
            <p>Mục đích của bước này là lấy thông tin API Token. Thông tin này dùng để điền vào SePay, giúp SePay có thể chuyển trạng thái đơn hàng tại Haravan sau khi có khách thanh toán thành công.</p>
             
             
            <ul class="lh-lg">
                <li>Truy cập vào <b>giao diện Admin</b> của Haravan -> <b>Ứng dụng</b> -> chọn vào <b>Ứng dụng riêng</b> ở phía trên bên phải. Chọn vào <b>Tạo ứng dụng riêng</b></li>
                <li>Tại phần <b>Tên ứng dụng</b> bạn điền <b>Tích hợp SePay</b></li>
                <li>Tại Các quyền quản trị API, dòng <b>Đơn hàng</b> bạn chọn <b>Đọc và ghi</b>. Tất cả quyền còn lại chọn vào Không có quyền.</li>
                <li>Chọn <b>Tạo ứng dụng riêng</b>. Sau khi tạo thành công, bạn sẽ thấy thông tin API Token.</li>
            </ul>

            <p  class="h3 my-3"><b>Bước 2: Tạo tích hợp phía SePay.</b></p>
            <p>Vào <b>my.sepay.vn</b> -> <b>Tích hợp Haravan</b> -> <b>Thêm tích hợp</b></p>
            <p>Tại giao diện tạo tích hợp, điền các thông tin sau:
                <ul class="lh-lg">
                    <li><b>Đặt tên cho tích hợp</b>: Điền bất cứ tên nào. Ví dụ Tích hợp Haravan.</li>
                    <li><b>Chọn Ngân hàng</b>: Chọn tài khoản ngân hàng mà bạn muốn nhận thanh toán trên website Haravan. Thông tin ngân hàng mà bạn chọn sẽ hiển thị khi khách hàng hoàn tất đặt hàng.</li>
                    <li><b>API Token</b>: Điền API Token lấy ở bước 1</li>

                </ul>
            </p>
            <p>Bấm <b>Thêm</b>. Hệ thống sẽ hiển thị mã code tích hợp. Hãy sao chép code này và tiếp tục thực hiện bước tiếp theo.</p>
            <p  class="h3 my-3"><b>Bước 3: Thêm code tích hợp tại Haravan</b></p>
            <p>Mục đích của bước này là để hiện mã QR Code kèm thông tin thanh toán khi khách hàng chọn hình thức thanh toán là Chuyển khoản ngân hàng.</p>
            <ul class="lh-lg">
                <li>Vào <b>Haravan Admin</b> -> <b>Cấu hình</b> -> <b>Thanh toán</b></li>
                <li>Tìm đến đoạn <b>Xử lý đơn hàng</b>, tại ô <b>Nội dung thông báo thêm</b> bạn điền mã code tích hợp lấy tại SePay ở bước 2.</li>
                <li>Bấm <b>Lưu</b></li>
            </ul>
            <p  class="h3 my-3"><b>Bước 4: Thêm phương thức thanh toán tại Haravan</b></p>
            <p>Mục đích của bước này là tạo thêm phương thức thanh toán <b>Chuyển khoản ngân hàng</b> tại Haravan.</p>
            

            

            <ul class="lh-lg">
                <li>Vào <b>Haravan Admin</b> -> <b>Cấu hình</b> -> <b>Phương thức thanh toán</b></li>
                <li>Tại phần <b>Chọn phương thức thanh toán</b>, bạn chọn <b>Chuyển khoản qua ngân hàng</b></li>
                <li>Tại <b>Tên phương thức</b> điền  <b>Chuyển khoản ngân hàng (Tự động xác nhận giao dịch)</b>. Lưu ý tên phương thức phải có từ <b>chuyển khoản</b> hoặc từ <b>VietQR</b> thì website của bạn mới hiện thông tin thanh toán.</li>
                <li>Tại <b>Hướng dẫn thanh toán</b> điền <b>Dùng App ngân hàng quét mã QR Code, tự động xác nhận thanh toán trong 10 giây.</b></li>
                <li>Chọn  <b>Kích hoạt</b> để thêm phương thức thanh toán mới.</li>

            </ul>
            
            <p>Đến đây bạn đã hoàn tất tích hợp SePay với Haravan.</p>
            <h2 class="my-3">Kiểm tra các cấu hình của bạn</h2>
            <p>Để biết chúng mọi thứ có hoạt động đúng như cấu hình hay không. Bạn có thể thực hiện như sau:</p>
            <ul class="lh-lg">
                <li> Thử đặt hàng trên website Haravan của bạn.</li>
                <li>Đến bước Thanh toán bạn chọn hình thức thanh toán là Chuyển khoản ngân hàng (Tự động xác nhận giao dịch) như đã thêm ở bước 4.</li>
                <li>Nếu mã QR code hiện ra sau khi đặt hàng nghĩa là bạn đã cấu hình thành công.</li>
               
                
            </ul>
            <div class="alert alert-soft-danger mt-2" role="alert">
                <div class="d-flex align-items-baseline">
                    <div class="flex-shrink-0">
                        <i class="bi-info-circle me-1"></i>
                    </div>

                    <div class="flex-grow-1 ms-2">Lưu ý: Nếu bạn chỉnh sửa định dạng mã đơn hàng tại Haravan (mặc định là #), bạn phải tùy chỉnh lại mã thanh toán tại SePay cho trùng khớp. Ví dụ mã đơn hàng tại Haravan bắt đầu bằng <b>KL</b> thì tại cấu hình chung của SePay, Tiền tố phải đặt là <b>KL</b>.</div>
                </div>
            </div>

            <div class="text-center"><img src="assets/img/others/haravan-checkout.png" class="img-fluid"></div>

        
           
            
            
             

              <div class="my-2"><p>Đọc tiếp: <a href="woocommerce.html">Hướng dẫn tích hợp WooCommerce<i class="bi bi-chevron-right"></i></a></p></div>

        </div>
        <!-- End Content -->
    </main>
    <!-- ========== END MAIN CONTENT ========== -->

    <!-- ========== SECONDARY CONTENTS ========== -->
    <!--contact-box-html-docs-->
  <div id="contact-box-overlay"></div>
  <div id="contact-box">
      <div class="popup">
          <a href="https://m.me/117903214582465" target="_blank" class="item">
              <div class="logo">
                  <img src="../assets/img/others/fb-messenger.png" width="50%" />
              </div>
              <div class="meta">
                  <p class="title">Facebook Messenger</p>
                  <small class="description">Hỗ trợ live chat 24/7</small>
              </div>
          </a>
          <div class="divide"></div>
          <a href="tel:02873059589" class="item">
              <div class="icon" style="background-color: #22c55e; color: #fff;">
                  <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;"><path d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" fill="transparent"></path></svg>
              </div>
              <div class="meta">
                  <p class="title">Hotline</p>
                  <small class="description">Điện thoại hỗ trợ</small>
              </div>
          </a>
          <div class="divide"></div>
          <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
              <div class="logo">
                  <img src="../assets/img/others/youtube-social.png" />
              </div>
              <div class="meta">
                  <p class="title">Youtube</p>
                  <small class="description">Theo dõi video mới nhất của SePay</small>
              </div>
          </a>
          <div class="divide"></div>
          <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
              <div class="logo">
                  <img src="../assets/img/others/telegram-social.png" />
              </div>
              <div class="meta">
                  <p class="title">Telegram</p>
                  <small class="description">Nhận thông tin mới nhất từ SePay</small>
              </div>
          </a>
      </div>
      <div class="container">
          <div class="dot-ping">
              <div class="ping"></div>
              <div class="dot"></div>
          </div>
          <div class="contact-icon">
              <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;"><path fill-rule="evenodd" clip-rule="evenodd" d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z" fill="currentColor"></path></svg>
          </div>
          <span style="font-weight: bold;">Liên hệ chúng tôi</span>
      </div>
  </div>
  <!--/contact-box-html-docs-->

    <!-- Go To -->
    <a class="js-go-to go-to position-fixed" href="javascript:;" style="visibility: hidden;" data-hs-go-to-options='{
       "offsetTop": 700,
       "position": {
         "init": {
           "right": "2rem"
         },
         "show": {
           "bottom": "2rem"
         },
         "hide": {
           "bottom": "-2rem"
         }
       }
     }'>
        <i class="bi-chevron-up"></i>
    </a>
    <!-- ========== END SECONDARY CONTENTS ========== -->

    <!-- JS Global Compulsory  -->
    <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

    <!-- JS Implementing Plugins -->
    <script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
    <script src="../assets/vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js"></script>

    <script src="../assets/vendor/list.js/dist/list.min.js"></script>
    <script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>
    <script src="../assets/vendor/hs-video-bg/dist/hs-video-bg.min.js"></script>

    <!-- JS Front -->
    <script src="../assets/js/theme.min.js"></script>

    <!-- contact-box-js -->
    <script src="../assets/js/contact-box.js"></script>
    <!-- /contact-box-js -->

    <!-- JS Plugins Init. -->
    <script>
        (function () {
            // INITIALIZATION OF HEADER
            // =======================================================
            new HSHeader('#header').init()

          // INITIALIZATION OF NAV SCROLLER
            // =======================================================
            new HsNavScroller('.js-nav-scroller', {
                delay: 400,
                offset: 140
            }) 
            // INITIALIZATION OF LISTJS COMPONENT
            // =======================================================
            const docsSearch = HSCore.components.HSList.init('#docsSearch')




            // INITIALIZATION OF GO TO
            // =======================================================
            new HSGoTo('.js-go-to')
        })()

        function setCookie(cname, cvalue, exdays) {
            var d = new Date();
            d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
            var expires = "expires=" + d.toUTCString();
            document.cookie = cname + "=" + cvalue + "; " + expires + "; path=/; domain=.sepay.vn";
        }

        let urlParams = new URLSearchParams(document.location.search);

        ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content'].forEach(function(param) {
            let value = urlParams.get(param);

            if (value) {
                setCookie(param, value, 90);
            }
        });

        if (document.referrer != '' && document.cookie.indexOf('referer') === -1) {
            setCookie('referer', document.referrer, 90);
        }
    </script>
</body>

</html>