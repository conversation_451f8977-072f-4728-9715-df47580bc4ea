let contactPopupShow = false;
let contactBoxOverlay;
let contactBox
let contactPopup;

window.onload = () => {
  contactBoxOverlay = document.getElementById('contact-box-overlay');
  contactBox = document.getElementById('contact-box');
  contactPopup = document.querySelector('#contact-box .popup');

  contactBox.addEventListener('click', () => {
    if (contactPopupShow) {
      contactBoxOverlay.style.display = 'none';
      contactPopup.style.display = 'none';
    } else {
      contactBoxOverlay.style.display = 'block';
      contactPopup.style.display = 'block';
    }

    contactPopupShow = !contactPopupShow
  })

  contactBoxOverlay.addEventListener('click', () => {
    contactBoxOverlay.style.display = 'none';
    contactPopup.style.display = 'none';

    contactPopupShow = false
  })
}

window.addEventListener('keyup', (event) => {
  if (event.key === 'Escape') {
    contactBoxOverlay.style.display = 'none';
    contactPopup.style.display = 'none';

    contactPopupShow = false
  }
})