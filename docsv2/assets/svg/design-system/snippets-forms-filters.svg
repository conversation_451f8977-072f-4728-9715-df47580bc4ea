<svg width="200" height="144" viewBox="0 0 200 144" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0)">
<path d="M196 0H4C1.79086 0 0 1.79086 0 4V140C0 142.209 1.79086 144 4 144H196C198.209 144 200 142.209 200 140V4C200 1.79086 198.209 0 196 0Z" fill="#F3F4F6"/>
<g filter="url(#filter0_d)">
<path d="M27 24C27 21.7909 28.7909 20 31 20H169C171.209 20 173 21.7909 173 24V144H27V24Z" fill="white"/>
<path d="M31 20.5H169C170.933 20.5 172.5 22.067 172.5 24V143.5H27.5V24C27.5 22.067 29.067 20.5 31 20.5Z" stroke="#E7EAF3"/>
</g>
<g filter="url(#filter1_d)">
<rect x="92" y="53" width="60" height="73" rx="2" fill="white"/>
</g>
<g filter="url(#filter2_d)">
<rect x="128.012" y="115" width="19.3333" height="6" rx="1.33333" fill="#377dff"/>
</g>
<g filter="url(#filter3_d)">
<rect x="111.453" y="115" width="14" height="6" rx="1.33333" fill="white"/>
<rect x="111.786" y="115.333" width="13.3333" height="5.33333" rx="1" stroke="#D9DDEA" stroke-width="0.666667"/>
</g>
<rect x="133.345" y="117.667" width="8.66667" height="0.666667" rx="0.333333" fill="white"/>
<rect x="115.453" y="117.667" width="6" height="0.666667" rx="0.333333" fill="#D9DDEA"/>
<rect x="96" y="93" width="10" height="1" rx="0.5" fill="#D9DDEA"/>
<rect x="96.5" y="97.5" width="23" height="6" rx="0.5" stroke="#D9DDEA"/>
<rect x="124" y="93" width="10" height="1" rx="0.5" fill="#D9DDEA"/>
<rect x="124.5" y="97.5" width="23" height="6" rx="0.5" stroke="#D9DDEA"/>
<rect x="37" y="30" width="15" height="2" rx="1" fill="#D9DDEA"/>
<rect x="37.5" y="35.5" width="48" height="10" rx="1.5" stroke="#D9DDEA"/>
<rect x="96" y="82.5" width="52" height="1" rx="0.5" fill="#D9DDEA"/>
<rect x="112" y="82.5" width="20" height="1" rx="0.5" fill="#377dff"/>
<rect x="96" y="40" width="12" height="1" rx="0.5" fill="#377dff"/>
<rect x="92.5" y="35.5" width="19" height="10" rx="1.5" stroke="#377dff"/>
<rect x="118.5" y="35.5" width="19" height="10" rx="1.5" stroke="#D9DDEA"/>
<rect x="144.5" y="35.5" width="18" height="10" rx="1.5" stroke="#D9DDEA"/>
<rect x="154" y="37" width="7" height="7" rx="1" fill="#D9DDEA"/>
<path d="M0 104H200V140C200 142.209 198.209 144 196 144H4C1.79086 144 0 142.209 0 140V104Z" fill="url(#paint0_linear)"/>
<g filter="url(#filter4_d)">
<circle cx="112" cy="83" r="3" fill="white"/>
</g>
<g filter="url(#filter5_d)">
<circle cx="132" cy="83" r="3" fill="white"/>
</g>
<rect x="92" y="109" width="60" height="1" fill="#F8FAFD"/>
<rect x="96" y="73" width="2" height="4" fill="#D9DDEA"/>
<rect x="101" y="69" width="2" height="8" fill="#D9DDEA"/>
<rect x="106" y="66" width="2" height="11" fill="#D9DDEA"/>
<rect x="111" y="62" width="2" height="15" fill="#377dff"/>
<rect x="116" y="59" width="2" height="18" fill="#377dff"/>
<rect x="121" y="65" width="2" height="12" fill="#377dff"/>
<rect x="126" y="69" width="2" height="8" fill="#377dff"/>
<rect x="131" y="64" width="2" height="13" fill="#377dff"/>
<rect x="136" y="60" width="2" height="17" fill="#D9DDEA"/>
<rect x="141" y="62" width="2" height="15" fill="#D9DDEA"/>
<rect x="146" y="70" width="2" height="7" fill="#D9DDEA"/>
</g>
<defs>
<filter id="filter0_d" x="22" y="18" width="156" height="134" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="3"/>
<feGaussianBlur stdDeviation="2.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.596078 0 0 0 0 0.643137 0 0 0 0.125 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter1_d" x="84" y="50" width="76" height="89" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="5"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.596078 0 0 0 0 0.643137 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter2_d" x="126.679" y="114.333" width="22" height="8.66667" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="0.666667"/>
<feGaussianBlur stdDeviation="0.666667"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.215686 0 0 0 0 0.490196 0 0 0 0 1 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter3_d" x="110.12" y="114.333" width="16.6667" height="8.66667" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="0.666667"/>
<feGaussianBlur stdDeviation="0.666667"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.596078 0 0 0 0 0.643137 0 0 0 0.18 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter4_d" x="108" y="80" width="8" height="8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.596078 0 0 0 0 0.643137 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter5_d" x="128" y="80" width="8" height="8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.596078 0 0 0 0 0.643137 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="100" y1="154" x2="100" y2="92.5" gradientUnits="userSpaceOnUse">
<stop offset="0.256387" stop-color="#F3F4F6"/>
<stop offset="0.749415" stop-color="#F3F4F6" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0">
<rect width="200" height="144" fill="white"/>
</clipPath>
</defs>
</svg>
