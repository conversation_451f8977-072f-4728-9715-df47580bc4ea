<svg width="200" height="144" viewBox="0 0 200 144" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0)">
<path d="M196 0H4C1.79086 0 0 1.79086 0 4V140C0 142.209 1.79086 144 4 144H196C198.209 144 200 142.209 200 140V4C200 1.79086 198.209 0 196 0Z" fill="#F3F4F6"/>
<g filter="url(#filter0_d)">
<path d="M27 24C27 21.7909 28.7909 20 31 20H169C171.209 20 173 21.7909 173 24V144H27V24Z" fill="#F5F6F9"/>
<path d="M31 20.5H169C170.933 20.5 172.5 22.067 172.5 24V143.5H27.5V24C27.5 22.067 29.067 20.5 31 20.5Z" stroke="#E7EAF3"/>
</g>
<path d="M28 24C28 22.3431 29.3431 21 31 21H169C170.657 21 172 22.3431 172 24V30H28V24Z" fill="white"/>
<circle cx="37.5" cy="25.5" r="1.5" fill="#ED6B5F"/>
<circle cx="43.5" cy="25.5" r="1.5" fill="#F7D289"/>
<circle cx="49.5" cy="25.5" r="1.5" fill="#61C454"/>
<g filter="url(#filter1_d)">
<rect x="38" y="58" width="35" height="45" rx="2" fill="white"/>
</g>
<rect x="42" y="84" width="19" height="2" rx="1" fill="#D9DDEA"/>
<rect x="42" y="90" width="22" height="2" rx="1" fill="#D9DDEA"/>
<rect x="42" y="96" width="22" height="2" rx="1" fill="#D9DDEA"/>
<rect x="42" y="78" width="26" height="2" rx="1" fill="#D9DDEA"/>
<rect x="42" y="72" width="19" height="2" rx="1" fill="##377dff"/>
<rect x="42" y="62" width="5" height="5" rx="2.5" fill="#71869D"/>
<g filter="url(#filter2_d)">
<rect x="82" y="58" width="80" height="86" rx="2" fill="white"/>
</g>
<rect x="110.003" y="66.2797" width="5.40626" height="1.08125" rx="0.540626" fill="#D9DDEA"/>
<rect x="110.003" y="78.1735" width="5.40626" height="1.08125" rx="0.540626" fill="#D9DDEA"/>
<rect x="133.25" y="66.2797" width="4.86563" height="1.08125" rx="0.540626" fill="#D9DDEA"/>
<rect x="110.273" y="69.2531" width="20.0032" height="5.40626" rx="0.810939" stroke="#D9DDEA" stroke-width="0.540626"/>
<rect x="110.273" y="81.1468" width="43.2501" height="5.40626" rx="0.810939" stroke="#D9DDEA" stroke-width="0.540626"/>
<rect x="133.52" y="69.2531" width="20.0032" height="5.40626" rx="0.810939" stroke="#D9DDEA" stroke-width="0.540626"/>
<rect x="90" y="72.2266" width="11.3531" height="1.08125" rx="0.540626" fill="#D9DDEA"/>
<rect x="90" y="68.4421" width="14.5969" height="1.08125" rx="0.540626" fill="#71869D"/>
<rect x="90" y="76.0109" width="11.3531" height="1.08125" rx="0.540626" fill="#D9DDEA"/>
<rect x="110.003" y="98.1766" width="5.40626" height="1.08125" rx="0.540626" fill="#D9DDEA"/>
<rect x="110.003" y="110.07" width="5.40626" height="1.08125" rx="0.540626" fill="#D9DDEA"/>
<rect x="133.25" y="98.1766" width="4.86563" height="1.08125" rx="0.540626" fill="#D9DDEA"/>
<rect x="110.273" y="101.15" width="20.0032" height="5.40626" rx="0.810939" stroke="#D9DDEA" stroke-width="0.540626"/>
<rect x="110.273" y="113.044" width="43.2501" height="5.40626" rx="0.810939" stroke="#D9DDEA" stroke-width="0.540626"/>
<rect x="133.52" y="101.15" width="20.0032" height="5.40626" rx="0.810939" stroke="#D9DDEA" stroke-width="0.540626"/>
<rect x="90" y="104.123" width="11.3531" height="1.08125" rx="0.540626" fill="#D9DDEA"/>
<rect x="90" y="100.339" width="14.5969" height="1.08125" rx="0.540626" fill="#71869D"/>
<rect x="90" y="107.908" width="11.3531" height="1.08125" rx="0.540626" fill="#D9DDEA"/>
<rect x="28" y="30" width="144" height="1" fill="#EBEDF5"/>
<rect x="28" y="31" width="144" height="18" fill="#D9DDEA" fill-opacity="0.4"/>
<rect x="38" y="37" width="15" height="2" rx="1" fill="#71869D"/>
<rect x="38" y="41" width="21.637" height="2" rx="1" fill="#D9DDEA"/>
<path d="M0 104H200V140C200 142.209 198.209 144 196 144H4C1.79086 144 0 142.209 0 140V104Z" fill="url(#paint0_linear)"/>
</g>
<defs>
<filter id="filter0_d" x="22" y="18" width="156" height="134" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="3"/>
<feGaussianBlur stdDeviation="2.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.596078 0 0 0 0 0.643137 0 0 0 0.125 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter1_d" x="35" y="57" width="41" height="51" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.596078 0 0 0 0 0.643137 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter2_d" x="77" y="56" width="90" height="96" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="3"/>
<feGaussianBlur stdDeviation="2.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.596078 0 0 0 0 0.643137 0 0 0 0.125 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="100" y1="154" x2="100" y2="92.5" gradientUnits="userSpaceOnUse">
<stop offset="0.256387" stop-color="#F3F4F6"/>
<stop offset="0.749415" stop-color="#F3F4F6" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0">
<rect width="200" height="144" fill="white"/>
</clipPath>
</defs>
</svg>
