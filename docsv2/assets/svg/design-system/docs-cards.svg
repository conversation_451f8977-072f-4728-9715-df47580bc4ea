<svg width="200" height="144" viewBox="0 0 200 144" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0)">
<path d="M196 0H4C1.79086 0 0 1.79086 0 4V140C0 142.209 1.79086 144 4 144H196C198.209 144 200 142.209 200 140V4C200 1.79086 198.209 0 196 0Z" fill="#F8FAFD"/>
<g filter="url(#filter0_d)">
<rect x="20" y="20" width="160" height="104" rx="4" fill="white"/>
</g>
<rect x="33" y="33" width="71" height="10" rx="5" fill="#71869D" fill-opacity="0.7"/>
<rect x="146" y="109" width="21" height="8" rx="4" fill="#71869D"/>
<rect x="119" y="109" width="21" height="8" rx="4" fill="#D9DDEA"/>
<rect x="33" y="58" width="134" height="6" rx="3" fill="#D9DDEA"/>
<rect x="33" y="70" width="134" height="6" rx="3" fill="#D9DDEA"/>
<rect x="33" y="82" width="134" height="6" rx="3" fill="#D9DDEA"/>
</g>
<defs>
<filter id="filter0_d" x="-4" y="1" width="208" height="152" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="5"/>
<feGaussianBlur stdDeviation="12"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.596078 0 0 0 0 0.643137 0 0 0 0.125 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<clipPath id="clip0">
<rect width="200" height="144" fill="white"/>
</clipPath>
</defs>
</svg>
