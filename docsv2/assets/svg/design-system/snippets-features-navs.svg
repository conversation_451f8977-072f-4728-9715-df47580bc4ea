<svg width="200" height="144" viewBox="0 0 200 144" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M196 0H4C1.79086 0 0 1.79086 0 4V140C0 142.209 1.79086 144 4 144H196C198.209 144 200 142.209 200 140V4C200 1.79086 198.209 0 196 0Z" fill="#F3F4F6"/>
<path d="M33.4635 27.6822H35.037V32H35.9517V27.6822H37.5253V26.9091H33.4635V27.6822ZM38.8477 32.0771C39.4468 32.0771 39.8047 31.7962 39.9688 31.4755H39.9986V32H40.8637V29.4446C40.8637 28.4354 40.0409 28.1321 39.3125 28.1321C38.5096 28.1321 37.8932 28.4901 37.6943 29.1861L38.5345 29.3054C38.624 29.0444 38.8775 28.8207 39.3175 28.8207C39.7351 28.8207 39.9638 29.0344 39.9638 29.4098V29.4247C39.9638 29.6832 39.6929 29.6957 39.0192 29.7678C38.2785 29.8473 37.57 30.0685 37.57 30.9286C37.57 31.6793 38.1194 32.0771 38.8477 32.0771ZM39.0814 31.4158C38.706 31.4158 38.4375 31.2443 38.4375 30.9137C38.4375 30.5682 38.7383 30.424 39.141 30.3668C39.3772 30.3345 39.8495 30.2749 39.9663 30.1804V30.6303C39.9663 31.0554 39.6233 31.4158 39.0814 31.4158ZM41.8083 32H42.6932V31.3984H42.7454C42.8871 31.6768 43.1829 32.0671 43.8392 32.0671C44.739 32.0671 45.4127 31.3537 45.4127 30.0959C45.4127 28.8232 44.7191 28.1321 43.8367 28.1321C43.163 28.1321 42.8821 28.5373 42.7454 28.8132H42.7081V26.9091H41.8083V32ZM42.6907 30.0909C42.6907 29.3501 43.0089 28.8704 43.5881 28.8704C44.1872 28.8704 44.4954 29.38 44.4954 30.0909C44.4954 30.8068 44.1822 31.3288 43.5881 31.3288C43.0139 31.3288 42.6907 30.8317 42.6907 30.0909ZM49.9747 26.9091H49.0973L47.832 27.7219V28.5671L49.0227 27.8065H49.0525V32H49.9747V26.9091Z" fill="#677788"/>
<g filter="url(#filter0_d)">
<rect x="64" y="20" width="32" height="18" rx="2" fill="white"/>
</g>
<path d="M71.4713 27.6822H73.0448V32H73.9596V27.6822H75.5331V26.9091H71.4713V27.6822ZM76.8555 32.0771C77.4546 32.0771 77.8125 31.7962 77.9766 31.4755H78.0064V32H78.8715V29.4446C78.8715 28.4354 78.0487 28.1321 77.3204 28.1321C76.5174 28.1321 75.901 28.4901 75.7021 29.1861L76.5423 29.3054C76.6318 29.0444 76.8853 28.8207 77.3253 28.8207C77.7429 28.8207 77.9716 29.0344 77.9716 29.4098V29.4247C77.9716 29.6832 77.7007 29.6957 77.027 29.7678C76.2863 29.8473 75.5778 30.0685 75.5778 30.9286C75.5778 31.6793 76.1272 32.0771 76.8555 32.0771ZM77.0892 31.4158C76.7138 31.4158 76.4454 31.2443 76.4454 30.9137C76.4454 30.5682 76.7461 30.424 77.1488 30.3668C77.385 30.3345 77.8573 30.2749 77.9741 30.1804V30.6303C77.9741 31.0554 77.6311 31.4158 77.0892 31.4158ZM79.8161 32H80.701V31.3984H80.7532C80.8949 31.6768 81.1907 32.0671 81.847 32.0671C82.7468 32.0671 83.4205 31.3537 83.4205 30.0959C83.4205 28.8232 82.727 28.1321 81.8445 28.1321C81.1709 28.1321 80.89 28.5373 80.7532 28.8132H80.716V26.9091H79.8161V32ZM80.6986 30.0909C80.6986 29.3501 81.0167 28.8704 81.5959 28.8704C82.195 28.8704 82.5032 29.38 82.5032 30.0909C82.5032 30.8068 82.19 31.3288 81.5959 31.3288C81.0217 31.3288 80.6986 30.8317 80.6986 30.0909ZM85.9119 32H89.4019V31.2294H87.1846V31.1946L88.0621 30.3022C89.0514 29.3526 89.3249 28.8903 89.3249 28.3161C89.3249 27.4634 88.6313 26.8395 87.6072 26.8395C86.598 26.8395 85.8821 27.4659 85.8821 28.4329H86.7595C86.7595 27.9134 87.0877 27.5877 87.5948 27.5877C88.0795 27.5877 88.4399 27.8835 88.4399 28.3633C88.4399 28.7884 88.1814 29.0916 87.6793 29.6012L85.9119 31.3338V32Z" fill="#377dff"/>
<path d="M109.365 27.6822H110.939V32H111.854V27.6822H113.427V26.9091H109.365V27.6822ZM114.75 32.0771C115.349 32.0771 115.707 31.7962 115.871 31.4755H115.9V32H116.766V29.4446C116.766 28.4354 115.943 28.1321 115.214 28.1321C114.411 28.1321 113.795 28.4901 113.596 29.1861L114.436 29.3054C114.526 29.0444 114.779 28.8207 115.219 28.8207C115.637 28.8207 115.866 29.0344 115.866 29.4098V29.4247C115.866 29.6832 115.595 29.6957 114.921 29.7678C114.18 29.8473 113.472 30.0685 113.472 30.9286C113.472 31.6793 114.021 32.0771 114.75 32.0771ZM114.983 31.4158C114.608 31.4158 114.339 31.2443 114.339 30.9137C114.339 30.5682 114.64 30.424 115.043 30.3668C115.279 30.3345 115.751 30.2749 115.868 30.1804V30.6303C115.868 31.0554 115.525 31.4158 114.983 31.4158ZM117.71 32H118.595V31.3984H118.647C118.789 31.6768 119.085 32.0671 119.741 32.0671C120.641 32.0671 121.315 31.3537 121.315 30.0959C121.315 28.8232 120.621 28.1321 119.739 28.1321C119.065 28.1321 118.784 28.5373 118.647 28.8132H118.61V26.9091H117.71V32ZM118.593 30.0909C118.593 29.3501 118.911 28.8704 119.49 28.8704C120.089 28.8704 120.397 29.38 120.397 30.0909C120.397 30.8068 120.084 31.3288 119.49 31.3288C118.916 31.3288 118.593 30.8317 118.593 30.0909ZM125.633 32.0696C126.722 32.0696 127.515 31.4457 127.512 30.5856C127.515 29.9492 127.117 29.4918 126.404 29.3899V29.3501C126.955 29.2308 127.326 28.8207 127.323 28.2489C127.326 27.4734 126.665 26.8395 125.648 26.8395C124.659 26.8395 123.893 27.4286 123.873 28.2812H124.76C124.775 27.8537 125.173 27.5877 125.643 27.5877C126.118 27.5877 126.433 27.8761 126.431 28.3036C126.433 28.7486 126.066 29.0444 125.539 29.0444H125.089V29.7553H125.539C126.182 29.7553 126.565 30.0785 126.563 30.5384C126.565 30.9883 126.175 31.2965 125.631 31.2965C125.118 31.2965 124.723 31.0305 124.701 30.6154H123.766C123.791 31.4755 124.559 32.0696 125.633 32.0696Z" fill="#677788"/>
<path d="M147.314 27.6822H148.888V32H149.802V27.6822H151.376V26.9091H147.314V27.6822ZM152.698 32.0771C153.297 32.0771 153.655 31.7962 153.819 31.4755H153.849V32H154.714V29.4446C154.714 28.4354 153.891 28.1321 153.163 28.1321C152.36 28.1321 151.744 28.4901 151.545 29.1861L152.385 29.3054C152.475 29.0444 152.728 28.8207 153.168 28.8207C153.586 28.8207 153.814 29.0344 153.814 29.4098V29.4247C153.814 29.6832 153.543 29.6957 152.87 29.7678C152.129 29.8473 151.421 30.0685 151.421 30.9286C151.421 31.6793 151.97 32.0771 152.698 32.0771ZM152.932 31.4158C152.557 31.4158 152.288 31.2443 152.288 30.9137C152.288 30.5682 152.589 30.424 152.992 30.3668C153.228 30.3345 153.7 30.2749 153.817 30.1804V30.6303C153.817 31.0554 153.474 31.4158 152.932 31.4158ZM155.659 32H156.544V31.3984H156.596C156.738 31.6768 157.034 32.0671 157.69 32.0671C158.59 32.0671 159.263 31.3537 159.263 30.0959C159.263 28.8232 158.57 28.1321 157.687 28.1321C157.014 28.1321 156.733 28.5373 156.596 28.8132H156.559V26.9091H155.659V32ZM156.541 30.0909C156.541 29.3501 156.86 28.8704 157.439 28.8704C158.038 28.8704 158.346 29.38 158.346 30.0909C158.346 30.8068 158.033 31.3288 157.439 31.3288C156.864 31.3288 156.541 30.8317 156.541 30.0909ZM161.663 31.0554H164.091V32H164.971V31.0554H165.623V30.2972H164.971V26.9091H163.823L161.663 30.3221V31.0554ZM164.101 30.2972H162.607V30.2575L164.061 27.9531H164.101V30.2972Z" fill="#677788"/>
<g filter="url(#filter1_d)">
<rect x="24" y="47" width="152" height="77" rx="3.28767" fill="white"/>
<rect x="24.411" y="47.411" width="151.178" height="76.1781" rx="2.87671" stroke="#E7EAF3" stroke-width="0.821918"/>
</g>
<rect x="37" y="61" width="33" height="4" rx="2" fill="#71869D"/>
<rect x="37" y="70" width="35" height="4" rx="2" fill="#D9DDEA"/>
<rect x="37" y="88" width="23" height="4" rx="2" fill="#D9DDEA"/>
<rect x="76" y="70" width="50" height="4" rx="2" fill="#D9DDEA"/>
<rect x="64" y="88" width="46" height="4" rx="2" fill="#D9DDEA"/>
<rect x="130" y="70" width="32" height="4" rx="2" fill="#D9DDEA"/>
<rect x="114" y="88" width="48" height="4" rx="2" fill="#D9DDEA"/>
<rect x="37" y="79" width="60" height="4" rx="2" fill="#D9DDEA"/>
<rect x="101" y="79" width="25" height="4" rx="2" fill="#D9DDEA"/>
<rect x="130" y="79" width="17" height="4" rx="2" fill="#D9DDEA"/>
<rect x="151" y="79" width="11" height="4" rx="2" fill="#D9DDEA"/>
<rect x="37" y="97" width="35" height="4" rx="2" fill="#D9DDEA"/>
<rect x="76" y="97" width="50" height="4" rx="2" fill="#D9DDEA"/>
<rect x="130" y="97" width="32" height="4" rx="2" fill="#D9DDEA"/>
<rect x="37" y="106" width="60" height="4" rx="2" fill="#D9DDEA"/>
<rect x="101" y="106" width="25" height="4" rx="2" fill="#D9DDEA"/>
<rect x="130" y="106" width="17" height="4" rx="2" fill="#D9DDEA"/>
<rect x="151" y="106" width="11" height="4" rx="2" fill="#D9DDEA"/>
<path d="M0 104H200V140C200 142.209 198.209 144 196 144H4C1.79086 144 0 142.209 0 140V104Z" fill="url(#paint0_linear)"/>
<defs>
<filter id="filter0_d" x="62" y="19" width="36" height="22" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.596078 0 0 0 0 0.643137 0 0 0 0.18 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter1_d" x="19.8904" y="45.3562" width="160.219" height="85.2192" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="2.46575"/>
<feGaussianBlur stdDeviation="2.05479"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.596078 0 0 0 0 0.643137 0 0 0 0.125 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="100" y1="154" x2="100" y2="92.5" gradientUnits="userSpaceOnUse">
<stop offset="0.256387" stop-color="#F3F4F6"/>
<stop offset="0.749415" stop-color="#F3F4F6" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
