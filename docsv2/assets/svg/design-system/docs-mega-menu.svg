<svg width="200" height="144" viewBox="0 0 200 144" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0)">
<path d="M196 0H4C1.79086 0 0 1.79086 0 4V140C0 142.209 1.79086 144 4 144H196C198.209 144 200 142.209 200 140V4C200 1.79086 198.209 0 196 0Z" fill="#F8FAFD"/>
<g filter="url(#filter0_d)">
<rect x="20" y="48" width="160" height="66" rx="4" fill="white"/>
</g>
<rect x="20" y="31" width="30" height="9" rx="2" fill="#71869D"/>
<rect x="66" y="31" width="30" height="9" rx="2" fill="#D9DDEA"/>
<rect x="112" y="31" width="30" height="9" rx="2" fill="#D9DDEA"/>
<rect x="33" y="67" width="60" height="6" rx="3" fill="#D9DDEA"/>
<rect x="33" y="78" width="60" height="6" rx="3" fill="#D9DDEA"/>
<rect x="33" y="90" width="60" height="6" rx="3" fill="#D9DDEA"/>
<rect x="107" y="66" width="60" height="6" rx="3" fill="#D9DDEA"/>
<rect x="107" y="78" width="60" height="6" rx="3" fill="#D9DDEA"/>
<rect x="107" y="90" width="60" height="6" rx="3" fill="#D9DDEA"/>
</g>
<defs>
<filter id="filter0_d" x="-4" y="29" width="208" height="114" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="5"/>
<feGaussianBlur stdDeviation="12"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.596078 0 0 0 0 0.643137 0 0 0 0.125 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<clipPath id="clip0">
<rect width="200" height="144" fill="white"/>
</clipPath>
</defs>
</svg>
