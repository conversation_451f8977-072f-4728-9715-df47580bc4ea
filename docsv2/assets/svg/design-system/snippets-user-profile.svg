<svg width="200" height="144" viewBox="0 0 200 144" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0)">
<path d="M196 0H4C1.79086 0 0 1.79086 0 4V140C0 142.209 1.79086 144 4 144H196C198.209 144 200 142.209 200 140V4C200 1.79086 198.209 0 196 0Z" fill="#F3F4F6"/>
<g filter="url(#filter0_d)">
<path d="M27 24C27 21.7909 28.7909 20 31 20H169C171.209 20 173 21.7909 173 24V144H27V24Z" fill="white"/>
<path d="M31 20.5H169C170.933 20.5 172.5 22.067 172.5 24V143.5H27.5V24C27.5 22.067 29.067 20.5 31 20.5Z" stroke="#E7EAF3"/>
</g>
<path d="M0 104H200V140C200 142.209 198.209 144 196 144H4C1.79086 144 0 142.209 0 140V104Z" fill="url(#paint0_linear)"/>
<path d="M43 57C43 54.7909 44.7909 53 47 53H153C155.209 53 157 54.7909 157 57V133H43V57Z" fill="#D9DDEA" fill-opacity="0.5"/>
<path d="M0 93H200V129C200 131.209 198.209 133 196 133H4C1.79086 133 0 131.209 0 129V93Z" fill="url(#paint1_linear)"/>
<rect x="58" y="35" width="31.0798" height="2.58998" rx="1.29499" fill="#71869D"/>
<rect x="58" y="40.1799" width="12.9499" height="2.58998" rx="1.29499" fill="#D9DDEA"/>
<g filter="url(#filter1_d)">
<rect x="39" y="32" width="12" height="12" rx="6" fill="white"/>
</g>
<path d="M45 37.475C45.8698 37.475 46.575 36.7699 46.575 35.9C46.575 35.0302 45.8698 34.325 45 34.325C44.1301 34.325 43.425 35.0302 43.425 35.9C43.425 36.7699 44.1301 37.475 45 37.475Z" fill="#377dff"/>
<path d="M47.955 40.19C46.74 41.825 44.4375 42.1625 42.8025 40.955C42.51 40.7375 42.255 40.4825 42.0375 40.19C41.9325 40.0325 41.9175 39.83 42 39.665L42.1125 39.4325C42.375 38.8775 42.93 38.525 43.545 38.525H46.4625C47.0625 38.525 47.61 38.87 47.88 39.41L48 39.6575C48.075 39.83 48.0675 40.0325 47.955 40.19Z" fill="#377dff"/>
</g>
<defs>
<filter id="filter0_d" x="22" y="18" width="156" height="134" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="3"/>
<feGaussianBlur stdDeviation="2.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.596078 0 0 0 0 0.643137 0 0 0 0.125 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter1_d" x="37" y="31" width="16" height="16" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.596078 0 0 0 0 0.643137 0 0 0 0.18 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="100" y1="154" x2="100" y2="92.5" gradientUnits="userSpaceOnUse">
<stop offset="0.256387" stop-color="#F3F4F6"/>
<stop offset="0.749415" stop-color="#F3F4F6" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear" x1="100" y1="143" x2="100" y2="81.5" gradientUnits="userSpaceOnUse">
<stop offset="0.256387" stop-color="#F3F4F6"/>
<stop offset="0.749415" stop-color="#F3F4F6" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0">
<rect width="200" height="144" fill="white"/>
</clipPath>
</defs>
</svg>
