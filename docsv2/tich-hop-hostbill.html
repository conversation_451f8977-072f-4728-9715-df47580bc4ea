<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Required Meta Tags Always Come First -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- Title -->
    <title>Hướng dẫn tích hợp HostBill | SePay</title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />

    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">
    <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
    <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />

    <!-- CSS Implementing Plugins -->
    <link rel="stylesheet" href="../assets/vendor/bootstrap-icons/font/bootstrap-icons.css">

    <!-- CSS Front Template -->
    <link rel="stylesheet" href="../assets/css/theme.min.css">
    <link rel="stylesheet" href="../assets/css/docs.min.css">

    <!--contact-box-css-->
    <link rel="stylesheet" href="../assets/css/contact-box.css">
    <!--/contact-box-css-->

    <style>
        .docs-navbar-sidebar-aside-body {
            padding-top: 3.5rem !important;
        }

        .navbar-sidebar-aside-content img {
            margin-bottom: 1rem;
        }
    </style>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());

        gtag('config', 'G-J8DLMQTKSQ');
    </script>
</head>

<body class="navbar-sidebar-aside-lg">
    <!-- ========== HEADER ========== -->
    <header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
        <div class="container-fluid">
            <nav class="navbar-nav-wrap">
                <div class="row flex-grow-1">
                    <!-- Default Logo -->
                    <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
                        <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                            <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png"
                                alt="Logo">
                        </a>

                    </div>
                    <!-- End Default Logo -->

                    <div class="col-md px-lg-0">
                        <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
                            <div class="d-none d-md-block">


                                <!-- List Item Template -->
                                <div class="d-none">
                                    <div id="searchTemplate" class="dropdown-item">
                                        <a class="d-block link" href="#">
                                            <span class="category d-block fw-normal text-muted mb-1"></span>
                                            <span class="component text-dark"></span>
                                        </a>
                                    </div>
                                </div>
                                <!-- End List Item Template -->
                            </div>

                            <!-- Navbar -->
                            <ul class="navbar-nav p-0">
                                <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html"
                                        target="_blank">
                                        Support <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN"
                                        target="_blank">
                                        Youtube <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm"
                                        href="https://www.facebook.com/messages/t/sepay.vn" target="_blank">
                                        Facebook <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel"
                                        target="_blank">
                                        Telegram <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                </li>

                            </ul>
                            <!-- End Navbar -->
                        </div>
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Row -->
            </nav>
        </div>
    </header>
    <!-- ========== END HEADER ========== -->

    <!-- ========== MAIN CONTENT ========== -->
    <main id="content" role="main">
        <!-- Navbar -->
        <nav class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
            data-hs-nav-scroller-options='{
      "type": "vertical",
      "target": ".navbar-nav .active",
      "offset": 80
     }'>
            <!-- Navbar Toggle -->
            <button type="button" class="navbar-toggler btn btn-white d-grid w-100" data-bs-toggle="collapse"
                data-bs-target="#navbarVerticalNavMenu" aria-label="Toggle navigation" aria-expanded="false"
                aria-controls="navbarVerticalNavMenu">
                <span class="d-flex justify-content-between align-items-center">
                    <span class="h3 mb-0">Nav menu</span>

                    <span class="navbar-toggler-default">
                        <i class="bi-list"></i>
                    </span>

                    <span class="navbar-toggler-toggled">
                        <i class="bi-x"></i>
                    </span>
                </span>
            </button>
            <!-- End Navbar Toggle -->

            <!-- Navbar Collapse -->
            <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
                <div class="navbar-brand-wrapper border-end" style="height: auto;">
                    <!-- Default Logo -->
                    <div class="d-flex align-items-center mb-3">
                        <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                            <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png"
                                alt="Logo">
                        </a>

                    </div>
                    <!-- End Default Logo -->


                </div>

                <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
                    <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
                        <li class="nav-item">
                            <span class="nav-subtitle">Giới thiệu</span>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="sepay-la-gi.html">SePay là gì?</a>
                        </li>
                        <li class="nav-item my-2 mt-lg-5">
                            <span class="nav-subtitle">Gói dịch vụ</span>
                        </li>
              
                        <li class="nav-item">
                            <a class="nav-link" href="goi-theo-diem-ban">Gói theo điểm bán</a>
                        </li>
                        <li class="nav-item my-2 my-lg-5"></li>

                        <li class="nav-item">
                            <span class="nav-subtitle">Hướng dẫn chung</span>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="dang-ky-sepay.html">Đăng ký SePay</a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="them-tai-khoan-ngan-hang.html">Thêm tài khoản ngân hàng</a>
                        </li>



                        <li class="nav-item">
                            <a class="nav-link " href="xem-giao-dich.html">Xem giao dịch</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="nguoi-dung-va-phan-quyen.html">Người dùng & Phân quyền</a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="tai-khoan-phu.html">Tài khoản phụ</a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="cau-hinh-tai-khoan-ngan-hang.html">Cấu hình TK ngân hàng</a>
                        </li>

                        <li class="nav-item my-2 my-lg-5"></li>

                        <li class="nav-item">
                            <span class="nav-subtitle">Cấu hình công ty</span>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link " href="goi-dich-vu.html">Gói dịch vụ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="hoa-don-va-thanh-toan.html">Hóa đơn & thanh toán</a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link " href="cau-hinh-chung.html">Cấu hình chung</a>
                        </li>

                        <li class="nav-item my-2 my-lg-5"></li>

                        <li class="nav-item">
                            <span class="nav-subtitle">Chia sẻ biến động số dư</span>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="tich-hop-telegram.html">Tích hợp Telegram <i
                                    class="bi bi-telegram ms-2 text-info"></i></a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="tich-hop-lark-messenger.html">Tích hợp Lark Messenger <img
                                    src="assets/img/lark/lark-icon.png" class="img-fluid" style="max-height:25px"></a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="tich-hop-viber.html">Tích hợp Viber <img src="assets/img/viber/viber-icon.png" class="img-fluid rounded ms-2" style="height:25px;width:25px;"></a>
                        </li>

                        <li class="nav-item"> <a class="nav-link d-flex align-items-center"
                                href="mobile-app.html">Mobile App <i
                                    class="bi bi-phone-vibrate ms-2 fs-3 text-info"></i> </a> </li>
                                    <li class="nav-item">
                                        <a class="nav-link " href="tich-hop-loa-thanh-toan.html">Tích hợp Loa thanh toán 
                                            <svg class="mb-0 ms-1" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#377dff" class="bi bi-speaker" viewBox="0 0 16 16">
                                                <path d="M12 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1zM4 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2z"></path>
                                                <path d="M8 4.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5M8 6a2 2 0 1 0 0-4 2 2 0 0 0 0 4m0 3a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3m-3.5 1.5a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0"></path>
                                              </svg>
                                           </a>
                                      </li>
                        <li class="nav-item my-2 my-lg-5"></li>

                        <li class="nav-item">
                            <span class="nav-subtitle">Tích hợp web</span>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="tich-hop-shopify.html">Tích hợp Shopify <img class="ms-2"
                                    src="assets/img/others/shopify-icon.png" style="width:22px; height: 22px;"></a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="tich-hop-sapo.html">Tích hợp Sapo <img class="ms-2"
                                    src="assets/img/others/sapo-icon.png" style="width:18px; height: 18px;"></a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="tich-hop-haravan.html">Tích hợp Haravan <img class="ms-2"
                                    src="assets/img/others/haravan-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="tich-hop-gohighlevel.html">Tích hợp GoHighLevel <img class="ms-2" src="assets/img/others/gohighlevel-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="woocommerce.html">Tích hợp WooCommerce <img class="ms-2"
                                    src="assets/img/others/woocommerce-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="tich-hop-google-sheets.html">Tích hợp Google Sheets <img
                                    class="ms-2" src="assets/img/others/google-sheets-icon.png"
                                    style="width:22px; height: 22px;"></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="tich-hop-hostbill.html">Tích hợp HostBill <img class="ms-2"
                                    src="assets/img/others/hostbill-icon.png" style=" height: 22px;"></a>
                        </li>
                        <li class="nav-item my-2 my-lg-5"></li>

                        <li class="nav-item">
                            <span class="nav-subtitle">Lập trình & Tích hợp</span>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="tich-hop-webhooks.html">Tích hợp WebHooks</a>
                        </li>

                        <li class="nav-item ">
                            <a class="nav-link" href="lap-trinh-webhooks.html">Lập trình WebHooks</a>
                        </li>
                        <li class="nav-item ">
                            <a class="nav-link" href="gia-lap-giao-dich.html">Giả lập giao dịch</a>
                        </li>
                        <li class="nav-item ">
                            <a class="nav-link" href="tao-qr-code-vietqr-dong.html">Tạo & nhúng QR Code</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/oauth2">OAuth2</a>
                        </li>
                        <li class="nav-item my-2 my-lg-5"></li>

                        <li class="nav-item">
                            <span class="nav-subtitle">SePay API</span>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="gioi-thieu-api.html">Giới thiệu API</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="tao-api-token.html">Tạo API Token</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="api-giao-dich.html">API Giao dịch</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="api-tai-khoan-ngan-hang.html">API Tài khoản ngân hàng</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="api-va-theo-don-hang.html">API VA theo Đơn hàng</a>
                        </li>

                    </ul>
                </div>
            </div>
            <!-- End Navbar Collapse -->
        </nav>
        <!-- End Navbar -->

        <!-- Content -->
        <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">
            <div class="docs-page-header">
                <div class="row align-items-center">
                    <div class="col-sm">
                        <h1 class="docs-page-header-title">Hướng dẫn tích hợp HostBill <img src="assets/img/others/hostbill-icon.png" style="width:30px"></h1>
                        <p class="docs-page-header-text">Tích hợp SePay vào HostBill giúp xác nhận thanh toán ngay sau khi khách hàng chuyển khoản. Đơn hàng cũng sẽ chuyển sang trạng thái đã thanh toán.</p>
                    </div>
                </div>
            </div>
            <p>Nếu bạn đang sử dụng HostBill, bài viết này sẽ hướng dẫn bạn tích hợp SePay vào HostBill giúp tự động hóa việc xác nhận thanh toán qua chuyển khoản. </p>
            <div class="text-center"><a class="btn btn-primary btn-transition" href="https://sepay.vn/uploads/hostbill/sepay_v1.4.zip" download><i class="bi bi-cloud-download"></i> Tải Module HostBill SePay </a></div>
            <p class="fw-bold">Kết quả sau khi tích hợp:</p>
            <p class="fw-bold">1. Phía người dùng</p>
            <ul class="lh-lg">
                <li>Cho phép chọn ngân hàng để thanh toán</li>
                <li>Hiển thị mã QR để quét mã thanh toán</li>
                <li>Tự động thông báo nhận thanh toán thành công sau khi người dùng thanh toán</li>
            </ul>
            <p class="fw-bold">2. Phía Hostbill (admin)</p>
            <ul class="lh-lg">
                <li>Cho phép cấu hình ngân hàng nhận thanh toán</li>
                <li>Hiển thị ngân hàng thanh toán theo điều kiện số tiền</li>
                <li>Tự chuyển hóa đơn sang trạng thái đã thanh toán sau khi nhận giao dịch</li>
                <li>Và nhiều option tùy biến khác</li>
                <li>Tại SePay, có thể cấu hình để bắn thông tin giao dịch lên telegram. Tùy chọn chỉ bắn thông tin nếu giao dịch không được ghi nhận vào hostbill (không add transaction).</li>
            </ul>

            <h2 class="mb-3 mt-5">Hướng dẫn tích hợp</h2>
            <p class="h3 my-3" id="h1"><b>1. Cài đặt và cấu hình module SePay vào HostBill</b></p>
            <p class="h4 my-3" id="h1.1"><b>1.1. Cài đặt Module</b></p>
            <ul class="lh-lg">
                <li><b>Tải xuống và giải nén file module</b>: Đảm bảo bạn đã tải xuống file <code>.zip</code> của module, sau đó giải nén để sẽ thấy một thư mục <code>sepay</code> chứa file <code>class.sepay.php</code>.</li>
                <div><img style="max-height: 250px;" src="assets/img/others/hostbill/filezipmodule.png" class="img-fluid"></div>
                <li><b>Upload module vào thư mục đúng</b>: Mở source code HostBill và truy cập vào đường dẫn <code>public_html/includes/modules/Payment/</code>, sau đó sao chép và dán thư mục <code>sepay</code> đã được giải nào vào đường dẫn này, kết quả như hình bên dưới:</li>
                <div><img style="max-height: 250px;" src="assets/img/others/hostbill/filepathmodule.png" class="img-fluid"></div>
            </ul>
            <p>Sau khi hoàn tất các bước trên, <b>module SePay</b> đã được cài đặt thành công vào <b>HostBill</b>. Để kiểm tra xem module đã được cài đặt hay chưa, bạn có thể thực hiện các bước sau:</p>
            <ul class="lh-lg">
                <li><b>Truy cập trang quản trị HostBill</b>: Mở trình duyệt web, nhập địa chỉ trang quản trị HostBill của bạn, sau đó đăng nhập bằng tài khoản quản trị viên.</li>
                <li><b>Đi đến phần quản lý modules</b>: Trên thanh menu ở trang quản trị của bạn, chọn mục <code>Settings -> Modules -> Payment Modules</code> như hình bên dưới:</li>
                <div><img style="max-height: 250px;" src="assets/img/others/hostbill/howtoopenmoduleconfig.png" class="img-fluid"></div>
                <li><b>Kích hoạt module</b>: Tại giao diện <b>Payment Modules</b> bạn sẽ thấy 2 tab <b>Active (Đã kích hoạt)</b> và <b>Inactive (Chưa kích hoạt)</b>, khi module vừa được cài đặt sẽ xuất hiện ở tab <b>Inactive (Chưa kích hoạt)</b> <img style="max-height: 50px;" src="assets/img/others/hostbill/tablayoutmoduleconfig.png" class="img-fluid">, chọn tab <b>Inactive</b> và kéo xuống tìm tới <b>module SePay</b> của chúng ta:</li>
                <div><img style="max-height: 250px;" src="assets/img/others/hostbill/sepaymoduleinactive.png" class="img-fluid"></div>
            </ul>
            <p>Nếu bạn nhìn thấy module SePay xuất hiện trong danh sách <b>Inactive (Chưa kích hoạt)</b>, điều đó có nghĩa là module đã được cài đặt thành công.</p>
            <p class="h4 my-3" id="h1.2"><b>1.2. Cấu hình Module</b></p>
            <p>Để sử dụng phương thức thanh toán qua SePay trong HostBill, bạn cần thiết lập một số thông tin cần thiết để kết nối hệ thống với SePay. Các thông tin này sẽ đảm bảo HostBill có thể giao tiếp và xử lý thanh toán thông qua nền tảng của SePay.</p>
            <p class="h4 my-3" id="h1.2.1"><b>1.2.1. Kích hoạt Module</b></p>
            <p><b>Trong bước <a href="#h1.1">1.1</a></b>, chúng ta đã cài đặt module SePay vào hệ thống HostBill. Để kiểm tra, hãy truy cập vào trang quản lý <code>Payment Modules</code>. Tại đây, bạn sẽ thấy module SePay xuất hiện trong danh sách <b>Inactive (Chưa kích hoạt)</b>. Trên giao diện, nút <b>Activate (Kích hoạt)</b> sẽ hiển thị bên cạnh module này. Để kích hoạt module, chỉ cần nhấn vào nút <b>Activate</b>.</p>
            <div><img style="max-height: 250px;" src="assets/img/others/hostbill/activatemodule.png" class="img-fluid"></div>
            <p>Sau khi kích hoạt thành công, module sẽ xuất hiện ở tab <b>Active (Đã kích hoạt)</b> như minh họa trong hình dưới đây:</p>
            <div><img style="max-height: 500px;" src="assets/img/others/hostbill/sepaymoduleconfigtemp.png" class="img-fluid"></div>
            <p class="h4 my-3" id="h1.2.2"><b>1.2.2. Cấu hình Thanh toán</b></p>
            <p>Để hệ thống tự động xác nhận thanh toán, bạn cần cấu hình thông tin thanh toán trong phần quản lý của HostBill. Cụ thể:</p>
            <ul class="lh-lg">
                <li>Truy cập vào phần <b>Payment Modules</b> (ở mục <a href="#h1.2.1">1.2.1</a>)</li>
                <li>Chọn module SePay đã kích hoạt ở tab <b>Active (Đã kích hoạt)</b>.</li>
                <li>Chọn nút <b>Edit General Settings</b> để nhập thông tin cấu hình cho module.</li>
                <div><img style="max-height: 500px;" src="assets/img/others/hostbill/btnsettingmodule.png" class="img-fluid"></div>
            </ul>
            <p>Các thông tin cấu hình này cần khớp với thông tin từ hệ thống SePay (được giới thiệu chi tiết trong mục <a href="#1.2.3">1.2.3</a>). Cụ thể, từng trường thông tin sẽ được hướng dẫn cụ thể như sau:</p>
            <div><img style="max-height: 500px;" src="assets/img/others/hostbill/sepaymoduleconfigtemp.png" class="img-fluid"></div>
            <p class="h4 my-3" id="h*******"><b>*******. Trường <span class="text-danger">Module Display Name (Tên module)</span>: </b></p>
            <div><img style="max-height: 250px;" src="assets/img/others/hostbill/banknamefield.png" class="img-fluid"></div>
            <p>Đây là tên hiển thị của module, và nó sẽ xuất hiện trên trang thanh toán, trong phần lựa chọn phương thức thanh toán. Bạn cũng có thể chỉnh sửa tên này theo ý muốn để phù hợp với yêu cầu của mình.</p>
            <div><img style="max-height: 250px;" src="assets/img/others/hostbill/banknamedisplay1.png" class="img-fluid"></div>
            <p><i>Hiển thị ở giỏ hàng khi order</i></p>
            <div><img style="max-height: 250px;" src="assets/img/others/hostbill/banknamedisplay2.png" class="img-fluid"></div>
            <p><i>Hiển thị ở trang thanh toán</i></p>
            <p class="h4 my-3" id="h*******"><b>*******. Trường <span class="text-danger">Bank Name (Ngân hàng)</span>: </b></p>
            <div><img style="max-height: 250px;" src="assets/img/others/hostbill/banknamefield2.png" class="img-fluid"></div>
            <p>Trường này chứa danh sách các ngân hàng mà SePay hỗ trợ. Bạn có thể chọn một hoặc nhiều ngân hàng mà bạn muốn sử dụng để thực hiện thanh toán. Tuy nhiên, sau khi chọn ngân hàng này, bạn cần thực hiện kết nối với các ngân hàng của mình bên phía SePay để hoạt động, theo hướng dẫn chi tiết tại mục <a href="#*******">*******</a>.</p>
            <p class="h4 my-3" id="h*******"><b>*******. Trường <span class="text-danger">Bank Info (Thông tin ngân hàng chuyển khoản)</span> </b></p>
            <div><img style="max-height: 250px;" src="assets/img/others/hostbill/bankinfofield.png" class="img-fluid"></div>
            <p>Là nơi bạn nhập các thông tin liên quan đến ngân hàng để khách hàng có thể thực hiện thanh toán qua chuyển khoản. Thông tin sẽ được quy ước như sau:</p>
            <p><code>Tên_ngân_hàng|Tên_chủ_tài_khoản|Số_tài_khoản|Số_tiền_tối_thiểu_của_hoá_đơn(option)|Số_tiền_tối_đa_của_hoá_đơn(option)</code></p>
            <p>Mỗi trường thông tin được phân cách bởi dấu <code> | </code>, và thông tin của mỗi ngân hàng sẽ nằm trên một dòng riêng biệt.</p>
            <ul class="lh-lg">
                <li><code>Tên_ngân_hàng</code>: Đây là tên ngân hàng mà bạn đã chọn ở mục <b>Bank Name</b> (mục <a href="#h*******">*******</a>). Bạn cần nhập chính xác tên ngân hàng đã được lựa chọn trong mục đó.</li>
                <p>Ví dụ bạn chọn ngân hàng VietinBank ở trường <b>Bank Name</b></p>
                <div><img style="max-height: 250px;" src="assets/img/others/hostbill/banknameactive.png" class="img-fluid"></div>
                <p>thì bạn cần phải nhập tên ngân hàng đó vào trường <b>Bank Info</b></p>
                <div><img style="max-height: 250px;" src="assets/img/others/hostbill/bankinfoinputbankname.png" class="img-fluid"></div>
                <li><code>Tên_chủ_tài_khoản</code>: Tên của người sở hữu hoặc quản lý tài khoản. Đây thường là tên của cá nhân hoặc tổ chức đăng ký tài khoản dịch vụ, giúp xác định và liên kết tài khoản với chủ sở hữu của nó.</li>
                <div><img style="max-height: 250px;" src="assets/img/others/hostbill/bankinfoinputbankaccountname.png" class="img-fluid"></div>
                <li><code>Số_tài_khoản</code>: Là một dãy số duy nhất được ngân hàng cấp cho mỗi khách hàng khi mở tài khoản. Nó được sử dụng để nhận diện tài khoản của bạn trong hệ thống ngân hàng.</li>
                <p><b>Tuy nhiên</b>: Đối với các ngân hàng <b>OCB, KienLongBank, MSB, BIDV</b> vui lòng sử dụng số <b>VA</b> (<a href="https://sepay.vn/blog/tai-khoan-ngan-hang-ao-la-gi-phan-loai-va-ung-dung" target="_blank">số VA là gì?</a>) thay cho số tài khoản ngân hàng.</p>
                <div><img style="max-height: 250px;" src="assets/img/others/hostbill/bankinfoinputbanknumber.png" class="img-fluid"></div>
                <li><code>Số_tiền_tối_thiểu_của_hoá_đơn(option)</code>: Là số tiền thấp nhất của hóa đơn mà khi đạt đến hoặc vượt qua, tài khoản ngân hàng này mới được hiển thị trên trang thanh toán.</li>
                <p><b>Ví dụ</b>: Nếu bạn đặt số tiền tối thiểu cho hoá đơn là <b>10.000 VNĐ</b></p>
                <div><img style="max-height: 250px;" src="assets/img/others/hostbill/bankinfoinputminamounttest.png" class="img-fluid"></div>
                <p>thì chỉ những hoá đơn có tổng giá trị lớn hơn <b>10.000 VNĐ</b> mới hiển thị ngân hàng này. Nếu tổng giá trị hoá đơn nhỏ hơn <b>10.000 VNĐ</b>, ngân hàng sẽ không được hiển thị.</p>
                <li><code>Số_tiền_tối_đa_của_hoá_đơn(option)</code>: Là số tiền lớn nhất của hóa đơn mà ngân hàng này được phép hiển thị.</li>
                <p><b>Ví dụ</b>: Nếu bạn đặt số tiền tối đa của hơn đơn là <b>20.000.000 VNĐ</b></p>
                <div><img style="max-height: 250px;" src="assets/img/others/hostbill/bankinfoinputmaxamounttest.png" class="img-fluid"></div>
                <p>thì chỉ những hoá đơn có tổng giá trị nhỏ hơn <b>20.000.000 VNĐ</b> mới hiển thị ngân hàng này. Nếu tổng giá trị hoá đơn lớn hơn <b>20.000.000 VNĐ</b>, ngân hàng sẽ không được hiển thị.</p>
            </ul>
            <p><b class="text-danger">Lưu ý</b>: Khi lựa chọn nhiều ngân hàng, bạn cần nhập thông tin quy ước cho từng ngân hàng trên một dòng riêng biệt. Mỗi dòng sẽ tương ứng với một ngân hàng khác nhau.</p>
            <p><b>Ví dụ</b>: Nhập 3 ngân hàng như sau:</p>
            <div><img style="max-height: 250px;" src="assets/img/others/hostbill/bankinfoinputmultibank.png" class="img-fluid"></div>
            <p>Cấu hình như hình trên tương ứng với:</p>
            <ul class="lh-lg">
                <li>Đối với hoá đơn có tổng số tiền <b>từ 0 đến 19.999.999 VNĐ</b> sẽ hiển thị 2 ngân hàng <b>VietinBank</b> và <b>Agribank</b> của <b>NGUYEN VAN A</b>.</li>
                <li>Đối với hoá đơn từ <b>20.000.000 VNĐ trở lên</b> sẽ hiển thị ngân hàng <b>Techcombank</b> của <b>CONG TY TNHH ABC</b>.</li>
            </ul>
            <p class="h4 my-3" id="h*******"><b>*******. Trường <span class="text-danger">Allow adding transactions to invoice when payment amount is less than invoice amount</span>:</b></p>
            <div><img style="max-height: 250px;" src="assets/img/others/hostbill/allowamountlessthaninvoicefield.png" class="img-fluid"></div>
            <p>Khi bạn chọn trường này, điều đó có nghĩa là hệ thống sẽ cho phép thêm giao dịch vào hóa đơn ngay cả khi số tiền thanh toán thấp hơn số tiền tổng của hóa đơn.</p>
            <p class="h4 my-3" id="h*******"><b>*******. Trường <span class="text-danger">Allow adding transactions to invoice when payment amount is greater than invoice amount</span>:</b></p>
            <div><img style="max-height: 250px;" src="assets/img/others/hostbill/allowamountgreaththaninvoicefield.png" class="img-fluid"></div>
            <p>Khi bạn chọn trường này, điều đó có nghĩa là hệ thống sẽ cho phép thêm giao dịch vào hóa đơn ngay cả khi số tiền thanh toán lớn hơn số tiền tổng của hóa đơn.</p>
            <p class="h4 my-3" id="h*******"><b>*******. Trường <span class="text-danger">Payment Code Prefix</span>:</b></p>
            <div><img style="max-height: 250px;" src="assets/img/others/hostbill/paymentprefixcodefield.png" class="img-fluid"></div>
            <p>Trường này là tiền tố của nội dung chuyển khoản <b>(Điều này là bắt buộc)</b>. Trong HostBill, nội dung chuyển khoản sẽ bao gồm mã hóa đơn, và tiền tố này sẽ được thêm vào trước mã hóa đơn. Mã tiền tố này phải khớp với mã tiền tố mà bạn đã thiết lập bên phía SePay để xác nhận giao dịch (thông tin chi tiết có thể tham khảo ở mục <a href="#h*******">*******</a>).</p>
            <p>Ví dụ bạn đặt mã tiền tố là <b>HB</b>: <img style="max-height: 50px;" src="assets/img/others/hostbill/paymentprefixcodefieldex.png" class="img-fluid"> Tại thông tin thanh toán sẽ hiển thị như sau:</p>
            <div><img style="max-height: 250px;" src="assets/img/others/hostbill/bankinfodisplayex.png" class="img-fluid"></div>
            <p class="h4 my-3" id="h*******"><b>*******. Trường <span class="text-danger">Template QR (Mẫu QR)</span>:</b></p>
            <div><img style="max-height: 250px;" src="assets/img/others/hostbill/templateqrfield.png" class="img-fluid"></div>
            <p>Mẫu Template cho ảnh QR gồm 3 lựa chọn:</p>
            <div class="lh-lg row">
                <div class="text-center col-12 col-lg-4 my-3">
                    <p><b>Bao gồm Khung VietQR (compact)</b></p>
                    <div><img style="max-height: 250px;" src="assets/img/others/hostbill/qrtemplate1.png" class="img-fluid"></div>
                </div>
                <div class="text-center col-12 col-lg-4 my-3">
                    <p><b>Hiện mã QR kèm Logo V</b></p>
                    <div><img style="max-height: 250px;" src="assets/img/others/hostbill/qrtemplate2.png" class="img-fluid"></div>
                </div>
                <div class="text-center col-12 col-lg-4 my-3">
                    <p><b>Chỉ hiển thị mã QR (qronly)</b></p>
                    <div><img style="max-height: 250px;" src="assets/img/others/hostbill/qrtemplate3.png" class="img-fluid"></div>
                </div>
            </div>
            <p class="h4 my-3" id="h*******"><b>*******. Trường <span class="text-danger">API Key (Khoá chứng thực)</span>:</b></p>
            <div><img style="max-height: 250px;" src="assets/img/others/hostbill/apikeyfield.png" class="img-fluid"></div>
            <p>Khóa này là một chuỗi ngẫu nhiên mà bạn cần nhập vào <b>(bắt buộc)</b>, và nó phải khớp với cấu hình chứng thực webhook từ SePay (thông tin chi tiết về cách cấu hình này có thể được tham khảo trong mục <a href="#h*******">*******</a>).</p>
            <p class="h4 my-3" id="h*******"><b>*******. Trường <span class="text-danger">Callback URL</span>:</b></p>
            <div><img style="max-height: 250px;" src="assets/img/others/hostbill/callbackfield.png" class="img-fluid"></div>
            <p>Đây là đường dẫn mà SePay sẽ sử dụng để gọi lại hệ thống của HostBill sau khi người dùng hoàn tất giao dịch thanh toán. Mục đích của việc này là để HostBill tiếp tục xử lý giao dịch và cập nhật trạng thái của hóa đơn. Đường dẫn này sẽ được thiết lập từ phía SePay, và bạn sẽ được hướng dẫn chi tiết ở mục <a href="#h*******">*******</a>.</p>
            <p class="h4 my-3" id="h*******0"><b>*******0. Trường <span class="text-danger">Periodic invoice status check interval (s)</span></b></p>
            <div><img style="max-height: 250px;" src="assets/img/others/hostbill/timeintervalfield.png" class="img-fluid"></div>
            <p>Tính năng này cho phép người dùng điều chỉnh khoảng thời gian mà hệ thống kiểm tra trạng thái hóa đơn định kỳ. Thời gian có thể được thiết lập từ 1 giây đến 10 giây, với giá trị mặc định là 3 giây.</p>
            <p>Nếu yêu cầu hệ thống cần hoạt động nhanh hơn, bạn có thể giảm thời gian xuống. Ngược lại, nếu hệ thống cần thời gian phản hồi dài hơn để tránh quá tải, bạn có thể tăng thời gian kiểm tra.</p>
            <p class="h4 my-3" id="h*******1"><b>*******1. Các trường còn lại: <span class="text-danger">Background conversion</span>, <span class="text-danger">Force background conversion</span> và <span class="text-danger">Limit to selected currencies</span></b></p>
            <div><img style="max-height: 250px;" src="assets/img/others/hostbill/otherfielddefault.png" class="img-fluid"></div>
            <p>Các trường này là thiết lập mặc định trong HostBill dành cho các module thanh toán, liên quan đến việc cấu hình đơn vị tiền tệ. Tuy nhiên, đối với SePay, hiện tại chỉ hỗ trợ đơn vị tiền tệ VNĐ. Do đó, bạn có thể <b>bỏ qua</b> mục này khi cấu hình module SePay.</p>
            <p class="h4 my-3" id="h1.2.3"><b>1.2.3. Tạo Webhook phía SePay</b></p>
            <p>Để SePay có thể nhận giao dịch và tự động cập nhật trạng thái hóa đơn trong HostBill khi người dùng thực hiện thanh toán, bạn cần tạo một webhook từ phía SePay.</p>
            <p><b class="text-danger">Lưu ý</b>: Nếu bạn chưa có tài khoản SePay, vui lòng đăng ký tài khoản mới theo hướng dẫn <a href="https://docs.sepay.vn/dang-ky-sepay.html" target="_blank">tại đây</a>. Sau khi hoàn tất việc đăng ký tài khoản, bạn cần thêm thông tin tài khoản ngân hàng mà bạn muốn sử dụng. Hướng dẫn chi tiết về cách thêm tài khoản ngân hàng có sẵn <a href="https://docs.sepay.vn/them-tai-khoan-ngan-hang.html" target="_blank">tại đây</a>.</p>
            <p>Dưới đây là hướng dẫn chi tiết về cách tạo Webhook trên SePay để kết nối với HostBill:</p>
            <p class="h4 my-3" id="h*******"><b>*******. Tạo Webhook</b></p>
            <p><b>Bước 1</b>: Truy cập vào trang <a href="https://my.sepay.vn/" target="_blank">https://my.sepay.vn/</a> trên trình duyệt, sau đó đăng nhập bằng tài khoản của bạn (nếu chưa có tài khoản hãy tạo tài khoản). Sau khi đăng nhập thành công bạn sẽ được chuyển hướng đến giao diện trang chủ của My SePay như hình bên dưới:</p>
            <div><img style="max-height: 500px;" src="assets/img/others/hostbill/sepaydashboard.png" class="img-fluid"></div>
            <p><b>Bước 2</b>: Tại menu bên trái màn hình, chọn mục <b>Tích hợp Webhooks</b> để tiến hành tạo Webhook mới.</p>
            <div><img style="max-height: 500px;" src="assets/img/others/hostbill/webhookpath.png" class="img-fluid"></div>
            <p>Sau khi chọn mục <b>Tích hợp Webhooks</b> bạn sẽ được chuyển hướng đến trang danh sách Webhooks.</p>
            <div><img style="max-height: 500px;" src="assets/img/others/hostbill/wwebhookslayout.png" class="img-fluid"></div>
            <p><b>Bước 3</b>: Để thiết lập Webhooks mới, bạn nhấn vào nút <b>Thêm webhooks</b> <img style="max-height: 50px;" src="assets/img/others/hostbill/btnaddwbhook.png" class="img-fluid"> trên giao diện. Sau khi nhấn, một cửa sổ thiết lập (modal) sẽ xuất hiện, trong đó chứa các trường thông tin cần điền để cấu hình Webhooks như hình bên dưới:</p>
            <div><img src="assets/img/others/hostbill/formaddwwebhook.png" class="img-fluid"></div>
            <p><b class="text-danger">Lưu ý</b>: Mỗi ngân hàng sẽ phải tạo một Webhooks riêng biệt, ví dụ bên HostBill bạn thiết lập 3 ngân hàng như hình dưới thì bạn sẽ phải tạo 3 Webhooks cho các ngân hàng này:</p>
            <div><img style="max-height: 250px;" src="assets/img/others/hostbill/eachrowbankinfo.png" class="img-fluid"></div>
            <p><b>Bước 4</b>: Điền đầy đủ các thông tin, bao gồm:</p>
            <ul>
                <li><b>Đặt tên</b>: Điền tên bất kỳ, để phân biệt các webhook với nhau.</li>
                <p>Ví dụ đặt tên: <b>HostBill - Quét QR Code chuyển khoản</b></p>
                <div><img style="max-height: 250px;" src="assets/img/others/hostbill/setnamewebhooktest.png" class="img-fluid"></div>
                <li><b>Chọn sự kiện</b>: Chọn tùy chọn <b>Có tiền vào</b>. Việc chọn tùy chọn này sẽ cho phép SePay gửi thông tin về Callback URL trong HostBill mỗi khi có giao dịch <b>Có tiền vào</b>, phục vụ cho mục đích xác nhận thanh toán từ người dùng.</li>
                <div><img style="max-height: 250px;" src="assets/img/others/hostbill/setinmoneywebhook.png" class="img-fluid"></div>
                <li><b>Chọn điều kiện</b>: Chọn tài khoản ngân hàng mà bạn đã thiết lập trong HostBill tại mục <b>Bank Name</b> (mục <a href="#h*******">*******</a>). Lưu ý rằng, bạn cần kết nối tài khoản ngân hàng này với hệ thống trước khi tạo Webhooks (<a href="https://docs.sepay.vn/them-tai-khoan-ngan-hang.html">xem hướng dẫn</a>).</li>
                <li>Mục <b>Bỏ qua nếu nội dung giao dịch không có Code thanh toán?</b> hãy chọn <b>Có</b> nếu bạn chỉ muốn nhận giao dịch dựa vào tiền tố nội dung giao dịch thiết lập trên HostBill mục <a href="#*******">*******</a> (Tính năng nhận diện code thanh toán được cấu hình tại mục <a href="#*******">*******</a>). Tuy nhiên bạn có thể chọn <b>Không</b> để luôn nhận giao dịch dù nội dung không có mã tiền tố thanh toán và bạn nên tích hợp thông báo Telegram khi Webhooks thất bại (xem hướng dẫn tại mục <a href="#h2">2</a>).</li>
                <div><img style="max-height: 250px;" src="assets/img/others/hostbill/conditionwebhook.png" class="img-fluid"></div>
                <li><b>Thuộc tính WebHooks</b>: Bao gồm:</li>
                <ul class="lh-lg">
                    <li><b>Gọi đến URL</b>: Đây là địa chỉ mà SePay sẽ sử dụng để gửi thông tin về HostBill khi có giao dịch từ người dùng. Địa chỉ này được lấy từ trường Callback URL trong phần cấu hình module thanh toán SePay của HostBill, tại mục <a href="#*******">*******</a>.</li>
                    <li><b>Là WebHooks xác thực thanh toán?</b>: Chọn Đúng vì mục tiêu của chúng ta là sử dụng WebHooks để xác nhận việc thanh toán đã được thực hiện thành công.</li>
                    <li><b>Gọi lại WebHooks khi?</b>: Hiện tại, SePay hỗ trợ tính năng tự động gọi lại Webhooks trong trường hợp Callback trả về mã trạng thái <b>HTTP Status Code</b> không nằm trong phạm vi từ <code>200</code> đến <code>299</code>. Bạn có thể chọn không kích hoạt và thay vào đó, tích hợp thông báo Telegram khi mã trạng thái nằm ngoài phạm vi trên (hướng dẫn tích hợp thông báo Telegram ở mục <a href="#h2">2</a>).</li>
                </ul>
                <div><img style="max-height: 250px;" src="assets/img/others/hostbill/propswwebhook.png" class="img-fluid"></div>
                <li><b>Cấu hình chứng thực WebHooks</b>: Cấu hình chứng thực WebHooks là bước quan trọng để xác nhận tính hợp lệ của Webhook.</li>
                <p>Ở mục này hãy chọn <b>Kiểu chứng thực</b> là <b>API KEY</b> và điền vào mã khoá mà bạn đã cấu hình trên module thanh toán HostBill tại mục <a href="#h*******">*******</a>.</p>
                <p><b>Request Content type</b> vui lòng chọn <b>application/json</b>.</p>
                <div><img style="max-height: 250px;" src="assets/img/others/hostbill/authconfigwebhook.png" class="img-fluid"></div>
                <li><b>Trạng thái</b>: Chọn <b>Kích hoạt</b> để Webhook bắt đầu hoạt động ngay sau khi bạn hoàn tất việc tích hợp.</li>
            </ul>
            <p><b>Bước 5</b>: Nhấn nút Thêm <img style="max-height: 50px;" src="assets/img/others/hostbill/savewebhook.png" class="img-fluid"> trên cửa sổ để hoàn tất tích hợp.</p>
            <p>Sau khi hoàn thành tất cả các bước trên, bạn đã tích hợp thành công Webhooks. Bạn có thể kiểm tra kết quả bằng cách xem danh sách Webhooks, và nó sẽ xuất hiện như hình dưới đây:</p>
            <div><img style="max-height: 250px;" src="assets/img/others/hostbill/addwebhooksuccess.png" class="img-fluid"></div>
            <p class="h4 my-3" id="h*******"><b>*******. Cấu trúc mã thanh toán</b></p>
            <p>Tại mục <a href="#h*******">*******</a>, chúng ta đã cấu hình mã tiền tố cho nội dung chuyển khoản. Bây giờ, bước tiếp theo là thiết lập mã tiền tố này trong hệ thống SePay, để đảm bảo rằng Webhooks có thể được gửi thành công.</p>
            <p>Để cấu hình mã thanh toán, thực hiện các bước sau:</p>
            <p><b>Bước 1</b>: Trên giao diện My SePay, bạn tìm menu ở bên trái và cuộn xuống dưới cùng. Tại đây, bạn sẽ thấy phần <b>Thống kê & Cấu hình</b>. Hãy chọn mục <b>Cấu hình Công ty</b> và sau đó chọn <b>Cấu hình chung</b>, bạn sẽ được chuyển hướng đến giao diện Cấu hình chung.</p>
            <p><b>Bước 2</b>: Tại mục Cấu trúc mã thanh toán hãy nhấn nút Thêm mẫu mã mới <img style="max-height: 50px;" src="assets/img/others/hostbill/addtemplateprefix.png" class="img-fluid"> để tiến hành thêm mã mới của bạn</p>
            <div><img style="max-height: 250px;" src="assets/img/others/hostbill/addtemplaetprefixlayout.png" class="img-fluid"></div>
            <p><b>Bước 3</b>: Điền mã tiền tố mà bạn đã tạo ở mục <a href="#h*******">*******</a> trên HostBill vào ô <b>Tiền tố</b> và nhấn nút<b> Cập nhật</b>. Sau khi thực hiện bước này, bạn đã cấu hình thành công mã thanh toán trên SePay, tương ứng với mã tiền tố đã thiết lập trong HostBill.</p>
            <div><img style="max-height: 250px;" src="assets/img/others/hostbill/addtemplateprefixfull.png" class="img-fluid"></div>
            <p>Đến bước này, chúng ta đã hoàn thành việc cấu hình module xác nhận thanh toán SePay vào hệ thống HostBill và đã tạo Webhooks để nhận thông báo khi thanh toán thành công. Để kiểm tra và thử nghiệm, hãy xem tiếp phần tiếp theo.</p>
            <p class="h4 my-3" id="h1.3"><b>1.3 Thử nghiệm thanh toán</b></p>
            <p>Để kiểm tra xem mọi thứ đã hoạt động đúng theo cấu hình hay chưa, bạn có thể thực hiện các bước sau:</p>
            <p class="h4 my-3" id="h1.3.1"><b>1.3.1 Thực hiện thanh toán</b></p>
            <p><b>Bước 1</b>: Hãy thử đặt một đơn hàng trên website HostBill của bạn và chọn sản phẩm có giá trị thấp nhất để thực hiện kiểm tra.</p>
            <p>Ví dụ: Đặt một đơn hàng trên <code>NVMe VPS - P1</code> website và nhấn <b>Tiếp tục</b>:</p>
            <div><img style="max-height: 500px;" src="assets/img/others/hostbill/ordertest.png" class="img-fluid"></div>
            <p><b>Bước 2</b>: Chọn phương thức thanh toán theo tên module tại mục <a href="#h*******">*******</a>.</p>
            <div><img style="max-height: 250px;" src="assets/img/others/hostbill/choosemethodpayment.png" class="img-fluid"></div>
            <p><b>Bước 3</b>: Hệ thống sẽ hiển thị trang thanh toán hoặc chuyển hướng đến trang hóa đơn, nơi người dùng có thể thấy mục thanh toán. Giao diện của trang này sẽ phụ thuộc vào mẫu giao diện (template) bạn đang sử dụng trong HostBill.</p>
            <p>Dưới đây là ví dụ về một trang thanh toán:</p>
            <div><img style="max-height: 1000px;" src="assets/img/others/hostbill/testtemplatepayment.png" class="img-fluid"></div>
            <p>Cuối cùng, khách hàng có thể thực hiện thanh toán bằng cách quét mã QR hoặc chuyển khoản thủ công theo thông tin được cung cấp. Sau khi khách hàng hoàn tất giao dịch, hệ thống SePay sẽ tự động xử lý và gửi thông báo về HostBill thông qua đường dẫn Callback URL.</p>
            <p class="h4 my-3" id="h1.3.2"><b>1.3.2. Kiểm tra giao dịch và trạng thái Webhooks Callback</b></p>
            <p>Để kiểm tra giao dịch và trạng thái Webhooks ta thực hiện các bước sau:</p>
            <p><b>Bước 1</b>: Trên giao diện My SePay, tìm menu ở bên trái và chọn mục <b>Giao dịch</b></p>
            <div><img style="max-height: 250px;" src="assets/img/others/hostbill/menutranssepay.png" class="img-fluid"></div>
            <p>Bạn sẽ được chuyển hướng đến trang hiển thị danh sách giao dịch đã nhận được.</p>
            <div><img style="max-height: 500px;" src="assets/img/others/hostbill/transepaylayout.png" class="img-fluid"></div>
            <p><b>Bước 2</b>: Chúng ta có thể xem trạng thái Webhooks tại cột <b>Tự động</b></p>
            <div><img style="max-height: 500px;" src="assets/img/others/hostbill/transepaylayouthook.png" class="img-fluid"></div>
            <p>Nếu trạng thái của Webhooks hiển thị là <b>thành công</b>, điều này có nghĩa là giao dịch đã được xử lý thành công. Hệ thống HostBill sẽ nhận dữ liệu và tự động thêm giao dịch đó vào hóa đơn và cập nhật trạng thái của hóa đơn theo tiến trình phù hợp.</p>
            <p class="h3 my-3" id="h2"><b>2. Tích hợp thông báo Telegram khi cần xử lý giao dịch thủ công</b></p>
            <p>Trường hợp không SePay thể thêm giao dịch vào Hostbill (không thể add transaction tại Hostbill), bạn có thể cấu hình tại SePay để bắn thông báo lên nhóm Telegram, giúp kế toán có thể nắm bắt kịp thời thông tin giao dịch cần xử lý, từ đó nhanh chóng xử lý giao dịch bằng tay.</p>
            <p>Để SePay bắn thông báo lên nhóm Telegram các trường hợp không tự động hóa ghi nhận thanh toán như trên, hãy thực hiện các bước sau:</p>
            <p><b>Bước 1</b>: Trên giao diện My SePay, tìm menu ở bên trái và chọn mục <b>Tích hợp Telegram</b>, và bạn sẽ được chuyển hướng đến màn hình tích hợp.</p>
            <div><img style="max-height: 500px;" src="assets/img/others/hostbill/menusepaytelegram.png" class="img-fluid"></div>
            <p><b>Bước 2</b>: Chọn vào nút <b>Thêm tích hợp</b> <img style="max-height: 50px;" src="assets/img/others/hostbill/addtelegram.png" class="img-fluid"> ở phía trên, bên phải. Sau khi nhấn, một cửa sổ thiết lập (modal) sẽ xuất hiện, trong đó chứa các trường thông tin cần điền để cấu hình <b>Telegram</b> như hình bên dưới:</p>
            <div><img style="max-height: 1000px;" src="assets/img/others/hostbill/formaddtelegram.png" class="img-fluid"></div>
            <p><b>Bước 3</b>: Điền đầy đủ các thông tin, bao gồm:</p>
            <ul class="lh-lg">
                <li><b>Chọn sự kiện</b>: Chọn tùy chọn Có tiền vào. Việc chọn tùy chọn này sẽ cho phép nhận thông báo mỗi khi có giao dịch Có tiền vào.</li>
                <div><img style="max-height: 250px;" src="assets/img/others/hostbill/chooseeventtelegram.png" class="img-fluid"></div>
                <li><b>Cấu hình điều kiện</b>: Điều kiện để nhận được thông báo</li>
                <ul class="lh-lg">
                    <li><b>Khi tài khoản chính là</b>: Chọn tài khoản ngân hàng cần thông báo mà bạn đã thiết lập trong HostBill tại mục Bank Name (mục <a href="#h*******">*******</a>) hoặc có thể chọn tất cả</li>
                    <div><img style="max-height: 250px;" src="assets/img/others/hostbill/choosebanktelegram.png" class="img-fluid"></div>
                    <li><b>[Và] Khi trạng thái WebHooks là</b>: Chọn <b>Bỏ qua điều kiện này</b>.</li>
                    <div><img style="max-height: 250px;" src="assets/img/others/hostbill/chooseconditionwwebhook.png" class="img-fluid"></div>
                    <li><b>[Và] Khi WebHooks xác thực thanh toán</b>: Chọn <b>Thất bại</b> để nhận được thông báo khi gửi Webhooks xác thực thanh toán thất bại.</li>
                    <div><img style="max-height: 250px;" src="assets/img/others/hostbill/chooseconditionwwebhookpayment.png" class="img-fluid"></div>
                    <li><b>[Và] Khi tiền vào lớn hơn hoặc bằng</b>: Bỏ trống để nhận được tất cả thông báo.</li>
                    <div><img style="max-height: 250px;" src="assets/img/others/hostbill/amounttelegramoption.png" class="img-fluid"></div>
                    <li><b>[Và] Khi tiền vào nhỏ hơn hoặc bằng</b>: Bỏ trống để nhận được tất cả thông báo.</li>
                    <div><img style="max-height: 250px;" src="assets/img/others/hostbill/amounttelegramoptionless.png" class="img-fluid"></div>
                    <li><b>[Và] Khi nội dung thanh toán có từ</b>: Bỏ trống để nhận được tất cả thông báo.</li>
                    <div><img style="max-height: 250px;" src="assets/img/others/hostbill/contenttelegramoption.png" class="img-fluid"></div>
                    <li><b>Bỏ qua nếu nội dung thanh toán có từ</b>: Bỏ trống để nhận được tất cả thông báo.</li>
                    <div><img style="max-height: 250px;" src="assets/img/others/hostbill/contenttelegramoptionmiss.png" class="img-fluid"></div>
                </ul>
                <li><b>Cấu hình khác</b>: Bao gồm các cấu hình trên Telegram để nhân thông báo:</li>
                <ul class="lh-lg">
                    <li><b>Telegram Chat ID</b>: Là ID nhóm Telegram sẽ nhận thông báo. Để lấy được ID, bạn thực hiện như sau:</li>
                    <ul class="lh-lg">
                        <li>Bước 1: Vào phần Info của nhóm Telegram cần báo số dư</li>
                        <li>Bước 2: Chọn Add để thêm thành viên (Add Member)</li>
                        <li>Bước 3: Tìm <a href="https://t.me/autopay_telebot" target="_blank">SePay Bot</a> bằng cách gõ <code>autopay_telebot</code> vào khung tìm kiếm. Sau đó chọn thêm.</li>
                        <li>Bước 4: SePay Bot sẽ tự động chat lên nhóm Telegram để thông báo Chat ID. Bạn copy Chat ID và điền vào form tích hợp.</li>
                        <li>Bước 5: Chọn Gửi tin test, SePay Bot sẽ gửi tin nhắn test lên group Telegram.</li>
                        <li>Xem hướng dẫn chi tiết <a href="https://docs.sepay.vn/tich-hop-telegram.html#chat_id" target="_blank">tại đây</a>.</li>
                    </ul>
                    <li><b>Topic ID (Không bắt buộc)</b>: Dành cho nhóm Chat có Topic ID</li>
                    <li><b>Đặt tên cho tích hợp</b>: Bạn đặt tên bất kỳ phù hợp với mục đích.</li>
                    <li><b>Trạng thái</b>: Chọn Kích hoạt để hoạt động ngay sau khi bạn hoàn tất việc tích hợp.</li>
                </ul>
            </ul>
            <p><b>Bước 4</b>: Chọn <b>Thêm</b> <img style="max-height: 50px;" src="assets/img/others/hostbill/addformtelegramsubmit.png" class="img-fluid"> để tạo mới tích hợp.</p>
            <p><b>Bước 5</b>: Hệ thống sẽ dẫn bạn đến giao diện Tùy chỉnh <b>Nội dung chat</b> Telegram. Tại đây bạn có thể sửa lại nội dung tin nhắn Telegram theo ý mình.</p>
            <div><img style="max-height: 500px;" src="assets/img/others/hostbill/templatesendtelegram.png" class="img-fluid"></div>
            <p>Sau khi hoàn tất việc cấu hình, bạn có thể quay lại bước <a href="#h1.3">1.3</a> để kiểm tra hoạt động của hệ thống. Thực hiện thử nghiệm bằng cách cố ý nhập sai nội dung thanh toán, chẳng hạn như chuyển khoản với nội dung không chính xác. Khi đó, hệ thống sẽ gửi thông báo đến nhóm chat trên Telegram, giống như minh họa trong hình bên dưới:</p>
            <div><img style="max-height: 250px;" src="assets/img/others/hostbill/templatesendtelegramdemo.png" class="img-fluid"></div>
            <div class="my-2"><p>Đọc tiếp: <a href="tich-hop-webhooks.html">Tích hợp WebHooks<i class="bi bi-chevron-right"></i></a></p></div>
        </div>
        <!-- End Content -->
    </main>
    <!-- ========== END MAIN CONTENT ========== -->


    <!-- ========== SECONDARY CONTENTS ========== -->
    <!--contact-box-html-docs-->
    <div id="contact-box-overlay"></div>
    <div id="contact-box">
        <div class="popup">
            <a href="https://m.me/117903214582465" target="_blank" class="item">
                <div class="logo">
                    <img src="../assets/img/others/fb-messenger.png" width="50%" />
                </div>
                <div class="meta">
                    <p class="title">Facebook Messenger</p>
                    <small class="description">Hỗ trợ live chat 24/7</small>
                </div>
            </a>
            <div class="divide"></div>
            <a href="tel:02873059589" class="item">
                <div class="icon" style="background-color: #22c55e; color: #fff;">
                    <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20"
                        style="color: currentcolor;">
                        <path
                            d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" fill="transparent"></path>
                    </svg>
                </div>
                <div class="meta">
                    <p class="title">Hotline</p>
                    <small class="description">Điện thoại hỗ trợ</small>
                </div>
            </a>
            <div class="divide"></div>
            <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
                <div class="logo">
                    <img src="../assets/img/others/youtube-social.png" />
                </div>
                <div class="meta">
                    <p class="title">Youtube</p>
                    <small class="description">Theo dõi video mới nhất của SePay</small>
                </div>
            </a>
            <div class="divide"></div>
            <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
                <div class="logo">
                    <img src="../assets/img/others/telegram-social.png" />
                </div>
                <div class="meta">
                    <p class="title">Telegram</p>
                    <small class="description">Nhận thông tin mới nhất từ SePay</small>
                </div>
            </a>
        </div>
        <div class="container">
            <div class="dot-ping">
                <div class="ping"></div>
                <div class="dot"></div>
            </div>
            <div class="contact-icon">
                <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20"
                    style="color: currentcolor;">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z"
                        fill="currentColor"></path>
                </svg>
            </div>
            <span style="font-weight: bold;">Liên hệ chúng tôi</span>
        </div>
    </div>
    <!--/contact-box-html-docs-->

    <!-- Go To -->
    <a class="js-go-to go-to position-fixed" href="javascript:;" style="visibility: hidden;" data-hs-go-to-options='{
       "offsetTop": 700,
       "position": {
         "init": {
           "right": "2rem"
         },
         "show": {
           "bottom": "2rem"
         },
         "hide": {
           "bottom": "-2rem"
         }
       }
     }'>
        <i class="bi-chevron-up"></i>
    </a>
    <!-- ========== END SECONDARY CONTENTS ========== -->

    <!-- JS Global Compulsory  -->
    <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

    <!-- JS Implementing Plugins -->
    <script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
    <script src="../assets/vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js"></script>
    <script src="../assets/vendor/list.js/dist/list.min.js"></script>
    <script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>


    <!-- JS Front -->
    <script src="../assets/js/theme.min.js"></script>

    <!-- contact-box-js -->
    <script src="../assets/js/contact-box.js"></script>
    <!-- /contact-box-js -->

    <!-- JS Plugins Init. -->
    <script>
        (function () {
            // INITIALIZATION OF HEADER
            // =======================================================
            new HSHeader('#header').init()

            // INITIALIZATION OF NAV SCROLLER
            // =======================================================
            new HsNavScroller('.js-nav-scroller', {
                delay: 400,
                offset: 140
            })
            // INITIALIZATION OF LISTJS COMPONENT
            // =======================================================
            const docsSearch = HSCore.components.HSList.init('#docsSearch')




            // INITIALIZATION OF GO TO
            // =======================================================
            new HSGoTo('.js-go-to')
            
            function setCookie(cname, cvalue, exdays) {
                var d = new Date();
                d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
                var expires = "expires=" + d.toUTCString();
                document.cookie = cname + "=" + cvalue + "; " + expires + "; path=/; domain=.sepay.vn";
            }
            let urlParams = new URLSearchParams(document.location.search);
            ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content'].forEach(function(param) {
                let value = urlParams.get(param);
                if (value) {
                    setCookie(param, value, 90);
                }
            });
            if (document.referrer != '' && document.cookie.indexOf('referer') === -1) {
                setCookie('referer', document.referrer, 90);
            }
        })()
    </script>
</body>