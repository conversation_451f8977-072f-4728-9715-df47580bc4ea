<!DOCTYPE html>
<html lang="en">
<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

  <!-- Title -->
  <title>Hướng dẫn kết nối <PERSON>àng OCB cho doanh nghiệp | SePay</title>

  <!-- Favicon -->
  <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">

  <!-- CSS Implementing Plugins -->
  <link rel="stylesheet" href="../assets/vendor/bootstrap-icons/font/bootstrap-icons.css">
  <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
  <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />

  <!-- CSS Front Template -->
  <link rel="stylesheet" href="../assets/css/theme.min.css">
  <link rel="stylesheet" href="../assets/css/docs.min.css">

  <!--contact-box-css-->
  <link rel="stylesheet" href="../assets/css/contact-box.css">
  <!--/contact-box-css-->

  <style>
    .docs-navbar-sidebar-aside-body {
      padding-top: 3.5rem!important;
  }
  </style>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
  
      gtag('config', 'G-J8DLMQTKSQ');
    </script>
</head>

<body class="navbar-sidebar-aside-lg">
  <!-- ========== HEADER ========== -->
  <header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
    <div class="container-fluid">
      <nav class="navbar-nav-wrap">
        <div class="row flex-grow-1">
          <!-- Default Logo -->
          <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
              <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo">
            </a>
           
          </div>
          <!-- End Default Logo -->

          <div class="col-md px-lg-0">
            <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
              <div class="d-none d-md-block">
               

                <!-- List Item Template -->
                <div class="d-none">
                  <div id="searchTemplate" class="dropdown-item">
                    <a class="d-block link" href="#">
                      <span class="category d-block fw-normal text-muted mb-1"></span>
                      <span class="component text-dark"></span>
                    </a>
                  </div>
                </div>
                <!-- End List Item Template -->
              </div>

              <!-- Navbar -->
              <ul class="navbar-nav p-0">
                <li class="nav-item">
                  <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html" target="_blank">
                    Support <i class="bi-box-arrow-up-right ms-1"></i>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN" target="_blank">
                    Youtube <i class="bi-box-arrow-up-right ms-1"></i>
                  </a>
                </li>
                <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://www.facebook.com/messages/t/sepay.vn" target="_blank">
                                      Facebook <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                  </li>
                                  <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel" target="_blank">
                                      Telegram <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                  </li>
                
              </ul>
              <!-- End Navbar -->
            </div>
          </div>
          <!-- End Col -->
        </div>
        <!-- End Row -->
      </nav>
    </div>
  </header>
  <!-- ========== END HEADER ========== -->

  <!-- ========== MAIN CONTENT ========== -->
  <main id="content" role="main">
    <!-- Navbar -->
    <nav class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
          data-hs-nav-scroller-options='{
            "type": "vertical",
            "target": ".navbar-nav .active",
            "offset": 80
           }'>
      <!-- Navbar Toggle -->
      <button type="button" class="navbar-toggler btn btn-white d-grid w-100" data-bs-toggle="collapse" data-bs-target="#navbarVerticalNavMenu" aria-label="Toggle navigation" aria-expanded="false" aria-controls="navbarVerticalNavMenu">
        <span class="d-flex justify-content-between align-items-center">
          <span class="h3 mb-0">Nav menu</span>

          <span class="navbar-toggler-default">
            <i class="bi-list"></i>
          </span>

          <span class="navbar-toggler-toggled">
            <i class="bi-x"></i>
          </span>
        </span>
      </button>
      <!-- End Navbar Toggle -->

      <!-- Navbar Collapse -->
      <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
        <div class="navbar-brand-wrapper border-end" style="height: auto;">
          <!-- Default Logo -->
          <div class="d-flex align-items-center mb-3">
            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
              <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo">
            </a>
           
          </div>
          <!-- End Default Logo -->
      
       
        </div>

        <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
          <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
            <li class="nav-item">
              <span class="nav-subtitle">Giới thiệu</span>
            </li>
        
            <li class="nav-item">
              <a class="nav-link" href="sepay-la-gi.html">SePay là gì?</a>
            </li>
            <li class="nav-item my-2 mt-lg-5">
              <span class="nav-subtitle">Gói dịch vụ</span>
          </li>

          <li class="nav-item">
              <a class="nav-link" href="goi-theo-diem-ban">Gói theo điểm bán</a>
          </li>
            <li class="nav-item my-2 my-lg-5"></li>

            <li class="nav-item">
              <span class="nav-subtitle">Hướng dẫn chung</span>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="dang-ky-sepay.html">Đăng ký SePay</a>
            </li>

            <li class="nav-item">
              <a class="nav-link active" href="them-tai-khoan-ngan-hang.html">Thêm tài khoản ngân hàng</a>
            </li>

            

            <li class="nav-item">
                <a class="nav-link " href="xem-giao-dich.html">Xem giao dịch</a>
            </li>
            <li class="nav-item">
              <a class="nav-link " href="nguoi-dung-va-phan-quyen.html">Người dùng & Phân quyền</a>
            </li>
 
            <li class="nav-item">
              <a class="nav-link" href="tai-khoan-phu.html">Tài khoản phụ</a>
          </li>

            <li class="nav-item">
                <a class="nav-link" href="cau-hinh-tai-khoan-ngan-hang.html">Cấu hình TK ngân hàng</a>
            </li>

            <li class="nav-item my-2 my-lg-5"></li>

            <li class="nav-item">
                <span class="nav-subtitle">Cấu hình công ty</span>
            </li>

            <li class="nav-item">
                <a class="nav-link " href="goi-dich-vu.html">Gói dịch vụ</a>
            </li>
            <li class="nav-item">
                <a class="nav-link " href="hoa-don-va-thanh-toan.html">Hóa đơn & thanh toán</a>
            </li>

            <li class="nav-item">
                <a class="nav-link " href="cau-hinh-chung.html">Cấu hình chung</a>
            </li>

            <li class="nav-item my-2 my-lg-5"></li>

                        <li class="nav-item">
                            <span class="nav-subtitle">Chia sẻ biến động số dư</span>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link " href="tich-hop-telegram.html">Tích hợp Telegram <i class="bi bi-telegram ms-2 text-info"></i></a>
                          </li>
              
            <li class="nav-item">
              <a class="nav-link" href="tich-hop-lark-messenger.html">Tích hợp Lark Messenger <img src="assets/img/lark/lark-icon.png" class="img-fluid" style="max-height:25px"></a>
          </li>

                        <li class="nav-item">
                            <a class="nav-link" href="tich-hop-viber.html">Tích hợp Viber <img src="assets/img/viber/viber-icon.png" class="img-fluid rounded ms-2" style="height:25px;width:25px;"></a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link d-flex align-items-center" href="mobile-app.html">Mobile App <i class="bi bi-phone-vibrate ms-2 fs-3 text-info"></i> </a>
                        </li>
                        <li class="nav-item">
                          <a class="nav-link " href="tich-hop-loa-thanh-toan.html">Tích hợp Loa thanh toán 
                              <svg class="mb-0 ms-1" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#377dff" class="bi bi-speaker" viewBox="0 0 16 16">
                                  <path d="M12 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1zM4 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2z"></path>
                                  <path d="M8 4.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5M8 6a2 2 0 1 0 0-4 2 2 0 0 0 0 4m0 3a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3m-3.5 1.5a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0"></path>
                                </svg>
                          </a>
                        </li>
            <li class="nav-item my-2 my-lg-5"></li>

            <li class="nav-item">
                <span class="nav-subtitle">Tích hợp web</span>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="tich-hop-shopify.html">Tích hợp Shopify <img class="ms-2" src="assets/img/others/shopify-icon.png" style="width:22px; height: 22px;"></a>
            </li>
            
            <li class="nav-item">
              <a class="nav-link" href="tich-hop-sapo.html">Tích hợp Sapo <img class="ms-2" src="assets/img/others/sapo-icon.png" style="width:18px; height: 18px;"></a>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="tich-hop-haravan.html">Tích hợp Haravan <img class="ms-2" src="assets/img/others/haravan-icon.png" style="width:22px; height: 22px;"></a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="tich-hop-gohighlevel.html">Tích hợp GoHighLevel <img class="ms-2" src="assets/img/others/gohighlevel-icon.png" style="width:22px; height: 22px;"></a>
        </li>
          <li class="nav-item">
            <a class="nav-link" href="woocommerce.html">Tích hợp WooCommerce <img class="ms-2" src="assets/img/others/woocommerce-icon.png" style="width:22px; height: 22px;"></a>
        </li>
          <li class="nav-item">
            <a class="nav-link" href="tich-hop-google-sheets.html">Tích hợp Google Sheets <img class="ms-2" src="assets/img/others/google-sheets-icon.png" style="width:22px; height: 22px;"></a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="tich-hop-hostbill.html">Tích hợp HostBill <img class="ms-2"
                  src="assets/img/others/hostbill-icon.png" style="height: 22px;"></a>
      </li>
            <li class="nav-item my-2 my-lg-5"></li>

            <li class="nav-item">
              <span class="nav-subtitle">Lập trình & Tích hợp</span>
          </li>
          <li class="nav-item">
              <a class="nav-link" href="tich-hop-webhooks.html">Tích hợp WebHooks</a>
          </li>

          <li class="nav-item ">
              <a class="nav-link" href="lap-trinh-webhooks.html">Lập trình WebHooks</a>
          </li>
          <li class="nav-item ">
            <a class="nav-link" href="gia-lap-giao-dich.html">Giả lập giao dịch</a>
        </li>
        <li class="nav-item ">
          <a class="nav-link" href="tao-qr-code-vietqr-dong.html">Tạo & nhúng QR Code</a>
      </li>
      <li class="nav-item ">
        <a class="nav-link" href="/oauth2">OAuth2</a>
      </li>

 
      <li class="nav-item my-2 my-lg-5"></li>

      <li class="nav-item">
          <span class="nav-subtitle">SePay API</span>
      </li>
      <li class="nav-item">
          <a class="nav-link" href="gioi-thieu-api.html">Giới thiệu API</a>
      </li>
      <li class="nav-item">
          <a class="nav-link " href="tao-api-token.html">Tạo API Token</a>
      </li>
      <li class="nav-item">
          <a class="nav-link " href="api-giao-dich.html">API Giao dịch</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="api-tai-khoan-ngan-hang.html">API Tài khoản ngân hàng</a>
    </li>
      <li class="nav-item">
          <a class="nav-link" href="api-va-theo-don-hang.html">API VA theo Đơn hàng</a>
      </li>

      
          </ul>
        </div>
      </div>
      <!-- End Navbar Collapse -->
    </nav>
    <!-- End Navbar -->

    <!-- Content -->
    <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">
         
        <div class="docs-page-header">
            <div class="row align-items-center">
              <div class="col-sm">
                <h1 class="docs-page-header-title">Hướng dẫn kết nối tài khoản OCB vào SePay cho doanh nghiệp</h1>
                <p class="docs-page-header-text">Bài viết này sẽ hướng dẫn bạn kết nối tài khoản Ngân Hàng Phương Đông OCB vào SePay thông qua API dành cho doanh nghiệp.</p>
              </div>
            </div>
          </div>

<p>SePay chính thức hợp tác mở API doanh nghiệp cùng Ngân hàng OCB. Quý khách đã có thể nhận và quản lý giao dịch cho doanh nghiệp của mình ngay tức thì.</p>

<p>Quy trình liên kết API OCB doanh nghiệp với SePay</p>

<p><img src="assets/img/ocb-enterprise-connect/ocb-enterprise-connect-progress.png" class="img-fluid" style="max-height:500px"></p>

<p>Sau đây là các bước kết nối API doanh nghiệp Ngân hàng OCB:</p>
<p><b>Bước 1:</b> Vào my.sepay.vn -> Chọn menu <b>Ngân hàng</b> -> <b>Kết nối tài khoản</b></p>
<p class="px-3 px-sm-0"><img src="assets/img/ocb-enterprise-connect/bank-account-connect.png" class="img-fluid" style="max-height:500px; margin: 0 -2rem;"></p>
<p><b>Bước 2:</b> Giao diện API ngân hàng sẽ hiện lên. Chọn thẻ <b>Doanh nghiệp</b>, sau đó chọn kết nối tại Ngân hàng OCB dành cho doanh nghiệp.</p>
<p class="px-3 px-sm-0"><img src="assets/img/ocb-enterprise-connect/ocb-enterprise-connect.png" class="img-fluid" style="max-height:500px; margin: 0 -2rem;"></p>

<p>Tại đây sẽ có hai trường hợp:</p>
<p><img src="assets/img/ocb-enterprise-connect/ocb-enterprise-connect-2.png" class="img-fluid" style="width:500px; max-width: 100%;"></p>
<p>
  <ol>
    <li>
      <p><b>Bạn chưa mở API OCB dành cho doanh nghiệp</b>: Vui lòng chọn <b>Gửi thông tin</b>, cung cấp các thông tin cần thiết để SePay có thể liên hệ và tư vấn cho bạn. Sau khi đã mở được tài khoản OCB doanh nghiệp và quyết định đi đến liên kết tài khoản với SePay, bạn đi tiếp đến <b>bước 3</b>.</p>
    </li>
    <li>
      <p><b>Bạn đã được ngân hàng OCB mở API</b>: Chọn <b>Kết nối OCB</b> và chuyển qua <b>bước 4</b>.</p>
    </li>
  </ol>
</p>

<div class="alert alert-soft-dark" role="alert">
  <div class="d-sm-flex">
    <div class="flex-shrink-0">
      <img class="avatar avatar-xl mb-2 mb-sm-0" src="../assets/svg/illustrations/oc-megaphone.svg" alt="Image Description">
    </div>

    <div class="flex-grow-1 ms-sm-4">
      <h3>Lưu ý:</h3>
      <p class="mb-1">Từ các <b>bước 4</b> và <b>bước 5</b>, SePay sẽ ủy quyền để thực hiện giúp bạn liên kết API OCB doanh nghiệp.</p>
      <p class="mb-0">Nếu bạn muốn tự thực hiện những bước này, vui lòng liên hệ SePay để được hỗ trợ.</p>
    </div>
  </div>
</div>

<p><b>Bước 3</b>: Ký kết ủy quyền ba bên giữa OCB, SePay và khách hàng doanh nghiệp. Ở bước này, bạn cần liên hệ SePay để được tư vấn chi tiết.</p>

<p><b>Bước 4:</b> Ở bước này sẽ thực hiện liên kết tài khoản ngân hàng OCB doanh nghiệp với SePay sau khi đã mở thành công. 
<p>Điền đầy đủ chính xác các thông tin được cung cấp từ phía ngân hàng OCB, sau đó nhấn <b>Thêm</b>.</p>

<p><img src="assets/img/ocb-enterprise-connect/ocb-enterprise-connect-form.png" class="img-fluid" style="width:500px; max-width: 100%;"></p>

<p>Hệ thống sẽ chuyển hướng bạn đến giao diện quản lý tài khoản ngân hàng OCB dành cho doanh nghiệp.</p>

<p><b>Bước 5:</b> Để bắt đầu, bạn vui lòng liên hệ SePay để được cung cấp thông tin riêng truy cập OCB API Developer Portal, sau đó bạn cần liên kết một ứng dụng đã được tạo trên hệ thống của <a href="https://developer.api.ocb.com.vn/corporate/" target="_blank">OCB API Developer Portal</a>. Chọn thẻ <b>Ứng dụng</b>, sau đó nhấn vào nút <b>Thêm ứng dụng</b>.</p>

<p class="px-3 px-sm-0"><img src="assets/img/ocb-enterprise-connect/ocb-enterprise-connect-app.png?t=1733403267" class="img-fluid" style="max-height: 500px; margin: 0 -2rem;"></p>

<p>Điền chính xác các thông tin cần thiết bên dưới để thêm ứng dụng vào tài khoản, sau đó nhấn vào nút <b>Thêm</b>.</p>

<p><img src="assets/img/ocb-enterprise-connect/ocb-enterprise-connect-add-app.png" class="img-fluid" style="width: 500px; max-width: 100%;"></p>

<div class="alert alert-soft-dark" role="alert">
  <div class="d-flex align-items-baseline">
      <div class="flex-shrink-0">
          <i class="bi-info-circle me-1"></i>
      </div>
      <div class="flex-grow-1 ms-2">
        Bạn có thể nhấn vào nút <b>Thử kết nối</b> để kiểm tra thông tin kết nối trước khi thêm ứng dụng.
      </div>
  </div>
</div>

<p><b>Bước 6:</b> Bây giờ bạn có thể tạo tài khoản ảo (VA) đầu tiên bằng cách nhấp vào thẻ <b>Tài khoản ảo (VA)</b>, sau đó nhấn vào nút <b>Thêm VA</b>.</p>

<p class="px-3 px-sm-0"><img src="assets/img/ocb-enterprise-connect/ocb-enterprise-connect-app-va.png?t=1733403600" class="img-fluid" style="max-height: 500px; margin: 0 -2rem;"></p>

<p>Điền các thông tin cần thiết để tạo VA như hình bên dưới</p>

<p><img src="assets/img/ocb-enterprise-connect/ocb-enterprise-connect-add-va.png" class="img-fluid" style="width: 500px; max-width: 100%;"></p>

<p>Đến đây bạn đã hoàn tất thao tác kết nối ngân hàng OCB thông qua API dành cho doanh nghiệp. Hãy thực hiện chuyển một số tiền nhỏ đến số VA vừa tạo, bạn sẽ thấy SePay ghi nhận giao dịch tức thì.</p>

<div class="alert alert-soft-dark" role="alert" id="ho-tro-nhan-sms-tien-ra">
  <div class="d-sm-flex">
    <div class="flex-shrink-0">
      <img class="avatar avatar-xl mb-2 mb-sm-0" src="../assets/svg/illustrations/oc-megaphone.svg" alt="Image Description">
    </div>

    <div class="flex-grow-1 ms-sm-4">
      <h3>Lưu ý:</h3>
      Khi kết nối ngân hàng OCB thông qua API, mặc định SePay chỉ nhận báo có các giao dịch tiền vào từ ngân hàng OCB cung cấp. Để có thể nhận báo giao dịch tiền ra đối với tài khoản ngân hàng OCB thông qua API, bạn cần đăng ký gói dịch vụ hỗ trợ <a href="https://sepay.vn/bang-gia.html" target="_blank">SMS Banking</a> để được cấp SIM nhận SMS biến động số dư với các giao dịch tiền ra.
    </div>
  </div>
</div>

<p>Để thiết lập nhận báo giao dịch tiền ra cho tài khoản ngân hàng OCB thông qua API, bạn vào trang quản trị tài khoản ngân hàng OCB, nhấp vào nút <b>Gán SIM</b>.</p>
<p class="px-3 px-sm-1"><img src="assets/img/ocb-enterprise-connect/ocb-enterprise-sms-1.png?t=**********" class="img-fluid" style="max-height: 500px; margin: -1.5rem -2rem;"></p>
<p>Chọn SIM mà bạn muốn nhận SMS cho tài khoản ngân hàng OCB</p>
<p><img src="assets/img/ocb-enterprise-connect/ocb-enterprise-sms-2.png" class="img-fluid" style="max-width: 500px;"></p>
<p>Để SePay nhận được thông tin giao dịch tiền ra, vui lòng đăng ký số điện thoại đã chọn ở bước trên với ngân hàng OCB để nhận SMS báo tiền ra cho tài khoản ngân hàng.</p>
<div class="alert alert-soft-dark" role="alert">
  <div class="d-flex align-items-baseline">
      <div class="flex-shrink-0">
          <i class="bi-info-circle me-1"></i>
      </div>
      <div class="flex-grow-1 ms-2">
        Chỉ đăng ký số điện thoại trên nhận biến động số dư, KHÔNG đăng ký nhận OTP hoặc các dịch vụ khác của ngân hàng
      </div>
  </div>
</div>
<p>Sau khi hoàn tất, bạn có thể thử chuyển hoặc rút tiền từ tài khoản ngân hàng OCB để xem lịch sử giao dịch tiền ra tại SePay.</p>

<p>Ngoài ra, tại my.sepay.vn, bạn có thể:</p>
<ul>
    <li><a href="https://my.sepay.vn/transactions">Xem danh sách giao dịch</a></li>
    <li><a href="https://my.sepay.vn/bankaccount">Xem danh sách ngân hàng đã kết nối hoặc tạo thêm VA.</a></li>

</ul>
<p>Chúc bạn thành công!</p>


          <div class="my-2 mt-5"><p>Đọc tiếp: <a href="xem-giao-dich.html">Xem giao dịch<i class="bi bi-chevron-right"></i></a></p></div>

    </div>
    <!-- End Content -->
  </main>
  <!-- ========== END MAIN CONTENT ========== -->

  <!-- ========== SECONDARY CONTENTS ========== -->
  <!--contact-box-html-docs-->
  <div id="contact-box-overlay"></div>
  <div id="contact-box">
      <div class="popup">
          <a href="https://m.me/***************" target="_blank" class="item">
              <div class="logo">
                  <img src="../assets/img/others/fb-messenger.png" width="50%" />
              </div>
              <div class="meta">
                  <p class="title">Facebook Messenger</p>
                  <small class="description">Hỗ trợ live chat 24/7</small>
              </div>
          </a>
          <div class="divide"></div>
          <a href="tel:02873059589" class="item">
              <div class="icon" style="background-color: #22c55e; color: #fff;">
                  <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;"><path d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" fill="transparent"></path></svg>
              </div>
              <div class="meta">
                  <p class="title">Hotline</p>
                  <small class="description">Điện thoại hỗ trợ</small>
              </div>
          </a>
          <div class="divide"></div>
          <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
              <div class="logo">
                  <img src="../assets/img/others/youtube-social.png" />
              </div>
              <div class="meta">
                  <p class="title">Youtube</p>
                  <small class="description">Theo dõi video mới nhất của SePay</small>
              </div>
          </a>
          <div class="divide"></div>
          <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
              <div class="logo">
                  <img src="../assets/img/others/telegram-social.png" />
              </div>
              <div class="meta">
                  <p class="title">Telegram</p>
                  <small class="description">Nhận thông tin mới nhất từ SePay</small>
              </div>
          </a>
      </div>
      <div class="container">
          <div class="dot-ping">
              <div class="ping"></div>
              <div class="dot"></div>
          </div>
          <div class="contact-icon">
              <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;"><path fill-rule="evenodd" clip-rule="evenodd" d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z" fill="currentColor"></path></svg>
          </div>
          <span style="font-weight: bold;">Liên hệ chúng tôi</span>
      </div>
  </div>
  <!--/contact-box-html-docs-->

  <!-- Go To -->
  <a class="js-go-to go-to position-fixed" href="javascript:;" style="visibility: hidden;"
     data-hs-go-to-options='{
       "offsetTop": 700,
       "position": {
         "init": {
           "right": "2rem"
         },
         "show": {
           "bottom": "2rem"
         },
         "hide": {
           "bottom": "-2rem"
         }
       }
     }'>
    <i class="bi-chevron-up"></i>
  </a>
  <!-- ========== END SECONDARY CONTENTS ========== -->

  <!-- JS Global Compulsory  -->
  <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

  <!-- JS Implementing Plugins -->
  <script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
  <script src="../assets/vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js"></script>
  <script src="../assets/vendor/list.js/dist/list.min.js"></script>
  <script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>
  

  <!-- JS Front -->
  <script src="../assets/js/theme.min.js"></script>
  
  <!-- contact-box-js -->
  <script src="../assets/js/contact-box.js"></script>
  <!-- /contact-box-js -->

  <!-- JS Plugins Init. -->
  <script>
      (function () {
          // INITIALIZATION OF HEADER
          // =======================================================
          new HSHeader('#header').init()

          // INITIALIZATION OF NAV SCROLLER
          // =======================================================
          new HsNavScroller('.js-nav-scroller', {
              delay: 400,
              offset: 140
          })

      // INITIALIZATION OF LISTJS COMPONENT
      // =======================================================
      const docsSearch = HSCore.components.HSList.init('#docsSearch')


 

      // INITIALIZATION OF GO TO
      // =======================================================
      new HSGoTo('.js-go-to')
        })()

        function setCookie(cname, cvalue, exdays) {
            var d = new Date();
            d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
            var expires = "expires=" + d.toUTCString();
            document.cookie = cname + "=" + cvalue + "; " + expires + "; path=/; domain=.sepay.vn";
        }

        let urlParams = new URLSearchParams(document.location.search);

        ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content'].forEach(function(param) {
            let value = urlParams.get(param);

            if (value) {
                setCookie(param, value, 90);
            }
        });

        if (document.referrer != '' && document.cookie.indexOf('referer') === -1) {
            setCookie('referer', document.referrer, 90);
        }
    </script>
</body>
</html>
