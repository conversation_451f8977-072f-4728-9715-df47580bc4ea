<!DOCTYPE html>
<html lang="vi">
    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <title>Tích hợp Lark Messenger - SePay</title>
        <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet" />
        <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
        <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
        <link rel="stylesheet" href="../assets/vendor/bootstrap-icons/font/bootstrap-icons.css" />
        <link rel="stylesheet" href="../assets/css/theme.min.css" />
        <link rel="stylesheet" href="../assets/css/docs.min.css" />
        <link rel="stylesheet" href="../assets/css/contact-box.css" />
        <style>
            .docs-navbar-sidebar-aside-body {
                padding-top: 3.5rem !important;
            }
        </style>
        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                dataLayer.push(arguments);
            }
            gtag("js", new Date());

            gtag("config", "G-J8DLMQTKSQ");
        </script>
    </head>

    <body class="navbar-sidebar-aside-lg">
        <header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
            <div class="container-fluid">
                <nav class="navbar-nav-wrap">
                    <div class="row flex-grow-1">
                        <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
                            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                                <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo" />
                            </a>
                        </div>

                        <div class="col-md px-lg-0">
                            <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
                                <div class="d-none d-md-block">
                                    <div class="d-none">
                    <div id="searchTemplate" class="dropdown-item">
                        <a class="d-block link" href="#">
                            <span class="category d-block fw-normal text-muted mb-1"></span>
                            <span class="component text-dark"></span>
                        </a>
                    </div>
                                    </div>
                                </div>

                                <ul class="navbar-nav p-0">
                                    <li class="nav-item">
                    <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html" target="_blank"> Support <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                    <a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN" target="_blank"> Youtube <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                    <a class="btn btn-ghost-secondary btn-sm" href="https://www.facebook.com/messages/t/sepay.vn" target="_blank"> Facebook <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                    <a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel" target="_blank"> Telegram <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </nav>
            </div>
        </header>
        <main id="content" role="main">
            <!-- Sidebar -->
            <nav
              class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
              data-hs-nav-scroller-options='{
                "type": "vertical",
                "target": ".navbar-nav .active",
                "offset": 80
              }'
            >
              <button
                type="button"
                class="navbar-toggler btn btn-white d-grid w-100"
                data-bs-toggle="collapse"
                data-bs-target="#navbarVerticalNavMenu"
                aria-label="Toggle navigation"
                aria-expanded="false"
                aria-controls="navbarVerticalNavMenu"
              >
                <span class="d-flex justify-content-between align-items-center">
                  <span class="h3 mb-0">Menu</span>
                  <span class="navbar-toggler-default"><i class="bi-list"></i></span>
                  <span class="navbar-toggler-toggled"><i class="bi-x"></i></span>
                </span>
              </button>
          
              <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
                <div class="navbar-brand-wrapper border-end">
                  <div class="d-flex align-items-center mb-3">
                    <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="SePay Docs">
                      <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo" />
                    </a>
                  </div>
                </div>
          
                <div class="docs-navbar-sidebar-aside-body">
                  <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
                    <li class="nav-item"><span class="nav-subtitle">Giới thiệu SePay</span></li>
                    <li class="nav-item"><a class="nav-link" href="/goi-theo-diem-ban/">Tổng quan</a></li>
                    <li class="nav-item"><a class="nav-link" href="/goi-theo-diem-ban/dang-ky-goi-dich-vu.html">Đăng ký gói dịch vụ</a></li>
                    <li class="nav-item"><a class="nav-link" href="/goi-theo-diem-ban/thiet-lap-cua-hang.html">Thiết lập cửa hàng</a></li>
                    <li class="nav-item"><a class="nav-link " href="/goi-theo-diem-ban/quan-ly-nhan-vien.html">Quản lý nhân viên</a></li>
          
                    <li class="nav-item my-2 my-lg-5"></li>
          
                    <li class="nav-item"><span class="nav-subtitle">Chia sẻ biến động số dư</span></li>
                    <li class="nav-item"><a class="nav-link" href="/goi-theo-diem-ban/tich-hop-telegram.html">Telegram</a></li>
                    <li class="nav-item"><a class="nav-link active" href="/goi-theo-diem-ban/tich-hop-lark-messenger.html">Lark Messenger</a></li>
                    <li class="nav-item"><a class="nav-link" href="/goi-theo-diem-ban/tich-hop-mobile-app.html">Mobile App</a></li>
                    <li class="nav-item"><a class="nav-link" href="/goi-theo-diem-ban/tich-hop-loa-thanh-toan.html">Loa thanh toán</a></li>
                  </ul>
                </div>
              </div>
            </nav>
          
            <!-- Nội dung chính -->
            <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">
              <div style="max-width: 1200px;">
                <div class="docs-page-header">
                  <div class="row align-items-center">
                    <div class="col-sm">
                      <h1 class="docs-page-header-title">Hướng dẫn tích hợp Lark Messenger vào cửa hàng</h1>
                      <p class="docs-page-header-text mt-2">
                        Để vận hành cửa hàng hiệu quả và nhận thông báo giao dịch nhanh chóng, bạn có thể kết nối Lark Messenger với từng cửa hàng riêng. Hướng dẫn dưới đây sẽ giúp bạn thực hiện từng bước dễ dàng và nhanh chóng.
                      </p>
                    </div>
                  </div>
                </div>
            
                <h3 id="dang-ky" class="hs-docs-heading">
                  Các bước tích hợp Lark Messenger <a class="anchorjs-link" href="#dang-ky" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h3>
            
                <ul class="lh-lg">
                  <li>
                    <strong>Bước 1:</strong> Vào mục <strong>Lark Messenger</strong> tại thanh menu sidebar bên trái giao diện quản trị.
                    <br> 
                    Tại đây, bạn sẽ thấy danh sách các nhóm Lark đã tích hợp. Để thêm mới, nhấn vào nút <strong>Tích hợp ngay</strong>.
                    <br>
                    <img src="../assets/img/shop_billing/shop_lark_messenger.png" class="img-fluid mb-3" alt="Chọn tích hợp Lark Messenger">
                    <p>Tính năng này giúp bạn nhận thông tin giao dịch của cửa hàng trực tiếp qua Lark Messenger mà không cần truy cập vào hệ thống.</p>
                  </li>
            
                  <li>
                    <strong>Bước 2:</strong> Nhập các thông tin cần thiết để thiết lập tích hợp:
                    <ul>
                      <li><strong>Webhook URL:</strong> Được lấy khi bạn tạo bot tích hợp trên hệ thống Lark.</li>
                      <li><strong>Tên gợi nhớ:</strong> Đặt tên dễ nhớ và phân biệt với các nhóm khác.</li>
                    </ul>
                    <p>Sau khi nhập đầy đủ, nhấn <strong>Thử kết nối</strong> để kiểm tra kết nối trước khi chuyển sang bước tiếp theo.</p>
                    <img src="../assets/img/shop_billing/shop_lark_messenger_step1.png" class="img-fluid mb-3" alt="Nhập thông tin Lark Messenger">
                  </li>
            
                  <li>
                    <strong>Bước 3:</strong> Gán nhóm Lark Messenger cho cửa hàng:
                    <p>Sau khi thiết lập thành công, bạn sẽ được chuyển đến giao diện để chọn cửa hàng cần kết nối với nhóm Lark Messenger.</p>
                    <ul>
                      <li>Chọn cửa hàng muốn tích hợp từ danh sách có sẵn.</li>
                      <li>Có thể chọn một hoặc nhiều cửa hàng nếu cần.</li>
                    </ul>
                    <img src="../assets/img/shop_billing/shop_lark_messenger_step2.png" class="img-fluid mb-3" alt="Gán nhóm Lark Messenger cho cửa hàng">
                    <p>Nhấn <strong>Tiếp tục</strong> để hoàn tất gán nhóm cho cửa hàng.</p>
                  </li>
            
                  <li>
                    <strong>Bước 4:</strong> Cấu hình mẫu tin nhắn Lark Messenger:
                    <p>Tại bước này, bạn có thể tùy chỉnh nội dung mẫu tin nhắn gửi về nhóm hoặc giữ mẫu mặc định.</p>
                    <ul>
                      <li>Điều chỉnh thêm thông tin hiển thị trong nội dung tin nhắn nếu cần thiết.</li>
                    </ul>
                    <img src="../assets/img/shop_billing/shop_lark_messenger_step3.png" class="img-fluid mb-3" alt="Cấu hình mẫu tin nhắn">
                    <p>Nhấn <strong>Tiếp tục</strong> để lưu lại thiết lập.</p>
                  </li>
                </ul>
            
                <div class="mt-4">
                  <h3 class="hs-docs-heading">Hoàn tất và sử dụng</h3>
                  <p>
                    Sau khi hoàn tất tích hợp và gán nhóm Lark Messenger cho cửa hàng, bạn có thể quét thử mã QR hoặc gửi test message để kiểm tra kết nối.
                  </p>
                  <img src="../assets/img/shop_billing/shop_lark_messenger_step4.png" class="img-fluid mb-3" alt="Nhận thông báo Lark Messenger">
                  <p>
                    Việc kết nối riêng từng cửa hàng giúp bạn quản lý hoạt động kinh doanh hiệu quả, nhận thông báo nhanh chóng và bảo mật tốt hơn.
                  </p>
            
                  <h4 class="mt-4">Lưu ý:</h4>
                  <ul>
                    <li>Có thể tích hợp nhiều nhóm Lark Messenger cho từng cửa hàng khác nhau tùy theo nhu cầu.</li>
                    <li>Nên định kỳ kiểm tra và cập nhật thông tin nhóm để đảm bảo an toàn hệ thống.</li>
                    <li>Khi không còn sử dụng, hãy xóa hoặc ngắt kết nối nhóm Lark Messenger để tránh các rủi ro bảo mật.</li>
                  </ul>
                </div>
              </div>
            </div>
            
            
              
          </main>
          
        <div id="contact-box-overlay"></div>
        <div id="contact-box">
            <div class="popup">
                <a href="https://m.me/117903214582465" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/fb-messenger.png" width="50%" />
                    </div>
                    <div class="meta">
                        <p class="title">Facebook Messenger</p>
                        <small class="description">Hỗ trợ live chat 24/7</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="tel:02873059589" class="item">
                    <div class="icon" style="background-color: #22c55e; color: #fff;">
                        <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;">
                            <path
                                d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z"
                                stroke="currentColor"
                                stroke-width="1.5"
                                stroke-linecap="round"
                                fill="transparent"
                            ></path>
                        </svg>
                    </div>
                    <div class="meta">
                        <p class="title">Hotline</p>
                        <small class="description">Điện thoại hỗ trợ</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/youtube-social.png" />
                    </div>
                    <div class="meta">
                        <p class="title">Youtube</p>
                        <small class="description">Theo dõi video mới nhất của SePay</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/telegram-social.png" />
                    </div>
                    <div class="meta">
                        <p class="title">Telegram</p>
                        <small class="description">Nhận thông tin mới nhất từ SePay</small>
                    </div>
                </a>
            </div>
            <div class="container">
                <div class="dot-ping">
                    <div class="ping"></div>
                    <div class="dot"></div>
                </div>
                <div class="contact-icon">
                    <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;">
                        <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z"
                            fill="currentColor"
                        ></path>
                    </svg>
                </div>
                <span style="font-weight: bold;">Liên hệ chúng tôi</span>
            </div>
        </div>

        <a
            class="js-go-to go-to position-fixed"
            href="javascript:;"
            style="visibility: hidden;"
            data-hs-go-to-options='{
                "offsetTop": 700,
                "position": {
                    "init": {
                    "right": "2rem"
                    },
                    "show": {
                    "bottom": "2rem"
                    },
                    "hide": {
                    "bottom": "-2rem"
                    }
                }
                }'
        >
            <i class="bi-chevron-up"></i>
        </a>

        <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
        <script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
        <script src="../assets/vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js"></script>
        <script src="../assets/vendor/list.js/dist/list.min.js"></script>
        <script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>
        <script src="../assets/js/theme.min.js"></script>
        <script src="../assets/js/contact-box.js"></script>

        <script>
            (function () {
                new HSHeader("#header").init();

                new HsNavScroller(".js-nav-scroller", {
                    delay: 400,
                    offset: 140,
                });

                const docsSearch = HSCore.components.HSList.init("#docsSearch");

                new HSGoTo(".js-go-to");
            })();

            function setCookie(cname, cvalue, exdays) {
                var d = new Date();
                d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000);
                var expires = "expires=" + d.toUTCString();
                document.cookie = cname + "=" + cvalue + "; " + expires + "; path=/; domain=.sepay.vn";
            }

            let urlParams = new URLSearchParams(document.location.search);

            ["utm_source", "utm_medium", "utm_campaign", "utm_term", "utm_content"].forEach(function (param) {
                let value = urlParams.get(param);

                if (value) {
                    setCookie(param, value, 90);
                }
            });

            if (document.referrer != "" && document.cookie.indexOf("referer") === -1) {
                setCookie("referer", document.referrer, 90);
            }
        </script>
    </body>
</html>
