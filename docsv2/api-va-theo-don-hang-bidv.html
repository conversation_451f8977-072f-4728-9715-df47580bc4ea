<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />

        <title>API VA theo Đơn hàng | SePay</title>

		<link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />

		<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">
		<meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
		<meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />

		<link rel="stylesheet" href="../assets/css/bootstrap-icons/bootstrap-icons.css">
		<link rel="stylesheet" href="../assets/css/theme.min.css">
		<link rel="stylesheet" href="../assets/css/docs.min.css">
		<link rel="stylesheet" href="../assets/css/contact-box.css">

		<style>
			.docs-navbar-sidebar-aside-body {
				padding-top: 3.5rem !important;
			}

			.navbar-expand .nav-item:not(:last-child) {
				margin-right: 0;
			}
		</style>

		<script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
		<script>
			window.dataLayer = window.dataLayer || [];
			function gtag() {
				dataLayer.push(arguments);
			}
			gtag('js', new Date());

			gtag('config', 'G-J8DLMQTKSQ');
		</script>
    </head>

    <body class="navbar-sidebar-aside-lg">
		<header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
			<div class="container-fluid">
				<nav class="navbar-nav-wrap">
					<div class="row flex-grow-1">
						<div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
							<a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
								<img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="SePay" />
							</a>
						</div>
						<div class="col-md px-lg-0">
							<div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
								<div class="d-none d-md-block">
									<div class="d-none">
										<div id="searchTemplate" class="dropdown-item">
											<a class="d-block link" href="#">
												<span class="category d-block fw-normal text-muted mb-1"></span>
												<span class="component text-dark"></span>
											</a>
										</div>
									</div>
								</div>

								<ul class="navbar-nav p-0">
									<li class="nav-item">
										<a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html" target="_blank"> Support <i class="bi-box-arrow-up-right ms-1"></i> </a>
									</li>
									<li class="nav-item">
										<a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN" target="_blank"> Youtube <i class="bi-box-arrow-up-right ms-1"></i> </a>
									</li>
									<li class="nav-item">
										<a class="btn btn-ghost-secondary btn-sm" href="https://www.facebook.com/messages/t/sepay.vn" target="_blank"> Facebook <i class="bi-box-arrow-up-right ms-1"></i> </a>
									</li>
									<li class="nav-item">
										<a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel" target="_blank"> Telegram <i class="bi-box-arrow-up-right ms-1"></i> </a>
									</li>
								</ul>
							</div>
						</div>
					</div>
				</nav>
			</div>
		</header>

        <main id="content" role="main">
			<nav
				class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
				data-hs-nav-scroller-options='{
					"type": "vertical",
					"target": ".navbar-nav .active",
					"offset": 80
				}'
			>
				<button type="button" class="navbar-toggler btn btn-white d-grid w-100" data-bs-toggle="collapse" data-bs-target="#navbarVerticalNavMenu" aria-label="Toggle navigation" aria-expanded="false" aria-controls="navbarVerticalNavMenu">
					<span class="d-flex justify-content-between align-items-center">
						<span class="h3 mb-0">Nav menu</span>

						<span class="navbar-toggler-default">
							<i class="bi-list"></i>
						</span>

						<span class="navbar-toggler-toggled">
							<i class="bi-x"></i>
						</span>
					</span>
				</button>

				<div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
					<div class="navbar-brand-wrapper border-end" style="height: auto;">
						<div class="d-flex align-items-center mb-3">
							<a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
								<img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo" />
							</a>
						</div>
					</div>
					<div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
						<ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
							<li class="nav-item">
								<span class="nav-subtitle">Giới thiệu</span>
							</li>

							<li class="nav-item">
								<a class="nav-link" href="sepay-la-gi.html">SePay là gì?</a>
							</li>
                            <li class="nav-item my-2 mt-lg-5">
                                <span class="nav-subtitle">Gói dịch vụ</span>
                            </li>
    
                            <li class="nav-item">
                                <a class="nav-link" href="goi-theo-diem-ban">Gói theo điểm bán</a>
                            </li>
							<li class="nav-item my-2 my-lg-5"></li>

							<li class="nav-item">
								<span class="nav-subtitle">Hướng dẫn chung</span>
							</li>

							<li class="nav-item">
								<a class="nav-link" href="dang-ky-sepay.html">Đăng ký SePay</a>
							</li>

							<li class="nav-item">
								<a class="nav-link" href="them-tai-khoan-ngan-hang.html">Thêm tài khoản ngân hàng</a>
							</li>

							<li class="nav-item">
								<a class="nav-link" href="xem-giao-dich.html">Xem giao dịch</a>
							</li>
							<li class="nav-item">
								<a class="nav-link" href="nguoi-dung-va-phan-quyen.html">Người dùng & Phân quyền</a>
							</li>

							<li class="nav-item">
								<a class="nav-link" href="tai-khoan-phu.html">Tài khoản phụ</a>
							</li>

                            <li class="nav-item">
                                <a class="nav-link" href="cau-hinh-tai-khoan-ngan-hang.html">Cấu hình TK ngân hàng</a>
                            </li>

							<li class="nav-item my-2 my-lg-5"></li>

							<li class="nav-item">
								<span class="nav-subtitle">Cấu hình công ty</span>
							</li>

							<li class="nav-item">
								<a class="nav-link" href="goi-dich-vu.html">Gói dịch vụ</a>
							</li>
							<li class="nav-item">
								<a class="nav-link" href="hoa-don-va-thanh-toan.html">Hóa đơn & thanh toán</a>
							</li>

							<li class="nav-item">
								<a class="nav-link" href="cau-hinh-chung.html">Cấu hình chung</a>
							</li>

							<li class="nav-item my-2 my-lg-5"></li>

							<li class="nav-item">
								<span class="nav-subtitle">Chia sẻ biến động số dư</span>
							</li>

							<li class="nav-item">
								<a class="nav-link" href="tich-hop-telegram.html">Tích hợp Telegram <i class="bi bi-telegram ms-2 text-info"></i></a>
							</li>

							<li class="nav-item">
								<a class="nav-link" href="tich-hop-lark-messenger.html">Tích hợp Lark Messenger <img src="assets/img/lark/lark-icon.png" class="img-fluid" style="max-height: 25px;" /></a>
							</li>

                            <li class="nav-item">
                                <a class="nav-link" href="tich-hop-viber.html">Tích hợp Viber <img src="assets/img/viber/viber-icon.png" class="img-fluid rounded ms-2" style="height:25px;width:25px;"></a>
                            </li>

							<li class="nav-item">
								<a class="nav-link d-flex align-items-center" href="mobile-app.html">Mobile App <i class="bi bi-phone-vibrate ms-2 fs-3 text-info"></i> </a>
							</li>
                            <li class="nav-item">
                                <a class="nav-link " href="tich-hop-loa-thanh-toan.html">Tích hợp Loa thanh toán 
                                    <svg class="mb-0 ms-1" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#377dff" class="bi bi-speaker" viewBox="0 0 16 16">
                                        <path d="M12 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1zM4 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2z"></path>
                                        <path d="M8 4.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5M8 6a2 2 0 1 0 0-4 2 2 0 0 0 0 4m0 3a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3m-3.5 1.5a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0"></path>
                                      </svg>
                                    </a>
                              </li>
							<li class="nav-item my-2 my-lg-5"></li>

							<li class="nav-item">
								<span class="nav-subtitle">Tích hợp web</span>
							</li>

							<li class="nav-item">
								<a class="nav-link" href="tich-hop-shopify.html">Tích hợp Shopify <img class="ms-2" src="assets/img/others/shopify-icon.png" style="width: 22px; height: 22px;" /></a>
							</li>

							<li class="nav-item">
								<a class="nav-link" href="tich-hop-sapo.html">Tích hợp Sapo <img class="ms-2" src="assets/img/others/sapo-icon.png" style="width: 18px; height: 18px;" /></a>
							</li>

							<li class="nav-item">
								<a class="nav-link" href="tich-hop-haravan.html">Tích hợp Haravan <img class="ms-2" src="assets/img/others/haravan-icon.png" style="width: 22px; height: 22px;" /></a>
							</li>
                            <li class="nav-item">
                                <a class="nav-link" href="tich-hop-gohighlevel.html">Tích hợp GoHighLevel <img class="ms-2" src="assets/img/others/gohighlevel-icon.png" style="width:22px; height: 22px;"></a>
                            </li>
							<li class="nav-item">
								<a class="nav-link" href="woocommerce.html">Tích hợp WooCommerce <img class="ms-2" src="assets/img/others/woocommerce-icon.png" style="width: 22px; height: 22px;" /></a>
							</li>
							<li class="nav-item">
								<a class="nav-link" href="tich-hop-google-sheets.html">Tích hợp Google Sheets <img class="ms-2" src="assets/img/others/google-sheets-icon.png" style="width: 22px; height: 22px;" /></a>
							</li>
							<li class="nav-item">
								<a class="nav-link" href="tich-hop-hostbill.html">Tích hợp HostBill <img class="ms-2" src="assets/img/others/hostbill-icon.png" style="width: 22px; height: 22px;" /></a>
							</li>
							<li class="nav-item my-2 my-lg-5"></li>

							<li class="nav-item">
								<span class="nav-subtitle">Lập trình & Tích hợp</span>
							</li>
							<li class="nav-item">
								<a class="nav-link" href="tich-hop-webhooks.html">Tích hợp WebHooks</a>
							</li>

							<li class="nav-item">
								<a class="nav-link" href="lap-trinh-webhooks.html">Lập trình WebHooks</a>
							</li>
							<li class="nav-item">
								<a class="nav-link" href="gia-lap-giao-dich.html">Giả lập giao dịch</a>
							</li>
							<li class="nav-item">
								<a class="nav-link" href="tao-qr-code-vietqr-dong.html">Tạo & nhúng QR Code</a>
							</li>
                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2">OAuth2</a>
                            </li>

							<li class="nav-item my-2 my-lg-5"></li>

							<li class="nav-item">
								<span class="nav-subtitle">SePay API</span>
							</li>
							<li class="nav-item">
								<a class="nav-link" href="gioi-thieu-api.html">Giới thiệu API</a>
							</li>
							<li class="nav-item">
								<a class="nav-link" href="tao-api-token.html">Tạo API Token</a>
							</li>
							<li class="nav-item">
								<a class="nav-link" href="api-giao-dich.html">API Giao dịch</a>
							</li>
							<li class="nav-item">
								<a class="nav-link" href="api-tai-khoan-ngan-hang.html">API Tài khoản ngân hàng</a>
							</li>
							<li class="nav-item">
								<a class="nav-link active" href="api-va-theo-don-hang.html">API VA theo Đơn hàng</a>
							</li>
						</ul>
					</div>
				</div>
			</nav>

			<div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10" style="max-width: 1200px;">
                <div class="docs-page-header">
                    <div class="row align-items-center">
                        <div class="col-sm">
                            <h1 class="docs-page-header-title">API VA theo Đơn hàng cho BIDV doanh nghiệp</h1>
                            <p class="docs-page-header-text">API VA (Virtual Account) theo đơn hàng là giải pháp tự động hóa xác nhận thanh toán cho ngân hàng BIDV tài khoản doanh nghiệp. Thay vì sử dụng một VA cố định, mỗi đơn hàng sẽ được cấp một VA riêng với số tiền khớp chính xác.</p>
                        </div>
                    </div>
                </div>
                <h2 id="ban-co-the-lam-gi-voi-api-nay" class="hs-docs-heading">
                    Bạn có thể làm gì với API này? <a class="anchorjs-link" href="#ban-co-the-lam-gi-voi-api-nay" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                <p>SePay cho phép bạn thực hiện những truy vấn sau với VA theo đơn hàng:</p>
                <ul>
                    <li><a href="#danh-sach-don-hang">Lấy danh sách đơn hàng</a></li>
                    <li><a href="#tao-don-hang">Tạo đơn hàng mới</a></li>
                    <li><a href="#chi-tiet-don-hang">Lấy thông tin chi tiết đơn hàng</a></li>
                    <li><a href="#tao-them-va">Tạo thêm VA cho đơn hàng</a></li>
                    <li><a href="#huy-don-hang">Hủy đơn hàng</a></li>
                    <li><a href="#huy-va">Hủy VA của đơn hàng</a></li>
                </ul>
                <h2 id="bat-dau" class="hs-docs-heading">
                    Bắt đầu <a class="anchorjs-link" href="#bat-dau" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                <p>URL của API:</p>
                <pre class="rounded mb-4">
                    <code class="language-http" data-lang="http">
                        https://my.sepay.vn/userapi/bidv/{bank_account_id}
                    </code>
                </pre>
                <p>Để sử dụng API VA theo đơn hàng, bạn cần:</p>
                <ol>
                    <li>Tạo một <a href="tao-api-token.html">API Token</a> để xác thực các yêu cầu API của bạn.</li>
                    <li>
                        <p>Lấy <code>bank_account_id</code> của tài khoản BIDV doanh nghiệp từ <a href="api-tai-khoan-ngan-hang.html">API Tài khoản ngân hàng</a> hoặc lấy trực tiếp từ giao diện quản lý tài khoản ngân hàng trên SePay.</p>
                        <img src="assets/img/api-va-theo-don-hang/bank-account-id.png" alt="Lấy bank_account_id" class="img-fluid rounded mb-4" />
                    </li>
                </ol>
                <h2 id="thanh-toan-tung-phan" class="hs-docs-heading">
                    Thanh toán từng phần (Partially Payment) <a class="anchorjs-link" href="#thanh-toan-tung-phan" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                <p>SePay hỗ trợ thanh toán từng phần cho đơn hàng thông qua cơ chế Virtual Account (VA). Khi sử dụng chức năng này:</p>
                <ul>
                    <li>Bạn có thể tạo đơn hàng với số tiền đầy đủ, nhưng cho phép khách hàng thanh toán theo nhiều đợt bằng cách:
                        <ul>
                            <li>Thiết lập <code>amount</code> nhỏ hơn số tiền đơn hàng khi tạo VA mới</li>
                            <li>Đặt <code>amount</code> là <code>null</code> để VA có thể nhận nhiều lần thanh toán với bất kỳ số tiền nào cho đến khi đạt tổng số tiền đơn hàng</li>
                        </ul>
                    </li>
                    <li>
                        Khi khách hàng thanh toán một phần, trạng thái đơn hàng sẽ chuyển thành <code>Partially</code>
                    </li>
                    <li>
                        Khi tổng số tiền thanh toán đạt đủ số tiền đơn hàng, trạng thái đơn hàng sẽ tự động chuyển thành <code>Paid</code> và VA sẽ không còn nhận thêm thanh toán
                    </li>
                </ul>
                <p>Tính năng này đặc biệt hữu ích cho các đơn hàng có giá trị lớn hoặc khi khách hàng cần thanh toán theo đợt.</p>
                <h2 id="danh-sach-don-hang" class="hs-docs-heading">
                    Lấy danh sách đơn hàng <a class="anchorjs-link" href="#danh-sach-don-hang" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                <div class="mb-3">
                    <span class="badge bg-soft-success text-success py-2 px-3 me-2">GET</span>
                    <code>/orders</code>
                </div>
                <p>Lấy danh sách các đơn hàng đã được tạo với phân trang.</p>
                <p>Ví dụ:</p>
                <pre class="rounded mb-4">
                    <code class="language-http" data-lang="http">
                        GET https://my.sepay.vn/userapi/bidv/123456/orders
                        Authorization: Bearer {token}

                        HTTP/1.1 200 OK
                        Content-Type: application/json
                        {
                            "status": "success",
                            "data": {
                                "orders": [
                                    {
                                        "id": "b64247d3-c343-11ef-9c27-52c7e9b4f41b",
                                        "order_code": "ORD123456789",
                                        "amount": 2000,
                                        "paid_amount": 2000,
                                        "status": "Paid",
                                        "created_at": "2024-12-26 11:41:46",
                                        "bank_name": "BIDV",
                                        "account_number": "**********",
                                        "account_holder_name": "NGO QUOC DAT",
                                        "va": [
                                            {
                                                "va_number": "963NQDORDRSIKYXYPTZ",
                                                "va_holder_name": "NGO QUOC DAT",
                                                "amount": 2000,
                                                "status": "Paid",
                                                "expired_at": "2024-12-26 11:51:45",
                                                "paid_at": "2024-12-26 11:42:12"
                                            }
                                        ]
                                    }
                                ],
                                "pagination": {
                                    "total": 1,
                                    "per_page": 20,
                                    "current_page": 1,
                                    "last_page": 1
                                }
                            }
                        }
                    </code>
                </pre>
                <h2 id="tao-don-hang" class="hs-docs-heading">
                    Tạo đơn hàng mới <a class="anchorjs-link" href="#tao-don-hang" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                <div class="mb-3">
                    <span class="badge bg-soft-warning text-warning py-2 px-3 me-2">POST</span>
                    <code>/orders</code>
                </div>
                <p>Tạo một đơn hàng mới.</p>
                <div class="card mb-5">
                    <div class="table-responsive">
                        <table class="table">
                            <thead class="thead-light">
                                <tr>
                                    <th>Thuộc tính</th>
                                    <th>Mô tả</th>
                                    <th>Kiểu</th>
                                    <th>Bắt buộc</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>amount</td>
                                    <td>Số tiền đơn hàng, set là null để đơn hàng không hạn chế số tiền thanh toán</td>
                                    <td>number hoặc null</td>
                                    <td>Có</td>
                                </tr>
                                <tr>
                                    <td>order_code</td>
                                    <td>Mã đơn hàng</td>
                                    <td>string</td>
                                    <td>Không</td>
                                </tr>
                                <tr>
                                    <td>duration</td>
                                    <td>
                                        Thời gian hiệu lực của đơn hàng (tính bằng giây), set là null để đơn hàng không hạn chế thời gian thanh toán<br>
                                        - Mặc định là 600 giây (10 phút)<br>
                                        - Tối đa là 31536000 giây (1 năm)
                                    </td>
                                    <td>number hoặc null</td>
                                    <td>Không</td>
                                </tr>
                                <tr>
                                    <td>va_holder_name</td>
                                    <td>Tên chủ tài khoản VA (chỉ chấp nhận chữ in hoa, số và khoảng trắng, tối đa 70 ký tự)</td>
                                    <td>string</td>
                                    <td>Không</td>
                                </tr>
                                <tr>
                                    <td>with_qrcode</td>
                                    <td>Yêu cầu tạo QR Code</td>
                                    <td>boolean</td>
                                    <td>Không</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="alert alert-warning" role="alert">
                    <strong>Lưu ý:</strong> Tham số <code>va_holder_name</code> chỉ hỗ trợ cho các tài khoản BIDV doanh nghiệp được SePay bật tính năng custom tên hiển thị. Vui lòng liên hệ hỗ trợ bên SePay để được bật tính năng này.
                </div>
                <p>Ví dụ:</p>
                <pre class="rounded mb-4">
                    <code class="language-http" data-lang="http">
                        POST https://my.sepay.vn/userapi/bidv/123456/orders
                        Authorization: Bearer {token}
                        Content-Type: application/json

                        {
                            "amount": 100000,
                            "order_code": "ORD123456789",
                            "duration": 300,
                            "with_qrcode": true
                        }

                        HTTP/1.1 200 OK
                        Content-Type: application/json
                        {
                            "status": "success",
                            "message": "Order created successfully",
                            "data": {
                                "order_id": "f23cc0fe-c343-11ef-9c27-52c7e9b4f41b",
                                "order_code": "ORD123456789",
                                "va_number": "963NQDORDZVTBPJ3Z7H",
                                "va_holder_name": "NGO QUOC DAT",
                                "amount": 2000,
                                "status": "Pending",
                                "bank_name": "BIDV",
                                "account_holder_name": "NGO QUOC DAT",
                                "account_number": "**********",
                                "expired_at": "2024-12-26 11:53:26",
                                "qr_code": "data:image/png;base64,...==",
                                "qr_code_url": "https://qr.sepay.vn/img?acc=...",
                            }
                        }
                    </code>
                </pre>
                <h2 id="chi-tiet-don-hang" class="hs-docs-heading">
                    Chi tiết đơn hàng <a class="anchorjs-link" href="#chi-tiet-don-hang" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                <div class="mb-3">
                    <span class="badge bg-soft-success text-success py-2 px-3 me-2">GET</span>
                    <code>/orders/{order_id}</code>
                </div>
                <p>Lấy thông tin chi tiết của một đơn hàng theo ID.</p>
                <p>Ví dụ:</p>
                <pre class="rounded mb-4">
                    <code class="language-http" data-lang="http">
                        GET https://my.sepay.vn/userapi/bidv/123456/orders/b64247d3-c343-11ef-9c27-52c7e9b4f41b
                        Authorization: Bearer {token}

                        HTTP/1.1 200 OK
                        Content-Type: application/json
                        {
                            "status": "success",
                            "data": {
                                "id": "b64247d3-c343-11ef-9c27-52c7e9b4f41b",
                                "order_code": "ORD123456789",
                                "amount": 2000,
                                "paid_amount": 2000,
                                "status": "Paid",
                                "created_at": "2024-12-26 11:41:46",
                                "bank_name": "BIDV",
                                "account_number": "**********",
                                "account_holder_name": "NGO QUOC DAT",
                                "va": [
                                    {
                                        "va_number": "963NQDORDRSIKYXYPTZ",
                                        "va_holder_name": "NGO QUOC DAT",
                                        "amount": 2000,
                                        "status": "Paid",
                                        "expired_at": "2024-12-26 11:51:45",
                                        "paid_at": "2024-12-26 11:42:12"
                                    }
                                ]
                            }
                        }
                    </code>
                </pre>
                <h2 id="tao-them-va" class="hs-docs-heading">
                    Tạo thêm VA <a class="anchorjs-link" href="#tao-them-va" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                <div class="mb-3">
                    <span class="badge bg-soft-warning text-warning py-2 px-3 me-2">POST</span>
                    <code>/orders/{order_id}/va</code>
                </div>
                <p>Tạo thêm một VA mới cho đơn hàng đã tồn tại.</p>
                <div class="card mb-5">
                    <div class="table-responsive">
                        <table class="table">
                            <thead class="thead-light">
                                <tr>
                                    <th>Thuộc tính</th>
                                    <th>Mô tả</th>
                                    <th>Kiểu</th>
                                    <th>Bắt buộc</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>amount</td>
                                    <td>
                                        Số tiền của VA:<br>
                                        - Mặc định bằng số tiền của đơn hàng<br>
                                        - Có thể thiết lập nhỏ hơn số tiền đơn hàng để cho phép thanh toán từng phần<br>
                                        - Đặt <code>null</code> để VA có thể nhận nhiều lần thanh toán đến khi đủ số tiền đơn hàng
                                    </td>
                                    <td>number hoặc null</td>
                                    <td>Có</td>
                                </tr>
                                <tr>
                                    <td>va_holder_name</td>
                                    <td>Tên chủ tài khoản VA (chỉ chấp nhận chữ in hoa, số và khoảng trắng, tối đa 70 ký tự)</td>
                                    <td>string</td>
                                    <td>Không</td>
                                </tr>
                                <tr>
                                    <td>duration</td>
                                    <td>
                                        Thời gian hiệu lực của VA (tính bằng giây), set là null để đơn hàng không hạn chế thời gian thanh toán
                                        - Mặc định là 600 giây (10 phút)<br>
                                        - Tối đa là 31536000 giây (1 năm)
                                    </td>
                                    <td>number hoặc null</td>
                                    <td>Không</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <p>Ví dụ:</p>
                <pre class="rounded mb-4">
                    <code class="language-http" data-lang="http">
                        POST https://my.sepay.vn/userapi/bidv/123456/orders/b64247d3-c343-11ef-9c27-52c7e9b4f41b/va
                        Authorization: Bearer {token}
                        Content-Type: application/json

                        {
                            "amount": 100000,
                            "va_holder_name": "NGO QUOC DAT",
                            "duration": 300
                        }

                        HTTP/1.1 200 OK
                        Content-Type: application/json
                        {
                            "status": "success",
                            "message": "VA created successfully",
                            "data": {
                                "va_number": "963NQDORD8DTYFPW5MV",
                                "va_holder_name": "NGO QUOC DAT",
                                "amount": 2000,
                                "status": "Unpaid",
                                "expired_at": "2024-12-26 11:55:55"
                            }
                        }
                    </code>
                </pre>
                <h2 id="huy-don-hang" class="hs-docs-heading">
                    Hủy đơn hàng <a class="anchorjs-link" href="#huy-don-hang" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                <div class="mb-3">
                    <span class="badge bg-soft-danger text-danger py-2 px-3 me-2">DELETE</span>
                    <code>/orders/{order_id}</code>
                </div>
                <p>Hủy đơn hàng.</p>
                <p>Ví dụ:</p>
                <pre class="rounded mb-4">
                    <code class="language-http" data-lang="http">
                        DELETE https://my.sepay.vn/userapi/bidv/123456/orders/b64247d3-c343-11ef-9c27-52c7e9b4f41b
                        Authorization: Bearer {token}

                        HTTP/1.1 200 OK
                        Content-Type: application/json
                        {
                            "status": "success",
                            "message": "Order cancelled successfully",
                            "data": {
                                "status": "Cancelled"
                            }
                        }
                    </code>
                </pre>
                <h2 id="huy-va" class="hs-docs-heading">
                    Hủy VA <a class="anchorjs-link" href="#huy-va" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                <div class="mb-3">
                    <span class="badge bg-soft-danger text-danger py-2 px-3 me-2">DELETE</span>
                    <code>/orders/{order_id}/va/{va_number}</code>
                </div>
                <p>Hủy một VA.</p>
                <p>Ví dụ:</p>
                <pre class="rounded mb-4">
                    <code class="language-http" data-lang="http">
                        DELETE https://my.sepay.vn/userapi/bidv/123456/orders/b64247d3-c343-11ef-9c27-52c7e9b4f41b/va/963NQDORD8DTYFPW5MV
                        Authorization: Bearer {token}

                        HTTP/1.1 200 OK
                        Content-Type: application/json
                        {
                            "status": "success",
                            "message": "VA cancelled successfully",
                            "data": {
                                "status": "Cancelled"
                            }
                        }
                    </code>
                </pre>
                <h2 id="xu-ly-webhook" class="hs-docs-heading">Xử lý Webhook thông báo thanh toán <a class="anchorjs-link" href="#xu-ly-webhook" aria-label="Anchor" data-anchorjs-icon="#"></a></h2>
                <p>Khi khách hàng thanh toán thành công vào VA của đơn hàng, SePay sẽ gửi webhook thông báo đến website của bạn. Dữ liệu webhook sẽ bao gồm trường <code>code</code> chứa mã đơn hàng của VA đó.</p>
                <p>Ví dụ dữ liệu webhook khi VA được thanh toán:</p>
                <pre class="rounded mb-4">
                <code class="language-json">{
                    "id": 92704,
                    "gateway": "BIDV",
                    "transactionDate": "2024-01-07 14:02:37",
                    "code": "ORD123456789",           // Mã đơn hàng của VA
                    "transferAmount": 2277000,         // Số tiền giao dịch
                    "transferType": "in",              // Loại giao dịch (in: tiền vào)
                    ...
                }</code>
                </pre>
                <p>Website của bạn cần kiểm tra trường <code>code</code> để xác định đơn hàng được thanh toán và cập nhật trạng thái phù hợp.</p>
                <p>Chi tiết về cách thiết lập và xử lý webhook, bạn có thể tham khảo <a href="https://docs.sepay.vn/tich-hop-webhooks.html">Hướng dẫn tích hợp WebHooks</a>.</p>
                <h2 id="dinh-nghia-trang-thai" class="hs-docs-heading">
                    Định nghĩa trạng thái <a class="anchorjs-link" href="#dinh-nghia-trang-thai" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                <h3 id="trang-thai-don-hang" class="hs-docs-heading">
                    Trạng thái đơn hàng <a class="anchorjs-link" href="#trang-thai-don-hang" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h3>
                <div class="card mb-3">
                    <div class="table-responsive">
                        <table class="table">
                            <thead class="thead-light">
                                <tr>
                                    <th>Trạng thái</th>
                                    <th>Mô tả</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Pending</td>
                                    <td>Đơn hàng chưa thanh toán</td>
                                </tr>
                                <tr>
                                    <td>Paid</td>
                                    <td>Đơn hàng đã thanh toán</td>
                                </tr>
                                <tr>
                                    <td>Partially</td>
                                    <td>Đơn hàng đã thanh toán một phần</td>
                                </tr>
                                <tr>
                                    <td>Cancelled</td>
                                    <td>Đơn hàng đã bị hủy</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <h3 id="trang-thai-va" class="hs-docs-heading">
                    Trạng thái VA <a class="anchorjs-link" href="#trang-thai-va" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h3>
                <div class="card mb-3">
                    <div class="table-responsive">
                        <table class="table">
                            <thead class="thead-light">
                                <tr>
                                    <th>Trạng thái</th>
                                    <th>Mô tả</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Unpaid</td>
                                    <td>VA chưa thanh toán</td>
                                </tr>
                                <tr>
                                    <td>Paid</td>
                                    <td>VA đã thanh toán</td>
                                </tr>
                                <tr>
                                    <td>Cancelled</td>
                                    <td>VA đã bị hủy</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <h2 id="gioi-han-request" class="hs-docs-heading">
                    Giới hạn request <a class="anchorjs-link" href="#gioi-han-request" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                <p>SePay giới hạn số lượng request API theo đơn hàng như sau:</p>
                <ul>
                    <li>2 request/giây cho mỗi IP</li>
                    <li>Vượt quá giới hạn sẽ nhận response mã 429</li>
                    <li>Header <code>X-SePay-UserApi-Retry-After</code> chỉ ra thời gian cần đợi trước khi thử lại</li>
                </ul>
                <h2 id="ma-loi" class="hs-docs-heading">
                    Mã lỗi <a class="anchorjs-link" href="#ma-loi" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                <p>SePay sẽ trả về các mã lỗi sau:</p>
                <div class="card mb-5">
                    <div class="table-responsive">
                        <table class="table">
                            <thead class="thead-light">
                                <tr>
                                    <th>Mã lỗi</th>
                                    <th>Mô tả</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>400</td>
                                    <td>Bad Request - Tham số không hợp lệ hoặc thiếu</td>
                                </tr>
                                <tr>
                                    <td>401</td>
                                    <td>Unauthorized - Token không hợp lệ</td>
                                </tr>
                                <tr>
                                    <td>404</td>
                                    <td>Not Found - Không tìm thấy tài nguyên</td>
                                </tr>
                                <tr>
                                    <td>429</td>
                                    <td>Too Many Requests - Vượt quá giới hạn request</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <h2 id="tao-don-hang-khong-gioi-han-so-tien-va-thoi-gian" class="hs-docs-heading">
                    Tạo đơn hàng không giới hạn số tiền và thời gian <a class="anchorjs-link" href="#tao-don-hang-khong-gioi-han-so-tien-va-thoi-gian" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                <p>Bạn có thể tạo đơn hàng không giới hạn số tiền và thời gian thanh toán bằng cách:</p>
                <ul>
                    <li>Đặt <code>amount</code> là <code>null</code> để đơn hàng và VA không hạn chế số lần thanh toán</li>
                    <li>Đặt <code>duration</code> là <code>null</code> để đơn hàng không hạn chế thời gian thanh toán</li>
                </ul>
                <p>Ví dụ:</p>
                <pre class="rounded mb-4">
                    <code class="language-http" data-lang="http">
                        POST https://my.sepay.vn/userapi/bidv/123456/orders
                        Authorization: Bearer {token}
                        Content-Type: application/json

                        {
                            "amount": null,
                            "duration": null
                        }
                    </code>
                </pre>
                <p>Khi đơn hàng được tạo, bạn có thể tạo VA mới cho đơn hàng bằng cách gọi API <a href="#tao-them-va">Tạo thêm VA</a>.</p>
            </div>
        </main>
		<div id="contact-box-overlay"></div>
		<div id="contact-box">
			<div class="popup">
				<a href="https://m.me/117903214582465" target="_blank" class="item">
					<div class="logo">
						<img src="../assets/img/others/fb-messenger.png" width="50%" />
					</div>
					<div class="meta">
						<p class="title">Facebook Messenger</p>
						<small class="description">Hỗ trợ live chat 24/7</small>
					</div>
				</a>
				<div class="divide"></div>
				<a href="tel:02873059589" class="item">
					<div class="icon" style="background-color: #22c55e; color: #fff;">
						<svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;">
							<path
								d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z"
								stroke="currentColor"
								stroke-width="1.5"
								stroke-linecap="round"
								fill="transparent"
							></path>
						</svg>
					</div>
					<div class="meta">
						<p class="title">Hotline</p>
						<small class="description">Điện thoại hỗ trợ</small>
					</div>
				</a>
				<div class="divide"></div>
				<a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
					<div class="logo">
						<img src="../assets/img/others/youtube-social.png" />
					</div>
					<div class="meta">
						<p class="title">Youtube</p>
						<small class="description">Theo dõi video mới nhất của SePay</small>
					</div>
				</a>
				<div class="divide"></div>
				<a href="https://t.me/s/sepaychannel" target="_blank" class="item">
					<div class="logo">
						<img src="../assets/img/others/telegram-social.png" />
					</div>
					<div class="meta">
						<p class="title">Telegram</p>
						<small class="description">Nhận thông tin mới nhất từ SePay</small>
					</div>
				</a>
			</div>
			<div class="container">
				<div class="dot-ping">
					<div class="ping"></div>
					<div class="dot"></div>
				</div>
				<div class="contact-icon">
					<svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;">
						<path
							fill-rule="evenodd"
							clip-rule="evenodd"
							d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z"
							fill="currentColor"
						></path>
					</svg>
				</div>
				<span style="font-weight: bold;">Liên hệ chúng tôi</span>
			</div>
		</div>

        <a
            class="js-go-to go-to position-fixed"
            href="javascript:;"
            style="visibility: hidden;"
            data-hs-go-to-options='{
				"offsetTop": 700,
				"position": {
					"init": {
					"right": "2rem"
					},
					"show": {
					"bottom": "2rem"
					},
					"hide": {
					"bottom": "-2rem"
					}
				}
			}'
        >
            <i class="bi-chevron-up"></i>
        </a>

		<script src="../assets/js/vendor.min.js"></script>
		<script src="../assets/js/theme.min.js"></script>
		<script src="../assets/js/contact-box.js"></script>

		<script>
			(function () {
				new HSHeader("#header").init();

				new HsNavScroller('.js-nav-scroller', {
					delay: 400,
					offset: 140,
				});

				const docsSearch = HSCore.components.HSList.init('#docsSearch');
				new HSGoTo('.js-go-to');

                function setCookie(cname, cvalue, exdays) {
                    var d = new Date();
                    d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
                    var expires = "expires=" + d.toUTCString();
                    document.cookie = cname + "=" + cvalue + "; " + expires + "; path=/; domain=.sepay.vn";
                }
                let urlParams = new URLSearchParams(document.location.search);
                ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content'].forEach(function(param) {
                    let value = urlParams.get(param);
                    if (value) {
                        setCookie(param, value, 90);
                    }
                });
                if (document.referrer != '' && document.cookie.indexOf('referer') === -1) {
                    setCookie('referer', document.referrer, 90);
                }
			})();
		</script>
    </body>
</html>
