<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Required Meta Tags Always Come First -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- Title -->
    <title>SePay Integration Guide with WooCommerce | SePay</title>


    <!-- Favicon -->
    <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />

    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">

    <!-- CSS Implementing Plugins -->
    <link rel="stylesheet" href="../assets/vendor/bootstrap-icons/font/bootstrap-icons.css">
    <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
    <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
  
    <!-- CSS Front Template -->
    <link rel="stylesheet" href="../assets/css/theme.min.css">
    <link rel="stylesheet" href="../assets/css/docs.min.css">

    <!--contact-box-css-->
    <link rel="stylesheet" href="../assets/css/contact-box.css">
    <!--/contact-box-css-->

    <link rel="stylesheet" href="../assets/vendor/hs-video-bg/dist/hs-video-bg.min.css">
    <style>
        .docs-navbar-sidebar-aside-body {
            padding-top: 3.5rem !important;
        }
    </style>
      <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-J8DLMQTKSQ');
  </script>
</head>

<body class="navbar-sidebar-aside-lg">
    <!-- ========== HEADER ========== -->
    <header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
        <div class="container-fluid">
            <nav class="navbar-nav-wrap">
                <div class="row flex-grow-1">
                    <!-- Default Logo -->
                    <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
                        <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                            <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png"
                                alt="Logo">
                        </a>

                    </div>
                    <!-- End Default Logo -->

                    <div class="col-md px-lg-0">
                        <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
                            <div class="d-none d-md-block">


                                <!-- List Item Template -->
                                <div class="d-none">
                                    <div id="searchTemplate" class="dropdown-item">
                                        <a class="d-block link" href="#">
                                            <span class="category d-block fw-normal text-muted mb-1"></span>
                                            <span class="component text-dark"></span>
                                        </a>
                                    </div>
                                </div>
                                <!-- End List Item Template -->
                            </div>

                            <!-- Navbar -->
                            <ul class="navbar-nav p-0">
                                <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html"
                                        target="_blank">
                                        Support <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN" target="_blank">
                                      Youtube <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                  </li>
                                  <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://www.facebook.com/messages/t/sepay.vn" target="_blank">
                                      Facebook <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                  </li>
                                  <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel" target="_blank">
                                      Telegram <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                  </li>

                            </ul>
                            <!-- End Navbar -->
                        </div>
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Row -->
            </nav>
        </div>
    </header>
    <!-- ========== END HEADER ========== -->

    <!-- ========== MAIN CONTENT ========== -->
    <main id="content" role="main">
        <!-- Navbar -->
        <nav class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
            data-hs-nav-scroller-options='{
            "type": "vertical",
            "target": ".navbar-nav .active",
            "offset": 80
           }'>
            <!-- Navbar Toggle -->
            <button type="button" class="navbar-toggler btn btn-white d-grid w-100" data-bs-toggle="collapse"
                data-bs-target="#navbarVerticalNavMenu" aria-label="Toggle navigation" aria-expanded="false"
                aria-controls="navbarVerticalNavMenu">
                <span class="d-flex justify-content-between align-items-center">
                    <span class="h3 mb-0">Nav menu</span>

                    <span class="navbar-toggler-default">
                        <i class="bi-list"></i>
                    </span>

                    <span class="navbar-toggler-toggled">
                        <i class="bi-x"></i>
                    </span>
                </span>
            </button>
            <!-- End Navbar Toggle -->

            <!-- Navbar Collapse -->
            <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
                <div class="navbar-brand-wrapper border-end" style="height: auto;">
                    <!-- Default Logo -->
                    <div class="d-flex align-items-center mb-3">
                        <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                            <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png"
                                alt="Logo">
                        </a>

                    </div>
                    <!-- End Default Logo -->


                </div>

                <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
                    <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
                        <li class="nav-item">
                            <span class="nav-subtitle">Introduction</span>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="what-is-sepay.html">What is SePay?</a>
                        </li>
                
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">General Guide</span>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="register-sepay.html">Register SePay</a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="add-bank-account.html">Add Bank Account</a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link " href="view-transactions.html">View Transactions</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="users-and-permissions.html">Users & Permissions</a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="sub-accounts.html">Sub Accounts</a>
                        </li>
                
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">Company Configuration</span>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link " href="service-packages.html">Service Packages</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="invoices-and-payments.html">Invoices & Payments</a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link " href="general-settings.html">General Settings</a>
                        </li>
                
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">Balance Change Sharing</span>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link " href="telegram-integration.html">Integrate Telegram <i class="bi bi-telegram ms-2 text-info"></i></a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="lark-messenger-integration.html">Integrate Lark Messenger <img src="../assets/img/lark/lark-icon.png" class="img-fluid" style="max-height:25px"></a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link d-flex align-items-center" href="mobile-app.html">Mobile App <i class="bi bi-phone-vibrate ms-2 fs-3 text-info"></i> </a>
                        </li>
                                
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">Web Integration</span>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="shopify-integration.html">Integrate Shopify <img class="ms-2" src="../assets/img/others/shopify-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="sapo-integration.html">Integrate Sapo <img class="ms-2" src="../assets/img/others/sapo-icon.png" style="width:18px; height: 18px;"></a>
                        </li>
                    
                        <li class="nav-item">
                            <a class="nav-link" href="haravan-integration.html">Integrate Haravan <img class="ms-2" src="../assets/img/others/haravan-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link active" href="woocommerce.html">Integrate WooCommerce <img class="ms-2" src="../assets/img/others/woocommerce-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="google-sheets-integration.html">Integrate Google Sheets <img class="ms-2" src="../assets/img/others/google-sheets-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="hostbill-integration.html">Integrate HostBill <img class="ms-2" src="../assets/img/others/hostbill-icon.png" style="height: 22px;"></a>
                        </li>
                
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">Programming & Integration</span>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="webhooks-integration.html">Integrate WebHooks</a>
                        </li>
                
                        <li class="nav-item ">
                            <a class="nav-link" href="programming-webhooks.html">Programming WebHooks</a>
                        </li>
                        <li class="nav-item ">
                            <a class="nav-link" href="simulate-transactions.html">Simulate Transactions</a>
                        </li>
                        <li class="nav-item ">
                            <a class="nav-link" href="create-qr-code-vietqr-dynamic.html">Create & Embed QR Code</a>
                        </li>
                
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">SePay API</span>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="api-introduction.html">API Introduction</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="create-api-token.html">Create API Token</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="transaction-api.html">Transaction API</a>
                        </li>
<li class="nav-item">
                  <a class="nav-link" href="bank-account-api.html">Bank Account API</a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="api-va-order-bidv.html">API Va Order</a>
            </li>
                    </ul>
                </div>
                
                
            </div>
            <!-- End Navbar Collapse -->
        </nav>
        <!-- End Navbar -->

        <!-- Content -->
        <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">

            <div class="docs-page-header">
                <div class="row align-items-center">
                    <div class="col-sm">
                        <h1 class="docs-page-header-title">WooCommerce Integration Guide <img src="../assets/img/others/woocommerce-icon.png" style="width:30px"></h1>
                        <p class="docs-page-header-text">Integrating SePay into WooCommerce helps confirm payments immediately after customers transfer money. The order will also change to the "Paid" status.</p>
                    </div>
                </div>
            </div>
            
            <p>If you are using a WordPress and WooCommerce website, this guide will show you how to integrate SePay into WordPress WooCommerce to automate payment confirmation via bank transfer.</p>
            <div class="text-center"><a class="btn btn-primary btn-transition" href="https://wordpress.org/plugins/sepay-gateway/"><i class="bi bi-cloud-download"></i> Download the SePay Payment Gateway Plugin for WordPress</a></div>
            <p class="fw-bold">Results after integration:</p>
            <div class="ratio ratio-16x9"  style="max-width: 800px;">
                <iframe src="https://www.youtube.com/embed/28jVZt__fto" title="SePay WooCommerce Demo" allowfullscreen></iframe>
            </div>
            <p class="text-center text-muted">Demo video of the WordPress WooCommerce integration result</p>
            
            <p class="ms-2 fw-bold">1. From the customer's perspective when shopping online</p>
            
            <ul class="lh-lg">
                <li>When customers place an order and make a payment, there will be an additional option for <b>Bank Transfer</b></li>
                <div class="text-center"><img style="max-height:100px" src="../assets/img/others/woo-sepay-payment-chose.png" class="img-fluid"></div>
                <li>A QR code along with payment details will appear after selecting this payment method.</li>
                <div class="text-center"><img style="max-height:500px"  src="../assets/img/others/woo-sepay-payment.png" class="img-fluid"></div>
            
                <li>The website will display <b class="text-success">You have successfully paid</b> just a few seconds after the customer makes the payment.</li>
                <div class="text-center"><img style="max-height:200px"  src="../assets/img/others/woo-sepay-payment-success.png" class="img-fluid"></div>
            </ul>
            
            <p class="ms-2 mt-4 fw-bold">2. From the WooCommerce Order Management Interface</p>
            <p>The order will automatically record the payment and change status from On-Hold to Processing if the customer has made a full payment.</p>
            
            <div class="text-center"><img style="max-height: 600px;" src="../assets/img/others/woo-sepay-payment-success2.png" class="img-fluid"></div>
            


            <h2 class="mb-3 mt-5">Integration Guide</h2>

<p class="h3 my-3"><b>Step 1: Install the SePay Gateway Plugin on your WordPress website</b></p>

<p>This step helps you add the payment method to your website. Follow the steps below:</p>

<ul class="lh-lg">
    <li>Download the plugin <a href="https://sepay.vn/uploads/sepay-gateway.zip">here</a></li>
    <li>Access the <b>Admin Interface</b> of your WordPress website -> <b>Plugins</b> -> <b>Add New</b> -> <b>Upload Plugin</b>. Choose the sepay-gateway.zip file and click <b>Install Now</b>. Then select <b>Activate Plugin</b></li>

    <div class="text-center"><img style="max-height: 600px;" src="../assets/img/others/woo-sepay-install-plugin.png" class="img-fluid"> </div>

    <li>In the Plugin List, select Settings for the <b>SePay Payment Gateway</b> plugin</li>

    <div class="text-center"><img style="max-height: 250px;" src="../assets/img/others/woo-sepay-settings-link.png" class="img-fluid"> </div>

    <li>The SePay Gateway settings include the configurations below:
        <ul>
            <li><b>Enable/Disable</b>: Check to enable the new payment method.
                <div><img style="max-height: 250px;" src="../assets/img/others/woo-sepay-settings-1.png" class="img-fluid"> </div>
            </li>
            <li><b>Display Name</b>: The name displayed for the payment method.
                <div><img style="max-height: 250px;" src="../assets/img/others/woo-sepay-settings-3.png" class="img-fluid"> </div>
            </li>
            <li><b>Description</b>: The description for the payment method.
                <div><img style="max-height: 250px;" src="../assets/img/others/woo-sepay-settings-4.png" class="img-fluid"> </div>
            </li>
            <li><b>Bank & Account Number/VA Number</b>: Allows you to select <a href="https://sepay.vn/ngan-hang.html" target="_blank">all banks supported by SePay <i class="bi-box-arrow-up-right ms-1"></i></a>.
                <p class="mb-2"><span class="badge bg-primary rounded-pill">1</span> For banks currently supporting payment authentication via <a href="https://sepay.vn/blog/tai-khoan-ngan-hang-ao-la-gi-phan-loai-va-ung-dung/" target="_blank">VA numbers <i class="bi-box-arrow-up-right ms-1"></i></a> such as: <b>KienLongBank</b>, <b>OCB</b>, and <b>MSB</b>, SePay requires entering the corresponding <b>VA number</b>.</p>
                <div><img style="max-height: 250px;" src="../assets/img/others/woo-sepay-settings-7.png" class="img-fluid"> </div>
                <p class="mt-3 mb-2"><span class="badge bg-primary rounded-pill">2</span> For other banks, SePay requires the exact <b>account number</b> for payment authentication.</p>
                <div><img style="max-height: 250px;" src="../assets/img/others/woo-sepay-settings-6.png" class="img-fluid"> </div>
            </li>
            <li><b>Account Holder Name</b>: Enter the bank account holder's name.
                <div><img style="max-height: 250px;" src="../assets/img/others/woo-sepay-settings-8.png" class="img-fluid"> </div>
            </li>
            <li><b>Payment Code Prefix</b>: Enter the same prefix as in SePay. Refer to <a href="https://docs.sepay.vn/cau-hinh-chung.html#ma-thanh-toan">General Settings</a>.
                <div><img style="max-height: 250px;" src="../assets/img/others/woo-sepay-settings-9.png" class="img-fluid"> </div>
            </li>
            <li><b>Successful Payment Message</b>: The message displayed on the website after the customer successfully completes the payment. Supports plain text, HTML, and JavaScript. If you want to add JavaScript code to trigger events for tracking pages like Google Analytics, you can insert the code here.
                <div><img style="max-height: 250px;" src="../assets/img/others/woo-sepay-settings-11.png" class="img-fluid"> </div>
            </li>
            <li><b>Order Status After Payment</b>: The order status in WooCommerce after the customer completes the payment. If not specified, WooCommerce will decide the status. Alternatively, you can set it to <b>Processing</b> or <b>Completed</b>.
                <div><img style="max-height: 250px;" src="../assets/img/others/woo-sepay-settings-14.png" class="img-fluid"> </div>
            </li>
            <li><b>Download Mode After Payment</b>: This option is for digital product orders, allowing you to specify how users can download the product via <b>Manual</b> or <b>Automatic</b> methods.
                <div><img style="max-height: 250px;" src="../assets/img/others/woo-sepay-settings-12.png" class="img-fluid"> </div>
            </li>
            <li><b>Display Bank Name</b>: The display mode for the bank name on the payment screen. SePay supports three options: <b>Full Name</b>, <b>Abbreviated Name</b>, or <b>Full Name with Abbreviated Name</b>.
                <div><img style="max-height: 250px;" src="../assets/img/others/woo-sepay-settings-13.png" class="img-fluid"> </div>
            </li>
        </ul>
    </li>
    <li>Click <b>Save Changes</b> to save the configurations.</li>
    <li>Copy the API Key from the <b>API Key</b> section to prepare for the next step in SePay.
        <div><img style="max-height: 250px;" src="../assets/img/others/woo-sepay-settings-10.png" class="img-fluid"> </div>
    </li>
</ul>
<p><b class="text-danger">Note:</b>
    <ul>
        <li>Ensure that the bank information, account number, and account holder name are entered correctly.</li>
        <li>Keep your API Key secure and do not disclose it to anyone.</li>
    </ul>
</p>

<p class="h3 my-3"><b>Step 2: Create Webhook on SePay</b></p>
<p>Go to <b>my.sepay.vn</b> -> <b>Webhook Integration</b> -> <b>Add Webhook</b></p>
<p>In the webhook creation interface, fill in the following information:
    <ul class="lh-lg">
        <li><b>Integration Name</b>: Enter any name. For example, Woo Integration.</li>
        <li><b>Trigger Webhooks When</b>: Select <b>Money Received</b></li>
        <li><b>When the bank account is</b>: Select the bank account you entered in Step 1.</li>
        <li><b>Skip if transaction content does not have a Payment Code?</b>: Select <b>Yes</b></li>
        <li><b>Call to URL</b>: Enter the URL with the structure <code>https://your-website.com/wp-json/sepay-gateway/v1/add-payment</code>. Replace <code>https://your-website.com</code> with your website URL.</li>
        <li><b>Is this a payment verification Webhook?</b>: Select <b>Yes</b></li>
        <li><b>Authentication Type</b>: Select <b>API Key</b></li>
        <li><b>Request Content type</b>: Select <b>application/json</b></li>
        <li><b>API Key</b>: Enter the API Key copied in Step 1</li>
        <div class="text-center"><img style="max-height: 900px;" src="../assets/img/others/webhook-for-woo.png" class="img-fluid"></div>
    </ul>
</p>
<p>Click <b>Add</b> to complete the process.</p>

            
            
<p>You have now completed the integration of SePay with WooCommerce.</p>
<h2 class="my-3">Check your configurations</h2>
<p>To ensure everything is working correctly according to the configuration, you can do the following:</p>
<ul class="lh-lg">
    <li>Try placing an order on your website. Choose the order with the smallest value.</li>
    <li>In the Payment step, select the payment method as <b>Bank Transfer (QR Code Scan)</b> as added in step 1.</li>
    <li>If the QR code appears after placing the order, it means you have configured successfully.</li>
    <li>Try making a payment, and if the payment success screen appears, it means the configuration is correct. Also, check the order management interface, and you will see the order status changed to Processing.</li>
</ul>
<div class="text-center"><img style="max-height:500px" src="../assets/img/others/woo-sepay-payment.png" class="img-fluid"></div>

<div class="text-center"><img style="max-height:200px" src="../assets/img/others/woo-sepay-payment-success.png" class="img-fluid"></div>

<div class="my-3">
    <h3>Changelog</h3>
    <p class="fw-bold text-info">29/03/2024:</p>
    <ul class="lh-lg">
        <li>[Update] Support for Cart & Checkout blocks for <a href="https://docs.sepay.vn/woocommerce.html" target="_blank">SePay WooCommerce Payment plugin</a>.</li>
    </ul>
    <p class="fw-bold text-info">15/11/2023:</p>
    <ul class="lh-lg">
        <li>[New feature] Allows customization of bank name display. Includes: Abbreviation, full name, full name with abbreviation.</li>
        <li>[Bug fix] Fixed issue with transaction verification when using VA.</li>
    </ul>
    <p class="fw-bold text-info">07/11/2023:</p>
    <ul class="lh-lg">
        <li>[Update] Optimized CSS interface for compatibility with various WordPress themes.</li>
        <li>[New feature] Support for Digital/Downloadable products. Allows download after payment.</li>
        <li>[Bug fix] Fixed JSON response issue.</li>
    </ul>
    <p class="fw-bold text-info">04/10/2023:</p>
    <ul class="lh-lg">
        <li>[Change]: Changed the order note status from Customer note to Admin note. Now, notes automatically created by SePay will no longer send emails to customers.</li>
        <li>[New feature]: Allows customization of the message after the customer successfully completes payment. Supports plain text, HTML, and Javascript. If you want to add JavaScript code to trigger events on tracking pages like Google Analytics, you can insert the JavaScript code here.</li>
        <li>[New feature]: Customize the order status after the customer completes payment. If not specified, the status will be decided by WooCommerce. Alternatively, you can specify it as Processing or Completed.</li>
    </ul>
</div>

<div class="my-2"><p>Read more: <a href="tich-hop-google-sheets.html">Guide to integrating Google Sheets<i class="bi bi-chevron-right"></i></a></p></div>

        </div>
        <!-- End Content -->
    </main>
    <!-- ========== END MAIN CONTENT ========== -->

    <!-- ========== SECONDARY CONTENTS ========== -->
    <!--contact-box-html-docs-->
  <div id="contact-box-overlay"></div>
  <div id="contact-box">
    <div class="popup">
      <a href="https://m.me/117903214582465" target="_blank" class="item">
        <div class="logo">
          <img src="../assets/img/others/fb-messenger.png" width="50%" />
        </div>
        <div class="meta">
          <p class="title">Facebook Messenger</p>
          <small class="description">24/7 live chat support</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="tel:02873059589" class="item">
        <div class="icon" style="background-color: #22c55e; color: #fff;">
          <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20"
            style="color: currentcolor;">
            <path
              d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z"
              stroke="currentColor" stroke-width="1.5" stroke-linecap="round" fill="transparent"></path>
          </svg>
        </div>
        <div class="meta">
          <p class="title">Hotline</p>
          <small class="description">Support phone number</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
        <div class="logo">
          <img src="../assets/img/others/youtube-social.png" />
        </div>
        <div class="meta">
          <p class="title">YouTube</p>
          <small class="description">Follow SePay's latest videos</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
        <div class="logo">
          <img src="../assets/img/others/telegram-social.png" />
        </div>
        <div class="meta">
          <p class="title">Telegram</p>
          <small class="description">Get the latest updates from SePay</small>
        </div>
      </a>
    </div>
    <div class="container">
      <div class="dot-ping">
        <div class="ping"></div>
        <div class="dot"></div>
      </div>
      <div class="contact-icon">
        <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20"
          style="color: currentcolor;">
          <path fill-rule="evenodd" clip-rule="evenodd"
            d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z"
            fill="currentColor"></path>
        </svg>
      </div>
      <span style="font-weight: bold;">Contact Us</span>
    </div>
  </div>
  <!--/contact-box-html-docs-->

    <!-- Go To -->
    <a class="js-go-to go-to position-fixed" href="javascript:;" style="visibility: hidden;" data-hs-go-to-options='{
       "offsetTop": 700,
       "position": {
         "init": {
           "right": "2rem"
         },
         "show": {
           "bottom": "2rem"
         },
         "hide": {
           "bottom": "-2rem"
         }
       }
     }'>
        <i class="bi-chevron-up"></i>
    </a>
    <!-- ========== END SECONDARY CONTENTS ========== -->

    <!-- JS Global Compulsory  -->
    <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

    <!-- JS Implementing Plugins -->
    <script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
    <script src="../assets/vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js"></script>

    <script src="../assets/vendor/list.js/dist/list.min.js"></script>
    <script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>
    <script src="../assets/vendor/hs-video-bg/dist/hs-video-bg.min.js"></script>

    <!-- JS Front -->
    <script src="../assets/js/theme.min.js"></script>

    <!-- contact-box-js -->
    <script src="../assets/js/contact-box.js"></script>
    <!-- /contact-box-js -->

    <!-- JS Plugins Init. -->
    <script>
        (function () {
            // INITIALIZATION OF HEADER
            // =======================================================
            new HSHeader('#header').init()

          // INITIALIZATION OF NAV SCROLLER
            // =======================================================
            new HsNavScroller('.js-nav-scroller', {
                delay: 400,
                offset: 140
            }) 
            // INITIALIZATION OF LISTJS COMPONENT
            // =======================================================
            const docsSearch = HSCore.components.HSList.init('#docsSearch')




            // INITIALIZATION OF GO TO
            // =======================================================
            new HSGoTo('.js-go-to')
        })()
    </script>
</body>

</html>
