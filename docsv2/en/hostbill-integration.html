<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Required Meta Tags Always Come First -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- Title -->
    <title>HostBill Integration Guide | SePay</title>


    <!-- Favicon -->
    <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />

    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">
    <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
    <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />

    <!-- CSS Implementing Plugins -->
    <link rel="stylesheet" href="../assets/vendor/bootstrap-icons/font/bootstrap-icons.css">

    <!-- CSS Front Template -->
    <link rel="stylesheet" href="../assets/css/theme.min.css">
    <link rel="stylesheet" href="../assets/css/docs.min.css">

    <!--contact-box-css-->
    <link rel="stylesheet" href="../assets/css/contact-box.css">
    <!--/contact-box-css-->

    <style>
        .docs-navbar-sidebar-aside-body {
            padding-top: 3.5rem !important;
        }

        .navbar-sidebar-aside-content img {
            margin-bottom: 1rem;
        }
    </style>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());

        gtag('config', 'G-J8DLMQTKSQ');
    </script>
</head>

<body class="navbar-sidebar-aside-lg">
    <!-- ========== HEADER ========== -->
    <header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
        <div class="container-fluid">
            <nav class="navbar-nav-wrap">
                <div class="row flex-grow-1">
                    <!-- Default Logo -->
                    <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
                        <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                            <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png"
                                alt="Logo">
                        </a>

                    </div>
                    <!-- End Default Logo -->

                    <div class="col-md px-lg-0">
                        <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
                            <div class="d-none d-md-block">


                                <!-- List Item Template -->
                                <div class="d-none">
                                    <div id="searchTemplate" class="dropdown-item">
                                        <a class="d-block link" href="#">
                                            <span class="category d-block fw-normal text-muted mb-1"></span>
                                            <span class="component text-dark"></span>
                                        </a>
                                    </div>
                                </div>
                                <!-- End List Item Template -->
                            </div>

                            <!-- Navbar -->
                            <ul class="navbar-nav p-0">
                                <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html"
                                        target="_blank">
                                        Support <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN"
                                        target="_blank">
                                        Youtube <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm"
                                        href="https://www.facebook.com/messages/t/sepay.vn" target="_blank">
                                        Facebook <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel"
                                        target="_blank">
                                        Telegram <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                </li>

                            </ul>
                            <!-- End Navbar -->
                        </div>
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Row -->
            </nav>
        </div>
    </header>
    <!-- ========== END HEADER ========== -->

    <!-- ========== MAIN CONTENT ========== -->
    <main id="content" role="main">
        <!-- Navbar -->
        <nav class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
            data-hs-nav-scroller-options='{
      "type": "vertical",
      "target": ".navbar-nav .active",
      "offset": 80
     }'>
            <!-- Navbar Toggle -->
            <button type="button" class="navbar-toggler btn btn-white d-grid w-100" data-bs-toggle="collapse"
                data-bs-target="#navbarVerticalNavMenu" aria-label="Toggle navigation" aria-expanded="false"
                aria-controls="navbarVerticalNavMenu">
                <span class="d-flex justify-content-between align-items-center">
                    <span class="h3 mb-0">Nav menu</span>

                    <span class="navbar-toggler-default">
                        <i class="bi-list"></i>
                    </span>

                    <span class="navbar-toggler-toggled">
                        <i class="bi-x"></i>
                    </span>
                </span>
            </button>
            <!-- End Navbar Toggle -->

            <!-- Navbar Collapse -->
            <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
                <div class="navbar-brand-wrapper border-end" style="height: auto;">
                    <!-- Default Logo -->
                    <div class="d-flex align-items-center mb-3">
                        <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                            <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png"
                                alt="Logo">
                        </a>

                    </div>
                    <!-- End Default Logo -->


                </div>

                <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
                    <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
                        <li class="nav-item">
                            <span class="nav-subtitle">Introduction</span>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="what-is-sepay.html">What is SePay?</a>
                        </li>
                
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">General Guide</span>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="register-sepay.html">Register SePay</a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="add-bank-account.html">Add Bank Account</a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link " href="view-transactions.html">View Transactions</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="users-and-permissions.html">Users & Permissions</a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="sub-accounts.html">Sub Accounts</a>
                        </li>
                
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">Company Configuration</span>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link " href="service-packages.html">Service Packages</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="invoices-and-payments.html">Invoices & Payments</a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link " href="general-settings.html">General Settings</a>
                        </li>
                
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">Balance Change Sharing</span>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="telegram-integration.html">Telegram Integration <i
                                    class="bi bi-telegram ms-2 text-info"></i></a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="lark-messenger-integration.html">Lark Messenger Integration <img
                                    src="../assets/img/lark/lark-icon.png" class="img-fluid" style="max-height:25px"></a>
                        </li>
                
                        <li class="nav-item"> <a class="nav-link d-flex align-items-center"
                                href="mobile-app.html">Mobile App <i
                                class="bi bi-phone-vibrate ms-2 fs-3 text-info"></i> </a> </li>
                
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">Web Integration</span>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="shopify-integration.html">Shopify Integration <img class="ms-2"
                                    src="../assets/img/others/shopify-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="sapo-integration.html">Sapo Integration <img class="ms-2"
                                    src="../assets/img/others/sapo-icon.png" style="width:18px; height: 18px;"></a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="haravan-integration.html">Haravan Integration <img class="ms-2"
                                    src="../assets/img/others/haravan-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="woocommerce-integration.html">WooCommerce Integration <img class="ms-2"
                                    src="../assets/img/others/woocommerce-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="google-sheets-integration.html">Google Sheets Integration <img
                                    class="ms-2" src="../assets/img/others/google-sheets-icon.png"
                                    style="width:22px; height: 22px;"></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="hostbill-integration.html">HostBill Integration <img class="ms-2"
                                    src="../assets/img/others/hostbill-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">Programming & Integration</span>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="webhooks-integration.html">WebHooks Integration</a>
                        </li>
                
                        <li class="nav-item ">
                            <a class="nav-link" href="webhooks-programming.html">WebHooks Programming</a>
                        </li>
                        <li class="nav-item ">
                            <a class="nav-link" href="simulate-transactions.html">Simulate Transactions</a>
                        </li>
                        <li class="nav-item ">
                            <a class="nav-link" href="create-qr-code-vietqr-dynamic.html">Create & Embed QR Code</a>
                        </li>
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">SePay API</span>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="api-introduction.html">API Introduction</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="create-api-token.html">Create API Token</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="transaction-api.html">Transaction API</a>
                        </li>
<li class="nav-item">
                  <a class="nav-link" href="bank-account-api.html">Bank Account API</a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="api-va-order-bidv.html">API Va Order</a>
            </li>
                    </ul>
                </div>
                
                
            </div>
            <!-- End Navbar Collapse -->
        </nav>
        <!-- End Navbar -->

        <!-- Content -->
        <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">
            <div class="docs-page-header">
                <div class="row align-items-center">
                    <div class="col-sm">
                        <h1 class="docs-page-header-title">HostBill Integration Guide <img src="../assets/img/others/hostbill-icon.png" style="width:30px"></h1>
                        <p class="docs-page-header-text">Integrating SePay into HostBill helps confirm payments immediately after the customer transfers money. The order will also change to the "paid" status.</p>
                    </div>
                </div>
            </div>
            <p>If you are using HostBill, this article will guide you on how to integrate SePay into HostBill to automate the payment confirmation process for bank transfers.</p>
            <div class="text-center"><a class="btn btn-primary btn-transition" href="https://sepay.vn/uploads/hostbill/sepay_v1.4.zip" download><i class="bi bi-cloud-download"></i> Download HostBill SePay Module </a></div>
            <p class="fw-bold">Results after integration:</p>
            <p class="fw-bold">1. User Side</p>
            <ul class="lh-lg">
                <li>Allows users to choose a bank for payment</li>
                <li>Displays a QR code for payment scanning</li>
                <li>Automatically notifies the user of successful payment after payment is made</li>
            </ul>
            <p class="fw-bold">2. HostBill (Admin) Side</p>
            <ul class="lh-lg">
                <li>Allows configuration of receiving payment banks</li>
                <li>Displays payment banks based on transaction amount conditions</li>
                <li>Automatically changes the invoice status to "paid" after receiving the transaction</li>
                <li>And many other customizable options</li>
                <li>In SePay, you can configure to send transaction information to Telegram. Optionally, send information only if the transaction is not recorded in HostBill (not adding the transaction).</li>
            </ul>
            
            <h2 class="mb-3 mt-5">Integration Guide</h2>
            <p class="h3 my-3" id="h1"><b>1. Install and Configure SePay Module into HostBill</b></p>
            <p class="h4 my-3" id="h1.1"><b>1.1. Install Module</b></p>
            <ul class="lh-lg">
                <li><b>Download and extract the module file</b>: Ensure you have downloaded the <code>.zip</code> file of the module, then extract it to find a folder named <code>sepay</code> containing the <code>class.sepay.php</code> file.</li>
                <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/filezipmodule.png" class="img-fluid"></div>
                <li><b>Upload the module to the correct folder</b>: Open the HostBill source code and navigate to <code>public_html/includes/modules/Payment/</code>, then copy and paste the extracted <code>sepay</code> folder into this directory, as shown in the image below:</li>
                <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/filepathmodule.png" class="img-fluid"></div>
            </ul>
            <p>After completing the above steps, the <b>SePay module</b> has been successfully installed into <b>HostBill</b>. To check if the module has been installed, you can follow these steps:</p>
            <ul class="lh-lg">
                <li><b>Access the HostBill admin page</b>: Open your web browser, enter the URL for your HostBill admin page, and log in with your administrator account.</li>
                <li><b>Go to the module management section</b>: On the admin menu, select <code>Settings -> Modules -> Payment Modules</code> as shown in the image below:</li>
                <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/howtoopenmoduleconfig.png" class="img-fluid"></div>
                <li><b>Activate the module</b>: In the <b>Payment Modules</b> interface, you will see two tabs: <b>Active</b> and <b>Inactive</b>. When the module is first installed, it will appear in the <b>Inactive</b> tab <img style="max-height: 50px;" src="../assets/img/others/hostbill/tablayoutmoduleconfig.png" class="img-fluid">. Select the <b>Inactive</b> tab and scroll down to find the <b>SePay module</b>:</li>
                <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/sepaymoduleinactive.png" class="img-fluid"></div>
            </ul>
            
            <p>If you see the SePay module appear in the <b>Inactive</b> list, it means that the module has been successfully installed.</p>
<p class="h4 my-3" id="h1.2"><b>1.2. Module Configuration</b></p>
<p>To use the SePay payment method in HostBill, you need to configure some necessary information to connect the system with SePay. This information will ensure that HostBill can communicate and process payments through the SePay platform.</p>
<p class="h4 my-3" id="h1.2.1"><b>1.2.1. Activate Module</b></p>
<p><b>In step <a href="#h1.1">1.1</a></b>, we installed the SePay module into the HostBill system. To check, go to the <code>Payment Modules</code> management page. Here, you will see the SePay module appear in the <b>Inactive</b> list. The <b>Activate</b> button will appear next to this module. To activate the module, simply click the <b>Activate</b> button.</p>
<div><img style="max-height: 250px;" src="../assets/img/others/hostbill/activatemodule.png" class="img-fluid"></div>
<p>After successful activation, the module will appear in the <b>Active</b> tab as shown in the image below:</p>
<div><img style="max-height: 500px;" src="../assets/img/others/hostbill/sepaymoduleconfigtemp.png" class="img-fluid"></div>
<p class="h4 my-3" id="h1.2.2"><b>1.2.2. Payment Configuration</b></p>
<p>To automatically confirm payments, you need to configure payment information in the HostBill management section. Specifically:</p>
<ul class="lh-lg">
    <li>Go to the <b>Payment Modules</b> section (in <a href="#h1.2.1">1.2.1</a>)</li>
    <li>Select the SePay module that has been activated in the <b>Active</b> tab.</li>
    <li>Click the <b>Edit General Settings</b> button to enter configuration information for the module.</li>
    <div><img style="max-height: 500px;" src="../assets/img/others/hostbill/btnsettingmodule.png" class="img-fluid"></div>
</ul>
<p>This configuration information must match the information from the SePay system (detailed in section <a href="#1.2.3">1.2.3</a>). Specifically, each field will be guided as follows:</p>
<div><img style="max-height: 500px;" src="../assets/img/others/hostbill/sepaymoduleconfigtemp.png" class="img-fluid"></div>
<p class="h4 my-3" id="h*******"><b>*******. Field <span class="text-danger">Module Display Name</span>: </b></p>
<div><img style="max-height: 250px;" src="../assets/img/others/hostbill/banknamefield.png" class="img-fluid"></div>
<p>This is the display name of the module, and it will appear on the payment page, in the payment method selection section. You can also edit this name to suit your needs.</p>
<div><img style="max-height: 250px;" src="../assets/img/others/hostbill/banknamedisplay1.png" class="img-fluid"></div>
<p><i>Displayed in the cart when ordering</i></p>
<div><img style="max-height: 250px;" src="../assets/img/others/hostbill/banknamedisplay2.png" class="img-fluid"></div>
<p><i>Displayed on the payment page</i></p>
<p class="h4 my-3" id="h*******"><b>*******. Field <span class="text-danger">Bank Name</span>: </b></p>
<div><img style="max-height: 250px;" src="../assets/img/others/hostbill/banknamefield2.png" class="img-fluid"></div>
<p>This field contains a list of banks supported by SePay. You can select one or more banks that you want to use for payments. However, after selecting this bank, you need to connect with your bank on the SePay side to make it work, as detailed in section <a href="#*******">*******</a>.</p>
<p class="h4 my-3" id="h*******"><b>*******. Field <span class="text-danger">Bank Info</span></b></p>
<div><img style="max-height: 250px;" src="../assets/img/others/hostbill/bankinfofield.png" class="img-fluid"></div>
<p>This is where you enter bank information for customers to make payments via bank transfer. The information will be formatted as follows:</p>
<p><code>Bank_Name|Account_Holder_Name|Account_Number|Minimum_Invoice_Amount(option)|Maximum_Invoice_Amount(option)</code></p>
<p>Each piece of information is separated by the <code>|</code> symbol, and each bank's information will be on a separate line.</p>
<ul class="lh-lg">
    <li><code>Bank_Name</code>: This is the name of the bank you selected in the <b>Bank Name</b> field (section <a href="#h*******">*******</a>). You need to enter the exact name of the bank selected in that section.</li>
    <p>For example, if you select VietinBank in the <b>Bank Name</b> field</p>
    <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/banknameactive.png" class="img-fluid"></div>
    <p>then you need to enter that bank's name in the <b>Bank Info</b> field</p>
    <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/bankinfoinputbankname.png" class="img-fluid"></div>
    <li><code>Account_Holder_Name</code>: The name of the person or entity that owns or manages the account. This is typically the name of the individual or organization registered with the account, helping to identify and link the account to its owner.</li>
    <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/bankinfoinputbankaccountname.png" class="img-fluid"></div>
    <li><code>Account_Number</code>: A unique number assigned by the bank to each customer when opening an account. It is used to identify your account in the banking system.</li>
    <p><b>However</b>: For the banks <b>OCB, KienLongBank, MSB, BIDV</b>, please use the <b>VA</b> number (<a href="https://sepay.vn/blog/tai-khoan-ngan-hang-ao-la-gi-phan-loai-va-ung-dung" target="_blank">What is a VA number?</a>) instead of the bank account number.</p>
    <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/bankinfoinputbanknumber.png" class="img-fluid"></div>
    <li><code>Minimum_Invoice_Amount(option)</code>: The minimum invoice amount for which this bank will be displayed on the payment page.</li>
    <p><b>Example</b>: If you set the minimum invoice amount to <b>10,000 VND</b></p>
    <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/bankinfoinputminamounttest.png" class="img-fluid"></div>
    <p>then only invoices with a total value greater than <b>10,000 VND</b> will display this bank. If the invoice total is less than <b>10,000 VND</b>, the bank will not be displayed.</p>
    <li><code>Maximum_Invoice_Amount(option)</code>: The maximum invoice amount for which this bank will be displayed.</li>
    <p><b>Example</b>: If you set the maximum invoice amount to <b>20,000,000 VND</b></p>
    <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/bankinfoinputmaxamounttest.png" class="img-fluid"></div>
    <p>then only invoices with a total value less than <b>20,000,000 VND</b> will display this bank. If the invoice total exceeds <b>20,000,000 VND</b>, the bank will not be displayed.</p>
</ul>

<p><b class="text-danger">Note</b>: When selecting multiple banks, you need to enter the standard information for each bank on a separate line. Each line will correspond to a different bank.</p>
<p><b>Example</b>: Enter 3 banks as follows:</p>
<div><img style="max-height: 250px;" src="../assets/img/others/hostbill/bankinfoinputmultibank.png" class="img-fluid"></div>
<p>The configuration shown in the image corresponds to:</p>
<ul class="lh-lg">
    <li>For invoices with a total amount of <b>from 0 to 19,999,999 VND</b>, two banks <b>VietinBank</b> and <b>Agribank</b> of <b>NGUYEN VAN A</b> will be displayed.</li>
    <li>For invoices of <b>20,000,000 VND or more</b>, the bank <b>Techcombank</b> of <b>CONG TY TNHH ABC</b> will be displayed.</li>
</ul>
<p class="h4 my-3" id="h*******"><b>*******. Field <span class="text-danger">Allow adding transactions to invoice when payment amount is less than invoice amount</span>:</b></p>
<div><img style="max-height: 250px;" src="../assets/img/others/hostbill/allowamountlessthaninvoicefield.png" class="img-fluid"></div>
<p>When you select this field, it means the system will allow adding transactions to the invoice even when the payment amount is less than the total invoice amount.</p>
<p class="h4 my-3" id="h*******"><b>*******. Field <span class="text-danger">Allow adding transactions to invoice when payment amount is greater than invoice amount</span>:</b></p>
<div><img style="max-height: 250px;" src="../assets/img/others/hostbill/allowamountgreaththaninvoicefield.png" class="img-fluid"></div>
<p>When you select this field, it means the system will allow adding transactions to the invoice even when the payment amount is greater than the total invoice amount.</p>
<p class="h4 my-3" id="h*******"><b>*******. Field <span class="text-danger">Payment Code Prefix</span>:</b></p>
<div><img style="max-height: 250px;" src="../assets/img/others/hostbill/paymentprefixcodefield.png" class="img-fluid"></div>
<p>This field is the prefix for the transfer content <b>(This is required)</b>. In HostBill, the transfer content will include the invoice code, and this prefix will be added before the invoice code. This prefix must match the prefix you have set on the SePay side to verify the transaction (detailed information can be found in section <a href="#h*******">*******</a>).</p>
<p>For example, you set the prefix as <b>HB</b>: <img style="max-height: 50px;" src="../assets/img/others/hostbill/paymentprefixcodefieldex.png" class="img-fluid"> The payment information will display as follows:</p>
<div><img style="max-height: 250px;" src="../assets/img/others/hostbill/bankinfodisplayex.png" class="img-fluid"></div>
<p class="h4 my-3" id="h*******"><b>*******. Field <span class="text-danger">Template QR (QR Template)</span>:</b></p>
<div><img style="max-height: 250px;" src="../assets/img/others/hostbill/templateqrfield.png" class="img-fluid"></div>
<p>The Template for the QR image includes 3 options:</p>
<div class="lh-lg row">
    <div class="text-center col-12 col-lg-4 my-3">
        <p><b>Includes VietQR Frame (compact)</b></p>
        <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/qrtemplate1.png" class="img-fluid"></div>
    </div>
    <div class="text-center col-12 col-lg-4 my-3">
        <p><b>Shows QR code with V logo</b></p>
        <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/qrtemplate2.png" class="img-fluid"></div>
    </div>
    <div class="text-center col-12 col-lg-4 my-3">
        <p><b>Only shows QR code (qronly)</b></p>
        <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/qrtemplate3.png" class="img-fluid"></div>
    </div>
</div>
<p class="h4 my-3" id="h*******"><b>*******. Field <span class="text-danger">API Key (Authentication Key)</span>:</b></p>
<div><img style="max-height: 250px;" src="../assets/img/others/hostbill/apikeyfield.png" class="img-fluid"></div>
<p>This key is a random string that you need to enter <b>(required)</b>, and it must match the authentication configuration for the webhook from SePay (detailed information on how to configure this can be found in section <a href="#h*******">*******</a>).</p>
<p class="h4 my-3" id="h*******"><b>*******. Field <span class="text-danger">Callback URL</span>:</b></p>
<div><img style="max-height: 250px;" src="../assets/img/others/hostbill/callbackfield.png" class="img-fluid"></div>
<p>This is the URL that SePay will use to call back HostBill's system after the user completes the payment transaction. The purpose of this is for HostBill to continue processing the transaction and update the invoice status. This URL will be set from the SePay side, and you will be guided in detail in section <a href="#h*******">*******</a>.</p>
<p class="h4 my-3" id="h*******0"><b>*******0. Field <span class="text-danger">Periodic invoice status check interval (s)</span></b></p>
<div><img style="max-height: 250px;" src="../assets/img/others/hostbill/timeintervalfield.png" class="img-fluid"></div>
<p>This feature allows users to adjust the time interval for the system to periodically check the invoice status. The time can be set from 1 second to 10 seconds, with the default value being 3 seconds.</p>
<p>If you need the system to operate faster, you can reduce the time. Conversely, if the system needs more response time to avoid overload, you can increase the check time.</p>
<p class="h4 my-3" id="h*******1"><b>*******1. Other fields: <span class="text-danger">Background conversion</span>, <span class="text-danger">Force background conversion</span> and <span class="text-danger">Limit to selected currencies</span></b></p>
<div><img style="max-height: 250px;" src="../assets/img/others/hostbill/otherfielddefault.png" class="img-fluid"></div>
<p>These fields are default settings in HostBill for payment modules related to currency configuration. However, for SePay, currently only the VND currency is supported. Therefore, you can <b>ignore</b> this section when configuring the SePay module.</p>
<p class="h4 my-3" id="h1.2.3"><b>1.2.3. Create Webhook on SePay</b></p>
<p>To enable SePay to receive transactions and automatically update the invoice status in HostBill when users make payments, you need to create a webhook on SePay.</p>
<p><b class="text-danger">Note</b>: If you don't have a SePay account, please register a new account following the guide <a href="https://docs.sepay.vn/dang-ky-sepay.html" target="_blank">here</a>. After completing the registration, you need to add the bank account information you want to use. Detailed instructions on how to add a bank account can be found <a href="https://docs.sepay.vn/them-tai-khoan-ngan-hang.html" target="_blank">here</a>.</p>
<p>Below is a detailed guide on how to create a Webhook on SePay to connect with HostBill:</p>
<p class="h4 my-3" id="h*******"><b>*******. Create Webhook</b></p>
<p><b>Step 1</b>: Access the page <a href="https://my.sepay.vn/" target="_blank">https://my.sepay.vn/</a> in your browser, then log in with your account (if you don't have an account, create one). After logging in successfully, you will be redirected to the SePay dashboard as shown below:</p>
<div><img style="max-height: 500px;" src="../assets/img/others/hostbill/sepaydashboard.png" class="img-fluid"></div>
<p><b>Step 2</b>: In the left menu, select <b>Webhook Integration</b> to create a new Web

    <li><b>Name</b>: Enter any name to differentiate the webhooks from each other.</li>
    <p>Example name: <b>HostBill - QR Code Payment Transfer</b></p>
    <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/setnamewebhooktest.png" class="img-fluid"></div>
    <li><b>Select Event</b>: Choose the option <b>Incoming Payment</b>. Selecting this option will allow SePay to send information to the Callback URL in HostBill whenever there is an <b>Incoming Payment</b> transaction, used for confirming payments from users.</li>
    <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/setinmoneywebhook.png" class="img-fluid"></div>
    <li><b>Select Condition</b>: Choose the bank account you have set up in HostBill under the <b>Bank Name</b> section (section <a href="#h*******">*******</a>). Please note that you need to connect this bank account to the system before creating Webhooks (<a href="https://docs.sepay.vn/them-tai-khoan-ngan-hang.html">see instructions</a>).</li>
    <li>The <b>Ignore if transaction content does not have Payment Code?</b> section should be set to <b>Yes</b> if you only want to receive transactions based on the prefix set in the transaction content on HostBill in section <a href="#*******">*******</a> (Payment code recognition feature is configured in section <a href="#*******">*******</a>). However, you can select <b>No</b> to always receive transactions even if the content does not contain the payment prefix code, and you should integrate Telegram notifications when Webhooks fail (see instructions in section <a href="#h2">2</a>).</li>
    <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/conditionwebhook.png" class="img-fluid"></div>
    <li><b>Webhook Properties</b>: Includes:</li>
    <ul class="lh-lg">
        <li><b>Call to URL</b>: This is the address that SePay will use to send information to HostBill when there is a transaction from the user. This address is taken from the Callback URL field in the SePay payment module configuration in HostBill, section <a href="#*******">*******</a>.</li>
        <li><b>Is this a Payment Confirmation Webhook?</b>: Select Yes because the goal is to use Webhooks to confirm that the payment has been successfully made.</li>
        <li><b>Retry Webhooks when?</b>: Currently, SePay supports the feature of automatically retrying Webhooks if the Callback returns a status code <b>HTTP Status Code</b> outside the range of <code>200</code> to <code>299</code>. You can choose not to enable this and instead integrate Telegram notifications when the status code is outside the range (see Telegram notification integration instructions in section <a href="#h2">2</a>).</li>
    </ul>
    <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/propswwebhook.png" class="img-fluid"></div>
    <li><b>Webhook Authentication Configuration</b>: Configuring Webhook authentication is an important step to verify the validity of the Webhook.</li>
    <p>In this section, select <b>Authentication Type</b> as <b>API KEY</b> and enter the key that you have configured in the HostBill payment module in section <a href="#h*******">*******</a>.</p>
    <p><b>Request Content Type</b>, please select <b>application/json</b>.</p>
    <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/authconfigwebhook.png" class="img-fluid"></div>
    <li><b>Status</b>: Select <b>Activate</b> to enable the Webhook as soon as you complete the integration.</li>
    </ul>
    <p><b>Step 5</b>: Click the Add button <img style="max-height: 50px;" src="../assets/img/others/hostbill/savewebhook.png" class="img-fluid"> on the window to complete the integration.</p>
    <p>After completing all the above steps, you have successfully integrated Webhooks. You can check the result by viewing the Webhook list, and it will appear as shown below:</p>
    <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/addwebhooksuccess.png" class="img-fluid"></div>
    <p class="h4 my-3" id="h*******"><b>*******. Payment Code Structure</b></p>
    <p>In section <a href="#h*******">*******</a>, we have configured the prefix code for the transaction content. Now, the next step is to set up this prefix code in the SePay system to ensure that Webhooks can be sent successfully.</p>
    <p>To configure the payment code, follow these steps:</p>
    <p><b>Step 1</b>: On the My SePay interface, find the menu on the left and scroll down to the bottom. Here, you will find the <b>Statistics & Configuration</b> section. Select <b>Company Configuration</b> and then choose <b>General Settings</b>, and you will be redirected to the General Settings interface.</p>
    <p><b>Step 2</b>: In the Payment Code Structure section, click the Add New Template button <img style="max-height: 50px;" src="../assets/img/others/hostbill/addtemplateprefix.png" class="img-fluid"> to add your new code.</p>
    <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/addtemplaetprefixlayout.png" class="img-fluid"></div>
    <p><b>Step 3</b>: Enter the prefix code that you created in section <a href="#h*******">*******</a> on HostBill into the <b>Prefix</b> field and click the <b>Update</b> button. After completing this step, you have successfully configured the payment code in SePay, corresponding to the prefix code set in HostBill.</p>
    <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/addtemplateprefixfull.png" class="img-fluid"></div>
    <p>At this point, we have completed the configuration of the SePay payment confirmation module in the HostBill system and created Webhooks to receive notifications when the payment is successful. To check and test, please proceed to the next section.</p>
    <p class="h4 my-3" id="h1.3"><b>1.3 Payment Testing</b></p>
    <p>To check if everything is working according to the configuration, you can follow these steps:</p>
    <p class="h4 my-3" id="h1.3.1"><b>1.3.1 Perform Payment</b></p>
    
    <p><b>Step 1</b>: Try placing an order on your HostBill website and select the product with the lowest value for testing.</p>
    <p>For example: Place an order for <code>NVMe VPS - P1</code> on the website and click <b>Continue</b>:</p>
    <div><img style="max-height: 500px;" src="../assets/img/others/hostbill/ordertest.png" class="img-fluid"></div>
    <p><b>Step 2</b>: Select the payment method according to the module name in section <a href="#h*******">*******</a>.</p>
    <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/choosemethodpayment.png" class="img-fluid"></div>
    <p><b>Step 3</b>: The system will display the payment page or redirect to the invoice page, where the user can see the payment section. The interface of this page will depend on the template you are using in HostBill.</p>
    <p>Here is an example of a payment page:</p>
    <div><img style="max-height: 1000px;" src="../assets/img/others/hostbill/testtemplatepayment.png" class="img-fluid"></div>
    <p>Finally, the customer can make the payment by scanning the QR code or making a manual transfer according to the information provided. After the customer completes the transaction, the SePay system will automatically process and send a notification to HostBill through the Callback URL.</p>
    <p class="h4 my-3" id="h1.3.2"><b>1.3.2. Check Transaction and Webhooks Callback Status</b></p>
    <p>To check the transaction and webhook status, follow these steps:</p>
    <p><b>Step 1</b>: In the My SePay interface, find the menu on the left and select <b>Transactions</b></p>
    <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/menutranssepay.png" class="img-fluid"></div>
    <p>You will be redirected to the page displaying the list of received transactions.</p>
    <div><img style="max-height: 500px;" src="../assets/img/others/hostbill/transepaylayout.png" class="img-fluid"></div>
    <p><b>Step 2</b>: You can view the webhook status in the <b>Auto</b> column</p>
    <div><img style="max-height: 500px;" src="../assets/img/others/hostbill/transepaylayouthook.png" class="img-fluid"></div>
    <p>If the webhook status shows <b>success</b>, it means the transaction has been processed successfully. HostBill will receive the data and automatically add the transaction to the invoice and update the invoice status accordingly.</p>
    <p class="h3 my-3" id="h2"><b>2. Integrating Telegram Notifications for Manual Transaction Processing</b></p>
    <p>In cases where SePay cannot add the transaction to HostBill (unable to add transaction in HostBill), you can configure SePay to send notifications to a Telegram group, allowing the accounting team to promptly catch the transaction information that needs to be processed, and quickly handle the transaction manually.</p>
    <p>To have SePay send notifications to a Telegram group for cases where automatic payment recording is not possible as mentioned above, follow these steps:</p>
    <p><b>Step 1</b>: In the My SePay interface, find the menu on the left and select <b>Telegram Integration</b>, and you will be redirected to the integration screen.</p>
    <div><img style="max-height: 500px;" src="../assets/img/others/hostbill/menusepaytelegram.png" class="img-fluid"></div>
    <p><b>Step 2</b>: Click the <b>Add Integration</b> button <img style="max-height: 50px;" src="../assets/img/others/hostbill/addtelegram.png" class="img-fluid"> at the top right. After clicking, a setup window (modal) will appear, which contains the fields that need to be filled in to configure <b>Telegram</b> as shown below:</p>
    <div><img style="max-height: 1000px;" src="../assets/img/others/hostbill/formaddtelegram.png" class="img-fluid"></div>
    <p><b>Step 3</b>: Fill in all the information, including:</p>
    <ul class="lh-lg">
        <li><b>Select Event</b>: Choose the option "Incoming Funds". Selecting this option will allow you to receive notifications every time there is an incoming funds transaction.</li>
        <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/chooseeventtelegram.png" class="img-fluid"></div>
        <li><b>Configure Conditions</b>: Conditions for receiving notifications</li>
        <ul class="lh-lg">
            <li><b>When the main account is</b>: Select the bank account for which you want notifications that you have set up in HostBill under the Bank Name section (section <a href="#h*******">*******</a>), or you can select all.</li>
            <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/choosebanktelegram.png" class="img-fluid"></div>
            <li><b>[And] When the WebHooks status is</b>: Select <b>Ignore this condition</b>.</li>
            <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/chooseconditionwwebhook.png" class="img-fluid"></div>
            <li><b>[And] When WebHooks validates payment</b>: Select <b>Failed</b> to receive notifications when sending failed payment validation WebHooks.</li>
            <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/chooseconditionwwebhookpayment.png" class="img-fluid"></div>
            <li><b>[And] When the incoming amount is greater than or equal to</b>: Leave blank to receive all notifications.</li>
            <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/amounttelegramoption.png" class="img-fluid"></div>
            <li><b>[And] When the incoming amount is less than or equal to</b>: Leave blank to receive all notifications.</li>
            <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/amounttelegramoptionless.png" class="img-fluid"></div>
            <li><b>[And] When the payment content contains</b>: Leave blank to receive all notifications.</li>
            <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/contenttelegramoption.png" class="img-fluid"></div>
            <li><b>Ignore if payment content contains</b>: Leave blank to receive all notifications.</li>
            <div><img style="max-height: 250px;" src="../assets/img/others/hostbill/contenttelegramoptionmiss.png" class="img-fluid"></div>
        </ul>
    </ul>
    
    <li><b>Other Configurations</b>: Includes configurations on Telegram to receive notifications:</li>
    <ul class="lh-lg">
        <li><b>Telegram Chat ID</b>: This is the ID of the Telegram group that will receive notifications. To obtain the ID, follow these steps:</li>
        <ul class="lh-lg">
            <li>Step 1: Go to the Info section of the Telegram group that needs the balance notification.</li>
            <li>Step 2: Choose Add Member.</li>
            <li>Step 3: Search for <a href="https://t.me/autopay_telebot" target="_blank">SePay Bot</a> by typing <code>autopay_telebot</code> in the search bar and select Add.</li>
            <li>Step 4: SePay Bot will automatically send a message to the Telegram group to notify the Chat ID. Copy the Chat ID and enter it into the integration form.</li>
            <li>Step 5: Select Send Test Message, and SePay Bot will send a test message to the Telegram group.</li>
            <li>For detailed instructions, see <a href="https://docs.sepay.vn/tich-hop-telegram.html#chat_id" target="_blank">here</a>.</li>
        </ul>
        <li><b>Topic ID (Optional)</b>: For groups with a Topic ID.</li>
        <li><b>Name the Integration</b>: Give any name suitable for your purpose.</li>
        <li><b>Status</b>: Select Activate to make it active immediately after completing the integration.</li>
    </ul>
</ul>
<p><b>Step 4</b>: Select <b>Add</b> <img style="max-height: 50px;" src="../assets/img/others/hostbill/addformtelegramsubmit.png" class="img-fluid"> to create a new integration.</p>
<p><b>Step 5</b>: The system will take you to the Customize <b>Telegram Chat Content</b> interface. Here you can modify the content of the Telegram message as you like.</p>
<div><img style="max-height: 500px;" src="../assets/img/others/hostbill/templatesendtelegram.png" class="img-fluid"></div>
<p>After completing the configuration, you can return to step <a href="#h1.3">1.3</a> to check the system's activity. Perform a test by deliberately entering incorrect payment content, such as a transfer with inaccurate details. The system will then send a notification to the Telegram group chat, as shown in the image below:</p>
<div><img style="max-height: 250px;" src="../assets/img/others/hostbill/templatesendtelegramdemo.png" class="img-fluid"></div>
<div class="my-2"><p>Read more: <a href="woocommerce-integration.html">WebHooks Integration<i class="bi bi-chevron-right"></i></a></p></div>
</div>

        <!-- End Content -->
    </main>
    <!-- ========== END MAIN CONTENT ========== -->


    <!-- ========== SECONDARY CONTENTS ========== -->
    <!--contact-box-html-docs-->
    <div id="contact-box-overlay"></div>
    <div id="contact-box">
        <div class="popup">
          <a href="https://m.me/117903214582465" target="_blank" class="item">
            <div class="logo">
              <img src="../assets/img/others/fb-messenger.png" width="50%" />
            </div>
            <div class="meta">
              <p class="title">Facebook Messenger</p>
              <small class="description">24/7 live chat support</small>
            </div>
          </a>
          <div class="divide"></div>
          <a href="tel:02873059589" class="item">
            <div class="icon" style="background-color: #22c55e; color: #fff;">
              <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20"
                style="color: currentcolor;">
                <path
                  d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z"
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" fill="transparent"></path>
              </svg>
            </div>
            <div class="meta">
              <p class="title">Hotline</p>
              <small class="description">Support phone number</small>
            </div>
          </a>
          <div class="divide"></div>
          <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
            <div class="logo">
              <img src="../assets/img/others/youtube-social.png" />
            </div>
            <div class="meta">
              <p class="title">YouTube</p>
              <small class="description">Follow SePay's latest videos</small>
            </div>
          </a>
          <div class="divide"></div>
          <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
            <div class="logo">
              <img src="../assets/img/others/telegram-social.png" />
            </div>
            <div class="meta">
              <p class="title">Telegram</p>
              <small class="description">Get the latest updates from SePay</small>
            </div>
          </a>
        </div>
        <div class="container">
          <div class="dot-ping">
            <div class="ping"></div>
            <div class="dot"></div>
          </div>
          <div class="contact-icon">
            <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20"
              style="color: currentcolor;">
              <path fill-rule="evenodd" clip-rule="evenodd"
                d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z"
                fill="currentColor"></path>
            </svg>
          </div>
          <span style="font-weight: bold;">Contact Us</span>
        </div>
      </div>
    <!--/contact-box-html-docs-->

    <!-- Go To -->
    <a class="js-go-to go-to position-fixed" href="javascript:;" style="visibility: hidden;" data-hs-go-to-options='{
       "offsetTop": 700,
       "position": {
         "init": {
           "right": "2rem"
         },
         "show": {
           "bottom": "2rem"
         },
         "hide": {
           "bottom": "-2rem"
         }
       }
     }'>
        <i class="bi-chevron-up"></i>
    </a>
    <!-- ========== END SECONDARY CONTENTS ========== -->

    <!-- JS Global Compulsory  -->
    <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

    <!-- JS Implementing Plugins -->
    <script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
    <script src="../assets/vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js"></script>
    <script src="../assets/vendor/list.js/dist/list.min.js"></script>
    <script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>


    <!-- JS Front -->
    <script src="../assets/js/theme.min.js"></script>

    <!-- contact-box-js -->
    <script src="../assets/js/contact-box.js"></script>
    <!-- /contact-box-js -->

    <!-- JS Plugins Init. -->
    <script>
        (function () {
            // INITIALIZATION OF HEADER
            // =======================================================
            new HSHeader('#header').init()

            // INITIALIZATION OF NAV SCROLLER
            // =======================================================
            new HsNavScroller('.js-nav-scroller', {
                delay: 400,
                offset: 140
            })
            // INITIALIZATION OF LISTJS COMPONENT
            // =======================================================
            const docsSearch = HSCore.components.HSList.init('#docsSearch')




            // INITIALIZATION OF GO TO
            // =======================================================
            new HSGoTo('.js-go-to')
        })()
    </script>
</body>