<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Required Meta Tags Always Come First -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- Title -->
    <title>Guide to Setting Up General Settings for the Company | SePay</title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />

    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">
    <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
    <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />

    <!-- CSS Implementing Plugins -->
    <link rel="stylesheet" href="../assets/vendor/bootstrap-icons/font/bootstrap-icons.css">

    <!-- CSS Front Template -->
    <link rel="stylesheet" href="../assets/css/theme.min.css">
    <link rel="stylesheet" href="../assets/css/docs.min.css">

    <!--contact-box-css-->
    <link rel="stylesheet" href="../assets/css/contact-box.css">
    <!--/contact-box-css-->

    <style>
        .docs-navbar-sidebar-aside-body {
            padding-top: 3.5rem !important;
        }
    </style>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());

        gtag('config', 'G-J8DLMQTKSQ');
    </script>
</head>

<body class="navbar-sidebar-aside-lg">
    <!-- ========== HEADER ========== -->
    <header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
        <div class="container-fluid">
            <nav class="navbar-nav-wrap">
                <div class="row flex-grow-1">
                    <!-- Default Logo -->
                    <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
                        <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                            <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png"
                                alt="Logo">
                        </a>
                    </div>
                    <!-- End Default Logo -->
    
                    <div class="col-md px-lg-0">
                        <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
                            <div class="d-none d-md-block">
                                <!-- List Item Template -->
                                <div class="d-none">
                                    <div id="searchTemplate" class="dropdown-item">
                                        <a class="d-block link" href="#">
                                            <span class="category d-block fw-normal text-muted mb-1"></span>
                                            <span class="component text-dark"></span>
                                        </a>
                                    </div>
                                </div>
                                <!-- End List Item Template -->
                            </div>
    
                            <!-- Navbar -->
                            <ul class="navbar-nav p-0">
                                <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html"
                                        target="_blank">
                                        Support <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN"
                                        target="_blank">
                                        Youtube <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm"
                                        href="https://www.facebook.com/messages/t/sepay.vn" target="_blank">
                                        Facebook <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel"
                                        target="_blank">
                                        Telegram <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                </li>
                            </ul>
                            <!-- End Navbar -->
                        </div>
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Row -->
            </nav>
        </div>
    </header>
    
    <!-- ========== END HEADER ========== -->

    <!-- ========== MAIN CONTENT ========== -->
    <main id="content" role="main">
        <!-- Navbar -->
        <nav class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
        data-hs-nav-scroller-options='{
        "type": "vertical",
        "target": ".navbar-nav .active",
        "offset": 80
       }'>
        <!-- Navbar Toggle -->
        <button type="button" class="navbar-toggler btn btn-white d-grid w-100" data-bs-toggle="collapse"
            data-bs-target="#navbarVerticalNavMenu" aria-label="Toggle navigation" aria-expanded="false"
            aria-controls="navbarVerticalNavMenu">
            <span class="d-flex justify-content-between align-items-center">
                <span class="h3 mb-0">Nav menu</span>
    
                <span class="navbar-toggler-default">
                    <i class="bi-list"></i>
                </span>
    
                <span class="navbar-toggler-toggled">
                    <i class="bi-x"></i>
                </span>
            </span>
        </button>
        <!-- End Navbar Toggle -->
    
        <!-- Navbar Collapse -->
        <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
            <div class="navbar-brand-wrapper border-end" style="height: auto;">
                <!-- Default Logo -->
                <div class="d-flex align-items-center mb-3">
                    <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                        <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png"
                            alt="Logo">
                    </a>
                </div>
                <!-- End Default Logo -->
            </div>
    
            <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
                <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
                    <li class="nav-item">
                        <span class="nav-subtitle">Introduction</span>
                    </li>
    
                    <li class="nav-item">
                        <a class="nav-link" href="what-is-sepay.html">What is SePay?</a>
                    </li>
    
                    <li class="nav-item my-2 my-lg-5"></li>
    
                    <li class="nav-item">
                        <span class="nav-subtitle">General Instructions</span>
                    </li>
    
                    <li class="nav-item">
                        <a class="nav-link" href="register-sepay.html">Register SePay</a>
                    </li>
    
                    <li class="nav-item">
                        <a class="nav-link" href="add-bank-account.html">Add Bank Account</a>
                    </li>
    
                    <li class="nav-item">
                        <a class="nav-link" href="view-transactions.html">View Transactions</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link " href="users-and-permissions.html">Users & Permissions</a>
                    </li>
    
                    <li class="nav-item">
                        <a class="nav-link" href="sub-accounts.html">Sub Accounts</a>
                    </li>
    
                    <li class="nav-item my-2 my-lg-5"></li>
    
                    <li class="nav-item">
                        <span class="nav-subtitle">Company Configuration</span>
                    </li>
    
                    <li class="nav-item">
                        <a class="nav-link " href="service-packages.html">Service Packages</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link " href="invoices-and-payments.html">Invoices & Payments</a>
                    </li>
    
                    <li class="nav-item">
                        <a class="nav-link active" href="general-settings.html">General Settings</a>
                    </li>
    
                    <li class="nav-item my-2 my-lg-5"></li>
    
                    <li class="nav-item">
                        <span class="nav-subtitle">Balance Change Sharing</span>
                    </li>
    
                    <li class="nav-item">
                        <a class="nav-link " href="telegram-integration.html">Telegram Integration <i
                                class="bi bi-telegram ms-2 text-info"></i></a>
                    </li>
    
                    <li class="nav-item">
                        <a class="nav-link" href="lark-messenger-integration.html">Lark Messenger Integration <img
                                src="../assets/img/lark/lark-icon.png" class="img-fluid" style="max-height:25px"></a>
                    </li>
    
                    <li class="nav-item">
                        <a class="nav-link d-flex align-items-center" href="mobile-app.html">Mobile App <i
                                class="bi bi-phone-vibrate ms-2 fs-3 text-info"></i> </a>
                    </li>
    
                    <li class="nav-item my-2 my-lg-5"></li>
    
                    <li class="nav-item">
                        <span class="nav-subtitle">Web Integration</span>
                    </li>
    
                    <li class="nav-item">
                        <a class="nav-link" href="shopify-integration.html">Shopify Integration <img class="ms-2"
                                src="../assets/img/others/shopify-icon.png" style="width:22px; height: 22px;"></a>
                    </li>
    
                    <li class="nav-item">
                        <a class="nav-link" href="sapo-integration.html">Sapo Integration <img class="ms-2"
                                src="../assets/img/others/sapo-icon.png" style="width:18px; height: 18px;"></a>
                    </li>
    
                    <li class="nav-item">
                        <a class="nav-link" href="haravan-integration.html">Haravan Integration <img class="ms-2"
                                src="../assets/img/others/haravan-icon.png" style="width:22px; height: 22px;"></a>
                    </li>
    
                    <li class="nav-item">
                        <a class="nav-link" href="woocommerce.html">WooCommerce Integration <img class="ms-2"
                                src="../assets/img/others/woocommerce-icon.png" style="width:22px; height: 22px;"></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="google-sheets-integration.html">Google Sheets Integration <img
                                class="ms-2" src="../assets/img/others/google-sheets-icon.png"
                                style="width:22px; height: 22px;"></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="hostbill-integration.html">HostBill Integration <img class="ms-2"
                                src="../assets/img/others/hostbill-icon.png" style="height: 22px;"></a>
                    </li>
    
                    <li class="nav-item my-2 my-lg-5"></li>
    
                    <li class="nav-item">
                        <span class="nav-subtitle">Programming & Integration</span>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="webhooks-integration.html">WebHooks Integration</a>
                    </li>
    
                    <li class="nav-item ">
                        <a class="nav-link" href="webhooks-programming.html">WebHooks Programming</a>
                    </li>
                    <li class="nav-item ">
                        <a class="nav-link" href="simulate-transaction.html">Simulate Transaction</a>
                    </li>
                    <li class="nav-item ">
                        <a class="nav-link" href="create-qr-code-vietqr-static.html">Create & Embed QR Code</a>
                    </li>
                    <li class="nav-item my-2 my-lg-5"></li>
    
                    <li class="nav-item">
                        <span class="nav-subtitle">SePay API</span>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="api-introduction.html">API Introduction</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link " href="create-api-token.html">Create API Token</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link " href="transaction-api.html">Transaction API</a>
                    </li>
                   <li class="nav-item">
                  <a class="nav-link" href="bank-account-api.html">Bank Account API</a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="api-va-order-bidv.html">API Va Order</a>
            </li>
    
                </ul>
            </div>
        </div>
        <!-- End Navbar Collapse -->
    </nav>
    
        <!-- End Navbar -->

        <!-- Content -->
        <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">

            <div class="docs-page-header">
                <div class="row align-items-center">
                    <div class="col-sm">
                        <h1 class="docs-page-header-title">Guide to Setting Up General Settings for the Company</h1>
                        <p class="docs-page-header-text"></p>
                    </div>
                </div>
            </div>
        
            <p>This feature can be found in the menu <i class="bi bi-gear-wide-connected"></i> Company -> <a
                    href="https://my.sepay.vn/company/configuration">General Settings</a></p>
            <p>The <b>General Settings</b> feature helps you set up the General Settingss for the company, including:</p>
            <ul class="lh-lg">
                <li><b>Sub-accounts</b>: Enable / disable sub-accounts.</li>
                <li><b>Payment Code Recognition</b>: Enable / disable automatic payment code recognition.</li>
            </ul>
            <p>When you disable the sub-account feature, the <a href="https://my.sepay.vn/transactions">Transaction List</a>
                will no longer have the Sub-account column.</p>
        
            <div class="alert alert-soft-dark" role="alert">
                <div class="d-flex align-items-baseline">
                    <div class="flex-shrink-0">
                        <i class="bi-info-circle me-1"></i>
                    </div>
        
                    <div class="flex-grow-1 ms-2"><b>Sub-account</b> (identity account) is a virtual bank account belonging to the main account. When users transfer money into the sub-account, the money will go into the main account. Sub-accounts are useful for identifying the revenue of each store/branch.</div>
                </div>
            </div>
        
            <h3 id="payment-code" class="hs-docs-heading">
                Payment Code Structure Configuration <a class="anchorjs-link" href="#payment-code" aria-label="Anchor"
                    data-anchorjs-icon="#"></a>
            </h3>
        
            <p>The payment code is a text entered in the Payment Content when customers make a transfer. It helps differentiate the payment content for a specific order, invoice, or customer. It is useful for features such as:</p>
        
            <ul class="lh-lg">
                <li>WebHooks for automatic transaction validation</li>
                <li>Automatic order validation for e-commerce platforms like Sapo, Haravan, Shopify...</li>
            </ul>
        
            <div class="alert alert-soft-dark" role="alert">
                <div class="d-flex align-items-baseline">
                    <div class="flex-shrink-0">
                        <i class="bi-info-circle me-1"></i>
                    </div>
        
                    <div class="flex-grow-1 ms-2">After configuring the payment code, every time a webhook is triggered, the payment code will be visible in the <code>code</code> field. Learn more about webhooks <a
                            href="https://docs.sepay.vn/tich-hop-webhooks.html#du-lieu">here</a></div>
                </div>
            </div>
        
            <p>In General Settings, you can configure one or more payment codes for SePay to recognize. The configurations include:</p>
            <ul class="lh-lg">
                <li><b>Prefix</b>: The first 2 to 3 characters, must be letters. For example, SEM, DH, VIN</li>
                <li><b>Suffix</b>: The characters following the prefix. Can be configured from 1 to 10 characters, either numeric or alphanumeric.</li>
            </ul>
        
            <p>Examples of payment codes:</p>
            <ul class="lh-lg">
                <li><code>DH1202917</code>, <code>DH12378197</code>: The prefix is configured as <code>DH</code>, the suffix is from <code>7 characters</code> to <code>8 characters</code>, and is <code>Numeric</code>.</li>
                <li><code>VIN19AZ83DM</code>: The prefix is configured as <code>VIN</code>, the suffix is from <code>7 characters</code> to <code>8 characters</code>, and is <code>Alphanumeric</code>.</li>
            </ul>
        
            <img src="../assets/img/others/multi-paycode.png" style="max-height: 500px;" />
        
            <h3 id="storage-time" class="hs-docs-heading">
                Data Retention Time <a class="anchorjs-link" href="#storage-time" aria-label="Anchor"
                    data-anchorjs-icon="#"></a>
            </h3>
        
            <p class="mt-2">SePay allows you to customize the data retention time with options such as 1 hour, 8 hours, 1 day, 3 days, 7 days, 1 month, 3 months, 6 months (default), 1 year (requires additional purchase), 2 years (requires additional purchase), and 3 years (requires additional purchase).</p>
        
            <p class="mt-2">This option is useful if you don't want SePay to store data for too long. After the specified period, all data related to transactions, Telegram chat logs, Lark Messenger logs, and Webhook logs will be deleted.</p>
            <p class="mt-2">If you want SePay to store data for more than 6 months, please contact SePay for a quote.</p>
        
            <div class="alert alert-soft-dark" role="alert">
                <div class="d-flex align-items-baseline">
                    <div class="flex-shrink-0">
                        <i class="bi-info-circle me-1"></i>
                    </div>
        
                    <div class="flex-grow-1 ms-2">Note: Features in the Statistics section (such as Cash Flow Overview, Transaction Count, Account Balance, Money in Sub-Account) may lack data for display if you set a low data retention time. The Counter feature is not affected.</div>
                </div>
            </div>
            <p class="mt-2">The task of deleting expired data is performed every hour (for hourly retention options) and once a day (for daily, monthly, or yearly options). Therefore, if you recently changed the retention time configuration to a shorter period, the related data may not be deleted immediately.</p>
        
        </div>
        
        <!-- End Content -->
    </main>
    <!-- ========== END MAIN CONTENT ========== -->

    <!-- ========== SECONDARY CONTENTS ========== -->
    <!--contact-box-html-docs-->
    <div id="contact-box-overlay"></div>
    <div id="contact-box">
        <div class="popup">
            <a href="https://m.me/***************" target="_blank" class="item">
                <div class="logo">
                    <img src="../assets/img/others/fb-messenger.png" width="50%" />
                </div>
                <div class="meta">
                    <p class="title">Facebook Messenger</p>
                    <small class="description">24/7 live chat support</small>
                </div>
            </a>
            <div class="divide"></div>
            <a href="tel:02873059589" class="item">
                <div class="icon" style="background-color: #22c55e; color: #fff;">
                    <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20"
                        style="color: currentcolor;">
                        <path
                            d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" fill="transparent"></path>
                    </svg>
                </div>
                <div class="meta">
                    <p class="title">Hotline</p>
                    <small class="description">Support phone number</small>
                </div>
            </a>
            <div class="divide"></div>
            <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
                <div class="logo">
                    <img src="../assets/img/others/youtube-social.png" />
                </div>
                <div class="meta">
                    <p class="title">Youtube</p>
                    <small class="description">Follow SePay's latest videos</small>
                </div>
            </a>
            <div class="divide"></div>
            <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
                <div class="logo">
                    <img src="../assets/img/others/telegram-social.png" />
                </div>
                <div class="meta">
                    <p class="title">Telegram</p>
                    <small class="description">Get the latest updates from SePay</small>
                </div>
            </a>
        </div>
        <div class="container">
            <div class="dot-ping">
                <div class="ping"></div>
                <div class="dot"></div>
            </div>
            <div class="contact-icon">
                <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20"
                    style="color: currentcolor;">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z"
                        fill="currentColor"></path>
                </svg>
            </div>
            <span style="font-weight: bold;">Contact Us</span>
        </div>
    </div>
    
    <!--/contact-box-html-docs-->

    <!-- Go To -->
    <a class="js-go-to go-to position-fixed" href="javascript:;" style="visibility: hidden;" data-hs-go-to-options='{
       "offsetTop": 700,
       "position": {
         "init": {
           "right": "2rem"
         },
         "show": {
           "bottom": "2rem"
         },
         "hide": {
           "bottom": "-2rem"
         }
       }
     }'>
        <i class="bi-chevron-up"></i>
    </a>
    <!-- ========== END SECONDARY CONTENTS ========== -->

    <!-- JS Global Compulsory  -->
    <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

    <!-- JS Implementing Plugins -->
    <script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
    <script src="../assets/vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js"></script>
    <script src="../assets/vendor/list.js/dist/list.min.js"></script>
    <script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>


    <!-- JS Front -->
    <script src="../assets/js/theme.min.js"></script>

    <!-- contact-box-js -->
    <script src="../assets/js/contact-box.js"></script>
    <!-- /contact-box-js -->

    <!-- JS Plugins Init. -->
    <script>
        (function () {
            // INITIALIZATION OF HEADER
            // =======================================================
            new HSHeader('#header').init()

            // INITIALIZATION OF NAV SCROLLER
            // =======================================================
            new HsNavScroller('.js-nav-scroller', {
                delay: 400,
                offset: 140
            })
            // INITIALIZATION OF LISTJS COMPONENT
            // =======================================================
            const docsSearch = HSCore.components.HSList.init('#docsSearch')




            // INITIALIZATION OF GO TO
            // =======================================================
            new HSGoTo('.js-go-to')
        })()
    </script>
</body>

</html>