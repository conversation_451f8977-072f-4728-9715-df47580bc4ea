<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    
        <title>API VA by Order | SePay</title>
    
        <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />
    
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">
        <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
        <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
    
        <link rel="stylesheet" href="../assets/css/bootstrap-icons/bootstrap-icons.css">
        <link rel="stylesheet" href="../assets/css/theme.min.css">
        <link rel="stylesheet" href="../assets/css/docs.min.css">
        <link rel="stylesheet" href="../assets/css/contact-box.css">
    
        <style>
            .docs-navbar-sidebar-aside-body {
                padding-top: 3.5rem !important;
            }
    
            .navbar-expand .nav-item:not(:last-child) {
                margin-right: 0;
            }
        </style>
    
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());
    
            gtag('config', 'G-J8DLMQTKSQ');
        </script>
    </head>
    
    <body class="navbar-sidebar-aside-lg">
		<header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
            <div class="container-fluid">
              <nav class="navbar-nav-wrap">
                <div class="row flex-grow-1">
                  <!-- Default Logo -->
                  <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
                    <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                      <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo">
                    </a>
                   
                  </div>
                  <!-- End Default Logo -->
        
                  <div class="col-md px-lg-0">
                    <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
                      <div class="d-none d-md-block">
                       
        
                        <!-- List Item Template -->
                        <div class="d-none">
                          <div id="searchTemplate" class="dropdown-item">
                            <a class="d-block link" href="#">
                              <span class="category d-block fw-normal text-muted mb-1"></span>
                              <span class="component text-dark"></span>
                            </a>
                          </div>
                        </div>
                        <!-- End List Item Template -->
                      </div>
        
                      <!-- Navbar -->
                      <ul class="navbar-nav p-0">
                        <li class="nav-item">
                          <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html" target="_blank">
                            Support <i class="bi-box-arrow-up-right ms-1"></i>
                          </a>
                        </li>
                        <li class="nav-item">
                          <a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN" target="_blank">
                            Youtube <i class="bi-box-arrow-up-right ms-1"></i>
                          </a>
                        </li>
                        <li class="nav-item">
                                            <a class="btn btn-ghost-secondary btn-sm" href="https://www.facebook.com/messages/t/sepay.vn" target="_blank">
                                              Facebook <i class="bi-box-arrow-up-right ms-1"></i>
                                            </a>
                                          </li>
                                          <li class="nav-item">
                                            <a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel" target="_blank">
                                              Telegram <i class="bi-box-arrow-up-right ms-1"></i>
                                            </a>
                                          </li>
                        
                      </ul>
                      <!-- End Navbar -->
                    </div>
                  </div>
                  <!-- End Col -->
                </div>
                <!-- End Row -->
              </nav>
            </div>
          </header>

        <main id="content" role="main">
			<nav class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
            data-hs-nav-scroller-options='{
            "type": "vertical",
            "target": ".navbar-nav .active",
            "offset": 80
           }'>
            <!-- Navbar Toggle -->
            <button type="button" class="navbar-toggler btn btn-white d-grid w-100" data-bs-toggle="collapse"
                data-bs-target="#navbarVerticalNavMenu" aria-label="Toggle navigation" aria-expanded="false"
                aria-controls="navbarVerticalNavMenu">
                <span class="d-flex justify-content-between align-items-center">
                    <span class="h3 mb-0">Nav menu</span>
    
                    <span class="navbar-toggler-default">
                        <i class="bi-list"></i>
                    </span>
    
                    <span class="navbar-toggler-toggled">
                        <i class="bi-x"></i>
                    </span>
                </span>
            </button>
            <!-- End Navbar Toggle -->
    
            <!-- Navbar Collapse -->
            <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
                <div class="navbar-brand-wrapper border-end" style="height: auto;">
                    <!-- Default Logo -->
                    <div class="d-flex align-items-center mb-3">
                        <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                            <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png"
                                alt="Logo">
                        </a>
    
                    </div>
                    <!-- End Default Logo -->
                </div>
    
                <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
                    <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
                        <li class="nav-item">
                            <span class="nav-subtitle">Introduction</span>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="what-is-sepay.html">What is SePay?</a>
                        </li>
                
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">General Guide</span>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="register-sepay.html">Register SePay</a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="add-bank-account.html">Add Bank Account</a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link " href="view-transactions.html">View Transactions</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="users-and-permissions.html">Users & Permissions</a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="sub-accounts.html">Sub Accounts</a>
                        </li>
                
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">Company Configuration</span>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link " href="service-packages.html">Service Packages</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="invoices-and-payments.html">Invoices & Payments</a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link " href="general-settings.html">General Settings</a>
                        </li>
                
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">Balance Change Sharing</span>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link " href="telegram-integration.html">Telegram Integration <i class="bi bi-telegram ms-2 text-info"></i></a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="lark-messenger-integration.html">Lark Messenger Integration <img src="../assets/img/lark/lark-icon.png" class="img-fluid" style="max-height:25px"></a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link d-flex align-items-center" href="mobile-app.html">Mobile App <i class="bi bi-phone-vibrate ms-2 fs-3 text-info"></i> </a>
                        </li>
                
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">Web Integration</span>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="shopify-integration.html">Shopify Integration <img class="ms-2" src="../assets/img/others/shopify-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="sapo-integration.html">Sapo Integration <img class="ms-2" src="../assets/img/others/sapo-icon.png" style="width:18px; height: 18px;"></a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="haravan-integration.html">Haravan Integration <img class="ms-2" src="../assets/img/others/haravan-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="woocommerce-integration.html">WooCommerce Integration <img class="ms-2" src="../assets/img/others/woocommerce-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="google-sheets-integration.html">Google Sheets Integration <img class="ms-2" src="../assets/img/others/google-sheets-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="hostbill-integration.html">HostBill Integration <img class="ms-2"
                                    src="../assets/img/others/hostbill-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">Programming & Integration</span>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="webhooks-integration.html">WebHooks Integration</a>
                        </li>
                
                        <li class="nav-item ">
                            <a class="nav-link" href="webhooks-programming.html">WebHooks Programming</a>
                        </li>
                        <li class="nav-item ">
                            <a class="nav-link" href="simulate-transactions.html">Simulate Transactions</a>
                        </li>
                        <li class="nav-item ">
                            <a class="nav-link" href="create-embed-qr-code-vietqr.html">Create & Embed QR Code</a>
                        </li>
                
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">SePay API</span>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="api-introduction.html">API Introduction</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="create-api-token.html">Create API Token</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="transaction-api.html">Transaction API</a>
                        </li>
<li class="nav-item">
                  <a class="nav-link" href="bank-account-api.html">Bank Account API</a>
              </li>
            
                        <li class="nav-item">
                            <a class="nav-link active" href="api-va-order-bidv.html">API Va Order</a>
                        </li>
                    </ul>
                </div>
                
            </div>
            <!-- End Navbar Collapse -->
        </nav>

			<div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10" style="max-width: 1200px;">
                <div class="docs-page-header">
                    <div class="row align-items-center">
                        <div class="col-sm">
                            <h1 class="docs-page-header-title">Order-based VA API for BIDV Business Accounts</h1>
                            <p class="docs-page-header-text">The order-based Virtual Account (VA) API is a payment confirmation automation solution for BIDV business accounts. Instead of using a fixed VA, each order will be assigned a unique VA with an exact matching amount.</p>
                        </div>
                    </div>
                </div>
                <img src="images/va-order/va-order-flow.png" alt="VA Order Flow" class="img-fluid rounded mb-4" />
                <h2 id="workflow-explanation" class="hs-docs-heading">
                    Workflow Explanation
                </h2>
                <p>When a customer makes a payment through a Virtual Account (VA), the entire process is handled automatically through the following steps:</p>
                <ol>
                    <li>After the customer completes their order on your website, the system automatically connects to SePay via the API to create a new order. SePay will return a unique VA number for this order.</li>
                    <li>Your website immediately displays the payment information to the customer, including:
                        <ul>
                            <li>The VA account number to transfer to</li>
                            <li>The exact amount to be paid</li>
                            <li>The validity period of the VA</li>
                            <li>A QR code for quick payment scanning</li>
                        </ul>
                    </li>
                    <li>The customer can easily make a payment by scanning the QR code or directly transferring the amount to the assigned VA.</li>
                    <li>Once the transaction is successfully completed, the bank will automatically send a notification to SePay to confirm the payment.</li>
                    <li>SePay receives the notification and immediately forwards this information to your website via <a href="webhook-integration.html" target="_blank">Webhook</a>.</li>
                    <li>Your system processes the received information, updates the order status, and displays a payment confirmation notification to the customer.</li>
                </ol>
                <h2 id="what-you-can-do-with-this-api" class="hs-docs-heading">
                    What Can You Do with This API? <a class="anchorjs-link" href="#what-you-can-do-with-this-api" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                
                <p>SePay allows you to perform the following queries with order-based Virtual Accounts (VA):</p>
                <ul>
                    <li><a href="#order-list">Retrieve the list of orders</a></li>
                    <li><a href="#create-order">Create a new order</a></li>
                    <li><a href="#order-details">Retrieve order details</a></li>
                    <li><a href="#create-additional-va">Create an additional VA for an order</a></li>
                    <li><a href="#huy-don-hang">Cancel an order</a></li>
                    <li><a href="#huy-va">Cancel the VA of an order</a></li>
                </ul>
                <h2 id="getting-started" class="hs-docs-heading">
                    Getting Started <a class="anchorjs-link" href="#getting-started" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                <p>API URL:</p>
                <pre class="rounded mb-4">
                    <code class="language-http" data-lang="http">
                        https://my.sepay.vn/userapi/bidv/{bank_account_id}
                    </code>
                </pre>
                <p>To use the order-based VA API, you need to:</p>
                <ol>
                    <li>Create an <a href="create-api-token.html">API Token</a> to authenticate your API requests.</li>
                    <li>
                        <p>Obtain the <code>bank_account_id</code> for your BIDV business account from the <a href="bank-account-api.html">Bank Account API</a> or directly from the bank account management interface on SePay.</p>
                        <img src="../assets/img/api-va-theo-don-hang/bank-account-id.png" alt="Obtain bank_account_id" class="img-fluid rounded mb-4" />
                    </li>
                </ol>
                <h2 id="prrliylymet
                    Partial Payment <a class="anchorjs-link" href="#prrli-lorsyme-ticon="#"></a>
                </h2>
                <p>SePay supports partial payments for orders through the Virtual Account (VA) mechanism. When using this feature:</p>
                <ul>
                    <li>You can create an order with the full amount, but allow customers to pay in multiple installments by:
                        <ul>
                            <li>Setting the <code>amount</code> smaller than the order amount when creating a new VA</li>
                            <li>Setting <code>amount</code> to <code>null</code> so the VA can receive multiple payments of any amount until the total order amount is reached</li>
                        </ul>
                    </li>
                    <li>
                        When a customer makes a partial payment, the order status will change to <code>Partially</code>
                    </li>
                    <li>
                        When the total payment amount reaches the full order amount, the order status will automatically change to <code>Paid</code> and the VA will no longer accept additional payments
                    </li>
                </ul>
                <h2 id="order-list" class="hs-docs-heading">
                    Retrieve the List of Orders <a class="anchorjs-link" href="#order-list" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                <div class="mb-3">
                    <span class="badge bg-soft-success text-success py-2 px-3 me-2">GET</span>
                    <code>/orders</code>
                </div>
                <p>Retrieve a paginated list of orders that have been created.</p>
                <p>Example:</p>
                <pre class="rounded mb-4">
                    <code class="language-http" data-lang="http">
                        GET https://my.sepay.vn/userapi/bidv/123456/orders
                        Authorization: Bearer {token}
                
                        HTTP/1.1 200 OK
                        Content-Type: application/json
                        {
                            "status": "success",
                            "data": {
                                "orders": [
                                    {
                                        "id": "b64247d3-c343-11ef-9c27-52c7e9b4f41b",
                                        "order_code": "ORD123456789",
                                        "amount": 2000,
                                        "paid_amount": 2000,
                                        "status": "Paid",
                                        "created_at": "2024-12-26 11:41:46",
                                        "bank_name": "BIDV",
                                        "account_number": "**********",
                                        "account_holder_name": "NGO QUOC DAT",
                                        "va": [
                                            {
                                                "va_number": "963NQDORDRSIKYXYPTZ",
                                                "va_holder_name": "NGO QUOC DAT",
                                                "amount": 2000,
                                                "status": "Paid",
                                                "expired_at": "2024-12-26 11:51:45",
                                                "paid_at": "2024-12-26 11:42:12"
                                            }
                                        ]
                                    }
                                ],
                                "pagination": {
                                    "total": 1,
                                    "per_page": 20,
                                    "current_page": 1,
                                    "last_page": 1
                                }
                            }
                        }
                    </code>
                </pre>
                
                <h2 id="create-order" class="hs-docs-heading">
                    Create a New Order <a class="anchorjs-link" href="#create-order" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                
                <div class="mb-3">
                    <span class="badge bg-soft-warning text-warning py-2 px-3 me-2">POST</span>
                    <code>/orders</code>
                </div>
                <p>Create a new order.</p>
                <div class="card mb-5">
                    <div class="table-responsive">
                        <table class="table">
                            <thead class="thead-light">
                                <tr>
                                    <th>Attribute</th>
                                    <th>Description</th>
                                    <th>Type</th>
                                    <th>Required</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>amount</td>
                                    <td>Order amount</td>
                                    <td>number</td>
                                    <td>Yes</td>
                                </tr>
                                <tr>
                                    <td>order_code</td>
                                    <td>Order code</td>
                                    <td>string</td>
                                    <td>No</td>
                                </tr>
                                <tr>
                                    <td>duration</td>
                                    <td>
                                        Order validity duration (in seconds), set to null for unlimited payment time<br>
                                        - Default is 600 seconds (10 minutes)<br>
                                        - Maximum is ******** seconds (1 year)
                                    </td>
                                    <td>number or null</td>
                                    <td>No</td>
                                </tr>
                                <tr>
                                    <td>va_holder_name</td>
                                    <td>
                                        VA account holder name (only accepts uppercase letters, numbers, and spaces, maximum 70 characters)
                                    </td>
                                    <td>string</td>
                                    <td>No</td>
                                </tr>
                                <tr>
                                    <td>with_qrcode</td>
                                    <td>Request to generate QR Code</td>
                                    <td>boolean</td>
                                    <td>No</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="alert alert-warning" role="alert">
                    <strong>Note:</strong> The <code>va_holder_name</code> parameter is only supported for BIDV enterprise accounts that have enabled the custom VA holder name feature. Please contact SePay support to enable this feature.
                </div>
                <p>Example:</p>
                <pre class="rounded mb-4">
                    <code class="language-http" data-lang="http">
                        POST https://my.sepay.vn/userapi/bidv/123456/orders
                        Authorization: Bearer {token}
                        Content-Type: application/json

                        {
                            "amount": 100000,
                            "order_code": "ORD123456789",
                            "duration": 300,
                            "with_qrcode": true
                        }

                        HTTP/1.1 200 OK
                        Content-Type: application/json
                        {
                            "status": "success",
                            "message": "Order created successfully",
                            "data": {
                                "order_id": "f23cc0fe-c343-11ef-9c27-52c7e9b4f41b",
                                "order_code": "ORD123456789",
                                "va_number": "963NQDORDZVTBPJ3Z7H",
                                "va_holder_name": "NGO QUOC DAT",
                                "amount": 2000,
                                "status": "Pending",
                                "bank_name": "BIDV",
                                "account_holder_name": "NGO QUOC DAT",
                                "account_number": "**********",
                                "expired_at": "2024-12-26 11:53:26",
                                "qr_code": "data:image/png;base64,...==",
                                "qr_code_url": "https://qr.sepay.vn/img?acc=...",
                            }
                        }
                    </code>
                </pre>

                <h2 id="order-details" class="hs-docs-heading">
                    Order Details <a class="anchorjs-link" href="#order-details" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                <div class="mb-3">
                    <span class="badge bg-soft-success text-success py-2 px-3 me-2">GET</span>
                    <code>/orders/{order_id}</code>
                </div>
                <p>Retrieve the details of an order by its ID.</p>

                <p>Example:</p>
                <pre class="rounded mb-4">
                    <code class="language-http" data-lang="http">
                        GET https://my.sepay.vn/userapi/bidv/123456/orders/b64247d3-c343-11ef-9c27-52c7e9b4f41b
                        Authorization: Bearer {token}

                        HTTP/1.1 200 OK
                        Content-Type: application/json
                        {
                            "status": "success",
                            "data": {
                                "id": "b64247d3-c343-11ef-9c27-52c7e9b4f41b",
                                "order_code": "ORD123456789",
                                "amount": 2000,
                                "paid_amount": 2000,
                                "status": "Paid",
                                "created_at": "2024-12-26 11:41:46",
                                "bank_name": "BIDV",
                                "account_number": "**********",
                                "account_holder_name": "NGO QUOC DAT",
                                "va": [
                                    {
                                        "va_number": "963NQDORDRSIKYXYPTZ",
                                        "va_holder_name": "NGO QUOC DAT",
                                        "amount": 2000,
                                        "status": "Paid",
                                        "expired_at": "2024-12-26 11:51:45",
                                        "paid_at": "2024-12-26 11:42:12"
                                    }
                                ]
                            }
                        }
                    </code>
                </pre>
                <h2 id="tao-them-va" class="hs-docs-heading">
                    Create Additional VA <a class="anchorjs-link" href="#tao-them-va" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                <div class="mb-3">
                    <span class="badge bg-soft-warning text-warning py-2 px-3 me-2">POST</span>
                    <code>/orders/{order_id}/va</code>
                </div>
                <p>Create a new VA for an existing order.</p>
                
                <div class="card mb-5">
                    <div class="table-responsive">
                        <table class="table">
                            <thead class="thead-light">
                                <tr>
                                    <th>Attribute</th>
                                    <th>Description</th>
                                    <th>Type</th>
                                    <th>Required</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>amount</td>
                                    <td>
                                        VA amount<br>
                                        - Default is the order amount<br>
                                        - Can be set to a smaller value to allow partial payments<br>
                                        - Set to <code>null</code> to allow multiple payments of any amount until the total order amount is reached
                                    </td>
                                    <td>number or null</td>
                                    <td>Yes</td>
                                </tr>
                                <tr>
                                    <td>duration</td>
                                    <td>
                                        VA lifetime (in seconds), set to null for unlimited payment time<br>
                                        - Default is 600 seconds (10 minutes)<br>
                                        - Maximum is ******** seconds (1 year)
                                    </td>
                                    <td>number or null</td>
                                    <td>No</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <p>Example:</p>
                <pre class="rounded mb-4">
                    <code class="language-http" data-lang="http">
                        POST https://my.sepay.vn/userapi/bidv/123456/orders/b64247d3-c343-11ef-9c27-52c7e9b4f41b/va
                        Authorization: Bearer {token}
                        Content-Type: application/json

                        {
                            "amount": 100000,
                            "va_holder_name": "NGO QUOC DAT",
                            "duration": 300
                        }

                        HTTP/1.1 200 OK
                        Content-Type: application/json
                        {
                            "status": "success",
                            "message": "VA created successfully",
                            "data": {
                                "va_number": "963NQDORD8DTYFPW5MV",
                                "va_holder_name": "NGO QUOC DAT",
                                "amount": 2000,
                                "status": "Unpaid",
                                "expired_at": "2024-12-26 11:55:55"
                            }
                        }
                    </code>
                </pre>
                <h2 id="huy-don-hang" class="hs-docs-heading">
                    Cancel Order <a class="anchorjs-link" href="#huy-don-hang" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                <div class="mb-3">
                    <span class="badge bg-soft-danger text-danger py-2 px-3 me-2">DELETE</span>
                    <code>/orders/{order_id}</code>
                </div>
                <p>Cancel order.</p>
                <p>Example:</p>
                <pre class="rounded mb-4">
                    <code class="language-http" data-lang="http">
                        DELETE https://my.sepay.vn/userapi/bidv/123456/orders/b64247d3-c343-11ef-9c27-52c7e9b4f41b
                        Authorization: Bearer {token}

                        HTTP/1.1 200 OK
                        Content-Type: application/json
                        {
                            "status": "success",
                            "message": "Order cancelled successfully",
                            "data": {
                                "status": "Cancelled"
                            }
                        }
                    </code>
                </pre>
                <h2 id="huy-va" class="hs-docs-heading">
                    Cancel Va <a class="anchorjs-link" href="#huy-va" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                <div class="mb-3">
                    <span class="badge bg-soft-danger text-danger py-2 px-3 me-2">DELETE</span>
                    <code>/orders/{order_id}/va/{va_number}</code>
                </div>
                <p>Cancel VA.</p>
                <p>Example:</p>
                <pre class="rounded mb-4">
                    <code class="language-http" data-lang="http">
                        DELETE https://my.sepay.vn/userapi/bidv/123456/orders/b64247d3-c343-11ef-9c27-52c7e9b4f41b/va/963NQDORD8DTYFPW5MV
                        Authorization: Bearer {token}

                        HTTP/1.1 200 OK
                        Content-Type: application/json
                        {
                            "status": "success",
                            "message": "VA cancelled successfully",
                            "data": {
                                "status": "Cancelled"
                            }
                        }
                    </code>
                </pre>
                <h2 id="xu-ly-webhook" class="hs-docs-heading">
                    Handling Payment Notification Webhook 
                    <a class="anchorjs-link" href="#xu-ly-webhook" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                <p>When a customer successfully makes a payment to the order's VA, SePay will send a payment notification webhook to your website. The webhook data will include a <code>code</code> field containing the order code for that VA.</p>
                <p>Example of webhook data when a VA is paid:</p>
                
                <pre class="rounded mb-4">
                <code class="language-json">{
                    "id": 92704,
                    "gateway": "BIDV",
                    "transactionDate": "2024-01-07 14:02:37",
                    "code": "ORD123456789",           // Mã đơn hàng của VA
                    "transferAmount": 2277000,         // Số tiền giao dịch
                    "transferType": "in",              // Loại giao dịch (in: tiền vào)
                    ...
                }</code>
                </pre>
                <p>Your website needs to check the <code>code</code> field to identify the paid order and update its status accordingly.</p>
                <p>For more details on how to set up and handle webhooks, refer to the <a href="https://docs.sepay.vn/en/webhooks-integration.html">WebHooks Integration Guide</a>.</p>
                <h2 id="dinh-nghia-trang-thai" class="hs-docs-heading">
                    Status Definitions <a class="anchorjs-link" href="#dinh-nghia-trang-thai" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                <h3 id="trang-thai-don-hang" class="hs-docs-heading">
                    Order Status <a class="anchorjs-link" href="#trang-thai-don-hang" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h3>

                <div class="card mb-3">
                    <div class="table-responsive">
                        <table class="table">
                            <thead class="thead-light">
                                <tr>
                                    <th>Status</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Pending</td>
                                    <td>Order not yet paid</td>
                                </tr>
                                <tr>
                                    <td>Paid</td>
                                    <td>Order has been paid</td>
                                </tr>
                                <tr>
                                    <td>Partially</td>
                                    <td>Order has been partially paid</td>
                                </tr>
                                <tr>
                                    <td>Cancelled</td>
                                    <td>Order has been cancelled</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <h3 id="trang-thai-va" class="hs-docs-heading">
                    VA Status <a class="anchorjs-link" href="#trang-thai-va" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h3>
                <div class="card mb-3">
                    <div class="table-responsive">
                        <table class="table">
                            <thead class="thead-light">
                                <tr>
                                    <th>Status</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Unpaid</td>
                                    <td>VA not yet paid</td>
                                </tr>
                                <tr>
                                    <td>Paid</td>
                                    <td>VA has been paid</td>
                                </tr>
                                <tr>
                                    <td>Cancelled</td>
                                    <td>VA has been cancelled</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <h2 id="gioi-han-request" class="hs-docs-heading">
                    Request Limits <a class="anchorjs-link" href="#gioi-han-request" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                <p>SePay limits the number of API requests per order as follows:</p>
                <ul>
                    <li>2 requests/second per IP</li>
                    <li>Exceeding the limit will return a 429 response code</li>
                    <li>The <code>X-SePay-UserApi-Retry-After</code> header indicates the wait time before retrying</li>
                </ul>
                <h2 id="ma-loi" class="hs-docs-heading">
                    Error Codes <a class="anchorjs-link" href="#ma-loi" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                <p>SePay will return the following error codes:</p>
                
                <div class="card mb-5">
                    <div class="table-responsive">
                        <table class="table">
                            <thead class="thead-light">
                                <tr>
                                    <th>Error Code</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>400</td>
                                    <td>Bad Request - Invalid or missing parameters</td>
                                </tr>
                                <tr>
                                    <td>401</td>
                                    <td>Unauthorized - Invalid token</td>
                                </tr>
                                <tr>
                                    <td>404</td>
                                    <td>Not Found - Resource not found</td>
                                </tr>
                                <tr>
                                    <td>429</td>
                                    <td>Too Many Requests - Request limit exceeded</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <h2 id="tao-don-hang-khong-gioi-han-so-tien-va-thoi-gian" class="hs-docs-heading">
                    Creating Orders Without Amount and Time Limits <a class="anchorjs-link" href="#tao-don-hang-khong-gioi-han-so-tien-va-thoi-gian" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                <p>You can create orders without amount and time payment limits by:</p>
                <ul>
                    <li>Setting <code>amount</code> to <code>null</code> to allow unlimited payment transactions for the order and VA</li>
                    <li>Setting <code>duration</code> to <code>null</code> to remove time limits for the order</li>
                </ul>
                <p>Example:</p>
                <pre class="rounded mb-4">
                    <code class="language-http" data-lang="http">
                        POST https://my.sepay.vn/userapi/bidv/123456/orders
                        Authorization: Bearer {token}
                        Content-Type: application/json

                        {
                            "amount": null,
                            "duration": null
                        }
                    </code>
                </pre>
                <p>Once the order is created, you can create new VAs for the order by calling the <a href="#tao-them-va">Create Additional VA</a> API.</p>
            </div>
        </main>
		<div id="contact-box-overlay"></div>
		<div id="contact-box">
            <div class="popup">
              <a href="https://m.me/117903214582465" target="_blank" class="item">
                <div class="logo">
                  <img src="../assets/img/others/fb-messenger.png" width="50%" />
                </div>
                <div class="meta">
                  <p class="title">Facebook Messenger</p>
                  <small class="description">24/7 live chat support</small>
                </div>
              </a>
              <div class="divide"></div>
              <a href="tel:02873059589" class="item">
                <div class="icon" style="background-color: #22c55e; color: #fff;">
                  <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20"
                    style="color: currentcolor;">
                    <path
                      d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z"
                      stroke="currentColor" stroke-width="1.5" stroke-linecap="round" fill="transparent"></path>
                  </svg>
                </div>
                <div class="meta">
                  <p class="title">Hotline</p>
                  <small class="description">Support phone number</small>
                </div>
              </a>
              <div class="divide"></div>
              <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
                <div class="logo">
                  <img src="../assets/img/others/youtube-social.png" />
                </div>
                <div class="meta">
                  <p class="title">YouTube</p>
                  <small class="description">Follow SePay's latest videos</small>
                </div>
              </a>
              <div class="divide"></div>
              <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
                <div class="logo">
                  <img src="../assets/img/others/telegram-social.png" />
                </div>
                <div class="meta">
                  <p class="title">Telegram</p>
                  <small class="description">Get the latest updates from SePay</small>
                </div>
              </a>
            </div>
            <div class="container">
              <div class="dot-ping">
                <div class="ping"></div>
                <div class="dot"></div>
              </div>
              <div class="contact-icon">
                <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20"
                  style="color: currentcolor;">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z"
                    fill="currentColor"></path>
                </svg>
              </div>
              <span style="font-weight: bold;">Contact Us</span>
            </div>
          </div>

        <a
            class="js-go-to go-to position-fixed"
            href="javascript:;"
            style="visibility: hidden;"
            data-hs-go-to-options='{
				"offsetTop": 700,
				"position": {
					"init": {
					"right": "2rem"
					},
					"show": {
					"bottom": "2rem"
					},
					"hide": {
					"bottom": "-2rem"
					}
				}
			}'
        >
            <i class="bi-chevron-up"></i>
        </a>

		<script src="../assets/js/vendor.min.js"></script>
		<script src="../assets/js/theme.min.js"></script>
		<script src="../assets/js/contact-box.js"></script>

		<script>
			(function () {
				new HSHeader("#header").init();

				new HsNavScroller('.js-nav-scroller', {
					delay: 400,
					offset: 140,
				});

				const docsSearch = HSCore.components.HSList.init('#docsSearch');
				new HSGoTo('.js-go-to');

                function setCookie(cname, cvalue, exdays) {
                    var d = new Date();
                    d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
                    var expires = "expires=" + d.toUTCString();
                    document.cookie = cname + "=" + cvalue + "; " + expires + "; path=/; domain=.sepay.vn";
                }
                let urlParams = new URLSearchParams(document.location.search);
                ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content'].forEach(function(param) {
                    let value = urlParams.get(param);
                    if (value) {
                        setCookie(param, value, 90);
                    }
                });
                if (document.referrer != '' && document.cookie.indexOf('referer') === -1) {
                    setCookie('referer', document.referrer, 90);
                }
			})();
		</script>
    </body>
</html>
