<!DOCTYPE html>
<html lang="en">
<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

  <!-- Title -->
  <title>Webhooks Programming Guide | SePay</title>


  <!-- Favicon -->
  <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">
  <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
  <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />

  <!-- CSS Implementing Plugins -->
  <link rel="stylesheet" href="../assets/vendor/bootstrap-icons/font/bootstrap-icons.css">

  <!-- CSS Front Template -->
  <link rel="stylesheet" href="../assets/css/theme.min.css">
  <link rel="stylesheet" href="../assets/css/docs.min.css">

  <!--contact-box-css-->
  <link rel="stylesheet" href="../assets/css/contact-box.css">
  <!--/contact-box-css-->

  <style>
    .docs-navbar-sidebar-aside-body {
      padding-top: 3.5rem!important;
  }
  </style>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
  
      gtag('config', 'G-J8DLMQTKSQ');
    </script>
</head>

<body class="navbar-sidebar-aside-lg">
  <!-- ========== HEADER ========== -->
  <header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
    <div class="container-fluid">
      <nav class="navbar-nav-wrap">
        <div class="row flex-grow-1">
          <!-- Default Logo -->
          <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
              <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo">
            </a>
           
          </div>
          <!-- End Default Logo -->

          <div class="col-md px-lg-0">
            <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
              <div class="d-none d-md-block">
               

                <!-- List Item Template -->
                <div class="d-none">
                  <div id="searchTemplate" class="dropdown-item">
                    <a class="d-block link" href="#">
                      <span class="category d-block fw-normal text-muted mb-1"></span>
                      <span class="component text-dark"></span>
                    </a>
                  </div>
                </div>
                <!-- End List Item Template -->
              </div>

              <!-- Navbar -->
              <ul class="navbar-nav p-0">
                <li class="nav-item">
                  <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html" target="_blank">
                    Support <i class="bi-box-arrow-up-right ms-1"></i>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN" target="_blank">
                    Youtube <i class="bi-box-arrow-up-right ms-1"></i>
                  </a>
                </li>
                <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://www.facebook.com/messages/t/sepay.vn" target="_blank">
                                      Facebook <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                  </li>
                                  <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel" target="_blank">
                                      Telegram <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                  </li>
                
              </ul>
              <!-- End Navbar -->
            </div>
          </div>
          <!-- End Col -->
        </div>
        <!-- End Row -->
      </nav>
    </div>
  </header>
  <!-- ========== END HEADER ========== -->

  <!-- ========== MAIN CONTENT ========== -->
  <main id="content" role="main">
    <!-- Navbar -->
    <nav class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
          data-hs-nav-scroller-options='{
            "type": "vertical",
            "target": ".navbar-nav .active",
            "offset": 80
           }'>
      <!-- Navbar Toggle -->
      <button type="button" class="navbar-toggler btn btn-white d-grid w-100" data-bs-toggle="collapse" data-bs-target="#navbarVerticalNavMenu" aria-label="Toggle navigation" aria-expanded="false" aria-controls="navbarVerticalNavMenu">
        <span class="d-flex justify-content-between align-items-center">
          <span class="h3 mb-0">Nav menu</span>

          <span class="navbar-toggler-default">
            <i class="bi-list"></i>
          </span>

          <span class="navbar-toggler-toggled">
            <i class="bi-x"></i>
          </span>
        </span>
      </button>
      <!-- End Navbar Toggle -->

      <!-- Navbar Collapse -->
      <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
        <div class="navbar-brand-wrapper border-end" style="height: auto;">
          <!-- Default Logo -->
          <div class="d-flex align-items-center mb-3">
            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
              <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo">
            </a>
           
          </div>
          <!-- End Default Logo -->
      
       
        </div>

        <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
          <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
            <li class="nav-item">
              <span class="nav-subtitle">Introduction</span>
            </li>
        
            <li class="nav-item">
              <a class="nav-link" href="what-is-sepay.html">What is SePay?</a>
            </li>
        
            <li class="nav-item my-2 my-lg-5"></li>
        
            <li class="nav-item">
              <span class="nav-subtitle">General Guides</span>
            </li>
        
            <li class="nav-item">
              <a class="nav-link" href="register-sepay.html">Register for SePay</a>
            </li>
        
            <li class="nav-item">
              <a class="nav-link" href="add-bank-account.html">Add Bank Account</a>
            </li>
        
            <li class="nav-item">
              <a class="nav-link" href="view-transactions.html">View Transactions</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="users-and-permissions.html">Users & Permissions</a>
            </li>
        
            <li class="nav-item">
              <a class="nav-link" href="sub-accounts.html">Sub-Accounts</a>
            </li>
        
            <li class="nav-item my-2 my-lg-5"></li>
        
            <li class="nav-item">
              <span class="nav-subtitle">Company Configuration</span>
            </li>
        
            <li class="nav-item">
              <a class="nav-link" href="service-packages.html">Service Packages</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="invoices-and-payments.html">Invoices & Payments</a>
            </li>
        
            <li class="nav-item">
              <a class="nav-link" href="general-settings.html">General Settings</a>
            </li>
        
            <li class="nav-item my-2 my-lg-5"></li>
        
            <li class="nav-item">
              <span class="nav-subtitle">Balance Notifications</span>
            </li>
        
            <li class="nav-item">
              <a class="nav-link" href="telegram-integration.html">Integrate with Telegram <i class="bi bi-telegram ms-2 text-info"></i></a>
            </li>
        
            <li class="nav-item">
              <a class="nav-link" href="lark-messenger-integration.html">Integrate with Lark Messenger <img src="../assets/img/lark/lark-icon.png" class="img-fluid" style="max-height:25px"></a>
            </li>
        
            <li class="nav-item">
              <a class="nav-link d-flex align-items-center" href="mobile-app.html">Mobile App <i class="bi bi-phone-vibrate ms-2 fs-3 text-info"></i></a>
            </li>
        
            <li class="nav-item my-2 my-lg-5"></li>
        
            <li class="nav-item">
              <span class="nav-subtitle">Web Integration</span>
            </li>
        
            <li class="nav-item">
              <a class="nav-link" href="shopify-integration.html">Integrate with Shopify <img class="ms-2" src="../assets/img/others/shopify-icon.png" style="width:22px; height:22px;"></a>
            </li>
        
            <li class="nav-item">
              <a class="nav-link" href="sapo-integration.html">Integrate with Sapo <img class="ms-2" src="../assets/img/others/sapo-icon.png" style="width:18px; height:18px;"></a>
            </li>
        
            <li class="nav-item">
              <a class="nav-link" href="haravan-integration.html">Integrate with Haravan <img class="ms-2" src="../assets/img/others/haravan-icon.png" style="width:22px; height:22px;"></a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="woocommerce.html">Integrate with WooCommerce <img class="ms-2" src="../assets/img/others/woocommerce-icon.png" style="width:22px; height:22px;"></a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="google-sheets-integration.html">Integrate with Google Sheets <img class="ms-2" src="../assets/img/others/google-sheets-icon.png" style="width:22px; height:22px;"></a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="hostbill-integration.html">Integrate with HostBill <img class="ms-2" src="../assets/img/others/hostbill-icon.png" style="width:22px; height:22px;"></a>
            </li>
        
            <li class="nav-item my-2 my-lg-5"></li>
        
            <li class="nav-item">
              <span class="nav-subtitle">Programming & Integration</span>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="webhooks-integration.html">Integrate with WebHooks</a>
            </li>
        
            <li class="nav-item">
              <a class="nav-link active" href="webhooks-programming.html">WebHooks Programming</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="simulate-transactions.html">Simulate Transactions</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="generate-qr-code-vietqr-dong.html">Generate & Embed QR Code</a>
            </li>
        
            <li class="nav-item my-2 my-lg-5"></li>
        
            <li class="nav-item">
              <span class="nav-subtitle">SePay API</span>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="api-introduction.html">API Introduction</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="create-api-token.html">Create API Token</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="transaction-api.html">Transaction API</a>
            </li>
            <li class="nav-item">
                  <a class="nav-link" href="bank-account-api.html">Bank Account API</a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="api-va-order-bidv.html">API Va Order</a>
            </li>
          </ul>
        </div>
        
        
      </div>
      <!-- End Navbar Collapse -->
    </nav>
    <!-- End Navbar -->

    <!-- Content -->
    <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">
         
      <div class="docs-page-header">
        <div class="row align-items-center">
          <div class="col-sm">
            <h1 class="docs-page-header-title">Guide to Simple Webhook Programming</h1>
            <p class="docs-page-header-text">Program a simple webhook URL to receive and store transaction information from SePay.</p>
          </div>
        </div>
      </div>

      <p>This article will guide you through programming a simple website to receive and store transactions from SePay. The programming language used is <a href="https://www.php.net/" target="_blank">PHP</a>, and the database is <a href="https://en.wikipedia.org/wiki/MySQL"  target="_blank">MySQL</a>.</p>
      <p>If you are using Laravel, use the package provided for SePay <a href="https://github.com/sepayvn/laravel-sepay">here</a></p>

      <p><b>Step 1:</b> Create a database and set permissions.</p>
      <p>Create a database named <b>webhooks_receiver</b>, MySQL user <b>webhooks_receiver</b> with password <b>EL2vKpfpDLsz</b></p>
      <p>Go to the MySQL Command Line interface and execute the following commands:</p>
      <pre>
create database webhooks_receiver;
create user 'webhooks_receiver'@'localhost' identified by 'EL2vKpfpDLsz';
grant all privileges on webhooks_receiver.* to  'webhooks_receiver'@'localhost' identified by 'EL2vKpfpDLsz';
      </pre>

      <div class="alert alert-soft-dark mb-4" role="alert">
        <div class="d-flex align-items-baseline">
          <div class="flex-shrink-0">
            <i class="bi-info-circle me-1"></i>
          </div>

          <div class="flex-grow-1 ms-2">For security, you should change <b>EL2vKpfpDLsz</b> to a different password.
          </div>
        </div>
      </div>
      <p><b>Step 2:</b> Create a table to store transaction information.</p>


          <pre>
use webhooks_receiver;
CREATE TABLE `tb_transactions` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `gateway` varchar(100) NOT NULL,
    `transaction_date` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
    `account_number` varchar(100) DEFAULT NULL,
    `sub_account` varchar(250) DEFAULT NULL,
    `amount_in` decimal(20,2) NOT NULL DEFAULT 0.00,
    `amount_out` decimal(20,2) NOT NULL DEFAULT 0.00,
    `accumulated` decimal(20,2) NOT NULL DEFAULT 0.00,
    `code` varchar(250) DEFAULT NULL,
    `transaction_content` text DEFAULT NULL,
    `reference_number` varchar(255) DEFAULT NULL,
    `body` text DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3;
          </pre>
 
          <p><b>Step 3:</b> Create the file <code>receiver.php</code> that SePay will call. The content of the file is as follows:</p>

<pre> 
&lt;?php
    $servername = "localhost";
    $username = "webhooks_receiver";
    $password = "EL2vKpfpDLsz";
    $dbname = "webhooks_receiver";


    // Connect to MySQ
    $conn = new mysqli($servername, $username, $password, $dbname);
    // Check the connection
    if ($conn->connect_error) {
        echo json_encode(['success'=>FALSE, 'message' => 'MySQL connection failed: '. $conn->connect_error]);
        die();
    }

    // get data webhooks, // View the data fields at https://docs.sepay.vn/tich-hop-webhooks.html#du-lieu
    $data = json_decode(file_get_contents('php://input'));

    if(!is_object($data)) {
        echo json_encode(['success'=>FALSE, 'message' => 'No data']);
        die();
    }

    // Initialize variables
    $gateway = $data->gateway;
    $transaction_date = $data->transactionDate;
    $account_number = $data->accountNumber;
    $sub_account = $data->subAccount;

    $transfer_type = $data->transferType;
    $transfer_amount = $data->transferAmount;
    $accumulated = $data->accumulated;

    $code = $data->code;
    $transaction_content = $data->content;
    $reference_number = $data->referenceCode;
    $body = $data->description;

    $amount_in = 0;
    $amount_out = 0;

    // Check if the transaction is incoming or outgoing

    if($transfer_type == "in")
        $amount_in = $transfer_amount;
    else if($transfer_type == "out")
        $amount_out = $transfer_amount;

        // Create SQL query

    $sql = "INSERT INTO tb_transactions (gateway, transaction_date, account_number, sub_account, amount_in, amount_out, accumulated, code, transaction_content, reference_number, body) VALUES ('{$gateway}', '{$transaction_date}', '{$account_number}', '{$sub_account}', '{$amount_in}', '{$amount_out}', '{$accumulated}', '{$code}', '{$transaction_content}', '{$reference_number}', '{$body}')";

    // Execute the query to save the transaction into the database

    if ($conn->query($sql) === TRUE) {
        echo json_encode(['success'=>TRUE]);
    } else {
        echo json_encode(['success'=>FALSE, 'message' => 'Can not insert record to mysql: ' . $conn->error]);
    }

?&gt;

</pre>


<p><b>Step 4:</b> <a href="https://docs.sepay.vn/tich-hop-webhooks.html" target="_blank">Add a new WebHook</a> in the <b>WebHooks</b> menu. Note the following parameters:</p>
    <ul>
        <li> Call URL: <code>https://your-website.tld/receiver.php</code></li>
        <li> Authentication type: <b>No authentication required</b></li>
    </ul> 

<p><b>Step 5:</b> Create a test transaction by logging into your Demo account, then go to <b>Transactions</b> -> <b>Simulate Transaction</b>. Select the correct Bank Account corresponding to the webhook you created.</p>
<p><b>Step 6:</b> After creating the test transaction, you can go to <b>Transactions</b> -> Click the <b>Pay</b> icon in the <b>Automatic</b> column to view the WebHook firing result. Or visit the <a href="https://my.sepay.vn/webhookslog">WebHooks Log</a>.</p>

<p><b>Step 7:</b> Check if the data has been saved in the database using the following queries:</p>
<pre>

use webhooks_receiver;
select * from tb_transactions \G
</pre>

<div class="alert alert-soft-dark mb-4" role="alert">
  <div class="d-flex align-items-baseline">
    <div class="flex-shrink-0">
      <i class="bi-info-circle me-1"></i>
    </div>

    <div class="flex-grow-1 ms-2">The example above does not authenticate the incoming source. For safety, you should whitelist SePay's IP or choose an authentication method.<br> With the API Key, you need to check if SePay sends the correct API Key in the header.
    </div>
  </div>
</div>

<p>Good luck!</p>

<div class="my-2"><p>Continue reading: <a href="simulate-transactions.html">Simulate a transaction<i class="bi bi-chevron-right"></i></a></p></div>

    </div>
    <!-- End Content -->
  </main>
  <!-- ========== END MAIN CONTENT ========== -->

  <!-- ========== SECONDARY CONTENTS ========== -->
  <!--contact-box-html-docs-->
  <div id="contact-box-overlay"></div>
  <div id="contact-box">
    <div class="popup">
      <a href="https://m.me/117903214582465" target="_blank" class="item">
        <div class="logo">
          <img src="../assets/img/others/fb-messenger.png" width="50%" />
        </div>
        <div class="meta">
          <p class="title">Facebook Messenger</p>
          <small class="description">24/7 live chat support</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="tel:02873059589" class="item">
        <div class="icon" style="background-color: #22c55e; color: #fff;">
          <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20"
            style="color: currentcolor;">
            <path
              d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z"
              stroke="currentColor" stroke-width="1.5" stroke-linecap="round" fill="transparent"></path>
          </svg>
        </div>
        <div class="meta">
          <p class="title">Hotline</p>
          <small class="description">Support phone number</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
        <div class="logo">
          <img src="../assets/img/others/youtube-social.png" />
        </div>
        <div class="meta">
          <p class="title">YouTube</p>
          <small class="description">Follow SePay's latest videos</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
        <div class="logo">
          <img src="../assets/img/others/telegram-social.png" />
        </div>
        <div class="meta">
          <p class="title">Telegram</p>
          <small class="description">Get the latest updates from SePay</small>
        </div>
      </a>
    </div>
    <div class="container">
      <div class="dot-ping">
        <div class="ping"></div>
        <div class="dot"></div>
      </div>
      <div class="contact-icon">
        <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20"
          style="color: currentcolor;">
          <path fill-rule="evenodd" clip-rule="evenodd"
            d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z"
            fill="currentColor"></path>
        </svg>
      </div>
      <span style="font-weight: bold;">Contact Us</span>
    </div>
  </div>
  <!--/contact-box-html-docs-->

  <!-- Go To -->
  <a class="js-go-to go-to position-fixed" href="javascript:;" style="visibility: hidden;"
     data-hs-go-to-options='{
       "offsetTop": 700,
       "position": {
         "init": {
           "right": "2rem"
         },
         "show": {
           "bottom": "2rem"
         },
         "hide": {
           "bottom": "-2rem"
         }
       }
     }'>
    <i class="bi-chevron-up"></i>
  </a>
  <!-- ========== END SECONDARY CONTENTS ========== -->

  <!-- JS Global Compulsory  -->
  <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

  <!-- JS Implementing Plugins -->
  <script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
  <script src="../assets/vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js"></script>
  <script src="../assets/vendor/list.js/dist/list.min.js"></script>
  <script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>
  

  <!-- JS Front -->
  <script src="../assets/js/theme.min.js"></script>

  <!-- contact-box-js -->
  <script src="../assets/js/contact-box.js"></script>
  <!-- /contact-box-js -->

  <!-- JS Plugins Init. -->
  <script>
      (function () {
          // INITIALIZATION OF HEADER
          // =======================================================
          new HSHeader('#header').init()

          // INITIALIZATION OF NAV SCROLLER
          // =======================================================
          new HsNavScroller('.js-nav-scroller', {
              delay: 400,
              offset: 140
          })
      // INITIALIZATION OF LISTJS COMPONENT
      // =======================================================
      const docsSearch = HSCore.components.HSList.init('#docsSearch')

 
      // INITIALIZATION OF GO TO
      // =======================================================
      new HSGoTo('.js-go-to')
    })()
  </script>
</body>
</html>
