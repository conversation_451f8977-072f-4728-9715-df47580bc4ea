<!DOCTYPE html>
<html lang="en">
<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

  <!-- Title -->
  <title>What is SePay | SePay</title>

  <!-- Favicon -->
  <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">

  <!-- CSS Implementing Plugins -->
  <link rel="stylesheet" href="../assets/vendor/bootstrap-icons/font/bootstrap-icons.css">

  <!-- CSS Front Template -->
  <link rel="stylesheet" href="../assets/css/theme.min.css">
  <link rel="stylesheet" href="../assets/css/docs.min.css">
  <style>
    .docs-navbar-sidebar-aside-body {
      padding-top: 3.5rem!important;
  }
  </style>
</head>

<body class="navbar-sidebar-aside-lg">
  <!-- ========== HEADER ========== -->
  <header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
    <div class="container-fluid">
      <nav class="navbar-nav-wrap">
        <div class="row flex-grow-1">
          <!-- Default Logo -->
          <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
              <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo">
            </a>
           
          </div>
          <!-- End Default Logo -->

          <div class="col-md px-lg-0">
            <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
              <div class="d-none d-md-block">
               

                <!-- List Item Template -->
                <div class="d-none">
                  <div id="searchTemplate" class="dropdown-item">
                    <a class="d-block link" href="#">
                      <span class="category d-block fw-normal text-muted mb-1"></span>
                      <span class="component text-dark"></span>
                    </a>
                  </div>
                </div>
                <!-- End List Item Template -->
              </div>

              <!-- Navbar -->
              <ul class="navbar-nav p-0">
                <li class="nav-item">
                  <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html" target="_blank">
                    Support <i class="bi-box-arrow-up-right ms-1"></i>
                  </a>
                </li>
                
              </ul>
              <!-- End Navbar -->
            </div>
          </div>
          <!-- End Col -->
        </div>
        <!-- End Row -->
      </nav>
    </div>
  </header>
  <!-- ========== END HEADER ========== -->

  <!-- ========== MAIN CONTENT ========== -->
  <main id="content" role="main">
    <!-- Navbar -->
    <nav class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
          data-hs-nav-scroller-options='{
            "type": "vertical",
            "target": ".navbar-nav .active",
            "offset": 80
           }'>
      <!-- Navbar Toggle -->
      <button type="button" class="navbar-toggler btn btn-white d-grid w-100" data-bs-toggle="collapse" data-bs-target="#navbarVerticalNavMenu" aria-label="Toggle navigation" aria-expanded="false" aria-controls="navbarVerticalNavMenu">
        <span class="d-flex justify-content-between align-items-center">
          <span class="h3 mb-0">Nav menu</span>

          <span class="navbar-toggler-default">
            <i class="bi-list"></i>
          </span>

          <span class="navbar-toggler-toggled">
            <i class="bi-x"></i>
          </span>
        </span>
      </button>
      <!-- End Navbar Toggle -->

      <!-- Navbar Collapse -->
      <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
        <div class="navbar-brand-wrapper border-end" style="height: auto;">
          <!-- Default Logo -->
          <div class="d-flex align-items-center mb-3">
            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
              <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo">
            </a>
           
          </div>
          <!-- End Default Logo -->
      
       
        </div>

        <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
          <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
              <li class="nav-item">
                  <span class="nav-subtitle">Introduction</span>
              </li>
      
              <li class="nav-item">
                  <a class="nav-link active" href="#">What is SePay?</a>
              </li>
      
              <li class="nav-item my-2 my-lg-5"></li>
      
              <li class="nav-item">
                  <span class="nav-subtitle">General Instructions</span>
              </li>
      
              <li class="nav-item">
                  <a class="nav-link" href="#">Register SePay</a>
              </li>
      
              <li class="nav-item">
                  <a class="nav-link" href="#">Add Bank Account</a>
              </li>
      
              <li class="nav-item">
                  <a class="nav-link" href="#">Integrate Telegram</a>
              </li>
      
              <li class="nav-item">
                  <a class="nav-link" href="#">Integrate WebHooks</a>
              </li>
      
              <li class="nav-item">
                  <a class="nav-link" href="tich-hop-shopify.html">Integrate Shopify <img class="ms-2" src="../assets/img/others/shopify-icon.png" style="width:22px; height: 22px;"></a>
              </li>
      
              <li class="nav-item">
                  <a class="nav-link" href="#">Transaction Check</a>
              </li>
              <li class="nav-item">
                  <a class="nav-link" href="#">Users & Permissions</a>
              </li>
      
              <li class="nav-item my-2 my-lg-5"></li>
      
              <li class="nav-item">
                  <span class="nav-subtitle">Reports & Statistics</span>
              </li>
      
              <li class="nav-item">
                  <a class="nav-link" href="#">Account Balance</a>
              </li>
      
              <li class="nav-item">
                  <a class="nav-link" href="#">Money into Sub-Account</a>
                  <a class="nav-link" href="#">Transaction Count</a>
                  <a class="nav-link" href="#">Cash Flow Overview</a>
              </li>
      
              <li class="nav-item">
                  <a class="nav-link" href="#">Transaction Count</a>
              </li>
              <li class="nav-item">
                  <a class="nav-link" href="#">Cash Flow Overview</a>
              </li>
      
              <li class="nav-item my-2 my-lg-5"></li>
      
              <li class="nav-item">
                  <span class="nav-subtitle">Company Configuration</span>
              </li>
      
              <li class="nav-item">
                  <a class="nav-link" href="#">Service Packages</a>
              </li>
              <li class="nav-item">
                  <a class="nav-link" href="#">Invoices & Payments</a>
              </li>
      
              <li class="nav-item">
                  <a class="nav-link" href="#">General Settings</a>
              </li>
          </ul>
      </div>
      
      </div>
      <!-- End Navbar Collapse -->
    </nav>
    <!-- End Navbar -->

    <!-- Content -->
    <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">
         
        <div class="docs-page-header">
            <div class="row align-items-center">
              <div class="col-sm">
                <h1 class="docs-page-header-title">What is SePay?</h1>
                <p class="docs-page-header-text">Front - Multipurpose Template + UI KIT for building responsive, mobile-first sites, with Bootstrap Framework.</p>
              </div>
            </div>
          </div>

          <h2 id="starter-template" class="hs-docs-heading">
            Starter template <a class="anchorjs-link" href="#starter-template" aria-label="Anchor" data-anchorjs-icon="#"></a>
          </h2>

          <ul class="list-py-1">
            <li>Avoiding the probabilities of conflicts between Space codes and third party plugins (libraries).</li>
            <li>Intuitive clear architecture.</li>
            <li>Everything is structured, each component in its own file and in its component in the main object.</li>
            <li>The ability of extending functionality without affecting the behavior of the core object and not changing the existing functionality.</li>
            <li>Creation of wrapper components simply solves complicated initializations structures for the users.</li>
            <li>Very easy access to any starters components and core settings from anywhere in the template.</li>
          </ul>


 
    </div>
    <!-- End Content -->
  </main>
  <!-- ========== END MAIN CONTENT ========== -->

  <!-- ========== SECONDARY CONTENTS ========== -->
  <!-- Go To -->
  <a class="js-go-to go-to position-fixed" href="javascript:;" style="visibility: hidden;"
     data-hs-go-to-options='{
       "offsetTop": 700,
       "position": {
         "init": {
           "right": "2rem"
         },
         "show": {
           "bottom": "2rem"
         },
         "hide": {
           "bottom": "-2rem"
         }
       }
     }'>
    <i class="bi-chevron-up"></i>
  </a>
  <!-- ========== END SECONDARY CONTENTS ========== -->

  <!-- JS Global Compulsory  -->
  <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

  <!-- JS Implementing Plugins -->
  <script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
  <script src="../assets/vendor/list.js/dist/list.min.js"></script>
  <script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>
  

  <!-- JS Front -->
  <script src="../assets/js/theme.min.js"></script>

  <!-- JS Plugins Init. -->
  <script>
    (function() {
      // INITIALIZATION OF HEADER
      // =======================================================
      new HSHeader('#header').init()


      // INITIALIZATION OF LISTJS COMPONENT
      // =======================================================
      const docsSearch = HSCore.components.HSList.init('#docsSearch')


      // GET JSON FILE RESULTS
      // =======================================================
      fetch('../assets/json/docs-search.json')
      .then(response => response.json())
      .then(data => {
        docsSearch.getItem(0).add(data)
      })

      // INITIALIZATION OF GO TO
      // =======================================================
      new HSGoTo('.js-go-to')
    })()
  </script>
</body>
</html>
