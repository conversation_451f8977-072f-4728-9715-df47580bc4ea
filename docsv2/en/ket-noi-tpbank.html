<!DOCTYPE html>
<html lang="vi">

<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

  <!-- Title -->
  <title>Guide to Connecting TPBank Personal and Business Accounts | SePay</title>


  <!-- Favicon -->
  <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">

  <!-- CSS Implementing Plugins -->
  <link rel="stylesheet" href="../assets/vendor/bootstrap-icons/font/bootstrap-icons.css">
  <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
  <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />

  <!-- CSS Front Template -->
  <link rel="stylesheet" href="../assets/css/theme.min.css">
  <link rel="stylesheet" href="../assets/css/docs.min.css">

  <!--contact-box-css-->
  <link rel="stylesheet" href="../assets/css/contact-box.css">
  <!--/contact-box-css-->

  <style>
    .docs-navbar-sidebar-aside-body {
      padding-top: 3.5rem !important;
    }
  </style>
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());

    gtag('config', 'G-J8DLMQTKSQ');
  </script>
</head>

<body class="navbar-sidebar-aside-lg">
  <!-- ========== HEADER ========== -->
  <header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
    <div class="container-fluid">
      <nav class="navbar-nav-wrap">
        <div class="row flex-grow-1">
          <!-- Default Logo -->
          <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
              <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo">
            </a>

          </div>
          <!-- End Default Logo -->

          <div class="col-md px-lg-0">
            <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
              <div class="d-none d-md-block">


                <!-- List Item Template -->
                <div class="d-none">
                  <div id="searchTemplate" class="dropdown-item">
                    <a class="d-block link" href="#">
                      <span class="category d-block fw-normal text-muted mb-1"></span>
                      <span class="component text-dark"></span>
                    </a>
                  </div>
                </div>
                <!-- End List Item Template -->
              </div>

              <!-- Navbar -->
              <ul class="navbar-nav p-0">
                <li class="nav-item">
                  <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html" target="_blank">
                    Support <i class="bi-box-arrow-up-right ms-1"></i>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN" target="_blank">
                    Youtube <i class="bi-box-arrow-up-right ms-1"></i>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="btn btn-ghost-secondary btn-sm" href="https://www.facebook.com/messages/t/sepay.vn"
                    target="_blank">
                    Facebook <i class="bi-box-arrow-up-right ms-1"></i>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel" target="_blank">
                    Telegram <i class="bi-box-arrow-up-right ms-1"></i>
                  </a>
                </li>

              </ul>
              <!-- End Navbar -->
            </div>
          </div>
          <!-- End Col -->
        </div>
        <!-- End Row -->
      </nav>
    </div>
  </header>
  <!-- ========== END HEADER ========== -->

  <!-- ========== MAIN CONTENT ========== -->
  <main id="content" role="main">
    <!-- Navbar -->
    <nav class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
    data-hs-nav-scroller-options='{
          "type": "vertical",
          "target": ".navbar-nav .active",
          "offset": 80
         }'>
    <!-- Navbar Toggle -->
    <button type="button" class="navbar-toggler btn btn-white d-grid w-100" data-bs-toggle="collapse"
      data-bs-target="#navbarVerticalNavMenu" aria-label="Toggle navigation" aria-expanded="false"
      aria-controls="navbarVerticalNavMenu">
      <span class="d-flex justify-content-between align-items-center">
        <span class="h3 mb-0">Nav menu</span>

        <span class="navbar-toggler-default">
          <i class="bi-list"></i>
        </span>

        <span class="navbar-toggler-toggled">
          <i class="bi-x"></i>
        </span>
      </span>
    </button>
    <!-- End Navbar Toggle -->

    <!-- Navbar Collapse -->
    <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
      <div class="navbar-brand-wrapper border-end" style="height: auto;">
        <!-- Default Logo -->
        <div class="d-flex align-items-center mb-3">
          <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
            <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo">
          </a>

        </div>
        <!-- End Default Logo -->
      </div>

      <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
        <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
          <li class="nav-item">
            <span class="nav-subtitle">Introduction</span>
          </li>

          <li class="nav-item">
            <a class="nav-link" href="what-is-sepay.html">What is SePay?</a>
          </li>

          <li class="nav-item my-2 my-lg-5"></li>

          <li class="nav-item">
            <span class="nav-subtitle">General Guide</span>
          </li>

          <li class="nav-item">
            <a class="nav-link" href="register-sepay.html">Register SePay</a>
          </li>

          <li class="nav-item">
            <a class="nav-link active" href="add-bank-account.html">Add Bank Account</a>
          </li>
          
          <li class="nav-item">
            <a class="nav-link " href="view-transactions.html">View Transactions</a>
          </li>
          <li class="nav-item">
            <a class="nav-link " href="users-and-permissions.html">Users & Permissions</a>
          </li>

          <li class="nav-item">
            <a class="nav-link" href="sub-accounts.html">Sub Accounts</a>
          </li>

          <li class="nav-item my-2 my-lg-5"></li>

          <li class="nav-item">
            <span class="nav-subtitle">Company Settings</span>
          </li>

          <li class="nav-item">
            <a class="nav-link " href="service-plans.html">Service Plans</a>
          </li>
          <li class="nav-item">
            <a class="nav-link " href="invoices-and-payments.html">Invoices & Payments</a>
          </li>

          <li class="nav-item">
            <a class="nav-link " href="general-settings.html">General Settings</a>
          </li>

          <li class="nav-item my-2 my-lg-5"></li>

          <li class="nav-item">
              <span class="nav-subtitle">Balance Change Sharing</span>
          </li>

          <li class="nav-item">
              <a class="nav-link " href="telegram-integration.html">Integrate Telegram <i class="bi bi-telegram ms-2 text-info"></i></a>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="lark-messenger-integration.html">Integrate Lark Messenger <img src="../assets/img/lark/lark-icon.png" class="img-fluid rounded" style="max-height:25px"></a>
          </li>

          <li class="nav-item">
              <a class="nav-link d-flex align-items-center" href="mobile-app.html">Mobile App <i class="bi bi-phone-vibrate ms-2 fs-3 text-info"></i> </a>
          </li>

          <li class="nav-item my-2 my-lg-5"></li>

          <li class="nav-item">
            <span class="nav-subtitle">Web Integration</span>
          </li>

          <li class="nav-item">
            <a class="nav-link" href="shopify-integration.html">Integrate Shopify <img class="ms-2" src="../assets/img/others/shopify-icon.png" style="width:22px; height: 22px;"></a>
          </li>
          
          <li class="nav-item">
            <a class="nav-link" href="sapo-integration.html">Integrate Sapo <img class="ms-2"
                src="../assets/img/others/sapo-icon.png" style="width:18px; height: 18px;"></a>
          </li>

          <li class="nav-item">
            <a class="nav-link" href="haravan-integration.html">Integrate Haravan <img class="ms-2"
                src="../assets/img/others/haravan-icon.png" style="width:22px; height: 22px;"></a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="woocommerce.html">Integrate WooCommerce <img class="ms-2"
                src="../assets/img/others/woocommerce-icon.png" style="width:22px; height: 22px;"></a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="google-sheets-integration.html">Integrate Google Sheets <img class="ms-2"
                src="../assets/img/others/google-sheets-icon.png" style="width:22px; height: 22px;"></a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="hostbill-integration.html">Integrate HostBill <img class="ms-2"
                    src="../assets/img/others/hostbill-icon.png" style="height: 22px;"></a>
        </li>
          <li class="nav-item my-2 my-lg-5"></li>

          <li class="nav-item">
            <span class="nav-subtitle">Programming & Integration</span>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="webhooks-integration.html">Integrate WebHooks</a>
          </li>

          <li class="nav-item ">
            <a class="nav-link" href="program-webhooks.html">Program WebHooks</a>
          </li>
          <li class="nav-item ">
            <a class="nav-link" href="simulate-transactions.html">Simulate Transactions</a>
          </li>
          <li class="nav-item ">
            <a class="nav-link" href="create-qr-code-vietqr-dynamic.html">Create & Embed QR Code</a>
          </li>


          <li class="nav-item my-2 my-lg-5"></li>

          <li class="nav-item">
            <span class="nav-subtitle">SePay API</span>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="api-introduction.html">API Introduction</a>
          </li>
          <li class="nav-item">
            <a class="nav-link " href="create-api-token.html">Create API Token</a>
          </li>
          <li class="nav-item">
            <a class="nav-link " href="api-transactions.html">API Transactions</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="api-bank-accounts.html">API Bank Accounts</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="bank-account-api.html">Bank Account API</a>
          </li>
        </ul>
      </div>
    </div>
    <!-- End Navbar Collapse -->
  </nav>

    <!-- End Navbar -->

    <!-- Content -->
    <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">

      <div class="docs-page-header">
        <div class="row align-items-center">
          <div class="col-sm">
            <h1 class="docs-page-header-title">Guide to Connecting TPBank Account to SePay</h1>
            <p class="docs-page-header-text">This article will guide you through connecting your TPBank account (for <a href="#ket-noi-ca-nhan">individuals</a> and <a href="#ket-noi-doanh-nghiep">businesses</a>) to SePay via API.</p>
          </div>
        </div>
      </div>

      <p>SePay has officially partnered with TPBank to offer API access for <a href="#ket-noi-ca-nhan">individuals</a> and <a href="#ket-noi-doanh-nghiep">businesses</a>. In just 5 minutes, you can open a TPBank account through the TPBank Mobile app, link it via SePay, and start managing transactions immediately.</p>
      <p>If you have previously linked your TPBank account via SMS Banking to SePay, you can <a href="#sms-banking-sang-api-banking">switch to API Banking</a> for a better experience.</p>

      <h3 id="ket-noi-ca-nhan" class="hs-docs-heading">
          Connect Individual Account <a class="anchorjs-link" href="#ket-noi-ca-nhan" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h3>

      <div class="ratio ratio-16x9" style="max-width: 800px;">
        <iframe src="https://www.youtube.com/embed/B15-bLRwLcc?si=_LVWqapWrF2M277l" title="Guide to Connecting TPBank Individual Account to SePay" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen style="border-radius: 10px;"></iframe>  
      </div>

      <p class="mt-3">Here are the steps to connect your TPBank individual account via API to SePay:</p>
      <p><b>Step 1:</b> Visit <a href="https://my.sepay.vn" target="_blank">my.sepay.vn</a> -> Select the <b>Banking</b> menu -> Choose <b>Connect Account</b></p>
      <img src="../assets/img/tpb-connect/ind-1.png" class="img-fluid rounded" style="max-height: 500px;">
      <p><b>Step 2:</b> In the API connection interface:</p>
      <p>Select Individual, then choose to connect with TPBank.</p>
      <img src="../assets/img/tpb-connect/ind-2.png" class="img-fluid rounded" style="max-height: 515px;">
      <p>A popup will appear, click the <b>Start Connection</b> button to continue.</p>
      <img src="../assets/img/tpb-connect/ind-3.png" class="img-fluid rounded" style="max-height: 515px;">
      <p><b>Step 3:</b> Follow the instructions to link your TPBank account in the newly opened window:</p>
      <ol>
        <li>
          <p>Enter your TPBank account and password, then click <b>Login</b>.</p>
          <img src="../assets/img/tpb-connect/ind-3.1.png" class="img-fluid rounded" style="max-height: 515px; margin-bottom: 1rem;">
        </li>
        <li>
          <p>Confirm to allow SePay to connect with your TPBank account by clicking <b>Allow</b>.</p>
          <img src="../assets/img/tpb-connect/ind-3.2.png" class="img-fluid rounded" style="max-height: 515px; margin-bottom: 1rem;">
        </li>
        <li>
          <p>Select the TPBank account you wish to link to SePay and click <b>Continue</b>.</p>
          <img src="../assets/img/tpb-connect/ind-3.3.png" class="img-fluid rounded" style="max-height: 515px; margin-bottom: 1rem;">
        </li>
        <li>
          <p>Next, click <b>Confirm</b> to agree to link SePay with your TPBank account.</p>
          <img src="../assets/img/tpb-connect/ind-3.4.png" class="img-fluid rounded" style="max-height: 515px; margin-bottom: 1rem;">
        </li>
        <li>
          <p>Enter the OTP sent to your registered phone number with TPBank and click <b>Authenticate</b>.</p>
          <img src="../assets/img/tpb-connect/ind-3.5.png" class="img-fluid rounded" style="max-height: 515px; margin-bottom: 1rem;">
        </li>
        <li>
          <p>After successfully authenticating the OTP, you will receive a notification confirming the successful connection of your TPBank account.</p>
          <img src="../assets/img/tpb-connect/ind-3.6.png" class="img-fluid rounded" style="max-height: 515px; margin-bottom: 1rem;">
        </li>
        <li>
          <p>Click <b>Finish</b> to close the window and return to the TPBank account management page in SePay.</p>
        </li>
      </ol>

      <p><b>Step 4:</b> At SePay, you will be redirected to your TPBank account management page.</p>
<img src="../assets/img/tpb-connect/ind-4.png?t=**********" class="img-fluid rounded" style="max-height: 575px; margin-bottom: 1rem;">

<p><b>Step 5:</b> To allow SePay to receive new transactions from the newly added account, you need to wait for less than 5 minutes for the balance change feature to activate.</p>

<p>Now you can make a transfer to the TPBank account that has just been linked.</p>

<img src="../assets/img/tpb-connect/ind-5.png" class="img-fluid rounded" style="max-height: 515px; margin: -1rem 0 1rem -1.5rem;">

<h3 id="ket-noi-doanh-nghiep" class="hs-docs-heading mt-3">
    Connect Business Account <a class="anchorjs-link" href="#ket-noi-doanh-nghiep" aria-label="Anchor" data-anchorjs-icon="#"></a>
</h3>

<div class="ratio ratio-16x9" style="max-width: 800px;">
  <iframe src="https://www.youtube.com/embed/m-I3BufL9cw?si=D9Z-SM6-D2VVmrvD" title="How to connect TPBank business account to SePay" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen style="border-radius: 10px;"></iframe>  
</div>

<div class="alert alert-soft-dark mt-3" role="alert">
  <div class="d-sm-flex">
    <div class="flex-shrink-0">
      <img class="avatar avatar-xl mb-2 mb-sm-0" src="../assets/svg/illustrations/oc-megaphone.svg" alt="Image Description">
    </div>
    <div class="flex-grow-1 ms-sm-4">
      <h3>Note:</h3>
      <p class="mb-0">To successfully connect the TPBank business account, you need to have the login information of the <strong>user who creates the order (accountant)</strong> and <strong>user who approves the order (director)</strong> of the business account.</p>
    </div>
  </div>
</div>

<p class="mt-3">Here are the steps to connect the TPBank business account API to SePay:</p>

<p><b>Step 1:</b> Visit <a href="https://my.sepay.vn" target="_blank">my.sepay.vn</a> -> Select the <b>Bank</b> menu -> Choose <a href="https://my.sepay.vn/bankaccount/connect" target="_blank" class="fw-bold">Connect Account</a>.</p>
<img src="../assets/img/tpb-connect/ind-1.png" class="img-fluid rounded" style="max-height: 500px;">

<p>In the bank API connection interface, select <strong>Business</strong> and click to connect to TPBank.</p>
<img src="../assets/img/tpb-connect/ent-2.png" class="img-fluid rounded" style="max-height: 515px;">

<p class="mt-3">A popup will appear, click the <b>Start Connection</b> button to proceed.</p>
<img src="../assets/img/tpb-connect/ent-3.png" class="img-fluid rounded" style="max-height: 515px;">

<p class="mt-3"><b>Step 2:</b> Perform the connection of the TPBank business account in the newly opened window:</p>
<ol>
  <li>
    <p>Use the <strong>order creator (accountant)</strong> to log in to the website in the newly opened window.</p>
    <img src="../assets/img/tpb-connect/ent-2.1.png" class="img-fluid rounded" style="max-height: 515px;">
  </li>
  <li>
    <p class="mt-3">Confirm to allow SePay to connect with your TPBank business account by clicking <strong>Allow</strong>.</p>
    <img src="../assets/img/tpb-connect/ent-2.2.png" class="img-fluid rounded" style="max-height: 515px;">
  </li>
  <li>
    <p class="mt-3">Select the TPBank business account you want to connect with SePay and click <strong>Continue</strong>.</p>
    <img src="../assets/img/tpb-connect/ent-2.3.png" class="img-fluid rounded" style="max-height: 515px;">
  </li>
  <li>
    <p class="mt-3">Next, click <strong>Confirm</strong> to agree to link SePay with your TPBank business account.</p>
    <img src="../assets/img/tpb-connect/ent-2.4.png" class="img-fluid rounded" style="max-height: 515px;">
  </li>
  <li>
    <p class="mt-3">Enter the OTP code sent to the phone number registered with TPBank and click <strong>Authenticate</strong>.</p>
    <img src="../assets/img/tpb-connect/ent-2.5.png" class="img-fluid rounded" style="max-height: 515px;">
  </li>
  <li>
    <p class="mt-3">After successfully verifying the OTP, you will receive a notification that the balance change feature has been successfully created.</p>
    <img src="../assets/img/tpb-connect/ent-2.6.png" class="img-fluid rounded" style="max-height: 515px;">
  </li>
  <li>
    <p class="mt-3">Click <strong>Finish</strong> to close the window and return to the SePay account connection waiting page <i>(note: do not close the tab waiting for SePay link)</i>.</p>
  </li>
</ol>

<p><b>Step 3:</b> Approve the order to share the balance change created in Step 3.</p>
<ol>
  <li>
    <p>Open a new tab and visit <a href="https://biz.tpb.vn" target="_blank">biz.tpb.vn</a> and use the <strong>order approver (director)</strong> to log in and approve the order.</p>
    <div class="alert alert-warning" role="alert">
        <p class="mb-0"><strong>Note:</strong> Currently, the order approval can only be done on the official TPBank Biz website at <a href="https://biz.tpb.vn" target="_blank">biz.tpb.vn</a>, not supported via the TPBank Biz app.</p>
    </div>
  </li>
  <li>
    <p>Go to <a href="https://biz.tpb.vn/main/approval/transactions" target="_blank">Transaction Approval</a>, click on the transaction order that needs approval.</p>
    <img src="../assets/img/tpb-connect/ent-4.png" class="img-fluid rounded" style="max-height: 515px;">
  </li>
  <li class="mt-3">
    <p>On the transaction details page, click <strong>Approve</strong> to proceed to the confirmation page.</p>
    <img src="../assets/img/tpb-connect/ent-5.png" class="img-fluid rounded" style="max-height: 515px;">
  </li>
  <li class="mt-3">
    <p>Click <strong>Confirm</strong> and enter the OTP code to complete the approval process.</p>
    <img src="../assets/img/tpb-connect/ent-6.png" class="img-fluid rounded" style="max-height: 515px;">
  </li>
  <li class="mt-3">
    <p>After successfully approving the order, you will receive a notification confirming the approval.</p>
    <img src="../assets/img/tpb-connect/ent-7.png" class="img-fluid rounded" style="max-height: 515px;">
  </li>
</ol>


<p><b>Step 4:</b> Complete the connection</p>
<ol>
    <li>Return to the SePay tab waiting for the connection</li>
    <li>The system will automatically redirect you to the TPBank account management page after the transaction is successfully approved</li>
</ol>
<p>Wait about 5 minutes for the balance tracking feature to be activated.</p>

<div class="alert alert-soft-dark" role="alert">
  <div class="d-sm-flex">
    <div class="flex-shrink-0">
      <img class="avatar avatar-xl mb-2 mb-sm-0" src="../assets/svg/illustrations/oc-megaphone.svg" alt="Image Description">
    </div>
    <div class="flex-grow-1 ms-sm-4">
      <h3>Note:</h3>
      When connecting to TPBank via API, SePay by default receives credit/debit transactions to the main account number provided by TPBank. You can create virtual accounts with the prefix <code>TKP</code> to authenticate payments through the payment content.
    </div>
  </div>
</div>

<h3 id="change-connection-method" class="hs-docs-heading">
  Change the connection method <a class="anchorjs-link" href="#change-connection-method" aria-label="Anchor" data-anchorjs-icon="#"></a>
</h3>

<p>If you are using TPBank with SMS Banking and want to upgrade to API Banking for a better experience, SePay has a solution for you.</p>
<p>Currently, SePay supports upgrading from SMS Banking to API Banking for TPBank. The process is very simple, just follow the steps below:</p>

<h4 class="hs-docs-heading mt-0 mb-4" id="sms-banking-to-api-banking">
  Guide to upgrade from SMS Banking to API Banking <a class="anchorjs-link" href="#sms-banking-to-api-banking" aria-label="Anchor" data-anchorjs-icon="#"></a>
</h4>

<p><b>Step 1:</b> Go to the <a href="https://my.sepay.vn/bankaccount" target="_blank">Bank Management</a> page and select the TPBank account to upgrade.</p>
<img src="../assets/img/tpb-connect/sms-to-api-1.png" class="img-fluid rounded" style="max-height: 500px;">
<p class="mt-3"><b>Step 2:</b> Click on the <b>Upgrade to API Banking</b> button on the screen</p>
<img src="../assets/img/tpb-connect/sms-to-api-2.png?t=**********" class="img-fluid rounded" style="max-height: 620px; margin: -1.5rem -2rem;">
<p class="mt-3">Next, select the appropriate account type <i>(the account you registered with TPBank)</i>: <strong>Personal</strong> or <strong>Business</strong>.</p>
<img src="../assets/img/tpb-connect/sms-to-api-3.png?t=**********" class="img-fluid rounded" style="max-height: 620px; margin: -1.5rem -2rem;">
<p class="mt-3">Click <strong>Start upgrade</strong> to open the connection window with TPBank.</p>
<img src="../assets/img/tpb-connect/sms-to-api-4.png" class="img-fluid rounded" style="max-height: 500px;">
<p class="mt-3"><b>Step 3:</b> Follow the connection instructions for the <a href="#personal-connection">Personal</a> or <a href="#business-connection">Business</a> account based on your selection.</p>
<p>Once completed, the system will notify you of the successful upgrade, and you can start using API Banking.</p>

<p class="mt-4">Additionally, at my.sepay.vn, you can:</p>
<ul>
  <li><a href="https://my.sepay.vn/transactions">View transaction history</a></li>
  <li><a href="https://my.sepay.vn/bankaccount">View connected banks or create additional VA.</a></li>
</ul>
<p>Good luck!</p>

<div class="my-2 mt-5">
  <p>Read more: <a href="view-transactions.html">View transactions<i class="bi bi-chevron-right"></i></a></p>
</div>

    </div>
    <!-- End Content -->
  </main>
  <!-- ========== END MAIN CONTENT ========== -->

  <!-- ========== SECONDARY CONTENTS ========== -->
  <!--contact-box-html-docs-->
  <div id="contact-box-overlay"></div>
  <div id="contact-box">
    <div class="popup">
      <a href="https://m.me/***************" target="_blank" class="item">
        <div class="logo">
          <img src="../assets/img/others/fb-messenger.png" width="50%" />
        </div>
        <div class="meta">
          <p class="title">Facebook Messenger</p>
          <small class="description">24/7 live chat support</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="tel:***********" class="item">
        <div class="icon" style="background-color: #22c55e; color: #fff;">
          <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20"
            style="color: currentcolor;">
            <path
              d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z"
              stroke="currentColor" stroke-width="1.5" stroke-linecap="round" fill="transparent"></path>
          </svg>
        </div>
        <div class="meta">
          <p class="title">Hotline</p>
          <small class="description">Support phone number</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
        <div class="logo">
          <img src="../assets/img/others/youtube-social.png" />
        </div>
        <div class="meta">
          <p class="title">YouTube</p>
          <small class="description">Follow SePay's latest videos</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
        <div class="logo">
          <img src="../assets/img/others/telegram-social.png" />
        </div>
        <div class="meta">
          <p class="title">Telegram</p>
          <small class="description">Get the latest updates from SePay</small>
        </div>
      </a>
    </div>
    <div class="container">
      <div class="dot-ping">
        <div class="ping"></div>
        <div class="dot"></div>
      </div>
      <div class="contact-icon">
        <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20"
          style="color: currentcolor;">
          <path fill-rule="evenodd" clip-rule="evenodd"
            d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z"
            fill="currentColor"></path>
        </svg>
      </div>
      <span style="font-weight: bold;">Contact Us</span>
    </div>
  </div>
  <!--/contact-box-html-docs-->

  <!-- Go To -->
  <a class="js-go-to go-to position-fixed" href="javascript:;" style="visibility: hidden;" data-hs-go-to-options='{
       "offsetTop": 700,
       "position": {
         "init": {
           "right": "2rem"
         },
         "show": {
           "bottom": "2rem"
         },
         "hide": {
           "bottom": "-2rem"
         }
       }
     }'>
    <i class="bi-chevron-up"></i>
  </a>
  <!-- ========== END SECONDARY CONTENTS ========== -->

  <!-- JS Global Compulsory  -->
  <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

  <!-- JS Implementing Plugins -->
  <script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
  <script src="../assets/vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js"></script>
  <script src="../assets/vendor/list.js/dist/list.min.js"></script>
  <script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>


  <!-- JS Front -->
  <script src="../assets/js/theme.min.js"></script>

  <!-- contact-box-js -->
  <script src="../assets/js/contact-box.js"></script>
  <!-- /contact-box-js -->

  <!-- JS Plugins Init. -->
  <script>
    (function () {
      // INITIALIZATION OF HEADER
      // =======================================================
      new HSHeader('#header').init()

      // INITIALIZATION OF NAV SCROLLER
      // =======================================================
      new HsNavScroller('.js-nav-scroller', {
        delay: 400,
        offset: 140
      })

      // INITIALIZATION OF LISTJS COMPONENT
      // =======================================================
      const docsSearch = HSCore.components.HSList.init('#docsSearch')




      // INITIALIZATION OF GO TO
      // =======================================================
      new HSGoTo('.js-go-to')
    })()
  </script>
</body>

</html>