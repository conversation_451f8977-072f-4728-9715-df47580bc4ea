<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Required Meta Tags Always Come First -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- Title -->
    <title>Guide to Integrating SePay with Shopify | SePay</title>


    <!-- Favicon -->
    <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />

    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">

    <!-- CSS Implementing Plugins -->
    <link rel="stylesheet" href="../assets/vendor/bootstrap-icons/font/bootstrap-icons.css">
    <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
    <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
  
    <!-- CSS Front Template -->
    <link rel="stylesheet" href="../assets/css/theme.min.css">
    <link rel="stylesheet" href="../assets/css/docs.min.css">

    <!--contact-box-css-->
    <link rel="stylesheet" href="../assets/css/contact-box.css">
    <!--/contact-box-css-->

    <link rel="stylesheet" href="../assets/vendor/hs-video-bg/dist/hs-video-bg.min.css">
    <style>
        .docs-navbar-sidebar-aside-body {
            padding-top: 3.5rem !important;
        }
    </style>
      <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-J8DLMQTKSQ');
  </script>
</head>
<style>
    .custom-alert-content {
    line-height: 1.8; /* Tăng khoảng cách giữa các dòng */
    margin-top: 10px; /* Tạo khoảng cách phía trên */
}

.custom-alert-content ol {
    padding-left: 20px; /* Thụt vào danh sách chính */
    margin-bottom: 10px; /* Khoảng cách dưới danh sách */
}

.custom-alert-content ul {
    padding-left: 30px; /* Thụt vào danh sách phụ */
    margin-bottom: 5px; /* Khoảng cách dưới danh sách phụ */
}

.custom-alert-content li {
    margin-bottom: 10px; /* Tăng khoảng cách giữa các mục */
}



</style>
<body class="navbar-sidebar-aside-lg">
    <!-- ========== HEADER ========== -->
    <header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
        <div class="container-fluid">
            <nav class="navbar-nav-wrap">
                <div class="row flex-grow-1">
                    <!-- Default Logo -->
                    <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
                        <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                            <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png"
                                alt="Logo">
                        </a>

                    </div>
                    <!-- End Default Logo -->

                    <div class="col-md px-lg-0">
                        <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
                            <div class="d-none d-md-block">


                                <!-- List Item Template -->
                                <div class="d-none">
                                    <div id="searchTemplate" class="dropdown-item">
                                        <a class="d-block link" href="#">
                                            <span class="category d-block fw-normal text-muted mb-1"></span>
                                            <span class="component text-dark"></span>
                                        </a>
                                    </div>
                                </div>
                                <!-- End List Item Template -->
                            </div>

                            <!-- Navbar -->
                            <ul class="navbar-nav p-0">
                                <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html"
                                        target="_blank">
                                        Support <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN" target="_blank">
                                      Youtube <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                  </li>
                                  <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://www.facebook.com/messages/t/sepay.vn" target="_blank">
                                      Facebook <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                  </li>
                                  <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel" target="_blank">
                                      Telegram <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                  </li>

                            </ul>
                            <!-- End Navbar -->
                        </div>
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Row -->
            </nav>
        </div>
    </header>
    <!-- ========== END HEADER ========== -->

    <!-- ========== MAIN CONTENT ========== -->
    <main id="content" role="main">
        <!-- Navbar -->
        <nav class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
            data-hs-nav-scroller-options='{
            "type": "vertical",
            "target": ".navbar-nav .active",
            "offset": 80
           }'>
            <!-- Navbar Toggle -->
            <button type="button" class="navbar-toggler btn btn-white d-grid w-100" data-bs-toggle="collapse"
                data-bs-target="#navbarVerticalNavMenu" aria-label="Toggle navigation" aria-expanded="false"
                aria-controls="navbarVerticalNavMenu">
                <span class="d-flex justify-content-between align-items-center">
                    <span class="h3 mb-0">Nav menu</span>

                    <span class="navbar-toggler-default">
                        <i class="bi-list"></i>
                    </span>

                    <span class="navbar-toggler-toggled">
                        <i class="bi-x"></i>
                    </span>
                </span>
            </button>
            <!-- End Navbar Toggle -->

            <!-- Navbar Collapse -->
            <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
                <div class="navbar-brand-wrapper border-end" style="height: auto;">
                    <!-- Default Logo -->
                    <div class="d-flex align-items-center mb-3">
                        <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                            <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png"
                                alt="Logo">
                        </a>

                    </div>
                    <!-- End Default Logo -->


                </div>

                <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
                    <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
                        <li class="nav-item">
                            <span class="nav-subtitle">Introduction</span>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="what-is-sepay.html">What is SePay?</a>
                        </li>
                
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">General Instructions</span>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="register-sepay.html">Register SePay</a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="add-bank-account.html">Add Bank Account</a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link " href="view-transactions.html">View Transactions</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="users-and-permissions.html">Users & Permissions</a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="sub-accounts.html">Sub Accounts</a>
                        </li>
                
                        
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">Company Configuration</span>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link " href="service-package.html">Service Package</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="invoices-and-payments.html">Invoices & Payments</a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link " href="general-settings.html">General Settings</a>
                        </li>
                
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">Balance Change Sharing</span>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link " href="telegram-integration.html">Integrate Telegram <i class="bi bi-telegram ms-2 text-info"></i></a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="lark-messenger-integration.html">Integrate Lark Messenger <img src="../assets/img/lark/lark-icon.png" class="img-fluid" style="max-height:25px"></a>
                        </li>
                
                        <li class="nav-item">               
                            <a class="nav-link d-flex align-items-center" href="mobile-app.html">Mobile App <i class="bi bi-phone-vibrate ms-2 fs-3 text-info"></i> </a>           
                        </li>
                
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">Web Integration</span>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="shopify-integration.html">Integrate Shopify <img class="ms-2" src="../assets/img/others/shopify-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="sapo-integration.html">Integrate Sapo <img class="ms-2" src="../assets/img/others/sapo-icon.png" style="width:18px; height: 18px;"></a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="haravan-integration.html">Integrate Haravan <img class="ms-2" src="../assets/img/others/haravan-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="woocommerce.html">Integrate WooCommerce <img class="ms-2" src="../assets/img/others/woocommerce-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="google-sheets-integration.html">Integrate Google Sheets <img class="ms-2" src="../assets/img/others/google-sheets-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="hostbill-integration.html">Integrate HostBill <img class="ms-2"
                                    src="../assets/img/others/hostbill-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">Programming & Integration</span>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="webhooks-integration.html">Integrate WebHooks</a>
                        </li>
                
                        <li class="nav-item ">
                            <a class="nav-link" href="program-webhooks.html">Program WebHooks</a>
                        </li>
                        <li class="nav-item ">
                            <a class="nav-link" href="simulate-transaction.html">Simulate Transaction</a>
                        </li>
                        <li class="nav-item ">
                            <a class="nav-link" href="create-qr-code-vietqr-dong.html">Create & Embed QR Code</a>
                        </li>
                
                        
                
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">SePay API</span>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="api-introduction.html">API Introduction</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="create-api-token.html">Create API Token</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="api-transactions.html">API Transactions</a>
                        </li>
                       <li class="nav-item">
                  <a class="nav-link" href="bank-account-api.html">Bank Account API</a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="api-va-order-bidv.html">API Va Order</a>
            </li>
                    </ul>
                </div>
                
            </div>
            <!-- End Navbar Collapse -->
        </nav>
        <!-- End Navbar -->

        <!-- Content -->
        <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">

            <div class="docs-page-header">
                <div class="row align-items-center">
                    <div class="col-sm">
                        <h1 class="docs-page-header-title">Shopify Integration Guide <img src="../assets/img/others/shopify-icon.png" style="width:30px"></h1>
                        <p class="docs-page-header-text">Integrating SePay with Shopify allows payment confirmation immediately after the customer transfers money. The order will also change to a "paid" status.</p>
                    </div>
                </div>
            </div>
        
            <p>If you are using a sales website on <a href="https://www.shopify.com" target="_blank">Shopify</a>, this article will guide you through integrating SePay with Shopify to automate payment confirmation via bank transfer.</p>
            <div class="alert alert-soft-danger mt-2" role="alert">
                <div class="d-flex align-items-baseline">
                    <div class="flex-shrink-0">
                        <i class="bi-info-circle me-1"></i>
                    </div>
        
                    <div class="flex-grow-1 ms-2 custom-alert-content">
                        Note: For SePay integrations made before <b>01/01/2025</b>, please follow these steps to synchronize the new integration as per Shopify's notification:
                        <ol>
                            <li>
                                In the SePay configuration of the app integrated in Shopify, select <b>Configure Admin API Scopes</b>.
                                In <b>Admin API Scopes</b>, find <b>Script Tags</b> and select the permission:
                                <ul>
                                    <li><b>Write and read script tags (write_script_tags, read_script_tags)</b></li>
                                </ul>
                                <b>Do not select</b> other permissions, then click <b>Save</b>.
                            </li>
                            <li>
                                After successfully adding the permission, go back to the Shopify integration section in SePay and select <b>Sync Now</b> under the integration name to update to the latest version.
                            </li>
                            <li>
                                Go to <b>Settings</b> -> <b>Checkout</b>, find and delete <b>Additional code for order status page</b> to avoid conflicts.
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
            
        
            <p class="fw-bold">Results after integration:</p>
            <p class="ms-2 fw-bold">1. For the online shopper</p>
                <ul class="lh-lg">
                    <li>When the customer places an order and makes a payment, there will be an additional option for <b>Bank Transfer</b></li>
                    <li>A QR code with payment details will appear after selecting the payment method.</li>
                    <li>The website will display <b class="text-success">You have successfully paid</b> after the customer makes the payment within seconds.</li>
                    <li>A payment confirmation email will be sent to the customer (from Shopify).</li>
                </ul>
        
                <div style="max-width: 800px;">
        
                    <div class="ratio ratio-4x3">
                        <iframe src="https://www.youtube.com/embed/zuAS-NRbSqw" title="YouTube video" allowfullscreen></iframe>
                      </div>
                      <p class="text-muted text-center mt-2">Demo video of Shopify integration result</p>
                </div>
                   
                
            <p class="ms-2 mt-4 fw-bold">2. For the Admin interface</p>
            <p>The order will automatically be recorded as paid and will change to "paid" status (if the customer has paid in full).</p>
        
            <h2 class="mb-3">Integration Guide</h2>
            
            <div style="max-width: 800px;">
        
                <div class="ratio ratio-4x3">
                       <iframe src="https://www.youtube.com/embed/Kvr8n2uTLUc" title="YouTube video" allowfullscreen></iframe>
                     </div>
                     <p class="text-muted text-center mt-2">Shopify integration tutorial video</p>
            </div>
                   
        
            <p  class="h3 my-3"><b>Step 1: Create a Private App on Shopify to get API information</b></p>
            
            <p>The purpose of this step is to obtain the API Token. This token is used to fill in SePay, allowing SePay to update the order status on Shopify after the customer has successfully paid.</p>
                     
                     
            <ul class="lh-lg">
                <li>Go to <b>Shopify Admin</b> -> <b>Settings</b> -> select <b>Apps and sales channels</b> at the top right. Choose <b>Develop apps</b> -> <b>Create an app</b>.</li>
                <li>In the <b>App name</b> field, enter <b>SePay Integration</b> -> click <b>Create app</b>.</li>
                <li>In <b>Overview</b> -> click <b>Configure Admin API Scopes</b> in <b>Admin API Scopes</b>, find <b>Orders</b> and select <b>Read and write orders (write_orders, read_orders)</b>, find <b>Script Tags</b> -> select <b>Read and write script tags (write_script_tags, read_script_tags)</b> -> Do not select other permissions -> Then click <b>Save</b>.</li>
                <li>In <b>App Settings</b>, click <b>App Settings</b>. After successful creation, you will see the API Token information.</li>
            </ul>
        
            <p  class="h3 my-3"><b>Step 2: Create the integration in SePay.</b></p>
            <p>Go to <b>my.sepay.vn</b> -> <b>Shopify Integration</b> -> <b>Add Integration</b></p>
            <p>In the integration creation interface, fill in the following information:
                <ul class="lh-lg">
                    <li><b>Integration name</b>: Enter any name. For example, Shopify Integration.</li>
                    <li><b>Select Bank</b>: Choose the bank account you want to receive payments on your Shopify website. The bank information you select will appear when the customer completes the order.</li>
                    <li><b>Store URL</b>: Enter the URL to your Shopify store.</li>
                    <li><b>API Token</b>: Enter the API Token obtained in Step 1.</li>
                    <li><b>Custom parameter configuration</b>: Currently, SePay supports customizing parameters such as <code>kind</code>, <code>source</code>. You can find more information at <a href="https://shopify.dev/docs/api/admin-rest/2024-07/resources/transaction" target="_blank">Shopify transaction</a>.</li>
                </ul>
            </p>
            <p>Click <b>Add</b>. The system will integrate successfully.</p>
            
            <p  class="h3 my-3"><b>Step 3: Add payment method in Shopify</b></p>
            <p>The purpose of this step is to create an additional payment method <b>Bank Transfer</b> in Shopify.</p>
            
        
            
        
            <ul class="lh-lg">
                <li>Go to <b>Shopify Admin</b> -> <b>Settings</b> -> <b>Payments</b></li>
                <li>In the <b>Manual payment methods</b> section, select <b>+ Manual payment method</b> and choose <b>Create custom payment method</b></li>
                <li>In <b>Custom payment method name</b>, enter <b>Bank Transfer (Auto-confirm transaction)</b>. Note that the payment method name must contain <b>transfer</b> or <b>VietQR</b> for the payment information to appear on your website.</li>
                <li>In <b>Additional details</b>, enter <b>Use bank app to scan QR code, auto-confirm transaction.</b></li>
                <li>In <b>Payment instructions</b>, enter <b>Use bank app to scan QR code, auto-confirm payment within 10 seconds.</b></li>
                <li>Select <b>Activate</b> to add the new payment method.</li>
        
            </ul>
            
            <p>At this point, you have successfully integrated SePay with Shopify.</p>
            <h2 class="my-3">Check your configurations</h2>
            <p>To ensure everything is working as configured, you can perform the following:</p>
            <ul class="lh-lg">
                <li>Place an order on your Shopify website.</li>
                <li>In the Payment step, choose the payment method <b>Bank Transfer (Auto-confirm transaction)</b> as added in Step 4.</li>
                <li>If the QR code appears after placing the order, it means you have configured successfully.</li>
            </ul>
            <div class="alert alert-soft-danger mt-2" role="alert">
                <div class="d-flex align-items-baseline">
                    <div class="flex-shrink-0">
                        <i class="bi-info-circle me-1"></i>
                    </div>
        
                    <div class="flex-grow-1 ms-2">Note: If you modify the order number format in Shopify (the default is #), you must adjust the payment code in SePay to match. For example, if the Shopify order number starts with <b>SE</b>, the prefix in SePay's general settings must be <b>SE</b>.</div>
                </div>
            </div>
        
            <div class="text-center"><img src="../assets/img/others/shopify-checkout.png" class="img-fluid"></div>
        
           
        
            <div class="my-2"><p>Read more: <a href="sapo-integration.html">Sapo Integration Guide<i class="bi bi-chevron-right"></i></a></p></div>
        
        </div>
        
        <!-- End Content -->
    </main>
    <!-- ========== END MAIN CONTENT ========== -->

    <!-- ========== SECONDARY CONTENTS ========== -->
    <!--contact-box-html-docs-->
  <div id="contact-box-overlay"></div>
  <div id="contact-box">
    <div class="popup">
      <a href="https://m.me/117903214582465" target="_blank" class="item">
        <div class="logo">
          <img src="../assets/img/others/fb-messenger.png" width="50%" />
        </div>
        <div class="meta">
          <p class="title">Facebook Messenger</p>
          <small class="description">24/7 live chat support</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="tel:02873059589" class="item">
        <div class="icon" style="background-color: #22c55e; color: #fff;">
          <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20"
            style="color: currentcolor;">
            <path
              d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z"
              stroke="currentColor" stroke-width="1.5" stroke-linecap="round" fill="transparent"></path>
          </svg>
        </div>
        <div class="meta">
          <p class="title">Hotline</p>
          <small class="description">Support phone number</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
        <div class="logo">
          <img src="../assets/img/others/youtube-social.png" />
        </div>
        <div class="meta">
          <p class="title">YouTube</p>
          <small class="description">Follow SePay's latest videos</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
        <div class="logo">
          <img src="../assets/img/others/telegram-social.png" />
        </div>
        <div class="meta">
          <p class="title">Telegram</p>
          <small class="description">Get the latest updates from SePay</small>
        </div>
      </a>
    </div>
    <div class="container">
      <div class="dot-ping">
        <div class="ping"></div>
        <div class="dot"></div>
      </div>
      <div class="contact-icon">
        <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20"
          style="color: currentcolor;">
          <path fill-rule="evenodd" clip-rule="evenodd"
            d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z"
            fill="currentColor"></path>
        </svg>
      </div>
      <span style="font-weight: bold;">Contact Us</span>
    </div>
  </div>
  <!--/contact-box-html-docs-->

    <!-- Go To -->
    <a class="js-go-to go-to position-fixed" href="javascript:;" style="visibility: hidden;" data-hs-go-to-options='{
       "offsetTop": 700,
       "position": {
         "init": {
           "right": "2rem"
         },
         "show": {
           "bottom": "2rem"
         },
         "hide": {
           "bottom": "-2rem"
         }
       }
     }'>
        <i class="bi-chevron-up"></i>
    </a>
    <!-- ========== END SECONDARY CONTENTS ========== -->

    <!-- JS Global Compulsory  -->
    <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

    <!-- JS Implementing Plugins -->
    <script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
    <script src="../assets/vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js"></script>

    <script src="../assets/vendor/list.js/dist/list.min.js"></script>
    <script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>
    <script src="../assets/vendor/hs-video-bg/dist/hs-video-bg.min.js"></script>

    <!-- JS Front -->
    <script src="../assets/js/theme.min.js"></script>

    <!-- contact-box-js -->
    <script src="../assets/js/contact-box.js"></script>
    <!-- /contact-box-js -->

    <!-- JS Plugins Init. -->
    <script>
        (function () {
            // INITIALIZATION OF HEADER
            // =======================================================
            new HSHeader('#header').init()

          // INITIALIZATION OF NAV SCROLLER
            // =======================================================
            new HsNavScroller('.js-nav-scroller', {
                delay: 400,
                offset: 140
            }) 
            // INITIALIZATION OF LISTJS COMPONENT
            // =======================================================
            const docsSearch = HSCore.components.HSList.init('#docsSearch')




            // INITIALIZATION OF GO TO
            // =======================================================
            new HSGoTo('.js-go-to')
        })()
    </script>
</body>

</html>