<!DOCTYPE html>
<html lang="en">
<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

  <!-- Title -->
  <title>Guide to Integrating Google Sheets for Automatically Fetching Bank Transactions | SePay</title>


  <!-- Favicon -->
  <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">

  <!-- CSS Implementing Plugins -->
  <link rel="stylesheet" href="../assets/vendor/bootstrap-icons/font/bootstrap-icons.css">
  
  <!-- meta tag -->

  <meta property="og:locale" content="vi_VN" />
  <link rel="canonical" href="https://docs.sepay.vn/tich-hop-google-sheets.html" />
  <meta name="description" content="Tích hợp này giúp SePay bắn giao dịch ngân hàng lên Google Sheets một cách tự động."/>
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://docs.sepay.vn/tich-hop-google-sheets.html" />
  <meta property="og:title" content="Hướng dẫn tích hợp Google Sheet tự động lấy giao dịch ngân hàng" />
  <meta property="og:description" content="Tích hợp này giúp SePay bắn giao dịch ngân hàng lên Google Sheets một cách tự động." />

  <meta property="og:site_name" content="Hướng dẫn tích hợp Google Sheet tự động lấy giao dịch ngân hàng" />
  <meta property="og:image" content="https://docs.sepay.vn/assets/img/google-sheets-oauth/hd-00.png" />
  <meta property="og:image:secure_url" content="https://docs.sepay.vn/assets/img/google-sheets-oauth/hd-00.png" />
  <!-- meta tag -->

  <!-- CSS Front Template -->
  <link rel="stylesheet" href="../assets/css/theme.min.css">
  <link rel="stylesheet" href="../assets/css/docs.min.css">

  <!--contact-box-css-->
  <link rel="stylesheet" href="../assets/css/contact-box.css">
  <!--/contact-box-css-->

  <style>
    .docs-navbar-sidebar-aside-body {
      padding-top: 3.5rem!important;
  }
  </style>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
  
      gtag('config', 'G-J8DLMQTKSQ');
    </script>
</head>

<body class="navbar-sidebar-aside-lg">
  <!-- ========== HEADER ========== -->
  <header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
    <div class="container-fluid">
      <nav class="navbar-nav-wrap">
        <div class="row flex-grow-1">
          <!-- Default Logo -->
          <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
              <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo">
            </a>
           
          </div>
          <!-- End Default Logo -->

          <div class="col-md px-lg-0">
            <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
              <div class="d-none d-md-block">
               

                <!-- List Item Template -->
                <div class="d-none">
                  <div id="searchTemplate" class="dropdown-item">
                    <a class="d-block link" href="#">
                      <span class="category d-block fw-normal text-muted mb-1"></span>
                      <span class="component text-dark"></span>
                    </a>
                  </div>
                </div>
                <!-- End List Item Template -->
              </div>

              <!-- Navbar -->
              <ul class="navbar-nav p-0">
                <li class="nav-item">
                  <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html" target="_blank">
                    Support <i class="bi-box-arrow-up-right ms-1"></i>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN" target="_blank">
                    Youtube <i class="bi-box-arrow-up-right ms-1"></i>
                  </a>
                </li>
                <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://www.facebook.com/messages/t/sepay.vn" target="_blank">
                                      Facebook <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                  </li>
                                  <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel" target="_blank">
                                      Telegram <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                  </li>
                
              </ul>
              <!-- End Navbar -->
            </div>
          </div>
          <!-- End Col -->
        </div>
        <!-- End Row -->
      </nav>
    </div>
  </header>
  <!-- ========== END HEADER ========== -->

  <!-- ========== MAIN CONTENT ========== -->
  <main id="content" role="main">
    <!-- Navbar -->
    <nav class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
          data-hs-nav-scroller-options='{
            "type": "vertical",
            "target": ".navbar-nav .active",
            "offset": 80
           }'>
      <!-- Navbar Toggle -->
      <button type="button" class="navbar-toggler btn btn-white d-grid w-100" data-bs-toggle="collapse" data-bs-target="#navbarVerticalNavMenu" aria-label="Toggle navigation" aria-expanded="false" aria-controls="navbarVerticalNavMenu">
        <span class="d-flex justify-content-between align-items-center">
          <span class="h3 mb-0">Nav menu</span>

          <span class="navbar-toggler-default">
            <i class="bi-list"></i>
          </span>

          <span class="navbar-toggler-toggled">
            <i class="bi-x"></i>
          </span>
        </span>
      </button>
      <!-- End Navbar Toggle -->

      <!-- Navbar Collapse -->
      <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
        <div class="navbar-brand-wrapper border-end" style="height: auto;">
          <!-- Default Logo -->
          <div class="d-flex align-items-center mb-3">
            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
              <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo">
            </a>
           
          </div>
          <!-- End Default Logo -->
      
       
        </div>

        <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
          <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
            <li class="nav-item">
              <span class="nav-subtitle">Introduction</span>
            </li>
        
            <li class="nav-item">
              <a class="nav-link" href="what-is-sepay.html">What is SePay?</a>
            </li>
        
            <li class="nav-item my-2 my-lg-5"></li>
        
            <li class="nav-item">
              <span class="nav-subtitle">General Instructions</span>
            </li>
        
            <li class="nav-item">
              <a class="nav-link" href="register-sepay.html">Register for SePay</a>
            </li>
        
            <li class="nav-item">
              <a class="nav-link" href="add-bank-account.html">Add Bank Account</a>
            </li>
        
            <li class="nav-item">
              <a class="nav-link" href="view-transactions.html">View Transactions</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="users-and-permissions.html">Users & Permissions</a>
            </li>
        
            <li class="nav-item">
              <a class="nav-link" href="sub-accounts.html">Sub Accounts</a>
            </li>
        
            <li class="nav-item my-2 my-lg-5"></li>
        
            <li class="nav-item">
              <span class="nav-subtitle">Company Configuration</span>
            </li>
        
            <li class="nav-item">
              <a class="nav-link" href="service-packages.html">Service Packages</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="invoices-and-payments.html">Invoices & Payments</a>
            </li>
        
            <li class="nav-item">
              <a class="nav-link" href="general-settings.html">General Settings</a>
            </li>
        
            <li class="nav-item my-2 my-lg-5"></li>
        
            <li class="nav-item">
              <span class="nav-subtitle">Balance Change Sharing</span>
            </li>
        
            <li class="nav-item">
              <a class="nav-link" href="telegram-integration.html">Telegram Integration <i class="bi bi-telegram ms-2 text-info"></i></a>
            </li>
        
            <li class="nav-item">
              <a class="nav-link" href="lark-messenger-integration.html">Lark Messenger Integration <img src="../assets/img/lark/lark-icon.png" class="img-fluid" style="max-height:25px"></a>
            </li>
        
            <li class="nav-item">
              <a class="nav-link d-flex align-items-center" href="mobile-app.html">Mobile App <i class="bi bi-phone-vibrate ms-2 fs-3 text-info"></i> </a>
            </li>
        
            <li class="nav-item my-2 my-lg-5"></li>
        
            <li class="nav-item">
              <span class="nav-subtitle">Web Integration</span>
            </li>
        
            <li class="nav-item">
              <a class="nav-link" href="shopify-integration.html">Shopify Integration <img class="ms-2" src="../assets/img/others/shopify-icon.png" style="width:22px; height: 22px;"></a>
            </li>
        
            <li class="nav-item">
              <a class="nav-link" href="sapo-integration.html">Sapo Integration <img class="ms-2" src="../assets/img/others/sapo-icon.png" style="width:18px; height: 18px;"></a>
            </li>
        
            <li class="nav-item">
              <a class="nav-link" href="haravan-integration.html">Haravan Integration <img class="ms-2" src="../assets/img/others/haravan-icon.png" style="width:22px; height: 22px;"></a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="woocommerce-integration.html">WooCommerce Integration <img class="ms-2" src="../assets/img/others/woocommerce-icon.png" style="width:22px; height: 22px;"></a>
            </li>
            <li class="nav-item">
              <a class="nav-link active" href="google-sheets-integration.html">Google Sheets Integration <img class="ms-2" src="../assets/img/others/google-sheets-icon.png" style="width:22px; height: 22px;"></a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="hostbill-integration.html">HostBill Integration <img class="ms-2" src="../assets/img/others/hostbill-icon.png" style="height: 22px;"></a>
            </li>
        
            <li class="nav-item my-2 my-lg-5"></li>
        
            <li class="nav-item">
              <span class="nav-subtitle">Programming & Integration</span>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="webhooks-integration.html">WebHooks Integration</a>
            </li>
        
            <li class="nav-item">
              <a class="nav-link" href="webhooks-programming.html">WebHooks Programming</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="simulate-transactions.html">Simulate Transactions</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="create-qr-code-vietqr-dong.html">Create & Embed QR Code</a>
            </li>
        
            <li class="nav-item my-2 my-lg-5"></li>
        
            <li class="nav-item">
              <span class="nav-subtitle">SePay API</span>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="api-introduction.html">API Introduction</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="create-api-token.html">Create API Token</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="transaction-api.html">Transaction API</a>
            </li>
            <li class="nav-item">
                  <a class="nav-link" href="bank-account-api.html">Bank Account API</a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="api-va-order-bidv.html">API Va Order</a>
            </li>
          </ul>
        </div>
        
        
      </div>
      <!-- End Navbar Collapse -->
    </nav>
    <!-- End Navbar -->

    <!-- Content -->
    <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">

      <div class="docs-page-header">
          <div class="row align-items-center">
              <div class="col-sm">
                  <h1 class="docs-page-header-title">Guide to Integrating Google Sheets<img src="../assets/img/others/google-sheets-icon.png" class="img-fluid" style="max-height:35px"></h1>
                  <p class="docs-page-header-text">
                      This integration helps SePay automatically send bank transactions to 
                      <a href="https://www.google.com/sheets/about/" target="_blank">Google Sheets</a>. 
                      Simple operations make it convenient for filtering and statistics. Additionally, it allows you to configure automatic transaction deletion after a specified time.
                  </p>
              </div>
          </div>
      </div>
      <p>Currently, SePay offers two methods for integrating Google Sheets: official integration via <b>Oauth2</b> and manual integration via <b>App Script</b>. <span class="text-info">SePay recommends using the Oauth2 method due to its simplicity, support for selecting multiple banks, and no need for using <b>WebHooks</b> or performing complex operations like the manual method.</span></p>
  
      <h2 class="docs-page-header-title">1. Official Google Sheets Integration via Oauth2</h2>
      <img src="../assets/img/google-sheets-oauth/hd-00.png" class="img-fluid mb-5">
  
      <p class="h3 my-3"><b>Step 1: Log in and Grant Permissions</b></p>
      <ul class="lh-lg">
          <li>Log in to my.sepay.vn -> <a href="https://my.sepay.vn/googlesheets" target="_blank">Google Sheets Integration</a>.</li>
          <li>Click on <b>Sign in with Google</b>.</li>
  
          <img src="../assets/img/google-sheets-oauth/hd-01.png" class="img-fluid mb-5">
  
          <li>A window will appear. Then, select the Google account you want to link with SePay.</li>
  
          <img src="../assets/img/google-sheets-oauth/hd-02.png" class="img-fluid mb-5">
  
          <li>Grant the necessary permissions for SePay to access and link with your spreadsheet. Then, click <b>Continue</b>.</li>
  
          <img src="../assets/img/google-sheets-oauth/hd-03.png" class="img-fluid mb-5">
      </ul>
  
      <p class="h3 my-3"><b>Step 2: Configure Google Sheets</b></p>
      <ul class="lh-lg">
          <li>Choose one of the following options:
              <ul>
                  <li><b>Create a new spreadsheet:</b> 
                      <ul>
                          <li>Click to create a new spreadsheet, then enter the spreadsheet name. If you choose the storage location as <b>Google Drive - My Drive</b>.</li>
                          <img src="../assets/img/google-sheets-oauth/hd-04.png" class="img-fluid mb-5">
                          <img src="../assets/img/google-sheets-oauth/hd-05.png" class="img-fluid mb-5">
                          <li>If you choose the storage location as <b>Google Drive - Select another folder</b>, the interface will display a list of folders for you to select as the storage location for the spreadsheet.</li>
                          <img src="../assets/img/google-sheets-oauth/hd-06.png" class="img-fluid mb-5">
                      </ul>
                  </li>
  
                  <li><b>Select an existing spreadsheet:</b> Click on <b>Select an existing spreadsheet</b>, then the interface will display a list of spreadsheets for you to choose from.</li>
                  <img src="../assets/img/google-sheets-oauth/hd-07.png" class="img-fluid mb-5">
                  <img src="../assets/img/google-sheets-oauth/hd-08.png" class="img-fluid mb-5">
              </ul>
          </li>
          <li>After successfully creating a new spreadsheet or selecting an existing one, you can choose the time period for <b>automatic transaction deletion</b>. Then, click <b>Continue</b>.</li>
          <img src="../assets/img/google-sheets-oauth/hd-09.png" class="img-fluid mb-5">
      </ul>
  
      <p class="h3 my-3"><b>Step 3: Select Banks</b></p>
      <ul class="lh-lg">
          <li>Select the bank for SePay to send transactions to your spreadsheet. There are 2 options:
              <ul>
                  <li><b>All banks:</b> SePay will send transactions from all banks.</li>
                  <li><b>Select specific banks:</b> You can choose multiple banks.</li>
              </ul>
          </li>
          <li>Configure the type of transactions (incoming, outgoing, or all).</li>
          <li>Optionally hide the bank account balance if needed.</li>
  
          <img src="../assets/img/google-sheets-oauth/hd-010.png" class="img-fluid mb-5">
      </ul>
  
      <p class="h3 my-3"><b>Step 4: Save Integration</b></p>
      <ul class="lh-lg">
          <li><b>SePay</b> will automatically add default data columns to the spreadsheet you have selected. After a successful integration, open the spreadsheet and try a transaction.</li>
          <img src="../assets/img/google-sheets-oauth/hd-011.png" class="img-fluid mb-5">
      </ul>
  
      <h2 class="docs-page-header-title">2. Manual Google Sheets Integration via App Script</h2>
      <p class="text-info">If you have already integrated Google Sheets in section 1, "Official Google Sheets Integration via OAuth2," please skip this section.</p>
      <p class="text-danger">(SePay no longer recommends integrating Google Sheets manually via App Script because this method requires many complex steps, does not support selecting multiple banks, and requires using WebHooks, whereas the Oauth2 method is simpler and more efficient.)</p>
  
      <img src="../assets/img/google-sheets/hd-00.png" class="img-fluid mb-5">
  
      <p class="h3 my-3"><b>Step 1: Create and Configure Google Sheets</b></p>
  
      <ul class="lh-lg">
          <li>Access the <a href="https://docs.google.com/spreadsheets/d/1WmS3WuMj0AjKb8-Aq2xCHtCah8z-p6PU25mVCqE47pk/edit?usp=sharing" target="_blank">sample Google Sheets link</a> prepared by SePay.</li>
          <li>Go to <b>File</b> => <b>Make a copy</b></li>
          <img src="../assets/img/google-sheets/hd-02.png" class="img-fluid mb-5">
          <li>Select <b>Make a copy</b></li>
          <img src="../assets/img/google-sheets/hd-03.png" class="img-fluid mb-5">
          <li>A copy of the sample Google Sheets will be created. Go to the <b>Extensions</b> menu => <b>Apps Script</b>. The system will redirect you to Google's Apps Script page.</li>
          <img src="../assets/img/google-sheets/hd-04.png" class="img-fluid mb-5">
          <li>Go to the <b>Deploy</b> menu => <b>New deployment</b></li>
          <img src="../assets/img/google-sheets/hd-05.png" class="img-fluid mb-5">
          <li>In the Description section, enter any name. In the Who has access section, select <b>Anyone</b>. Click <b>Deploy</b></li>
          <img src="../assets/img/google-sheets/hd-06.png" class="img-fluid mb-5">
          <li>Click <b>Authorize access</b></li>
          <img src="../assets/img/google-sheets/hd-07.png" class="img-fluid mb-5">
          <li>Select your email account, then click <b>Allow</b> to grant authorization</li>
          <img src="../assets/img/google-sheets/hd-08.png" class="img-fluid mb-5">
          <li>The steps in Google Sheets are complete. Copy the web app URL to add it as a new WebHook in SePay.</li>
          <img src="../assets/img/google-sheets/hd-09.png" class="img-fluid mb-5">
      </ul>
  
      <p class="h3 my-3"><b>Step 2: Create WebHook Integration in SePay</b></p>
      <ul class="lh-lg">
          <li>Log in to my.sepay.vn -> <a href="https://my.sepay.vn/webhooks" target="_blank">WebHook Integration</a>. Click <b>Add Integration</b>.</li>
          <li>Fill in the integration details:
              <ul class="lh-lg">
                  <li><b>Name:</b> Enter any name</li>
                  <li><span class="badge bg-primary rounded-pill">1</span> <b>Select event:</b> You can choose the event <code>Incoming money</code>, <code>Outgoing money</code>, or <code>Both</code></li>
                  <li><span class="badge bg-primary rounded-pill">2</span> <b>Configure conditions:</b> Select the bank account you want to send transactions from</li>
                  <li><span class="badge bg-primary rounded-pill">3</span> <b>WebHook properties:</b>
                      <ul id="chat_id">
                          <li><b class="text-danger">Call URL</b>: Enter the URL you copied in Step 1.</li>
                      </ul>
                  </li>
              </ul>
          </li>
          <img src="../assets/img/google-sheets/hd-10.png" class="img-fluid mb-5">
  
          <li>Click <b>Add</b> to complete.</li>
      </ul>
  
      <p>After completing the above steps, whenever there is a new transaction, SePay will send the transaction to Google Sheets.</p>
  
      <p class="h3 my-3"><b>Configure Automatic Transaction Deletion in Google Sheets</b></p>
      <p>If you don't want Google Sheets to store transactions for too long, SePay has prepared an option for automatic transaction deletion. To enable this option, go to Google Sheets, in the menu 🌟 <b>SePay</b> 🌟 => <b>Enable automatic transaction deletion</b></p>
      <img src="../assets/img/google-sheets/hd-11.png" class="img-fluid mb-5">
  
      <p>Once enabled, transactions will only be stored in Google Sheets for 60 minutes. After this time, they will be automatically deleted.</p>
      <p>To disable automatic transaction deletion, go to the menu 🌟 <b>SePay</b> 🌟 => <b>Disable automatic transaction deletion</b></p>
      <p>If you want to change the automatic deletion time, go back to <b>Apps Script</b>, at line 2 <code>const MIN = 60</code>, change 60 to the number of minutes you want, then save the project.</p>
      <img src="../assets/img/google-sheets/hd-12.png" class="img-fluid mb-5">
  
      <div class="my-2"><p>Read more: <a href="tich-hop-hostbill.html">HostBill Integration<i class="bi bi-chevron-right"></i></a></p></div>
  
  </div>

    <!-- End Content -->
  </main>
  <!-- ========== END MAIN CONTENT ========== -->

  <!-- ========== SECONDARY CONTENTS ========== -->
  <!--contact-box-html-docs-->
  <div id="contact-box-overlay"></div>
  <div id="contact-box">
    <div class="popup">
      <a href="https://m.me/117903214582465" target="_blank" class="item">
        <div class="logo">
          <img src="../assets/img/others/fb-messenger.png" width="50%" />
        </div>
        <div class="meta">
          <p class="title">Facebook Messenger</p>
          <small class="description">24/7 live chat support</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="tel:02873059589" class="item">
        <div class="icon" style="background-color: #22c55e; color: #fff;">
          <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20"
            style="color: currentcolor;">
            <path
              d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z"
              stroke="currentColor" stroke-width="1.5" stroke-linecap="round" fill="transparent"></path>
          </svg>
        </div>
        <div class="meta">
          <p class="title">Hotline</p>
          <small class="description">Support phone number</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
        <div class="logo">
          <img src="../assets/img/others/youtube-social.png" />
        </div>
        <div class="meta">
          <p class="title">YouTube</p>
          <small class="description">Follow SePay's latest videos</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
        <div class="logo">
          <img src="../assets/img/others/telegram-social.png" />
        </div>
        <div class="meta">
          <p class="title">Telegram</p>
          <small class="description">Get the latest updates from SePay</small>
        </div>
      </a>
    </div>
    <div class="container">
      <div class="dot-ping">
        <div class="ping"></div>
        <div class="dot"></div>
      </div>
      <div class="contact-icon">
        <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20"
          style="color: currentcolor;">
          <path fill-rule="evenodd" clip-rule="evenodd"
            d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z"
            fill="currentColor"></path>
        </svg>
      </div>
      <span style="font-weight: bold;">Contact Us</span>
    </div>
  </div>
  <!--/contact-box-html-docs-->

  <!-- Go To -->
  <a class="js-go-to go-to position-fixed" href="javascript:;" style="visibility: hidden;"
     data-hs-go-to-options='{
       "offsetTop": 700,
       "position": {
         "init": {
           "right": "2rem"
         },
         "show": {
           "bottom": "2rem"
         },
         "hide": {
           "bottom": "-2rem"
         }
       }
     }'>
    <i class="bi-chevron-up"></i>
  </a>
  <!-- ========== END SECONDARY CONTENTS ========== -->

  <!-- JS Global Compulsory  -->
  <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

  <!-- JS Implementing Plugins -->
  <script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
  <script src="../assets/vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js"></script>

  <script src="../assets/vendor/list.js/dist/list.min.js"></script>
  <script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>
  

  <!-- JS Front -->
  <script src="../assets/js/theme.min.js"></script>

  <!-- contact-box-js -->
  <script src="../assets/js/contact-box.js"></script>
  <!-- /contact-box-js -->

  <!-- JS Plugins Init. -->
  <script>
    (function() {
      // INITIALIZATION OF HEADER
      // =======================================================
      new HSHeader('#header').init()

        // INITIALIZATION OF NAV SCROLLER
        // =======================================================
        new HsNavScroller('.js-nav-scroller', {
            delay: 400,
            offset: 140
        })

      // INITIALIZATION OF LISTJS COMPONENT
      // =======================================================
      const docsSearch = HSCore.components.HSList.init('#docsSearch')


 

      // INITIALIZATION OF GO TO
      // =======================================================
      new HSGoTo('.js-go-to')
    })()
  </script>
</body>
</html>
