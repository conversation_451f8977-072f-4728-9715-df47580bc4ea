<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Required Meta Tags Always Come First -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- Title -->
    <title>WebHooks Integration Guide | SePay</title>


    <!-- Favicon -->
    <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />
    <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
    <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
  
    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">

    <!-- CSS Implementing Plugins -->
    <link rel="stylesheet" href="../assets/vendor/bootstrap-icons/font/bootstrap-icons.css">

    <!-- CSS Front Template -->
    <link rel="stylesheet" href="../assets/css/theme.min.css">
    <link rel="stylesheet" href="../assets/css/docs.min.css">

    <!--contact-box-css-->
    <link rel="stylesheet" href="../assets/css/contact-box.css">
    <!--/contact-box-css-->

    <style>
        .docs-navbar-sidebar-aside-body {
            padding-top: 3.5rem !important;
        }
    </style>
      <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-J8DLMQTKSQ');
  </script>
</head>

<body class="navbar-sidebar-aside-lg">
    <!-- ========== HEADER ========== -->
    <header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
        <div class="container-fluid">
            <nav class="navbar-nav-wrap">
                <div class="row flex-grow-1">
                    <!-- Default Logo -->
                    <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
                        <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                            <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png"
                                alt="Logo">
                        </a>

                    </div>
                    <!-- End Default Logo -->

                    <div class="col-md px-lg-0">
                        <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
                            <div class="d-none d-md-block">


                                <!-- List Item Template -->
                                <div class="d-none">
                                    <div id="searchTemplate" class="dropdown-item">
                                        <a class="d-block link" href="#">
                                            <span class="category d-block fw-normal text-muted mb-1"></span>
                                            <span class="component text-dark"></span>
                                        </a>
                                    </div>
                                </div>
                                <!-- End List Item Template -->
                            </div>

                            <!-- Navbar -->
                            <ul class="navbar-nav p-0">
                                <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html"
                                        target="_blank">
                                        Support <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN" target="_blank">
                                      Youtube <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                  </li>
                                  <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://www.facebook.com/messages/t/sepay.vn" target="_blank">
                                      Facebook <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                  </li>
                                  <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel" target="_blank">
                                      Telegram <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                  </li>

                            </ul>
                            <!-- End Navbar -->
                        </div>
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Row -->
            </nav>
        </div>
    </header>
    <!-- ========== END HEADER ========== -->

    <!-- ========== MAIN CONTENT ========== -->
    <main id="content" role="main">
        <!-- Navbar -->
        <nav class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
            data-hs-nav-scroller-options='{
            "type": "vertical",
            "target": ".navbar-nav .active",
            "offset": 80
           }'>
            <!-- Navbar Toggle -->
            <button type="button" class="navbar-toggler btn btn-white d-grid w-100" data-bs-toggle="collapse"
                data-bs-target="#navbarVerticalNavMenu" aria-label="Toggle navigation" aria-expanded="false"
                aria-controls="navbarVerticalNavMenu">
                <span class="d-flex justify-content-between align-items-center">
                    <span class="h3 mb-0">Nav menu</span>

                    <span class="navbar-toggler-default">
                        <i class="bi-list"></i>
                    </span>

                    <span class="navbar-toggler-toggled">
                        <i class="bi-x"></i>
                    </span>
                </span>
            </button>
            <!-- End Navbar Toggle -->

            <!-- Navbar Collapse -->
            <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
                <div class="navbar-brand-wrapper border-end" style="height: auto;">
                    <!-- Default Logo -->
                    <div class="d-flex align-items-center mb-3">
                        <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                            <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png"
                                alt="Logo">
                        </a>

                    </div>
                    <!-- End Default Logo -->


                </div>

                <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
                    <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
                        <li class="nav-item">
                            <span class="nav-subtitle">Introduction</span>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="what-is-sepay.html">What is SePay?</a>
                        </li>
                
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">General Guidelines</span>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="register-for-sepay.html">Register for SePay</a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="add-bank-account.html">Add Bank Account</a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="view-transactions.html">View Transactions</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="users-and-permissions.html">Users & Permissions</a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="sub-accounts.html">Sub-Accounts</a>
                        </li>
                
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">Company Configuration</span>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="service-packages.html">Service Packages</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="invoices-and-payments.html">Invoices & Payments</a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="general-settings.html">General Settings</a>
                        </li>
                
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">Balance Notifications</span>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="telegram-integration.html">Integrate Telegram <i class="bi bi-telegram ms-2 text-info"></i></a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="lark-messenger-integration.html">Integrate Lark Messenger <img src="../assets/img/lark/lark-icon.png" class="img-fluid" style="max-height:25px"></a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link d-flex align-items-center" href="mobile-app.html">Mobile App <i class="bi bi-phone-vibrate ms-2 fs-3 text-info"></i></a>
                        </li>
                
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">Web Integration</span>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="shopify-integration.html">Integrate Shopify <img class="ms-2" src="../assets/img/others/shopify-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="sapo-integration.html">Integrate Sapo <img class="ms-2" src="../assets/img/others/sapo-icon.png" style="width:18px; height: 18px;"></a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="haravan-integration.html">Integrate Haravan <img class="ms-2" src="../assets/img/others/haravan-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="woocommerce-integration.html">Integrate WooCommerce <img class="ms-2" src="../assets/img/others/woocommerce-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="google-sheets-integration.html">Integrate Google Sheets <img class="ms-2" src="../assets/img/others/google-sheets-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="hostbill-integration.html">Integrate HostBill <img class="ms-2" src="../assets/img/others/hostbill-icon.png" style="height: 22px;"></a>
                        </li>
                
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">Programming & Integration</span>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="webhooks-integration.html">Integrate WebHooks</a>
                        </li>
                
                        <li class="nav-item">
                            <a class="nav-link" href="programming-webhooks.html">Programming WebHooks</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="simulate-transactions.html">Simulate Transactions</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="generate-embed-qr-code.html">Generate & Embed QR Code</a>
                        </li>
                
                        <li class="nav-item my-2 my-lg-5"></li>
                
                        <li class="nav-item">
                            <span class="nav-subtitle">SePay API</span>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="api-introduction.html">API Introduction</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="create-api-token.html">Create API Token</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="transaction-api.html">Transaction API</a>
                        </li>
<li class="nav-item">
                  <a class="nav-link" href="bank-account-api.html">Bank Account API</a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="api-va-order-bidv.html">API Va Order</a>
            </li>
                    </ul>
                </div>
                
                
            </div>
            <!-- End Navbar Collapse -->
        </nav>
        <!-- End Navbar -->

        <!-- Content -->
        <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">

            <div class="docs-page-header">
                <div class="row align-items-center">
                    <div class="col-sm">
                        <h1 class="docs-page-header-title">WebHooks Integration Guide</h1>
                        <p class="docs-page-header-text">Integrating WebHooks enables SePay to send transaction information to your sales application. Whenever a transaction occurs, SePay will trigger WebHooks, allowing your application to recognize that the customer has made a payment and update the order status accordingly.</p>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-soft-dark" role="alert">
                <div class="d-sm-flex">
                    <div class="flex-shrink-0">
                        <img class="avatar avatar-xl mb-2 mb-sm-0" src="../assets/svg/illustrations/oc-megaphone.svg" alt="Image Description">
                    </div>
            
                    <div class="flex-grow-1 ms-sm-4">
                        If you need a <b>sandbox environment</b>, register an account at <a href="https://my.dev.sepay.vn/register">my.dev.sepay.vn</a>. Here, you can create simulated transactions and webhooks for development purposes. After registering, please <a href="https://sepay.vn/lien-he.html">contact</a> SePay to activate your account.
                    </div>
                </div>
            </div>
            
            <p><b>Step 1:</b> Access the <a href="https://my.sepay.vn/webhooks">WebHooks</a> menu.</p>
            <p><b>Step 2:</b> Click on the <a class="btn btn-primary btn-xs">+ Add WebHooks</a> button at the top right.</p>
            <div class="text-center"><img style="max-height: 1000px;" src="../assets/img/others/webhook-add.png?v=3" class="img-fluid my-2"></div>
            
            <p><b>Step 3:</b> Fill in all required information, including:</p>
            <ul class="lh-lg">
                <li><b>Name:</b> Assign any name you like.</li>
                <li><span class="badge bg-primary rounded-pill">1</span> <b>Select Event</b>: You can choose events for <b>Triggering WebHooks when</b> <code>Money In</code>, <code>Money Out</code>, or <code>Both</code>.</li>
                <li><span class="badge bg-primary rounded-pill">2</span> <b>Select Conditions</b>: Includes:</b>
                    <ul>
                        <li><b>When the bank account is</b>: Choose the account for which transactions will trigger webhooks. If you want to specify virtual accounts (VA) to receive notifications, check the <b>Filter by Virtual Account</b> box and select the accounts to monitor.</li>
                        <li><b>Ignore if the transaction description lacks a Payment Code?</b>: If selected, SePay will NOT trigger webhooks if the payment code is not identified in the transaction description.
                            <br>TIP: Configure the payment code detection under <b>Company</b> -> <b>General Settings</b> -> <b>Payment Code Structure</b>.</li>
                    </ul>
                </li>
                <li><span class="badge bg-primary rounded-pill">3</span> <b>WebHooks Properties</b>: Includes:</b>
                    <ul>
                        <li><b>Call to URL</b>: The URL where you want the WebHooks to be sent. If you want to program a website to receive webhooks, see the guide <a href="lap-trinh-webhooks.html">here</a>.</li>
                        <li><b>Is this a Payment Verification WebHook?</b>: Select Yes if this webhook is used for payment verification for your website/application.</li>
                        <li><b>Retry WebHooks when?</b>: Currently, SePay supports some conditions for automatic WebHooks retries:
                            <ul>
                                <li>HTTP Status Code is not in the range of 200 to 299.</li>
                            </ul>
                        </li>
                    </ul>
                </li>
                <li>
                    <span class="badge bg-primary rounded-pill">4</span> <b>Configure WebHooks Authentication</b>: Includes:</b>
                    <ul>
                        <li><b>Authentication Type</b>: SePay currently supports authentication with <b>OAuth 2.0</b>, <b>API Key</b>, or <b>No Authentication</b>.
                            <ul>
                                <li>If you choose <b>OAuth 2.0</b>, you need to provide details such as OAuth 2.0 Access Token URL, OAuth 2.0 Client ID, and OAuth 2.0 Client Secret.</li>
                                <li>If you choose <b>No Authentication</b> or <b>API Key</b>, you need to select the Request Content type as <b>application/json</b>, <b>multipart/form-data</b>, or <b>application/x-www-form-urlencoded</b> depending on your application's requirements.</li>
                            </ul>
                        </li>
                    </ul>
                </li>
            </ul>
            

            <p><b>Step 4:</b> Click <b>Add</b> to complete the integration.</p>

<h3 id="testing" class="hs-docs-heading">
    Data Sent via Webhooks <a class="anchorjs-link" href="#data" aria-label="Anchor"
        data-anchorjs-icon="#"></a>
</h3>

<p>SePay will send a request using the <b>POST</b> method with the following content:</p>
<pre>
{
    "id": 92704,                              // Transaction ID on SePay
    "gateway": "Vietcombank",                 // Bank brand name
    "transactionDate": "2023-03-25 14:02:37", // Transaction time on the bank's side
    "accountNumber": "**********",            // Bank account number
    "code": null,                             // Payment code (SePay auto-detects based on the configuration in Company -> General Settings)
    "content": "transfer money to buy iPhone",// Transfer content
    "transferType": "in",                     // Transaction type. "in" means money in, "out" means money out
    "transferAmount": 2277000,                // Transaction amount
    "accumulated": ********,                  // Account balance (accumulated)
    "subAccount": null,                       // Sub-account (virtual account)
    "referenceCode": "MBVCB.**********",      // Reference code from the SMS message
    "description": ""                         // Full content of the SMS message
}
</pre>

<div class="alert alert-soft-dark" role="alert">
    With authentication as <b>API Key</b>, SePay will send with the header <code>"Authorization": "Apikey YOUR_API_KEY"</code>
</div>

<h3 id="testing" class="hs-docs-heading">
    Test Activity <a class="anchorjs-link" href="#testing" aria-label="Anchor"
        data-anchorjs-icon="#"></a>
</h3>
<p>If using a Demo account, go to the menu <a href="https://my.sepay.vn/transactions">Transactions</a> -> Simulate transaction to add a new transaction. See information about Simulated transactions <a href="gia-lap-giao-dich.html">here</a></p>
<p>If not using a demo account, you can try transferring a small amount to create a test transaction.</p>
<p>Then go to the menu <b>Logs</b> -> <a href="https://my.sepay.vn/webhookslog">Webhooks logs</a> to see the list of webhooks that have been triggered.</p>
<p>Additionally, you can view the webhook messages sent for each transaction in the menu <a
        href="https://my.sepay.vn/transactions"><i class="bi bi-arrow-left-right"></i> Transactions</a> -> Automatic column -> click on <b>Pay</b></p>

<h3 id="recognition" class="hs-docs-heading">
    Successfully Recognize Webhooks<a class="anchorjs-link" href="#recognition"
        aria-label="Anchor" data-anchorjs-icon="#"></a>
</h3>
<p>When receiving webhooks from SePay, your website needs to respond according to the following conventions for SePay to know the result is successful:</p>
<ul class="lh-lg">
    <ul>
        <li>With OAuth 2.0 authentication</li>
        <ul>
            <li>The response content must be a JSON with <b>success: true</b>: <code>{"success": true, ....}</code></li>
            <li>The HTTP Status Code must be <span class="text-success">201</span></li>
        </ul>
        <li>With no authentication</li>
        <ul>
            <li>The response content must be a JSON with <b>success: true</b>: <code>{"success": true, ....}</code></li>
            <li>The HTTP Status Code must be <span class="text-success">201</span> or <span class="text-success">200</span></li>
        </ul>
    </ul>
</ul>

<p>If the response does not meet the above conditions, SePay will consider the webhook as failed.</p>
<div class="alert alert-soft-dark" role="alert">
    SePay will send notifications (webhooks) from the IP address: <strong>*************</strong>
</div>

<h3 id="retry" class="hs-docs-heading">
    Automatic Webhook Retry <a class="anchorjs-link" href="#retry" aria-label="Anchor" data-anchorjs-icon="#"></a>
</h3>

<p>SePay will retry the webhook if the <b>network connection</b> to the webhook URL fails. Additionally, you can customize the conditions that SePay supports to trigger a retry. The retry intervals are based on the Fibonacci sequence, increasing progressively in minutes <a href="https://en.wikipedia.org/wiki/Fibonacci_sequence">here</a>.</p>
<ul class="lh-lg">
    <li>The maximum number of retries is 7 times</li>
    <li>The maximum retry duration is 5 hours from the first failure</li>
    <li>SePay's network connect timeout is 5 seconds</li>
    <li>The maximum response wait time for SePay is 8 seconds</li>
</ul>
<div class="alert alert-soft-dark" role="alert">
    Note: SePay will NOT retry the webhook if the status is failure but the network connection is successful, unless the webhook is set up with retry conditions and will only retry when one of the configured conditions is met.
</div>

<h3 id="retry" class="hs-docs-heading">
    Duplicate Transaction Check Requirement <a class="anchorjs-link" href="#duplicate-checking-requirement" aria-label="Anchor" data-anchorjs-icon="#"></a>
</h3>

<p>To prevent duplicate transactions when network issues occur with the user's webhook connection during retry attempts, SePay recommends that users handle duplicate transaction checks when receiving transaction updates from SePay via their webhook.</p>

<p>Users should check the uniqueness of the <code>id</code> field, or combine other fields like <code>referenceCode</code>, <code>transferType</code>, <code>transferAmount</code> from the data SePay sends via the webhook to ensure the uniqueness of the transaction.</p>

<h3 id="retry" class="hs-docs-heading">
    Manual Webhook Retry <a class="anchorjs-link" href="#retry-manual" aria-label="Anchor" data-anchorjs-icon="#"></a>
</h3>

<p>You can manually retry a webhook by going to the details of a transaction -> Select View in the Triggered Webhooks -> Retry. Or go to the Webhooks Logs section, select Retry.</p>
<div class="text-center"><img style="max-height: 500px;" src="../assets/img/others/webhook-log-show.png" class="img-fluid"></div>


<div class="my-2"><p>Read more: <a href="programming-webhooks.html">Simple WebHooks Programming <i class="bi bi-chevron-right"></i></a></p></div>

        <!-- End Content -->
    </main>
    <!-- ========== END MAIN CONTENT ========== -->

    <!-- ========== SECONDARY CONTENTS ========== -->
    <!--contact-box-html-docs-->
  <div id="contact-box-overlay"></div>
  <div id="contact-box">
    <div class="popup">
      <a href="https://m.me/117903214582465" target="_blank" class="item">
        <div class="logo">
          <img src="../assets/img/others/fb-messenger.png" width="50%" />
        </div>
        <div class="meta">
          <p class="title">Facebook Messenger</p>
          <small class="description">24/7 live chat support</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="tel:02873059589" class="item">
        <div class="icon" style="background-color: #22c55e; color: #fff;">
          <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20"
            style="color: currentcolor;">
            <path
              d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z"
              stroke="currentColor" stroke-width="1.5" stroke-linecap="round" fill="transparent"></path>
          </svg>
        </div>
        <div class="meta">
          <p class="title">Hotline</p>
          <small class="description">Support phone number</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
        <div class="logo">
          <img src="../assets/img/others/youtube-social.png" />
        </div>
        <div class="meta">
          <p class="title">YouTube</p>
          <small class="description">Follow SePay's latest videos</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
        <div class="logo">
          <img src="../assets/img/others/telegram-social.png" />
        </div>
        <div class="meta">
          <p class="title">Telegram</p>
          <small class="description">Get the latest updates from SePay</small>
        </div>
      </a>
    </div>
    <div class="container">
      <div class="dot-ping">
        <div class="ping"></div>
        <div class="dot"></div>
      </div>
      <div class="contact-icon">
        <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20"
          style="color: currentcolor;">
          <path fill-rule="evenodd" clip-rule="evenodd"
            d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z"
            fill="currentColor"></path>
        </svg>
      </div>
      <span style="font-weight: bold;">Contact Us</span>
    </div>
  </div>
  <!--/contact-box-html-docs-->

    <!-- Go To -->
    <a class="js-go-to go-to position-fixed" href="javascript:;" style="visibility: hidden;" data-hs-go-to-options='{
       "offsetTop": 700,
       "position": {
         "init": {
           "right": "2rem"
         },
         "show": {
           "bottom": "2rem"
         },
         "hide": {
           "bottom": "-2rem"
         }
       }
     }'>
        <i class="bi-chevron-up"></i>
    </a>
    <!-- ========== END SECONDARY CONTENTS ========== -->

    <!-- JS Global Compulsory  -->
    <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

    <!-- JS Implementing Plugins -->
    <script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
    <script src="../assets/vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js"></script>

    <script src="../assets/vendor/list.js/dist/list.min.js"></script>
    <script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>
    

    <!-- JS Front -->
    <script src="../assets/js/theme.min.js"></script>

    <!-- contact-box-js -->
    <script src="../assets/js/contact-box.js"></script>
    <!-- /contact-box-js -->

    <!-- JS Plugins Init. -->
    <script>
        (function () {
            // INITIALIZATION OF HEADER
            // =======================================================
            new HSHeader('#header').init()

            // INITIALIZATION OF NAV SCROLLER
            // =======================================================
            new HsNavScroller('.js-nav-scroller', {
                delay: 400,
                offset: 140
            })

            // INITIALIZATION OF LISTJS COMPONENT
            // =======================================================
            const docsSearch = HSCore.components.HSList.init('#docsSearch')




            // INITIALIZATION OF GO TO
            // =======================================================
            new HSGoTo('.js-go-to')
        })()
    </script>
</body>

</html>