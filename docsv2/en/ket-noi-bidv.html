<!DOCTYPE html>
<html lang="en">

<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

  <!-- Title -->
  <title>Guide to Connecting BIDV Personal Bank Account | SePay</title>

  <!-- Favicon -->
  <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">

  <!-- CSS Implementing Plugins -->
  <link rel="stylesheet" href="../assets/vendor/bootstrap-icons/font/bootstrap-icons.css">
  <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
  <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />

  <!-- CSS Front Template -->
  <link rel="stylesheet" href="../assets/css/theme.min.css">
  <link rel="stylesheet" href="../assets/css/docs.min.css">

  <!--contact-box-css-->
  <link rel="stylesheet" href="../assets/css/contact-box.css">
  <!--/contact-box-css-->

  <style>
    .docs-navbar-sidebar-aside-body {
      padding-top: 3.5rem !important;
    }
  </style>
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());

    gtag('config', 'G-J8DLMQTKSQ');
  </script>
</head>

<body class="navbar-sidebar-aside-lg">
  <!-- ========== HEADER ========== -->
  <header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
    <div class="container-fluid">
      <nav class="navbar-nav-wrap">
        <div class="row flex-grow-1">
          <!-- Default Logo -->
          <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
              <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo">
            </a>
          </div>
          <!-- End Default Logo -->

          <div class="col-md px-lg-0">
            <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
              <div class="d-none d-md-block">
                <!-- List Item Template -->
                <div class="d-none">
                  <div id="searchTemplate" class="dropdown-item">
                    <a class="d-block link" href="#">
                      <span class="category d-block fw-normal text-muted mb-1"></span>
                      <span class="component text-dark"></span>
                    </a>
                  </div>
                </div>
                <!-- End List Item Template -->
              </div>

              <!-- Navbar -->
              <ul class="navbar-nav p-0">
                <li class="nav-item">
                  <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html" target="_blank">
                    Support <i class="bi-box-arrow-up-right ms-1"></i>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN" target="_blank">
                    Youtube <i class="bi-box-arrow-up-right ms-1"></i>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="btn btn-ghost-secondary btn-sm" href="https://www.facebook.com/messages/t/sepay.vn"
                    target="_blank">
                    Facebook <i class="bi-box-arrow-up-right ms-1"></i>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel" target="_blank">
                    Telegram <i class="bi-box-arrow-up-right ms-1"></i>
                  </a>
                </li>
              </ul>
              <!-- End Navbar -->
            </div>
          </div>
          <!-- End Col -->
        </div>
        <!-- End Row -->
      </nav>
    </div>
  </header>

  <!-- ========== END HEADER ========== -->

  <!-- ========== MAIN CONTENT ========== -->
  <main id="content" role="main">
    <!-- Navbar -->
    <nav class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
    data-hs-nav-scroller-options='{
          "type": "vertical",
          "target": ".navbar-nav .active",
          "offset": 80
         }'>
    <!-- Navbar Toggle -->
    <button type="button" class="navbar-toggler btn btn-white d-grid w-100" data-bs-toggle="collapse"
      data-bs-target="#navbarVerticalNavMenu" aria-label="Toggle navigation" aria-expanded="false"
      aria-controls="navbarVerticalNavMenu">
      <span class="d-flex justify-content-between align-items-center">
        <span class="h3 mb-0">Nav menu</span>

        <span class="navbar-toggler-default">
          <i class="bi-list"></i>
        </span>

        <span class="navbar-toggler-toggled">
          <i class="bi-x"></i>
        </span>
      </span>
    </button>
    <!-- End Navbar Toggle -->

    <!-- Navbar Collapse -->
    <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
      <div class="navbar-brand-wrapper border-end" style="height: auto;">
        <!-- Default Logo -->
        <div class="d-flex align-items-center mb-3">
          <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
            <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo">
          </a>
        </div>
        <!-- End Default Logo -->
      </div>

      <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
        <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
            <li class="nav-item">
                <span class="nav-subtitle">Introduction</span>
            </li>
    
            <li class="nav-item">
                <a class="nav-link" href="what-is-sepay.html">What is SePay?</a>
            </li>
    
            <li class="nav-item my-2 my-lg-5"></li>
    
            <li class="nav-item">
                <span class="nav-subtitle">General Instructions</span>
            </li>
    
            <li class="nav-item">
                <a class="nav-link" href="register-sepay.html">Register for SePay</a>
            </li>
    
            <li class="nav-item">
                <a class="nav-link active" href="add-bank-account.html">Add Bank Account</a>
            </li>
    
            <li class="nav-item">
                <a class="nav-link" href="view-transactions.html">View Transactions</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="users-and-permissions.html">Users & Permissions</a>
            </li>
    
            <li class="nav-item">
                <a class="nav-link" href="sub-accounts.html">Sub Accounts</a>
            </li>
    
            <li class="nav-item my-2 my-lg-5"></li>
    
            <li class="nav-item">
                <span class="nav-subtitle">Company Configuration</span>
            </li>
    
            <li class="nav-item">
                <a class="nav-link" href="service-packages.html">Service Packages</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="invoices-and-payments.html">Invoices & Payments</a>
            </li>
    
            <li class="nav-item">
                <a class="nav-link" href="general-settings.html">General Settings</a>
            </li>
    
            <li class="nav-item my-2 my-lg-5"></li>
    
            <li class="nav-item">
                <span class="nav-subtitle">Balance Change Sharing</span>
            </li>
    
            <li class="nav-item">
                <a class="nav-link" href="telegram-integration.html">Telegram Integration <i class="bi bi-telegram ms-2 text-info"></i></a>
            </li>
    
            <li class="nav-item">
                <a class="nav-link" href="lark-messenger-integration.html">Lark Messenger Integration <img src="../assets/img/lark/lark-icon.png" class="img-fluid" style="max-height:25px"></a>
            </li>
    
            <li class="nav-item">
                <a class="nav-link d-flex align-items-center" href="mobile-app.html">Mobile App <i class="bi bi-phone-vibrate ms-2 fs-3 text-info"></i> </a>
            </li>
    
            <li class="nav-item my-2 my-lg-5"></li>
    
            <li class="nav-item">
                <span class="nav-subtitle">Web Integration</span>
            </li>
    
            <li class="nav-item">
                <a class="nav-link" href="shopify-integration.html">Shopify Integration <img class="ms-2" src="../assets/img/others/shopify-icon.png" style="width:22px; height: 22px;"></a>
            </li>
    
            <li class="nav-item">
                <a class="nav-link" href="sapo-integration.html">Sapo Integration <img class="ms-2" src="../assets/img/others/sapo-icon.png" style="width:18px; height: 18px;"></a>
            </li>
    
            <li class="nav-item">
                <a class="nav-link" href="haravan-integration.html">Haravan Integration <img class="ms-2" src="../assets/img/others/haravan-icon.png" style="width:22px; height: 22px;"></a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="woocommerce-integration.html">WooCommerce Integration <img class="ms-2" src="../assets/img/others/woocommerce-icon.png" style="width:22px; height: 22px;"></a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="google-sheets-integration.html">Google Sheets Integration <img class="ms-2" src="../assets/img/others/google-sheets-icon.png" style="width:22px; height: 22px;"></a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="hostbill-integration.html">HostBill Integration <img class="ms-2" src="../assets/img/others/hostbill-icon.png" style="height: 22px;"></a>
            </li>
    
            <li class="nav-item my-2 my-lg-5"></li>
    
            <li class="nav-item">
                <span class="nav-subtitle">Programming & Integration</span>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="webhooks-integration.html">WebHooks Integration</a>
            </li>
    
            <li class="nav-item ">
                <a class="nav-link" href="webhooks-programming.html">WebHooks Programming</a>
            </li>
            <li class="nav-item ">
                <a class="nav-link" href="simulate-transactions.html">Simulate Transactions</a>
            </li>
            <li class="nav-item ">
                <a class="nav-link" href="create-embed-qr-code.html">Create & Embed QR Code</a>
            </li>
    
            <li class="nav-item my-2 my-lg-5"></li>
    
            <li class="nav-item">
                <span class="nav-subtitle">SePay API</span>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="api-introduction.html">API Introduction</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="create-api-token.html">Create API Token</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="transaction-api.html">Transaction API</a>
            </li>
          <li class="nav-item">
                  <a class="nav-link" href="bank-account-api.html">Bank Account API</a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="api-va-order-bidv.html">API Va Order</a>
            </li>
        </ul>
    </div>
    
    </div>
    <!-- End Navbar Collapse -->
  </nav>

    <!-- End Navbar -->

    <!-- Content -->
    <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">

      <p>SePay officially collaborates with BIDV Bank to provide an API for personal accounts. In just 5 minutes, you can open a BIDV account via the BIDV SmartBanking app, and in 3 minutes, link it through SePay to start receiving and managing transactions instantly.</p>

      <div class="ratio ratio-16x9" style="max-width: 800px;">
        <iframe src="https://www.youtube.com/embed/JUwzPM29EoQ?si=Bhfg4A5AbK70n7LL" title="Guide to Connecting Personal BIDV Account to SePay" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
      </div>
    
      <p class="mt-3">Here are the steps to connect the BIDV API:</p>
      <p><b>Step 1:</b> Go to <a href="https://my.sepay.vn" target="_blank">my.sepay.vn</a> -> Select the <b>Bank</b> menu -> Choose <b>Connect Account</b></p>
      <p class="px-3 px-sm-1"><img src="../assets/img/bidv-connect/bidv-connect-1.png" class="img-fluid"
          style="max-height: 500px; margin: -1.5rem -2rem;"></p>
      <p><b>Step 2:</b> In the bank API connection interface:</p>
      <p>Select the Personal option, then choose to connect with BIDV bank.</p>
      <p class="px-3 px-sm-1"><img src="../assets/img/bidv-connect/bidv-connect-2.png" class="img-fluid"
          style="max-height: 500px; margin: -1.5rem -2rem;"></p>
    
      <p><b>Step 3:</b> Enter the required information including:
      <ul>
        <li>BIDV personal bank account number</li>
        <li>Account holder's name</li>
        <li>ID card/Passport used to register the bank account</li>
        <li>Phone number used to register the bank account</li>
      </ul>
      <p>Click the <b>Continue</b> button.</p>
      <p class="px-3 px-sm-1"><img src="../assets/img/bidv-connect/bidv-connect-3.png" class="img-fluid"
          style="max-height: 500px; margin: -1.5rem -2rem;"></p>
    
      <div class="alert alert-soft-dark" role="alert">
        <div class="d-flex align-items-baseline">
          <div class="flex-shrink-0">
            <i class="bi-info-circle me-1"></i>
          </div>
          <div class="flex-grow-1 ms-2">
            If you don’t have an account, you can check out the <a href="https://sepay.vn/blog/huong-dan-mo-tai-khoan-bidv-ca-nhan-online-vo-cung-don-gian/" target="_blank">guide to opening an online BIDV personal account via the BIDV SmartBanking app</a>.
          </div>
        </div>
      </div>
    
      <p><b>Step 4:</b> Enter the virtual account number to be created first, then click the <b>Get OTP</b> button. BIDV will send an OTP verification code to your phone number.</p>
      
      <div class="alert alert-soft-dark" role="alert">
        <div class="d-flex align-items-baseline">
          <div class="flex-shrink-0">
            <i class="bi-info-circle me-1"></i>
          </div>
          <div class="flex-grow-1 ms-2">
            The virtual account (VA) structure for BIDV personal accounts will start with the prefix <code>96247</code>, followed by alphanumeric characters (A-Z, 0-9), with a maximum length of 19 characters.
          </div>
        </div>
      </div>
    
      <p class="px-3 px-sm-1"><img src="../assets/img/bidv-connect/bidv-connect-4.1.png" class="img-fluid"
        style="max-height: 300px;"></p>

<p>Please enter the OTP code and click the <b>Link API and create VA</b> button.</p>

<p class="px-3 px-sm-1"><img src="../assets/img/bidv-connect/bidv-connect-4.png" class="img-fluid"
        style="max-height: 500px; margin: -1.5rem -2rem;"></p>

<div class="alert alert-soft-dark" role="alert">
    <div class="d-flex align-items-baseline">
        <div class="flex-shrink-0">
            <i class="bi-info-circle me-1"></i>
        </div>
        <div class="flex-grow-1 ms-2">
            In case you do not receive the OTP or if the OTP expires, please click the <b>Get OTP</b> button again.
        </div>
    </div>
</div>

<p><b>Step 6:</b> After successfully linking the API, you can perform a test transaction to transfer money to the virtual BIDV account that you just created.</p>

<p class="px-3 px-sm-1"><img src="../assets/img/bidv-connect/bidv-connect-5.png" class="img-fluid"
        style="max-height: 500px; margin: -1.5rem -2rem;"></p>

<div class="alert alert-soft-dark" role="alert" id="ho-tro-nhan-sms-tien-ra">
    <div class="d-sm-flex">
        <div class="flex-shrink-0">
            <img class="avatar avatar-xl mb-2 mb-sm-0" src="../assets/svg/illustrations/oc-megaphone.svg"
                alt="Image Description">
        </div>

        <div class="flex-grow-1 ms-sm-4">
            <h3>Important:</h3>
            <p>For SePay to receive deposit transaction notifications (money in) from BIDV, transactions must be transferred to the virtual account (VA) you just created or any virtual accounts created in the future. Please <span class="text-danger">DO NOT</span> transfer money to the main account number.</p>
        </div>
    </div>
</div>

<p>If you receive a new transaction notification as shown below, your BIDV account API linking is complete.</p>
<p class="px-3 px-sm-1"><img src="../assets/img/bidv-connect/bidv-connect-6.png" class="img-fluid"
        style="max-height: 400px;"></p>

<h3 class="hs-docs-heading" id="quan-ly-va">Manage Virtual Accounts (VA) <a class="anchorjs-link" href="#quan-ly-va" aria-label="Anchor" data-anchorjs-icon="#"></a></h3>

<p>BIDV personal banking supports managing <a href="https://sepay.vn/blog/tai-khoan-ngan-hang-ao-la-gi-phan-loai-va-ung-dung/" target="_blank">official VA accounts</a> of the bank at SePay's interface, where you can perform actions like creating/deleting or disabling/enabling VAs.</p>

<h4 class="hs-docs-heading" id="tao-va"><span class="badge bg-primary rounded-pill me-2">1</span>Create VA<a class="anchorjs-link" href="#tao-va" aria-label="Anchor" data-anchorjs-icon="#"></a></h4>

<p><b>Step 1:</b> Select the BIDV bank account that has linked the API with SePay and needs a new VA added.</p>

<p class="px-3 px-sm-1"><img src="../assets/img/bidv-connect/bidv-connect-7.png" class="img-fluid"
        style="max-height: 500px; margin: -1.5rem -2rem;"></p>

<p><b>Step 2:</b> On the account details page, in the virtual account (VA) management table, click the <b>Add VA</b> button.</p>

<p class="px-3 px-sm-1"><img src="../assets/img/bidv-connect/bidv-connect-8.png?t=**********" class="img-fluid"
        style="max-height: 455px; margin: -1rem -1.25rem;"></p>

  
        <p><b>Step 3:</b> BIDV will send an OTP code to your phone. Please enter the OTP code and click the <b>Create VA</b> button.</p>

        <p class="px-3 px-sm-1"><img src="../assets/img/bidv-connect/bidv-connect-9.png" class="img-fluid"
            style="max-height: 400px;"></p>
        
        <div class="alert alert-soft-dark" role="alert">
          <div class="d-flex align-items-baseline">
            <div class="flex-shrink-0">
              <i class="bi-info-circle me-1"></i>
            </div>
            <div class="flex-grow-1 ms-2">
              If you do not receive the OTP code or the code expires, please click the <b>Get OTP</b> button again.
            </div>
          </div>
        </div>
        
        <p><b>Step 4:</b> After successfully creating the VA, you can perform a test transaction by transferring money to the newly created VA number to verify.</p>
        
        <h4 class="hs-docs-heading" id="edit-va"><span class="badge bg-primary rounded-pill me-2">2</span>Activate/Deactivate VA<a class="anchorjs-link" href="#edit-va" aria-label="Anchor" data-anchorjs-icon="#"></a></h4>
        
        <p>By default, when creating a VA with a BIDV personal bank account, it will be activated immediately. Occasionally, if you do not wish to receive credit notifications from a particular VA, you can click the <b>Deactivate</b> button to temporarily suspend the VA's activity. If you want to reactivate it, you can click the <b>Activate</b> button to undo the action.</p>
        
        <h4 class="hs-docs-heading" id="delete-va"><span class="badge bg-primary rounded-pill me-2">3</span>Delete VA<a class="anchorjs-link" href="#delete-va" aria-label="Anchor" data-anchorjs-icon="#"></a></h4>
        
        <p>SePay supports the VA deletion feature for BIDV personal accounts. When deleting any VA, the transaction history received from that VA will <b>NOT</b> be deleted.</p>
        
        <h3 class="hs-docs-heading" id="switch-connection-method">Switch Connection Method <a class="anchorjs-link" href="#switch-connection-method" aria-label="Anchor" data-anchorjs-icon="#"></a></h3>
        <p>Currently, SePay supports flexible switching of the connection method between API Banking and SMS Banking for the following cases:</p>
        
        <ul>
          <li>If you are using a BIDV account with SMS Banking connection and want to switch to API Banking to improve service quality.</li>
          <li>If the BIDV banking system is interrupted or under maintenance, and you want to switch to SMS Banking to ensure you still receive account balance notifications.</li>
        </ul>
        
        <h4 class="hs-docs-heading" id="sms-banking-to-api-banking" class="d-flex align-items-center mb-4 d-flex"><span class="badge bg-primary rounded-pill me-2">1</span>Switch Connection Method from SMS Banking -> API Banking<a class="anchorjs-link" href="#sms-banking-to-api-banking" aria-label="Anchor" data-anchorjs-icon="#"></a></h4>
        
        <p><b>Step 1:</b> Select the BIDV bank account you want to switch.</p>
        <p class="px-3 px-sm-1"><img src="../assets/img/bidv-connect/bidv-connect-10.png" class="img-fluid"
            style="max-height: 500px; margin: -1.5rem -2rem;"></p>
        
        <p><b>Step 2:</b> On the BIDV bank account management page, click the <b>Switch to API Banking Connection</b> button.</p>
        <p class="px-3 px-sm-1"><img src="../assets/img/bidv-connect/bidv-connect-11.png?t=**********.png" class="img-fluid"
            style="max-height: 470px; margin: -1.5rem -2rem;"></p>
        
        <p><b>Step 3:</b> Select the <b>Personal</b> account group.</p>
        <p class="px-3 px-sm-1"><img src="../assets/img/bidv-connect/bidv-connect-12.png" class="img-fluid mb-3"
            style="max-width: 500px;"></p>
        
        <p><b>Step 4:</b> Fill in the missing information to complete the API connection setup.</p>
        <p class="px-3 px-sm-1"><img src="../assets/img/bidv-connect/bidv-connect-13.png" class="img-fluid"
            style="max-height: 500px; margin: -1.5rem -2rem;"></p>
        
        <p>The remaining steps are similar to adding a BIDV bank account with the API connection method.</p>
        <p>After successfully switching to API Banking, please try performing a credit transaction to verify.</p>
        
        <h4 class="hs-docs-heading" id="api-banking-to-sms-banking" class="d-flex align-items-center mb-4 d-flex"><span class="badge bg-primary rounded-pill me-2">2</span>Switch Connection Method from API Banking -> SMS Banking<a class="anchorjs-link" href="#api-banking-to-sms-banking" aria-label="Anchor" data-anchorjs-icon="#"></a></h4>
        
        <div class="alert alert-soft-dark" role="alert">
          <div class="d-flex align-items-baseline">
            <div class="flex-shrink-0">
              <i class="bi-info-circle me-1"></i>
            </div>
            <div class="flex-grow-1 ms-2">
              The condition to switch to SMS Banking connection method is that the service package you are using must support SMS Banking. For more information, visit <a href="https://sepay.vn/bang-gia.html" target="_blank">Pricing Table</a>.
            </div>
          </div>
        </div>
  
        <p class="mt-2"><b>Step 1:</b> Select the BIDV bank account you want to switch to</p>
        <p class="px-3 px-sm-1"><img src="../assets/img/bidv-connect/bidv-connect-7.png" class="img-fluid"
            style="max-height: 500px; margin: -1.5rem -2rem;"></p>
  
        <p><b>Step 2:</b> On the BIDV bank account management page, click the <b>Switch to SMS Banking connection method</b> button</p>
        <p class="px-3 px-sm-1"><img src="../assets/img/bidv-connect/bidv-connect-14.png?t=**********" class="img-fluid"
            style="max-height: 470px; margin: -1.5rem -2rem;"></p>
  
        <p><b>Step 3:</b> In the confirmation dialog, please select the SIM you want to connect to the account, then click the <b>Switch</b> button</p>
        <p><img src="../assets/img/bidv-connect/bidv-connect-15.png" class="img-fluid" style="max-width: 515px;"></p>
  
        <div class="alert alert-soft-dark" role="alert" id="ho-tro-nhan-sms-tien-ra">
          <div class="d-sm-flex">
            <div class="flex-shrink-0">
              <img class="avatar avatar-xl mb-2 mb-sm-0" src="../assets/svg/illustrations/oc-megaphone.svg"
                alt="Image Description">
            </div>
  
            <div class="flex-grow-1 ms-sm-4">
              <h3>Note:</h3>
              <p>If the SIM has not been registered to receive Balance Change Sharing via SMS at BIDV previously, please register the selected phone number from <b>Step 3</b> with BIDV to receive SMS Balance Change Sharing for the account.</p>
              <p>If the phone number has already been registered to receive SMS Balance Change Sharing, please make any credit transaction to check.</p>
            </div>
          </div>
        </div>
  
        <p class="mt-4">Additionally, on my.sepay.vn, you can:</p>
        <ul>
          <li><a href="https://my.sepay.vn/transactions">View transaction list</a></li>
          <li><a href="https://my.sepay.vn/bankaccount">View connected bank accounts or create additional VA.</a></li>
        </ul>
        <p>We wish you success!</p>
  
        <div class="my-2 mt-5">
          <p>Continue reading: <a href="view-transactions.html">View transaction<i class="bi bi-chevron-right"></i></a></p>
        </div>
  
    </div>
    <!-- End Content -->
  </main>
  <!-- ========== END MAIN CONTENT ========== -->

  <!-- ========== SECONDARY CONTENTS ========== -->
  <!--contact-box-html-docs-->
  <div id="contact-box-overlay"></div>
  <div id="contact-box">
    <div class="popup">
      <a href="https://m.me/***************" target="_blank" class="item">
        <div class="logo">
          <img src="../assets/img/others/fb-messenger.png" width="50%" />
        </div>
        <div class="meta">
          <p class="title">Facebook Messenger</p>
          <small class="description">24/7 live chat support</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="tel:***********" class="item">
        <div class="icon" style="background-color: #22c55e; color: #fff;">
          <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20"
            style="color: currentcolor;">
            <path
              d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z"
              stroke="currentColor" stroke-width="1.5" stroke-linecap="round" fill="transparent"></path>
          </svg>
        </div>
        <div class="meta">
          <p class="title">Hotline</p>
          <small class="description">Support phone number</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
        <div class="logo">
          <img src="../assets/img/others/youtube-social.png" />
        </div>
        <div class="meta">
          <p class="title">YouTube</p>
          <small class="description">Follow SePay's latest videos</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
        <div class="logo">
          <img src="../assets/img/others/telegram-social.png" />
        </div>
        <div class="meta">
          <p class="title">Telegram</p>
          <small class="description">Get the latest updates from SePay</small>
        </div>
      </a>
    </div>
    <div class="container">
      <div class="dot-ping">
        <div class="ping"></div>
        <div class="dot"></div>
      </div>
      <div class="contact-icon">
        <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20"
          style="color: currentcolor;">
          <path fill-rule="evenodd" clip-rule="evenodd"
            d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z"
            fill="currentColor"></path>
        </svg>
      </div>
      <span style="font-weight: bold;">Contact Us</span>
    </div>
  </div>
  <!--/contact-box-html-docs-->

  <!-- Go To -->
  <a class="js-go-to go-to position-fixed" href="javascript:;" style="visibility: hidden;" data-hs-go-to-options='{
       "offsetTop": 700,
       "position": {
         "init": {
           "right": "2rem"
         },
         "show": {
           "bottom": "2rem"
         },
         "hide": {
           "bottom": "-2rem"
         }
       }
     }'>
    <i class="bi-chevron-up"></i>
  </a>
  <!-- ========== END SECONDARY CONTENTS ========== -->

  <!-- JS Global Compulsory  -->
  <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

  <!-- JS Implementing Plugins -->
  <script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
  <script src="../assets/vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js"></script>
  <script src="../assets/vendor/list.js/dist/list.min.js"></script>
  <script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>


  <!-- JS Front -->
  <script src="../assets/js/theme.min.js"></script>

  <!-- contact-box-js -->
  <script src="../assets/js/contact-box.js"></script>
  <!-- /contact-box-js -->

  <!-- JS Plugins Init. -->
  <script>
    (function () {
      // INITIALIZATION OF HEADER
      // =======================================================
      new HSHeader('#header').init()

      // INITIALIZATION OF NAV SCROLLER
      // =======================================================
      new HsNavScroller('.js-nav-scroller', {
        delay: 400,
        offset: 140
      })

      // INITIALIZATION OF LISTJS COMPONENT
      // =======================================================
      const docsSearch = HSCore.components.HSList.init('#docsSearch')




      // INITIALIZATION OF GO TO
      // =======================================================
      new HSGoTo('.js-go-to')
    })()
  </script>
</body>

</html>