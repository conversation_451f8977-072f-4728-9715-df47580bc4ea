<!DOCTYPE html>
<html lang="vi">
    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <title>OAuth2 Access Token - SePay</title>
        <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet" />
        <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
        <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
        <link rel="stylesheet" href="../assets/vendor/bootstrap-icons/font/bootstrap-icons.css" />
        <link rel="stylesheet" href="../assets/css/theme.min.css" />
        <link rel="stylesheet" href="../assets/css/docs.min.css" />
        <link rel="stylesheet" href="../assets/css/contact-box.css" />
        <link rel="stylesheet" href="../assets/highlight/styles/vs2015.min.css" />
        <style>
            .docs-navbar-sidebar-aside-body {
                padding-top: 3.5rem !important;
            }
            pre {
                border-radius: 0.5rem;
            }
        </style>
        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                dataLayer.push(arguments);
            }
            gtag("js", new Date());

            gtag("config", "G-J8DLMQTKSQ");
        </script>
    </head>

    <body class="navbar-sidebar-aside-lg">
        <header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
            <div class="container-fluid">
                <nav class="navbar-nav-wrap">
                    <div class="row flex-grow-1">
                        <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
                            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                                <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo" />
                            </a>
                        </div>

                        <div class="col-md px-lg-0">
                            <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
                                <div class="d-none d-md-block">
                                    <div class="d-none">
                                        <div id="searchTemplate" class="dropdown-item">
                                            <a class="d-block link" href="#">
                                                <span class="category d-block fw-normal text-muted mb-1"></span>
                                                <span class="component text-dark"></span>
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <ul class="navbar-nav p-0">
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html" target="_blank"> Support <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN" target="_blank"> Youtube <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://www.facebook.com/messages/t/sepay.vn" target="_blank"> Facebook <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel" target="_blank"> Telegram <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </nav>
            </div>
        </header>
        <main id="content" role="main">
            <nav
                class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
                data-hs-nav-scroller-options='{
                    "type": "vertical",
                    "target": ".navbar-nav .active",
                    "offset": 80
                }'
            >
                <button
                    type="button"
                    class="navbar-toggler btn btn-white d-grid w-100"
                    data-bs-toggle="collapse"
                    data-bs-target="#navbarVerticalNavMenu"
                    aria-label="Toggle navigation"
                    aria-expanded="false"
                    aria-controls="navbarVerticalNavMenu"
                >
                    <span class="d-flex justify-content-between align-items-center">
                        <span class="h3 mb-0">Nav menu</span>

                        <span class="navbar-toggler-default">
                            <i class="bi-list"></i>
                        </span>

                        <span class="navbar-toggler-toggled">
                            <i class="bi-x"></i>
                        </span>
                    </span>
                </button>

                <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
                    <div class="navbar-brand-wrapper border-end" style="height: auto;">
                        <div class="d-flex align-items-center mb-3">
                            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                                <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo" />
                            </a>
                        </div>
                    </div>

                    <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
                        <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
                            <li class="nav-item">
                                <span class="nav-subtitle">SePay OAuth2</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/">Tổng quan</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/dang-ky-ung-dung.html">Đăng ký ứng dụng</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/luong-xac-thuc.html">Luồng xác thực</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link active" href="/oauth2/access-token.html">Access Token</a>
                            </li>

                            <li class="nav-item my-2 my-lg-5"></li>

                            <li class="nav-item">
                                <span class="nav-subtitle">API</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-tai-khoan-ngan-hang.html">Tài khoản ngân hàng</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-giao-dich.html">Giao dịch</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-webhooks.html">Webhook</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-nguoi-dung.html">Người dùng</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-cong-ty.html">Công ty</a>
                            </li>

                        </ul>
                    </div>
                </div>
            </nav>

            <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">
                <div style="max-width: 1200px;">
                    <div class="docs-page-header">
                        <div class="row align-items-center">
                            <div class="col-sm">
                                <h1 class="docs-page-header-title">Access Token</h1>
                                <p class="docs-page-header-text">Tìm hiểu cách sử dụng và quản lý Access Token để truy cập API của SePay.</p>
                            </div>
                        </div>
                    </div>
                    
                    <h3 id="gioi-thieu" class="hs-docs-heading">
                        Giới thiệu về Access Token <a class="anchorjs-link" href="#gioi-thieu" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>Access Token là chuỗi ký tự được SePay cấp cho ứng dụng của bạn sau khi quá trình xác thực OAuth2 thành công. Token này đóng vai trò như một "chìa khóa" tạm thời để truy cập vào các API của SePay thay mặt cho người dùng.</p>
                    
                    <div class="alert alert-soft-primary" role="alert">
                        <div class="d-flex align-items-baseline">
                            <div class="flex-shrink-0">
                                <i class="bi-info-circle me-1"></i>
                            </div>
                            <div class="flex-grow-1 ms-2">
                                Access Token của SePay có thời hạn giới hạn 1 giờ và đi kèm với Refresh Token có thời hạn dài là 1 tháng để cấp lại Access Token mới khi hết hạn.
                            </div>
                        </div>
                    </div>
                    
                    <h3 id="cau-truc" class="hs-docs-heading">
                        Cấu trúc của Access Token <a class="anchorjs-link" href="#cau-truc" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>Access Token của SePay tuân theo chuẩn JWT (JSON Web Token) và bao gồm 3 phần, phân tách bởi dấu chấm (.):</p>
                    
                    <pre><code class="language-text">header.payload.signature</code></pre>
                    
                    <ul class="lh-lg">
                        <li><strong>Header</strong>: Chứa thông tin về loại token và thuật toán mã hóa</li>
                        <li><strong>Payload</strong>: Chứa các claims (thông tin) như ID người dùng, phạm vi quyền, thời gian hết hạn</li>
                        <li><strong>Signature</strong>: Chữ ký để xác minh token không bị sửa đổi</li>
                    </ul>
                    
                    <p>Ví dụ về nội dung giải mã của một JWT Access Token:</p>
                    
                    <pre><code class="language-json">{
    "iss": "https://my.sepay.vn",
    "sub": "12345",
    "aud": "client_67890",
    "exp": **********,
    "iat": **********,
    "scope": "profile bank-account:read transaction:read"
}</code></pre>

                    <p>Trong đó:</p>
                    <ul class="lh-lg">
                        <li><code>iss</code> (issuer): SePay - đơn vị cấp token</li>
                        <li><code>sub</code> (subject): ID của người dùng đã cấp quyền</li>
                        <li><code>aud</code> (audience): Client ID của ứng dụng</li>
                        <li><code>exp</code> (expiration time): Thời gian hết hạn của token</li>
                        <li><code>iat</code> (issued at): Thời gian token được cấp</li>
                        <li><code>scope</code>: Phạm vi quyền được cấp</li>
                    </ul>
                    
                    <div class="alert alert-soft-warning mb-4" role="alert">
                        <div class="d-flex align-items-baseline">
                            <div class="flex-shrink-0">
                                <i class="bi-exclamation-circle me-1"></i>
                            </div>
                            <div class="flex-grow-1 ms-2">
                                <b>Lưu ý</b>: Mặc dù JWT có thể giải mã để đọc thông tin, nhưng bạn <b>không nên</b> dựa vào việc giải mã token ở phía client để xác thực. Chữ ký JWT chỉ có thể được xác minh bởi SePay.
                            </div>
                        </div>
                    </div>

                    <h3 id="su-dung-token" class="hs-docs-heading">
                        Sử dụng Access Token <a class="anchorjs-link" href="#su-dung-token" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>Để sử dụng Access Token, bạn cần thêm nó vào header <code>Authorization</code> trong mỗi yêu cầu API với prefix "Bearer":</p>
                    
                    <pre><code class="language-http">GET https://my.sepay.vn/api/v1/bank-accounts
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************.signature</code></pre>
                    
                    <p>Dưới đây là ví dụ sử dụng Access Token với một số ngôn ngữ lập trình phổ biến:</p>
                    
                    <div class="mb-3">
                        <nav>
                            <div class="nav nav-tabs" id="nav-tab" role="tablist">
                                <button class="nav-link active" id="nav-curl-tab" data-bs-toggle="tab" data-bs-target="#nav-curl" type="button" role="tab" aria-controls="nav-curl" aria-selected="true">cURL</button>
                                <button class="nav-link" id="nav-php-tab" data-bs-toggle="tab" data-bs-target="#nav-php" type="button" role="tab" aria-controls="nav-php" aria-selected="false">PHP</button>
                                <button class="nav-link" id="nav-laravel-tab" data-bs-toggle="tab" data-bs-target="#nav-laravel" type="button" role="tab" aria-controls="nav-laravel" aria-selected="false">Laravel</button>
                                <button class="nav-link" id="nav-nodejs-tab" data-bs-toggle="tab" data-bs-target="#nav-nodejs" type="button" role="tab" aria-controls="nav-nodejs" aria-selected="false">Node.js</button>
                            </div>
                        </nav>
                        <div class="tab-content" id="nav-tabContent">
                            <div class="tab-pane fade show active" id="nav-curl" role="tabpanel" aria-labelledby="nav-curl-tab">
                                <pre style="border-top-left-radius: 0;"><code class="language-bash">curl -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
https://my.sepay.vn/api/v1/bank-accounts</code></pre>
                            </div>
                            <div class="tab-pane fade" id="nav-php" role="tabpanel" aria-labelledby="nav-php-tab">
                                <pre style="border-top-left-radius: 0;"><code class="language-php">&lt;?php

$accessToken = 'YOUR_ACCESS_TOKEN';
$url = 'https://my.sepay.vn/api/v1/bank-accounts';

$ch = curl_init($url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . $accessToken
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode === 200) {
    $data = json_decode($response, true);
    print_r($data);
} else {
    echo "Error: " . $httpCode . " - " . $response;
}
</code></pre>
                            </div>
                            <div class="tab-pane fade" id="nav-laravel" role="tabpanel" aria-labelledby="nav-laravel-tab">
                                <pre style="border-top-left-radius: 0;"><code class="language-php">use Illuminate\Support\Facades\Http;

$response = Http::withToken('ACCESS_TOKEN')->get('https://my.sepay.vn/api/v1/bank-accounts');

if ($response->ok()) {
    $data = $response->json();
    dd($data);
} else {
    dd($response->status(), $response->body());
}</code></pre>
                            </div>
                            <div class="tab-pane fade" id="nav-nodejs" role="tabpanel" aria-labelledby="nav-nodejs-tab">
                                <pre style="border-top-left-radius: 0;"><code class="language-javascript">async function getBankAccounts() {
    try {
        const response = await fetch('https://my.sepay.vn/api/v1/bank-accounts', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        console.log(data);
    } catch (error) {
        console.error('Error:', error.message);
    }
}

getBankAccounts();
</code></pre>
                            </div>
                        </div>
                    </div>
                    
                    <h3 id="luu-tru-token" class="hs-docs-heading">
                        Lưu trữ Access Token an toàn <a class="anchorjs-link" href="#luu-tru-token" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>Việc lưu trữ an toàn Access Token và Refresh Token là rất quan trọng để bảo vệ dữ liệu người dùng. Dưới đây là các thực hành tốt nhất:</p>
                    
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Loại ứng dụng</th>
                                    <th>Phương pháp lưu trữ được khuyến nghị</th>
                                    <th>Cần tránh</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Ứng dụng Web <br>(Server-side)</td>
                                    <td>
                                        <ul>
                                            <li>Lưu trữ trong session server</li>
                                            <li>Lưu trong database có mã hóa</li>
                                            <li>Dùng cookie có thuộc tính HttpOnly và Secure</li>
                                        </ul>
                                    </td>
                                    <td>
                                        <ul>
                                            <li>Lưu trong localStorage hoặc sessionStorage</li>
                                            <li>Lưu trong cookie không bảo mật</li>
                                        </ul>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Ứng dụng SPA <br>(Single Page Application)</td>
                                    <td>
                                        <ul>
                                            <li>Sử dụng BFF (Backend For Frontend) pattern</li>
                                            <li>Cookie HttpOnly được thiết lập từ server</li>
                                        </ul>
                                    </td>
                                    <td>
                                        <ul>
                                            <li>Lưu trữ trong localStorage hoặc sessionStorage</li>
                                            <li>Lưu trong biến JavaScript</li>
                                        </ul>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Ứng dụng Mobile</td>
                                    <td>
                                        <ul>
                                            <li>iOS: Keychain Services</li>
                                            <li>Android: EncryptedSharedPreferences</li>
                                            <li>Android: Android Keystore System</li>
                                        </ul>
                                    </td>
                                    <td>
                                        <ul>
                                            <li>SharedPreferences không mã hóa</li>
                                            <li>UserDefaults không bảo mật</li>
                                            <li>Lưu trong bộ nhớ ứng dụng thông thường</li>
                                        </ul>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="alert alert-soft-danger" role="alert">
                        <div class="d-flex align-items-baseline">
                            <div class="flex-shrink-0">
                                <i class="bi-shield-exclamation me-1"></i>
                            </div>
                            <div class="flex-grow-1 ms-2">
                                <b>Cảnh báo bảo mật</b>: Không bao giờ lưu trữ <code>client_secret</code> trong mã nguồn phía client hoặc ứng dụng mobile. Luôn giữ client_secret ở phía server.
                            </div>
                        </div>
                    </div>
                    
                    <h3 id="quan-ly-token" class="hs-docs-heading">
                        Quản lý Token hết hạn <a class="anchorjs-link" href="#quan-ly-token" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>Access Token có thời hạn giới hạn (mặc định là 1 giờ). Khi token hết hạn, ứng dụng của bạn cần xử lý tình huống này để duy trì trải nghiệm người dùng liên tục.</p>
                    
                    <h4>Chiến lược xử lý token hết hạn:</h4>
                    
                    <ol class="lh-lg">
                        <li>
                            <b>Làm mới trước khi hết hạn</b>: Theo dõi thời gian sắp hết hạn và chủ động làm mới token trước khi API trả về lỗi. Bạn có thể tính toán thời điểm làm mới dựa trên trường <code>expires_in</code> trong phản hồi token.
                        </li>
                        <li>
                            <b>Xử lý lỗi 401</b>: Khi API trả về mã lỗi 401 Unauthorized, thực hiện việc làm mới token và thử lại yêu cầu.
                        </li>
                    </ol>
                    
                    <p>Dưới đây là mẫu code xử lý token hết hạn và tự động làm mới token:</p>
                    
                    <pre style="max-height: 35rem;"><code class="language-php">function makeApiRequest($url, $accessToken) {
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $accessToken
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode === 401) {
        // Token hết hạn, làm mới token
        $newTokens = refreshAccessToken();
        $accessToken = $newTokens['access_token'];

        // Thử lại yêu cầu với token mới
        return makeApiRequest($url, $accessToken);
    }

    return json_decode($response, true);
}

function refreshAccessToken() {
    $url = 'https://my.sepay.vn/oauth/token';
    $data = [
        'grant_type' => 'refresh_token',
        'refresh_token' => 'YOUR_REFRESH_TOKEN',
        'client_id' => 'YOUR_CLIENT_ID',
        'client_secret' => 'YOUR_CLIENT_SECRET',
    ];

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));

    $response = curl_exec($ch);
    curl_close($ch);

    return json_decode($response, true);
}

$bankAccounts = makeApiRequest('https://my.sepay.vn/api/v1/bank-accounts', 'YOUR_ACCESS_TOKEN');
print_r($bankAccounts);
</code></pre>

                    <h3 id="xu-ly-loi" class="hs-docs-heading">
                        Xử lý lỗi liên quan đến Token <a class="anchorjs-link" href="#xu-ly-loi" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>Khi làm việc với Access Token, bạn có thể gặp phải các lỗi sau:</p>

                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                <th>HTTP Status</th>
                                <th>Mã lỗi</th>
                                <th>Mô tả</th>
                                <th>Xử lý</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>401</td>
                                <td><code>invalid_token</code></td>
                                <td>Token không hợp lệ hoặc hết hạn</td>
                                <td>Làm mới token hoặc yêu cầu người dùng đăng nhập lại</td>
                            </tr>
                            <tr>
                                <td>401</td>
                                <td><code>expired_token</code></td>
                                <td>Token đã hết hạn</td>
                                <td>Sử dụng refresh token để lấy token mới</td>
                            </tr>
                            <tr>
                                <td>403</td>
                                <td><code>insufficient_scope</code></td>
                                <td>Token không có quyền truy cập resource</td>
                                <td>Yêu cầu lại token với phạm vi quyền hạn rộng hơn</td>
                            </tr>
                            <tr>
                                <td>400</td>
                                <td><code>invalid_grant</code></td>
                                <td>Refresh token không hợp lệ hoặc hết hạn</td>
                                <td>Yêu cầu người dùng đăng nhập lại</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                    <h3 id="buoc-tiep-theo" class="hs-docs-heading">
                        Bước tiếp theo <a class="anchorjs-link" href="#buoc-tiep-theo" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>Sau khi hiểu rõ về cách sử dụng và quản lý Access Token, bạn đã sẵn sàng để tìm hiểu về các API resources của SePay.</p>
                    
                    <div class="row g-3">
                        <div class="col-md-6">
                            <a class="card card-sm card-transition h-100" href="/oauth2/luong-xac-thuc.html">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <i class="bi-arrow-left fs-2"></i>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h5 class="fs-4 card-title">Luồng xác thực</h5>
                                            <p class="card-text text-body small">Quay lại luồng xác thực OAuth2</p>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a class="card card-sm card-transition h-100" href="/oauth2/api-tai-khoan-ngan-hang.html">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1 me-3">
                                            <h5 class="fs-4 card-title">API Tài khoản ngân hàng</h5>
                                            <p class="card-text text-body small">Tìm hiểu API tài khoản ngân hàng</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="bi-arrow-right fs-2"></i>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <div id="contact-box-overlay"></div>
        <div id="contact-box">
            <div class="popup">
                <a href="https://m.me/117903214582465" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/fb-messenger.png" width="50%" />
                    </div>
                    <div class="meta">
                        <p class="title">Facebook Messenger</p>
                        <small class="description">Hỗ trợ live chat 24/7</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="tel:02873059589" class="item">
                    <div class="icon" style="background-color: #22c55e; color: #fff;">
                        <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;">
                            <path
                                d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z"
                                stroke="currentColor"
                                stroke-width="1.5"
                                stroke-linecap="round"
                                fill="transparent"
                            ></path>
                        </svg>
                    </div>
                    <div class="meta">
                        <p class="title">Hotline</p>
                        <small class="description">Điện thoại hỗ trợ</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/youtube-social.png" />
                    </div>
                    <div class="meta">
                        <p class="title">Youtube</p>
                        <small class="description">Theo dõi video mới nhất của SePay</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/telegram-social.png" />
                    </div>
                    <div class="meta">
                        <p class="title">Telegram</p>
                        <small class="description">Nhận thông tin mới nhất từ SePay</small>
                    </div>
                </a>
            </div>
            <div class="container">
                <div class="dot-ping">
                    <div class="ping"></div>
                    <div class="dot"></div>
                </div>
                <div class="contact-icon">
                    <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;">
                        <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z"
                            fill="currentColor"
                        ></path>
                    </svg>
                </div>
                <span style="font-weight: bold;">Liên hệ chúng tôi</span>
            </div>
        </div>

        <a
            class="js-go-to go-to position-fixed"
            href="javascript:;"
            style="visibility: hidden;"
            data-hs-go-to-options='{
                "offsetTop": 700,
                "position": {
                    "init": {
                    "right": "2rem"
                    },
                    "show": {
                    "bottom": "2rem"
                    },
                    "hide": {
                    "bottom": "-2rem"
                    }
                }
                }'
        >
            <i class="bi-chevron-up"></i>
        </a>

        <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
        <script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
        <script src="../assets/vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js"></script>
        <script src="../assets/vendor/list.js/dist/list.min.js"></script>
        <script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>
        <script src="../assets/js/theme.min.js"></script>
        <script src="../assets/js/contact-box.js"></script>
        <script src="../assets/highlight/highlight.min.js"></script>

        <script>
            hljs.highlightAll();

            (function () {
                new HSHeader("#header").init();

                new HsNavScroller(".js-nav-scroller", {
                    delay: 400,
                    offset: 140,
                });

                const docsSearch = HSCore.components.HSList.init("#docsSearch");

                new HSGoTo(".js-go-to");
            })();

            function setCookie(cname, cvalue, exdays) {
                var d = new Date();
                d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000);
                var expires = "expires=" + d.toUTCString();
                document.cookie = cname + "=" + cvalue + "; " + expires + "; path=/; domain=.sepay.vn";
            }

            let urlParams = new URLSearchParams(document.location.search);

            ["utm_source", "utm_medium", "utm_campaign", "utm_term", "utm_content"].forEach(function (param) {
                let value = urlParams.get(param);

                if (value) {
                    setCookie(param, value, 90);
                }
            });

            if (document.referrer != "" && document.cookie.indexOf("referer") === -1) {
                setCookie("referer", document.referrer, 90);
            }
        </script>
    </body>
</html>
