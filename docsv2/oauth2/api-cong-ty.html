<!DOCTYPE html>
<html lang="vi">
    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <title>OAuth2 API Người dùng - SePay</title>
        <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet" />
        <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
        <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
        <link rel="stylesheet" href="../assets/vendor/bootstrap-icons/font/bootstrap-icons.css" />
        <link rel="stylesheet" href="../assets/css/theme.min.css" />
        <link rel="stylesheet" href="../assets/css/docs.min.css" />
        <link rel="stylesheet" href="../assets/css/contact-box.css" />
        <link rel="stylesheet" href="../assets/highlight/styles/vs2015.min.css" />
        <style>
            .docs-navbar-sidebar-aside-body {
                padding-top: 3.5rem !important;
            }
            pre {
                border-radius: 0.5rem;
            }
        </style>
        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                dataLayer.push(arguments);
            }
            gtag("js", new Date());

            gtag("config", "G-J8DLMQTKSQ");
        </script>
    </head>

    <body class="navbar-sidebar-aside-lg">
        <header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
            <div class="container-fluid">
                <nav class="navbar-nav-wrap">
                    <div class="row flex-grow-1">
                        <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
                            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                                <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo" />
                            </a>
                        </div>

                        <div class="col-md px-lg-0">
                            <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
                                <div class="d-none d-md-block">
                                    <div class="d-none">
                                        <div id="searchTemplate" class="dropdown-item">
                                            <a class="d-block link" href="#">
                                                <span class="category d-block fw-normal text-muted mb-1"></span>
                                                <span class="component text-dark"></span>
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <ul class="navbar-nav p-0">
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html" target="_blank"> Support <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN" target="_blank"> Youtube <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://www.facebook.com/messages/t/sepay.vn" target="_blank"> Facebook <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel" target="_blank"> Telegram <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </nav>
            </div>
        </header>
        <main id="content" role="main">
            <nav
                class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
                data-hs-nav-scroller-options='{
                    "type": "vertical",
                    "target": ".navbar-nav .active",
                    "offset": 80
                }'
            >
                <button
                    type="button"
                    class="navbar-toggler btn btn-white d-grid w-100"
                    data-bs-toggle="collapse"
                    data-bs-target="#navbarVerticalNavMenu"
                    aria-label="Toggle navigation"
                    aria-expanded="false"
                    aria-controls="navbarVerticalNavMenu"
                >
                    <span class="d-flex justify-content-between align-items-center">
                        <span class="h3 mb-0">Nav menu</span>

                        <span class="navbar-toggler-default">
                            <i class="bi-list"></i>
                        </span>

                        <span class="navbar-toggler-toggled">
                            <i class="bi-x"></i>
                        </span>
                    </span>
                </button>

                <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
                    <div class="navbar-brand-wrapper border-end" style="height: auto;">
                        <div class="d-flex align-items-center mb-3">
                            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                                <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo" />
                            </a>
                        </div>
                    </div>

                    <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
                        <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
                            <li class="nav-item">
                                <span class="nav-subtitle">SePay OAuth2</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/">Tổng quan</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/dang-ky-ung-dung.html">Đăng ký ứng dụng</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/luong-xac-thuc.html">Luồng xác thực</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/access-token.html">Access Token</a>
                            </li>

                            <li class="nav-item my-2 my-lg-5"></li>

                            <li class="nav-item">
                                <span class="nav-subtitle">API</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-tai-khoan-ngan-hang.html">Tài khoản ngân hàng</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-giao-dich.html">Giao dịch</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-webhooks.html">Webhook</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-nguoi-dung.html">Người dùng</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link active" href="/oauth2/api-cong-ty.html">Công ty</a>
                            </li>

                        </ul>
                    </div>
                </div>
            </nav>

            <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">
                <div style="max-width: 1200px;">
                    <div class="docs-page-header">
                        <div class="row align-items-center">
                            <div class="col-sm">
                                <h1 class="docs-page-header-title">API Công ty</h1>
                                <p class="docs-page-header-text">Tài liệu về cách sử dụng API công ty thông qua OAuth2 trong SePay.</p>
                            </div>
                        </div>
                    </div>
                    
                    <h3 id="gioi-thieu" class="hs-docs-heading">
                        Giới thiệu <a class="anchorjs-link" href="#gioi-thieu" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>API công ty của SePay cho phép bạn truy cập thông tin về công ty hiện tại và cấu hình của công ty. API này chỉ dành cho người dùng có vai trò Admin hoặc SuperAdmin trong công ty.</p>
                    
                    <div class="alert alert-soft-primary" role="alert">
                        <div class="d-flex align-items-baseline">
                            <div class="flex-shrink-0">
                                <i class="bi-info-circle me-1"></i>
                            </div>
                            <div class="flex-grow-1 ms-2">
                                Để sử dụng API này, bạn cần có quyền <code>company</code> trong phạm vi (scope) của Access Token và phải là người dùng với vai trò Admin hoặc SuperAdmin.
                            </div>
                        </div>
                    </div>
                    
                    <h3 id="endpoints" class="hs-docs-heading">
                        Các Endpoints <a class="anchorjs-link" href="#endpoints" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>API công ty cung cấp các endpoints sau:</p>
                    
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                <th>Method</th>
                                <th>Endpoint</th>
                                <th>Mô tả</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>GET</td>
                                <td><code>/api/v1/company</code></td>
                                <td>Lấy thông tin của công ty hiện tại</td>
                            </tr>
                            <tr>
                                <td>PATCH</td>
                                <td><code>/api/v1/company/configurations</code></td>
                                <td>Cập nhật cấu hình công ty</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <h3 id="thong-tin-cong-ty" class="hs-docs-heading">
                        Lấy thông tin công ty <a class="anchorjs-link" href="#thong-tin-cong-ty" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p><span class="badge bg-primary">GET</span> <code>/api/v1/company</code></p>
                    
                    <p>Endpoint này trả về thông tin của công ty hiện tại và các cấu hình liên quan.</p>
                    
                    <h4>Quyền yêu cầu</h4>
                    <ul>
                        <li>Scope: <code>company</code></li>
                        <li>Vai trò người dùng: <code>Admin</code> hoặc <code>SuperAdmin</code></li>
                    </ul>
                    
                    <h4>Yêu cầu</h4>
                    
                    <pre><code class="language-http">GET /api/v1/company
Authorization: Bearer {YOUR_ACCESS_TOKEN}</code></pre>
                    
                    <h4>Phản hồi</h4>
                    
                    <pre><code class="language-json">{
    "status": "success",
    "data": {
        "id": 123,
        "full_name": "Công ty TNHH Một Thành Viên ABC",
        "short_name": "ABC Corp",
        "role": "Admin",
        "status": "active",
        "subscription": "Premium",
        "begin_date": "2023-01-01",
        "end_date": "2023-12-31",
        "configurations": {
            "bank_sub_account": true,
            "paycode": true,
            "data_storage_time": "90",
            "payment_code_formats": [
                {
                    "prefix": "DH",
                    "suffix_from": 6,
                    "suffix_to": 8,
                    "character_type": "NumberOnly",
                    "is_active": true
                },
                {
                    "prefix": "HD",
                    "suffix_from": 4,
                    "suffix_to": 6,
                    "character_type": "NumberAndLetter",
                    "is_active": true
                }
            ]
        }
    }
}</code></pre>
                    
                    <h4>Giải thích các trường dữ liệu</h4>
                    
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                <th>Trường</th>
                                <th>Kiểu</th>
                                <th>Mô tả</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>id</code></td>
                                <td>integer</td>
                                <td>ID của công ty</td>
                            </tr>
                            <tr>
                                <td><code>full_name</code></td>
                                <td>string</td>
                                <td>Tên đầy đủ của công ty</td>
                            </tr>
                            <tr>
                                <td><code>short_name</code></td>
                                <td>string</td>
                                <td>Tên viết tắt của công ty</td>
                            </tr>
                            <tr>
                                <td><code>role</code></td>
                                <td>string</td>
                                <td>Vai trò của người dùng trong công ty</td>
                            </tr>
                            <tr>
                                <td><code>status</code></td>
                                <td>string</td>
                                <td>Trạng thái của công ty (active, inactive, etc.)</td>
                            </tr>
                            <tr>
                                <td><code>subscription</code></td>
                                <td>string</td>
                                <td>Gói đăng ký của công ty</td>
                            </tr>
                            <tr>
                                <td><code>begin_date</code></td>
                                <td>string</td>
                                <td>Ngày bắt đầu gói đăng ký</td>
                            </tr>
                            <tr>
                                <td><code>end_date</code></td>
                                <td>string</td>
                                <td>Ngày kết thúc gói đăng ký</td>
                            </tr>
                            <tr>
                                <td><code>configurations</code></td>
                                <td>object</td>
                                <td>Các cấu hình của công ty</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                    <h4>Chi tiết trường configurations</h4>
                    
                    <div class="table-responsive">
                        <table class="table">
                        <thead>
                            <tr>
                                <th>Trường</th>
                                <th>Kiểu</th>
                                <th>Mô tả</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>bank_sub_account</code></td>
                                <td>boolean</td>
                                <td>Cấu hình tài khoản phụ ngân hàng</td>
                            </tr>
                            <tr>
                                <td><code>paycode</code></td>
                                <td>boolean</td>
                                <td>Bật/tắt tính năng mã thanh toán</td>
                            </tr>
                            <tr>
                                <td><code>data_storage_time</code></td>
                                <td>string</td>
                                <td>Thời gian lưu trữ dữ liệu (số ngày)</td>
                            </tr>
                            <tr>
                                <td><code>payment_code_formats</code></td>
                                <td>array</td>
                                <td>Danh sách các định dạng mã thanh toán</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                    <h4>Chi tiết payment_code_formats</h4>
                    
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Trường</th>
                                    <th>Kiểu</th>
                                    <th>Mô tả</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>prefix</code></td>
                                    <td>string</td>
                                    <td>Tiền tố của mã thanh toán</td>
                                </tr>
                                <tr>
                                    <td><code>suffix_from</code></td>
                                    <td>integer</td>
                                    <td>Độ dài tối thiểu của hậu tố</td>
                                </tr>
                                <tr>
                                    <td><code>suffix_to</code></td>
                                    <td>integer</td>
                                    <td>Độ dài tối đa của hậu tố</td>
                                </tr>
                                <tr>
                                    <td><code>character_type</code></td>
                                    <td>string</td>
                                    <td>Loại ký tự của hậu tố (NumberOnly, NumberAndLetter)</td>
                                </tr>
                                <tr>
                                    <td><code>is_active</code></td>
                                    <td>boolean</td>
                                    <td>Trạng thái kích hoạt của định dạng này</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <h3 id="cap-nhat-cau-hinh" class="hs-docs-heading">
                        Cập nhật cấu hình công ty <a class="anchorjs-link" href="#cap-nhat-cau-hinh" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p><span class="badge bg-success">PATCH</span> <code>/api/v1/company/configurations</code></p>
                    
                    <p>Endpoint này cho phép cập nhật cấu hình của công ty, đặc biệt là cấu trúc mã thanh toán.</p>
                    
                    <h4>Quyền yêu cầu</h4>
                    <ul>
                        <li>Scope: <code>company</code></li>
                        <li>Vai trò người dùng: <code>Admin</code> hoặc <code>SuperAdmin</code></li>
                    </ul>
                    
                    <h4>Tham số yêu cầu</h4>
                    
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                <th>Tham số</th>
                                <th>Kiểu</th>
                                <th>Mô tả</th>
                                <th>Ràng buộc</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>payment_code_formats</code></td>
                                <td>array</td>
                                <td>Danh sách các định dạng mã thanh toán</td>
                                <td>Bắt buộc nếu được gửi</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <h4>Chi tiết tham số payment_code_formats</h4>
                    
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Trường</th>
                                    <th>Kiểu</th>
                                    <th>Mô tả</th>
                                    <th>Ràng buộc</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>prefix</code></td>
                                    <td>string</td>
                                    <td>Tiền tố của mã thanh toán</td>
                                    <td>Chỉ chứa chữ cái, độ dài từ 2-3 ký tự</td>
                                </tr>
                                <tr>
                                    <td><code>suffix_from</code></td>
                                    <td>integer</td>
                                    <td>Độ dài tối thiểu của hậu tố</td>
                                    <td>Số nguyên từ 1-30</td>
                                </tr>
                                <tr>
                                    <td><code>suffix_to</code></td>
                                    <td>integer</td>
                                    <td>Độ dài tối đa của hậu tố</td>
                                    <td>Số nguyên từ 1-30, phải lớn hơn hoặc bằng suffix_from</td>
                                </tr>
                                <tr>
                                    <td><code>character_type</code></td>
                                    <td>string</td>
                                    <td>Loại ký tự của hậu tố</td>
                                    <td>Chỉ có thể là NumberOnly hoặc NumberAndLetter</td>
                                </tr>
                                <tr>
                                    <td><code>is_active</code></td>
                                    <td>integer</td>
                                    <td>Trạng thái kích hoạt của định dạng này</td>
                                    <td>Chỉ có thể là 0 hoặc 1</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <h4>Yêu cầu</h4>
                    
                    <pre><code class="language-http">PATCH /api/v1/company/configurations
Content-Type: application/json
Authorization: Bearer {YOUR_ACCESS_TOKEN}

{
    "payment_code_formats": [
        {
            "prefix": "DH",
            "suffix_from": 6,
            "suffix_to": 8,
            "character_type": "NumberOnly",
            "is_active": 1
        },
        {
            "prefix": "HD",
            "suffix_from": 4,
            "suffix_to": 6,
            "character_type": "NumberAndLetter",
            "is_active": 1
        }
    ]
}</code></pre>
                    
                    <h4>Phản hồi thành công</h4>
                    
                    <pre><code class="language-json">{
    "status": "success",
    "data": {
        "status": true,
        "data": {
            "bank_sub_account": true,
            "paycode": true,
            "data_storage_time": "90",
            "payment_code_formats": [
                {
                    "prefix": "DH",
                    "suffix_from": 6,
                    "suffix_to": 8,
                    "character_type": "NumberOnly",
                    "is_active": true
                },
                {
                    "prefix": "HD",
                    "suffix_from": 4,
                    "suffix_to": 6,
                    "character_type": "NumberAndLetter",
                    "is_active": true
                }
            ]
        }
    }
}</code></pre>

                    <h4>Phản hồi lỗi định dạng</h4>
                    
                    <pre><code class="language-json">{
    "status": "success",
    "data": {
        "status": false,
        "message": "Hậu tố mã thanh toán từ phải nhỏ hơn hoặc bằng hậu tố mã thanh toán đến (Cấu trúc mã thanh toán 1)"
    }
}</code></pre>
                    
                    <h3 id="vi-du-su-dung" class="hs-docs-heading">
                        Ví dụ sử dụng <a class="anchorjs-link" href="#vi-du-su-dung" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <div class="mb-3">
                        <nav>
                            <div class="nav nav-tabs" id="nav-tab" role="tablist">
                                <button class="nav-link active" id="nav-curl-tab" data-bs-toggle="tab" data-bs-target="#nav-curl" type="button" role="tab" aria-controls="nav-curl" aria-selected="true">cURL</button>
                                <button class="nav-link" id="nav-php-tab" data-bs-toggle="tab" data-bs-target="#nav-php" type="button" role="tab" aria-controls="nav-php" aria-selected="false">PHP</button>
                                <button class="nav-link" id="nav-nodejs-tab" data-bs-toggle="tab" data-bs-target="#nav-nodejs" type="button" role="tab" aria-controls="nav-nodejs" aria-selected="false">Node.js</button>
                            </div>
                        </nav>
                        <div class="tab-content" id="nav-tabContent">
                            <div class="tab-pane fade show active" id="nav-curl" role="tabpanel" aria-labelledby="nav-curl-tab">
                                <pre style="border-top-left-radius: 0;"><code class="language-bash"># Lấy thông tin công ty
curl -X GET "https://my.sepay.vn/api/v1/company" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Cập nhật cấu hình mã thanh toán
curl -X PATCH "https://my.sepay.vn/api/v1/company/configurations" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
           "payment_code_formats": [
             {
               "prefix": "DH",
               "suffix_from": 6,
               "suffix_to": 8,
               "character_type": "NumberOnly",
               "is_active": 1
             },
             {
               "prefix": "HD",
               "suffix_from": 4,
               "suffix_to": 6,
               "character_type": "NumberAndLetter",
               "is_active": 1
             }
           ]
         }'</code></pre>
                            </div>
                            <div class="tab-pane fade" id="nav-php" role="tabpanel" aria-labelledby="nav-php-tab">
                                <pre style="border-top-left-radius: 0; max-height: 35rem;"><code class="language-php">&lt;?php

$accessToken = 'YOUR_ACCESS_TOKEN';

// Hàm lấy thông tin công ty
function getCompanyInfo($accessToken) {
    $ch = curl_init('https://my.sepay.vn/api/v1/company');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $accessToken
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        return json_decode($response, true);
    } else {
        return ['error' => $httpCode, 'response' => $response];
    }
}

// Hàm cập nhật cấu hình công ty
function updateCompanyConfigurations($accessToken, $paymentCodeFormats) {
    $ch = curl_init('https://my.sepay.vn/api/v1/company/configurations');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PATCH');
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
        'payment_code_formats' => $paymentCodeFormats
    ]));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $accessToken,
        'Content-Type: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        return json_decode($response, true);
    } else {
        return ['error' => $httpCode, 'response' => $response];
    }
}

// Sử dụng các hàm
$companyInfo = getCompanyInfo($accessToken);
echo "Thông tin công ty:\n";
print_r($companyInfo);

// Cập nhật cấu hình mã thanh toán
$paymentCodeFormats = [
    [
        'prefix' => 'DH',
        'suffix_from' => 6,
        'suffix_to' => 8,
        'character_type' => 'NumberOnly',
        'is_active' => 1
    ],
    [
        'prefix' => 'HD',
        'suffix_from' => 4,
        'suffix_to' => 6,
        'character_type' => 'NumberAndLetter',
        'is_active' => 1
    ]
];

$updateResult = updateCompanyConfigurations($accessToken, $paymentCodeFormats);
echo "\nKết quả cập nhật:\n";
print_r($updateResult);</code></pre>
                            </div>
                            <div class="tab-pane fade" id="nav-nodejs" role="tabpanel" aria-labelledby="nav-nodejs-tab">
                                <pre style="border-top-left-radius: 0; max-height: 35rem;"><code class="language-javascript">const accessToken = 'YOUR_ACCESS_TOKEN';

// Hàm lấy thông tin công ty
async function getCompanyInfo() {
  try {
    const response = await fetch('https://my.sepay.vn/api/v1/company', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching company data:', error.message);
    throw error;
  }
}

// Hàm cập nhật cấu hình công ty
async function updateCompanyConfigurations(paymentCodeFormats) {
  try {
    const response = await fetch('https://my.sepay.vn/api/v1/company/configurations', {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        payment_code_formats: paymentCodeFormats
      })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error updating company configurations:', error.message);
    throw error;
  }
}

// Sử dụng các hàm
async function manageCompany() {
  try {
    // Lấy thông tin công ty
    const companyInfo = await getCompanyInfo();
    console.log('Thông tin công ty:');
    console.log(companyInfo);
    
    // Cập nhật cấu hình mã thanh toán
    const paymentCodeFormats = [
      {
        prefix: 'DH',
        suffix_from: 6,
        suffix_to: 8,
        character_type: 'NumberOnly',
        is_active: 1
      },
      {
        prefix: 'HD',
        suffix_from: 4,
        suffix_to: 6,
        character_type: 'NumberAndLetter',
        is_active: 1
      }
    ];
    
    const updateResult = await updateCompanyConfigurations(paymentCodeFormats);
    console.log('\nKết quả cập nhật:');
    console.log(updateResult);
  } catch (error) {
    console.error('Error:', error);
  }
}

manageCompany();</code></pre>
                            </div>
                        </div>
                    </div>

                    <h3 id="ma-loi" class="hs-docs-heading">
                        Mã lỗi <a class="anchorjs-link" href="#ma-loi" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>Dưới đây là các mã lỗi có thể gặp khi sử dụng API công ty:</p>
                    
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                <th>Mã HTTP</th>
                                <th>Mô tả</th>
                                <th>Nguyên nhân</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>401</td>
                                <td>Unauthorized</td>
                                <td>Access token không hợp lệ hoặc đã hết hạn</td>
                            </tr>
                            <tr>
                                <td>403</td>
                                <td>Forbidden</td>
                                <td>Không đủ quyền truy cập (không phải Admin hoặc SuperAdmin)</td>
                            </tr>
                            <tr>
                                <td>400</td>
                                <td>Bad Request</td>
                                <td>Dữ liệu gửi lên không hợp lệ (không đáp ứng các ràng buộc)</td>
                            </tr>
                            <tr>
                                <td>500</td>
                                <td>Internal Server Error</td>
                                <td>Lỗi máy chủ khi xử lý yêu cầu</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <a class="card card-sm card-transition h-100" href="/oauth2/api-nguoi-dung.html">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <i class="bi-arrow-left fs-2"></i>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h5 class="fs-4 card-title">API Người dùng</h5>
                                            <p class="card-text text-body small">Quay lại API Người dùng</p>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        <div id="contact-box-overlay"></div>
        <div id="contact-box">
            <div class="popup">
                <a href="https://m.me/117903214582465" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/fb-messenger.png" width="50%" />
                    </div>
                    <div class="meta">
                        <p class="title">Facebook Messenger</p>
                        <small class="description">Hỗ trợ live chat 24/7</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="tel:02873059589" class="item">
                    <div class="icon" style="background-color: #22c55e; color: #fff;">
                        <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;">
                            <path
                                d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z"
                                stroke="currentColor"
                                stroke-width="1.5"
                                stroke-linecap="round"
                                fill="transparent"
                            ></path>
                        </svg>
                    </div>
                    <div class="meta">
                        <p class="title">Hotline</p>
                        <small class="description">Điện thoại hỗ trợ</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/youtube-social.png" />
                    </div>
                    <div class="meta">
                        <p class="title">Youtube</p>
                        <small class="description">Theo dõi video mới nhất của SePay</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/telegram-social.png" />
                    </div>
                    <div class="meta">
                        <p class="title">Telegram</p>
                        <small class="description">Nhận thông tin mới nhất từ SePay</small>
                    </div>
                </a>
            </div>
            <div class="container">
                <div class="dot-ping">
                    <div class="ping"></div>
                    <div class="dot"></div>
                </div>
                <div class="contact-icon">
                    <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;">
                        <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z"
                            fill="currentColor"
                        ></path>
                    </svg>
                </div>
                <span style="font-weight: bold;">Liên hệ chúng tôi</span>
            </div>
        </div>

        <a
            class="js-go-to go-to position-fixed"
            href="javascript:;"
            style="visibility: hidden;"
            data-hs-go-to-options='{
                "offsetTop": 700,
                "position": {
                    "init": {
                    "right": "2rem"
                    },
                    "show": {
                    "bottom": "2rem"
                    },
                    "hide": {
                    "bottom": "-2rem"
                    }
                }
                }'
        >
            <i class="bi-chevron-up"></i>
        </a>

        <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
        <script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
        <script src="../assets/vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js"></script>
        <script src="../assets/vendor/list.js/dist/list.min.js"></script>
        <script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>
        <script src="../assets/js/theme.min.js"></script>
        <script src="../assets/js/contact-box.js"></script>
        <script src="../assets/highlight/highlight.min.js"></script>

        <script>
            hljs.highlightAll();

            (function () {
                new HSHeader("#header").init();

                new HsNavScroller(".js-nav-scroller", {
                    delay: 400,
                    offset: 140,
                });

                const docsSearch = HSCore.components.HSList.init("#docsSearch");

                new HSGoTo(".js-go-to");
            })();

            function setCookie(cname, cvalue, exdays) {
                var d = new Date();
                d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000);
                var expires = "expires=" + d.toUTCString();
                document.cookie = cname + "=" + cvalue + "; " + expires + "; path=/; domain=.sepay.vn";
            }

            let urlParams = new URLSearchParams(document.location.search);

            ["utm_source", "utm_medium", "utm_campaign", "utm_term", "utm_content"].forEach(function (param) {
                let value = urlParams.get(param);

                if (value) {
                    setCookie(param, value, 90);
                }
            });

            if (document.referrer != "" && document.cookie.indexOf("referer") === -1) {
                setCookie("referer", document.referrer, 90);
            }
        </script>
    </body>
</html>
