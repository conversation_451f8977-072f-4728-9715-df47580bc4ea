<!DOCTYPE html>
<html lang="vi">
    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <title>OAuth2 API Tài khoản ngân hàng - SePay</title>
        <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet" />
        <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
        <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
        <link rel="stylesheet" href="../assets/vendor/bootstrap-icons/font/bootstrap-icons.css" />
        <link rel="stylesheet" href="../assets/css/theme.min.css" />
        <link rel="stylesheet" href="../assets/css/docs.min.css" />
        <link rel="stylesheet" href="../assets/css/contact-box.css" />
        <link rel="stylesheet" href="../assets/highlight/styles/vs2015.min.css" />
        <style>
            .docs-navbar-sidebar-aside-body {
                padding-top: 3.5rem !important;
            }
            pre {
                border-radius: 0.5rem;
            }
        </style>
        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                dataLayer.push(arguments);
            }
            gtag("js", new Date());

            gtag("config", "G-J8DLMQTKSQ");
        </script>
    </head>

    <body class="navbar-sidebar-aside-lg">
        <header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
            <div class="container-fluid">
                <nav class="navbar-nav-wrap">
                    <div class="row flex-grow-1">
                        <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
                            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                                <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo" />
                            </a>
                        </div>

                        <div class="col-md px-lg-0">
                            <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
                                <div class="d-none d-md-block">
                                    <div class="d-none">
                                        <div id="searchTemplate" class="dropdown-item">
                                            <a class="d-block link" href="#">
                                                <span class="category d-block fw-normal text-muted mb-1"></span>
                                                <span class="component text-dark"></span>
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <ul class="navbar-nav p-0">
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html" target="_blank"> Support <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN" target="_blank"> Youtube <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://www.facebook.com/messages/t/sepay.vn" target="_blank"> Facebook <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel" target="_blank"> Telegram <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </nav>
            </div>
        </header>
        <main id="content" role="main">
            <nav
                class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
                data-hs-nav-scroller-options='{
                    "type": "vertical",
                    "target": ".navbar-nav .active",
                    "offset": 80
                }'
            >
                <button
                    type="button"
                    class="navbar-toggler btn btn-white d-grid w-100"
                    data-bs-toggle="collapse"
                    data-bs-target="#navbarVerticalNavMenu"
                    aria-label="Toggle navigation"
                    aria-expanded="false"
                    aria-controls="navbarVerticalNavMenu"
                >
                    <span class="d-flex justify-content-between align-items-center">
                        <span class="h3 mb-0">Nav menu</span>

                        <span class="navbar-toggler-default">
                            <i class="bi-list"></i>
                        </span>

                        <span class="navbar-toggler-toggled">
                            <i class="bi-x"></i>
                        </span>
                    </span>
                </button>

                <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
                    <div class="navbar-brand-wrapper border-end" style="height: auto;">
                        <div class="d-flex align-items-center mb-3">
                            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                                <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo" />
                            </a>
                        </div>
                    </div>

                    <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
                        <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
                            <li class="nav-item">
                                <span class="nav-subtitle">SePay OAuth2</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/">Tổng quan</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/dang-ky-ung-dung.html">Đăng ký ứng dụng</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/luong-xac-thuc.html">Luồng xác thực</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/access-token.html">Access Token</a>
                            </li>

                            <li class="nav-item my-2 my-lg-5"></li>

                            <li class="nav-item">
                                <span class="nav-subtitle">API</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link active" href="/oauth2/api-tai-khoan-ngan-hang.html">Tài khoản ngân hàng</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-giao-dich.html">Giao dịch</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-webhooks.html">Webhook</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-nguoi-dung.html">Người dùng</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-cong-ty.html">Công ty</a>
                            </li>

                        </ul>
                    </div>
                </div>
            </nav>

            <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">
                <div style="max-width: 1200px;">
                    <div class="docs-page-header">
                        <div class="row align-items-center">
                            <div class="col-sm">
                                <h1 class="docs-page-header-title">API Tài khoản ngân hàng</h1>
                                <p class="docs-page-header-text">Tài liệu về cách sử dụng API tài khoản ngân hàng thông qua OAuth2 trong SePay.</p>
                            </div>
                        </div>
                    </div>
                    
                    <h3 id="gioi-thieu" class="hs-docs-heading">
                        Giới thiệu <a class="anchorjs-link" href="#gioi-thieu" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>API tài khoản ngân hàng của SePay cho phép bạn truy vấn thông tin chi tiết về các tài khoản ngân hàng đã được thêm vào SePay. Bạn có thể lấy danh sách tài khoản, xem chi tiết từng tài khoản và tài khoản phụ liên kết với nó.</p>
                    
                    <div class="alert alert-soft-primary" role="alert">
                        <div class="d-flex align-items-baseline">
                            <div class="flex-shrink-0">
                                <i class="bi-info-circle me-1"></i>
                            </div>
                            <div class="flex-grow-1 ms-2">
                                Để sử dụng API này, bạn cần có quyền <code>bank-account:read</code> trong phạm vi (scope) của Access Token.
                            </div>
                        </div>
                    </div>
                    
                    <h3 id="endpoints" class="hs-docs-heading">
                        Các Endpoints <a class="anchorjs-link" href="#endpoints" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>API tài khoản ngân hàng cung cấp các endpoints sau:</p>

                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Method</th>
                                    <th>Endpoint</th>
                                    <th>Mô tả</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>GET</td>
                                    <td><code>/api/v1/bank-accounts</code></td>
                                    <td>Lấy danh sách tất cả tài khoản ngân hàng</td>
                                </tr>
                                <tr>
                                    <td>GET</td>
                                    <td><code>/api/v1/bank-accounts/{id}</code></td>
                                    <td>Lấy thông tin chi tiết một tài khoản ngân hàng</td>
                                </tr>
                                <tr>
                                    <td>GET</td>
                                    <td><code>/api/v1/bank-accounts/{id}/sub-accounts</code></td>
                                    <td>Lấy danh sách tài khoản phụ của một tài khoản ngân hàng</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <h3 id="danh-sach-tai-khoan" class="hs-docs-heading">
                        Lấy danh sách tài khoản ngân hàng <a class="anchorjs-link" href="#danh-sach-tai-khoan" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p><span class="badge bg-primary">GET</span> <code>/api/v1/bank-accounts</code></p>
                    
                    <p>Endpoint này trả về danh sách tài khoản ngân hàng thuộc công ty của bạn.</p>
                    
                    <h4>Quyền yêu cầu</h4>
                    <ul>
                        <li>Scope: <code>bank-account:read</code></li>
                        <li>Quyền người dùng: <strong>Tài khoản ngân hàng</strong> (<code>Xem danh sách tài khoản</code>)</li>
                    </ul>
                    
                    <h4>Tham số truy vấn</h4>
                    
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                <th>Tham số</th>
                                <th>Kiểu</th>
                                <th>Mô tả</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>page</code></td>
                                <td>integer</td>
                                <td>Số trang, bắt đầu từ 1</td>
                            </tr>
                            <tr>
                                <td><code>limit</code></td>
                                <td>integer</td>
                                <td>Số lượng kết quả trên mỗi trang</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <h4>Yêu cầu</h4>
                    
                    <pre><code class="language-http">GET /api/v1/bank-accounts
Authorization: Bearer {YOUR_ACCESS_TOKEN}</code></pre>
                    
                    <h4>Phản hồi</h4>
                    
                    <pre style="max-height: 35rem;"><code class="language-json">{
    "status": "success",
    "data": [
        {
            "id": 19,
            "label": "Cty Demo",
            "account_holder_name": "CONG TY TNHH DEMO",
            "account_number": "*************",
            "accumulated": **********.00,
            "active": true,
            "created_at": "2025-02-12 21:09:49",
            "bank": {
                "short_name": "Vietcombank",
                "full_name": "Ngân hàng TMCP Ngoại Thương Việt Nam",
                "code": "VCB",
                "bin": "970436",
                "icon_url": "https://my.sepay.vn/assets/images/banklogo/vietcombank-icon.png",
                "logo_url": "https://my.sepay.vn/assets/images/banklogo/vietcombank.png"
            }
        },
        {
            "id": 18,
            "label": null,
            "account_holder_name": "NGUYEN VAN A",
            "account_number": "*************",
            "accumulated": **********.00,
            "active": true,
            "created_at": "2025-02-12 20:05:47",
            "bank": {
                "short_name": "Vietcombank",
                "full_name": "Ngân hàng TMCP Ngoại Thương Việt Nam",
                "code": "VCB",
                "bin": "970436",
                "icon_url": "https://my.sepay.vn/assets/images/banklogo/vietcombank-icon.png",
                "logo_url": "https://my.sepay.vn/assets/images/banklogo/vietcombank.png"
            }
        }
    ],
    "meta": {
        "pagination": {
            "total": 2,
            "per_page": 20,
            "current_page": 1,
            "last_page": 1
        }
    }
}</code></pre>
                    
                    <h3 id="chi-tiet-tai-khoan" class="hs-docs-heading">
                        Lấy chi tiết tài khoản ngân hàng <a class="anchorjs-link" href="#chi-tiet-tai-khoan" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p><span class="badge bg-primary">GET</span> <code>/api/v1/bank-accounts/{id}</code></p>
                    
                    <p>Endpoint này trả về thông tin chi tiết của một tài khoản ngân hàng dựa trên ID.</p>
                    
                    <h4>Quyền yêu cầu</h4>
                    <ul>
                        <li>Scope: <code>bank-account:read</code></li>
                        <li>Quyền người dùng: <strong>Tài khoản ngân hàng</strong> (<code>Xem chi tiết tài khoản</code>)</li>
                    </ul>
                    
                    <h4>Tham số đường dẫn</h4>
                    
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Tham số</th>
                                    <th>Kiểu</th>
                                    <th>Mô tả</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>id</code></td>
                                    <td>integer</td>
                                    <td>ID của tài khoản ngân hàng</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <h4>Yêu cầu</h4>
                    
                    <pre><code class="language-http">GET /api/v1/bank-accounts/19
Authorization: Bearer {YOUR_ACCESS_TOKEN}</code></pre>
                    
                    <h4>Phản hồi</h4>
                    
                    <pre><code class="language-json">{
    "status": "success",
    "data": {
        "id": 19,
        "label": "Cty Demo",
        "account_holder_name": "CONG TY TNHH DEMO",
        "account_number": "*************",
        "accumulated": **********.00,
        "active": true,
        "created_at": "2025-02-12 21:09:49",
        "bank": {
            "short_name": "Vietcombank",
            "full_name": "Ngân hàng TMCP Ngoại Thương Việt Nam",
            "code": "VCB",
            "bin": "970436",
            "icon_url": "https://my.sepay.vn/assets/images/banklogo/vietcombank-icon.png",
            "logo_url": "https://my.sepay.vn/assets/images/banklogo/vietcombank.png"
        }
    }
}</code></pre>

                    <h3 id="danh-sach-tai-khoan-phu" class="hs-docs-heading">
                        Lấy danh sách tài khoản phụ <a class="anchorjs-link" href="#danh-sach-tai-khoan-phu" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p><span class="badge bg-primary">GET</span> <code>/api/v1/bank-accounts/{id}/sub-accounts</code></p>
                    
                    <p>Endpoint này trả về danh sách tài khoản phụ của một tài khoản ngân hàng.</p>
                    
                    <h4>Quyền yêu cầu</h4>
                    <ul>
                        <li>Scope: <code>bank-account:read</code></li>
                        <li>Quyền người dùng: <strong>Tài khoản ngân hàng</strong> (<code>Xem danh sách tài khoản</code>)</li>
                    </ul>
                    
                    <h4>Tham số đường dẫn</h4>
                    
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                <th>Tham số</th>
                                <th>Kiểu</th>
                                <th>Mô tả</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>id</code></td>
                                <td>integer</td>
                                <td>ID của tài khoản ngân hàng</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <h4>Tham số truy vấn</h4>
                    
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Tham số</th>
                                    <th>Kiểu</th>
                                    <th>Mô tả</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>page</code></td>
                                    <td>integer</td>
                                    <td>Số trang, bắt đầu từ 1</td>
                                </tr>
                                <tr>
                                    <td><code>limit</code></td>
                                    <td>integer</td>
                                    <td>Số lượng kết quả trên mỗi trang</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <h4>Yêu cầu</h4>
                    
                    <pre><code class="language-http">GET /api/v1/bank-accounts/19/sub-accounts
Authorization: Bearer {YOUR_ACCESS_TOKEN}</code></pre>
                    
                    <h4>Phản hồi</h4>
                    
                    <pre><code class="language-json">{
    "status": "success",
    "data": [
        {
            "id": 25,
            "bank_account_id": 19,
            "account_number": "VCB0011ABC001",
            "account_holder_name": "Chi nhánh 1",
            "label": "CN1",
            "acc_type": "Virtual",
            "active": true,
            "va_active": true,
            "created_at": "2025-02-20 09:15:32",
            "updated_at": "2025-02-20 09:15:32"
        },
        {
            "id": 26,
            "bank_account_id": 19,
            "account_number": "VCB0011ABC002",
            "account_holder_name": "Chi nhánh 2",
            "label": "CN2",
            "acc_type": "Virtual",
            "active": true,
            "va_active": true,
            "created_at": "2025-02-20 09:16:05",
            "updated_at": "2025-02-20 09:16:05"
        }
    ],
    "meta": {
        "pagination": {
            "total": 2,
            "per_page": 20,
            "current_page": 1,
            "last_page": 1
        }
    }
}</code></pre>
                    
                    <h3 id="ma-loi" class="hs-docs-heading">
                        Mã lỗi <a class="anchorjs-link" href="#ma-loi" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>Dưới đây là các mã lỗi có thể gặp khi sử dụng API tài khoản ngân hàng:</p>
                    
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                <th>Mã HTTP</th>
                                <th>Mã lỗi</th>
                                <th>Mô tả</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>401</td>
                                <td><code>unauthorized</code></td>
                                <td>Token không hợp lệ hoặc hết hạn</td>
                            </tr>
                            <tr>
                                <td>403</td>
                                <td><code>forbidden</code></td>
                                <td>Không có quyền truy cập vào tài nguyên này</td>
                            </tr>
                            <tr>
                                <td>404</td>
                                <td><code>resource_not_found</code></td>
                                <td>Tài khoản ngân hàng không tồn tại</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <h3 id="vi-du-su-dung" class="hs-docs-heading">
                        Ví dụ sử dụng <a class="anchorjs-link" href="#vi-du-su-dung" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <div class="mb-3">
                        <nav>
                            <div class="nav nav-tabs" id="nav-tab" role="tablist">
                                <button class="nav-link active" id="nav-curl-tab" data-bs-toggle="tab" data-bs-target="#nav-curl" type="button" role="tab" aria-controls="nav-curl" aria-selected="true">cURL</button>
                                <button class="nav-link" id="nav-php-tab" data-bs-toggle="tab" data-bs-target="#nav-php" type="button" role="tab" aria-controls="nav-php" aria-selected="false">PHP</button>
                                <button class="nav-link" id="nav-nodejs-tab" data-bs-toggle="tab" data-bs-target="#nav-nodejs" type="button" role="tab" aria-controls="nav-nodejs" aria-selected="false">Node.js</button>
                            </div>
                        </nav>
                        <div class="tab-content" id="nav-tabContent">
                            <div class="tab-pane fade show active" id="nav-curl" role="tabpanel" aria-labelledby="nav-curl-tab">
                                <pre style="border-top-left-radius: 0;"><code class="language-bash"># Lấy danh sách tài khoản ngân hàng
curl -X GET "https://my.sepay.vn/api/v1/bank-accounts" \
        -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Lấy chi tiết tài khoản ngân hàng
curl -X GET "https://my.sepay.vn/api/v1/bank-accounts/19" \
        -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Lấy danh sách tài khoản phụ
curl -X GET "https://my.sepay.vn/api/v1/bank-accounts/19/sub-accounts" \
        -H "Authorization: Bearer YOUR_ACCESS_TOKEN"</code></pre>
                            </div>
                            <div class="tab-pane fade" id="nav-php" role="tabpanel" aria-labelledby="nav-php-tab">
                                <pre style="border-top-left-radius: 0; max-height: 35rem;"><code class="language-php">&lt;?php

$accessToken = 'YOUR_ACCESS_TOKEN';
$apiUrl = 'https://my.sepay.vn/api/v1/bank-accounts';

// Lấy danh sách tài khoản ngân hàng
function getBankAccounts($apiUrl, $accessToken) {
    $ch = curl_init($apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $accessToken
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        return json_decode($response, true);
    } else {
        return ['error' => $httpCode, 'response' => $response];
    }
}

// Lấy chi tiết tài khoản ngân hàng
function getBankAccountDetail($apiUrl, $accessToken, $accountId) {
    $ch = curl_init($apiUrl . '/' . $accountId);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $accessToken
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        return json_decode($response, true);
    } else {
        return ['error' => $httpCode, 'response' => $response];
    }
}

// Sử dụng
$accounts = getBankAccounts($apiUrl, $accessToken);
$accountDetail = getBankAccountDetail($apiUrl, $accessToken, 19);

print_r($accounts);
print_r($accountDetail);</code></pre>
                            </div>
                            <div class="tab-pane fade" id="nav-nodejs" role="tabpanel" aria-labelledby="nav-nodejs-tab">
                                <pre style="border-top-left-radius: 0; max-height: 35rem;"><code class="language-javascript">const accessToken = 'YOUR_ACCESS_TOKEN';
const apiUrl = 'https://my.sepay.vn/api/v1/bank-accounts';

// Lấy danh sách tài khoản ngân hàng
async function getBankAccounts() {
  try {
    const response = await fetch(apiUrl, {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    
    const data = await response.json();
    console.log(data);
    return data;
  } catch (error) {
    console.error('Error:', error.message);
    throw error;
  }
}

// Lấy chi tiết tài khoản ngân hàng
async function getBankAccountDetail(accountId) {
  try {
    const response = await fetch(`${apiUrl}/${accountId}`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    
    const data = await response.json();
    console.log(data);
    return data;
  } catch (error) {
    console.error('Error:', error.message);
    throw error;
  }
}

// Lấy danh sách tài khoản phụ
async function getSubAccounts(accountId) {
  try {
    const response = await fetch(`${apiUrl}/${accountId}/sub-accounts`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    
    const data = await response.json();
    console.log(data);
    return data;
  } catch (error) {
    console.error('Error:', error.message);
    throw error;
  }
}

// Sử dụng
getBankAccounts();
getBankAccountDetail(19);
getSubAccounts(19);</code></pre>
                            </div>
                        </div>
                    </div>
                    
                    <h3 id="buoc-tiep-theo" class="hs-docs-heading">
                        Bước tiếp theo <a class="anchorjs-link" href="#buoc-tiep-theo" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>Tiếp theo, hãy tìm hiểu về API Giao dịch để lấy thông tin về các giao dịch liên quan đến tài khoản ngân hàng.</p>
                    
                    <div class="row g-3">
                        <div class="col-md-6">
                            <a class="card card-sm card-transition h-100" href="/oauth2/access-token.html">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <i class="bi-arrow-left fs-2"></i>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h5 class="fs-4 card-title">Access Token</h5>
                                            <p class="card-text text-body small">Quay lại trang Access Token</p>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a class="card card-sm card-transition h-100" href="/oauth2/api-giao-dich.html">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1 me-3">
                                            <h5 class="fs-4 card-title">API Giao dịch</h5>
                                            <p class="card-text text-body small">Tìm hiểu về API giao dịch</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="bi-arrow-right fs-2"></i>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <div id="contact-box-overlay"></div>
        <div id="contact-box">
            <div class="popup">
                <a href="https://m.me/117903214582465" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/fb-messenger.png" width="50%" />
                    </div>
                    <div class="meta">
                        <p class="title">Facebook Messenger</p>
                        <small class="description">Hỗ trợ live chat 24/7</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="tel:02873059589" class="item">
                    <div class="icon" style="background-color: #22c55e; color: #fff;">
                        <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;">
                            <path
                                d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z"
                                stroke="currentColor"
                                stroke-width="1.5"
                                stroke-linecap="round"
                                fill="transparent"
                            ></path>
                        </svg>
                    </div>
                    <div class="meta">
                        <p class="title">Hotline</p>
                        <small class="description">Điện thoại hỗ trợ</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/youtube-social.png" />
                    </div>
                    <div class="meta">
                        <p class="title">Youtube</p>
                        <small class="description">Theo dõi video mới nhất của SePay</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/telegram-social.png" />
                    </div>
                    <div class="meta">
                        <p class="title">Telegram</p>
                        <small class="description">Nhận thông tin mới nhất từ SePay</small>
                    </div>
                </a>
            </div>
            <div class="container">
                <div class="dot-ping">
                    <div class="ping"></div>
                    <div class="dot"></div>
                </div>
                <div class="contact-icon">
                    <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;">
                        <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z"
                            fill="currentColor"
                        ></path>
                    </svg>
                </div>
                <span style="font-weight: bold;">Liên hệ chúng tôi</span>
            </div>
        </div>

        <a
            class="js-go-to go-to position-fixed"
            href="javascript:;"
            style="visibility: hidden;"
            data-hs-go-to-options='{
                "offsetTop": 700,
                "position": {
                    "init": {
                    "right": "2rem"
                    },
                    "show": {
                    "bottom": "2rem"
                    },
                    "hide": {
                    "bottom": "-2rem"
                    }
                }
                }'
        >
            <i class="bi-chevron-up"></i>
        </a>

        <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
        <script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
        <script src="../assets/vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js"></script>
        <script src="../assets/vendor/list.js/dist/list.min.js"></script>
        <script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>
        <script src="../assets/js/theme.min.js"></script>
        <script src="../assets/js/contact-box.js"></script>
        <script src="../assets/highlight/highlight.min.js"></script>

        <script>
            hljs.highlightAll();

            (function () {
                new HSHeader("#header").init();

                new HsNavScroller(".js-nav-scroller", {
                    delay: 400,
                    offset: 140,
                });

                const docsSearch = HSCore.components.HSList.init("#docsSearch");

                new HSGoTo(".js-go-to");
            })();

            function setCookie(cname, cvalue, exdays) {
                var d = new Date();
                d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000);
                var expires = "expires=" + d.toUTCString();
                document.cookie = cname + "=" + cvalue + "; " + expires + "; path=/; domain=.sepay.vn";
            }

            let urlParams = new URLSearchParams(document.location.search);

            ["utm_source", "utm_medium", "utm_campaign", "utm_term", "utm_content"].forEach(function (param) {
                let value = urlParams.get(param);

                if (value) {
                    setCookie(param, value, 90);
                }
            });

            if (document.referrer != "" && document.cookie.indexOf("referer") === -1) {
                setCookie("referer", document.referrer, 90);
            }
        </script>
    </body>
</html>
