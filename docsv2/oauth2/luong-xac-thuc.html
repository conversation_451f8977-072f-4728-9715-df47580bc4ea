<!DOCTYPE html>
<html lang="vi">
    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <title>OAuth2 Luồng ứng dụng - SePay</title>
        <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet" />
        <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
        <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
        <link rel="stylesheet" href="../assets/vendor/bootstrap-icons/font/bootstrap-icons.css" />
        <link rel="stylesheet" href="../assets/css/theme.min.css" />
        <link rel="stylesheet" href="../assets/css/docs.min.css" />
        <link rel="stylesheet" href="../assets/css/contact-box.css" />
        <link rel="stylesheet" href="../assets/highlight/styles/vs2015.min.css" />
        <style>
            .docs-navbar-sidebar-aside-body {
                padding-top: 3.5rem !important;
            }
            pre {
                border-radius: 0.5rem;
            }
        </style>
        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                dataLayer.push(arguments);
            }
            gtag("js", new Date());

            gtag("config", "G-J8DLMQTKSQ");
        </script>
    </head>

    <body class="navbar-sidebar-aside-lg">
        <header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
            <div class="container-fluid">
                <nav class="navbar-nav-wrap">
                    <div class="row flex-grow-1">
                        <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
                            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                                <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo" />
                            </a>
                        </div>

                        <div class="col-md px-lg-0">
                            <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
                                <div class="d-none d-md-block">
                                    <div class="d-none">
                    <div id="searchTemplate" class="dropdown-item">
                        <a class="d-block link" href="#">
                            <span class="category d-block fw-normal text-muted mb-1"></span>
                            <span class="component text-dark"></span>
                        </a>
                    </div>
                                    </div>
                                </div>

                                <ul class="navbar-nav p-0">
                                    <li class="nav-item">
                    <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html" target="_blank"> Support <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                    <a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN" target="_blank"> Youtube <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                    <a class="btn btn-ghost-secondary btn-sm" href="https://www.facebook.com/messages/t/sepay.vn" target="_blank"> Facebook <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                    <a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel" target="_blank"> Telegram <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </nav>
            </div>
        </header>
        <main id="content" role="main">
            <nav
                class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
                data-hs-nav-scroller-options='{
                    "type": "vertical",
                    "target": ".navbar-nav .active",
                    "offset": 80
                }'
            >
                <button
                    type="button"
                    class="navbar-toggler btn btn-white d-grid w-100"
                    data-bs-toggle="collapse"
                    data-bs-target="#navbarVerticalNavMenu"
                    aria-label="Toggle navigation"
                    aria-expanded="false"
                    aria-controls="navbarVerticalNavMenu"
                >
                    <span class="d-flex justify-content-between align-items-center">
                        <span class="h3 mb-0">Nav menu</span>

                        <span class="navbar-toggler-default">
                            <i class="bi-list"></i>
                        </span>

                        <span class="navbar-toggler-toggled">
                            <i class="bi-x"></i>
                        </span>
                    </span>
                </button>

                <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
                    <div class="navbar-brand-wrapper border-end" style="height: auto;">
                        <div class="d-flex align-items-center mb-3">
                            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                                <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo" />
                            </a>
                        </div>
                    </div>

                    <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
                        <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
                            <li class="nav-item">
                                <span class="nav-subtitle">SePay OAuth2</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/">Tổng quan</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/dang-ky-ung-dung.html">Đăng ký ứng dụng</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link active" href="/oauth2/luong-xac-thuc.html">Luồng xác thực</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/access-token.html">Access Token</a>
                            </li>

                            <li class="nav-item my-2 my-lg-5"></li>

                            <li class="nav-item">
                                <span class="nav-subtitle">API</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-tai-khoan-ngan-hang.html">Tài khoản ngân hàng</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-giao-dich.html">Giao dịch</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-webhooks.html">Webhook</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-nguoi-dung.html">Người dùng</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-cong-ty.html">Công ty</a>
                            </li>

                        </ul>
                    </div>
                </div>
            </nav>

            <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">
                <div style="max-width: 1200px;">
                    <div class="docs-page-header">
                        <div class="row align-items-center">
                            <div class="col-sm">
                                <h1 class="docs-page-header-title">Luồng xác thực OAuth2</h1>
                                <p class="docs-page-header-text">Tìm hiểu chi tiết về luồng xác thực OAuth2 và cách triển khai trong ứng dụng của bạn.</p>
                            </div>
                        </div>
                    </div>
                    
                    <h3 id="tong-quan-luong" class="hs-docs-heading">
                        Tổng quan luồng xác thực <a class="anchorjs-link" href="#tong-quan-luong" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>SePay triển khai OAuth2 theo luồng Authorization Code, một trong những luồng xác thực phổ biến và an toàn nhất của OAuth2. Luồng này phù hợp cho hầu hết các ứng dụng web và ứng dụng server-side.</p>
                    
                    <div class="mb-4">
                        <img src="../assets/img/oauth2/oauth2-flow.png" class="img-fluid rounded" alt="Luồng xác thực OAuth2">
                    </div>
                    
                    <p>Luồng xác thực OAuth2 trong SePay bao gồm các bước sau:</p>
                    
                    <ol class="lh-lg">
                        <li>Ứng dụng yêu cầu ủy quyền từ người dùng bằng cách chuyển hướng đến URL ủy quyền của SePay</li>
                        <li>Người dùng đăng nhập vào SePay và đồng ý cấp quyền cho ứng dụng</li>
                        <li>SePay chuyển hướng về redirect URI của ứng dụng kèm theo mã ủy quyền (authorization code)</li>
                        <li>Ứng dụng đổi mã ủy quyền lấy access token từ SePay</li>
                        <li>Ứng dụng sử dụng access token để gọi các API của SePay</li>
                    </ol>
                    
                    <h3 id="buoc-1" class="hs-docs-heading">
                        Bước 1: Yêu cầu ủy quyền <a class="anchorjs-link" href="#buoc-1" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>Để bắt đầu quá trình xác thực, chuyển hướng người dùng đến URL ủy quyền của SePay:</p>
                    
                    <pre><code class="language-http">GET https://my.sepay.vn/oauth/authorize?
response_type=code&
client_id=YOUR_CLIENT_ID&
redirect_uri=YOUR_REDIRECT_URI&
scope=bank-account:read transaction:read&
state=RANDOM_STATE_VALUE</code></pre>
                    
                    <p>Các tham số:</p>

                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Tham số</th>
                                    <th>Mô tả</th>
                                    <th>Yêu cầu</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>response_type</code></td>
                                    <td>Phải là <code>code</code> cho luồng Authorization Code</td>
                                    <td>Bắt buộc</td>
                                </tr>
                                <tr>
                                    <td><code>client_id</code></td>
                                    <td>Client ID của ứng dụng bạn, nhận được khi đăng ký ứng dụng</td>
                                    <td>Bắt buộc</td>
                                </tr>
                                <tr>
                                    <td><code>redirect_uri</code></td>
                                    <td>URL nhận mã ủy quyền, phải khớp với URL đã đăng ký</td>
                                    <td>Bắt buộc</td>
                                </tr>
                                <tr>
                                    <td><code>scope</code></td>
                                    <td>Các quyền truy cập yêu cầu, phân tách bằng dấu cách</td>
                                    <td>Tùy chọn (mặc định là tất cả phạm vi đã đăng ký)</td>
                                </tr>
                                <tr>
                                    <td><code>state</code></td>
                                    <td>Giá trị ngẫu nhiên để ngăn tấn công CSRF, sẽ được trả về không thay đổi</td>
                                    <td>Khuyến nghị</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="alert alert-soft-info mb-4" role="alert">
                        <div class="d-flex align-items-baseline">
                            <div class="flex-shrink-0">
                                <i class="bi-shield-check me-1"></i>
                            </div>
                            <div class="flex-grow-1 ms-2">
                                <b>Bảo mật quan trọng</b>: Tham số <code>state</code> nên được sử dụng để bảo vệ khỏi tấn công CSRF. Tạo một giá trị ngẫu nhiên, lưu trong session và xác minh khi nhận callback.
                            </div>
                        </div>
                    </div>
                    
                    <h3 id="buoc-2" class="hs-docs-heading">
                        Bước 2: Người dùng đồng ý cấp quyền <a class="anchorjs-link" href="#buoc-2" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>Sau khi chuyển hướng đến URL ủy quyền, người dùng sẽ thấy màn hình đăng nhập SePay (nếu chưa đăng nhập) và sau đó là màn hình xác nhận cấp quyền:</p>
                    
                    <div class="mb-4">
                        <img src="../assets/img/oauth2/authorize-screen.png" class="img-fluid rounded" alt="Màn hình cấp quyền OAuth2">
                    </div>
                    
                    <p>Màn hình này hiển thị:</p>
                    <ul class="lh-lg">
                        <li>Tên ứng dụng yêu cầu truy cập</li>
                        <li>Các quyền mà ứng dụng yêu cầu</li>
                        <li>Nút đồng ý hoặc từ chối cấp quyền</li>
                    </ul>
                    
                    <h3 id="buoc-3" class="hs-docs-heading">
                        Bước 3: Nhận mã ủy quyền <a class="anchorjs-link" href="#buoc-3" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>Sau khi người dùng đồng ý cấp quyền, SePay sẽ chuyển hướng về <code>redirect_uri</code> của bạn kèm theo mã ủy quyền:</p>
                    
                    <pre><code class="language-http">GET https://your-app.com/callback?code=AUTHORIZATION_CODE&state=RANDOM_STATE_VALUE</code></pre>
                    
                    <p>Ứng dụng của bạn cần:</p>
                    <ol class="lh-lg">
                        <li>Xác minh tham số <code>state</code> khớp với giá trị đã gửi trước đó</li>
                        <li>Lấy mã ủy quyền từ tham số <code>code</code></li>
                    </ol>
                    
                    <div class="alert alert-warning" role="alert">
                        <div class="d-flex align-items-baseline">
                            <div class="flex-shrink-0">
                                <i class="bi-exclamation-circle me-1"></i>
                            </div>
                            <div class="flex-grow-1 ms-2">
                                <b>Lưu ý</b>: Mã ủy quyền chỉ có hiệu lực trong một thời gian ngắn (thường là 5 phút) và chỉ có thể sử dụng một lần. Bạn cần đổi nó lấy access token ngay sau khi nhận được.
                            </div>
                        </div>
                    </div>
                    
                    <h3 id="buoc-4" class="hs-docs-heading">
                        Bước 4: Đổi mã ủy quyền lấy access token <a class="anchorjs-link" href="#buoc-4" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>Sau khi nhận được mã ủy quyền, bạn cần đổi nó lấy access token bằng cách gửi yêu cầu POST đến endpoint token của SePay:</p>
                    
                    <pre><code class="language-http">POST https://my.sepay.vn/oauth/token
Content-Type: application/x-www-form-urlencoded

grant_type=authorization_code&
code=AUTHORIZATION_CODE&
redirect_uri=YOUR_REDIRECT_URI&
client_id=YOUR_CLIENT_ID&
client_secret=YOUR_CLIENT_SECRET</code></pre>
                    
                    <p>Các tham số:</p>
                    
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Tham số</th>
                                    <th>Mô tả</th>
                                    <th>Yêu cầu</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>grant_type</code></td>
                                    <td>Phải là <code>authorization_code</code></td>
                                    <td>Bắt buộc</td>
                                </tr>
                                <tr>
                                    <td><code>code</code></td>
                                    <td>Mã ủy quyền nhận được từ bước trước</td>
                                    <td>Bắt buộc</td>
                                </tr>
                                <tr>
                                    <td><code>redirect_uri</code></td>
                                    <td>URL chuyển hướng giống với URL đã sử dụng ở bước 1</td>
                                    <td>Bắt buộc</td>
                                </tr>
                                <tr>
                                    <td><code>client_id</code></td>
                                    <td>Client ID của ứng dụng bạn</td>
                                    <td>Bắt buộc</td>
                                </tr>
                                <tr>
                                    <td><code>client_secret</code></td>
                                    <td>Client Secret của ứng dụng bạn</td>
                                    <td>Bắt buộc</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <p>Phản hồi từ server nếu thành công:</p>
                    
                    <pre><code class="language-json">{
    "access_token": "ACCESS_TOKEN",
    "token_type": "Bearer",
    "expires_in": 3600,
    "refresh_token": "REFRESH_TOKEN"
}</code></pre>
                    
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Trường</th>
                                    <th>Mô tả</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>access_token</code></td>
                                    <td>Token dùng để gọi API SePay</td>
                                </tr>
                                <tr>
                                    <td><code>token_type</code></td>
                                    <td>Loại token, luôn là "Bearer"</td>
                                </tr>
                                <tr>
                                    <td><code>expires_in</code></td>
                                    <td>Thời gian hiệu lực của token (tính bằng giây)</td>
                                </tr>
                                <tr>
                                    <td><code>refresh_token</code></td>
                                    <td>Token dùng để lấy access token mới khi hết hạn</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="alert alert-soft-danger" role="alert">
                        <div class="d-flex align-items-baseline">
                            <div class="flex-shrink-0">
                                <i class="bi-exclamation-triangle me-1"></i>
                            </div>
                            <div class="flex-grow-1 ms-2">
                                <b>Quan trọng</b>: Yêu cầu đổi token này phải được thực hiện từ phía máy chủ, không bao giờ thực hiện từ phía client vì cần sử dụng client_secret.
                            </div>
                        </div>
                    </div>
                    
                    <h3 id="buoc-5" class="hs-docs-heading">
                        Bước 5: Sử dụng access token để gọi API <a class="anchorjs-link" href="#buoc-5" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>Sau khi nhận được access token, bạn có thể sử dụng nó để gọi các API của SePay bằng cách thêm vào header <code>Authorization</code>:</p>
                    
                    <pre><code class="language-http">GET https://my.sepay.vn/api/v1/bank-accounts
Authorization: Bearer ACCESS_TOKEN</code></pre>
                    
                    <p>Dưới đây là ví dụ sử dụng cURL:</p>
                    
                    <pre><code class="language-bash">curl -H "Authorization: Bearer ACCESS_TOKEN" https://my.sepay.vn/api/v1/bank-accounts</code></pre>
                    
                    <h3 id="buoc-6" class="hs-docs-heading">
                        Bước 6: Làm mới access token <a class="anchorjs-link" href="#buoc-6" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>Access token chỉ có hiệu lực trong một khoảng thời gian giới hạn (thường là 1 giờ). Khi access token hết hạn, bạn cần sử dụng refresh token để lấy token mới mà không yêu cầu người dùng xác thực lại:</p>
                    
                    <pre><code class="language-http">POST https://my.sepay.vn/oauth/token
Content-Type: application/x-www-form-urlencoded

grant_type=refresh_token&
refresh_token=REFRESH_TOKEN&
client_id=YOUR_CLIENT_ID&
client_secret=YOUR_CLIENT_SECRET</code></pre>
                    
                    <p>Các tham số:</p>
                    
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Tham số</th>
                                    <th>Mô tả</th>
                                    <th>Yêu cầu</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>grant_type</code></td>
                                    <td>Phải là <code>refresh_token</code></td>
                                    <td>Bắt buộc</td>
                                </tr>
                                <tr>
                                    <td><code>refresh_token</code></td>
                                    <td>Refresh token nhận được khi lấy access token</td>
                                    <td>Bắt buộc</td>
                                </tr>
                                <tr>
                                    <td><code>client_id</code></td>
                                    <td>Client ID của ứng dụng bạn</td>
                                    <td>Bắt buộc</td>
                                </tr>
                                <tr>
                                    <td><code>client_secret</code></td>
                                    <td>Client Secret của ứng dụng bạn</td>
                                    <td>Bắt buộc</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <p>Phản hồi tương tự như khi đổi mã ủy quyền, bao gồm access token mới, refresh token mới và thời gian hết hạn.</p>
                    
                    <div class="alert alert-soft-info" role="alert">
                        <div class="d-flex align-items-baseline">
                            <div class="flex-shrink-0">
                                <i class="bi-info-circle me-1"></i>
                            </div>
                            <div class="flex-grow-1 ms-2">
                                <b>Mẹo</b>: Nên lưu trữ refresh token an toàn và thực hiện làm mới token tự động khi access token gần hết hạn hoặc khi API trả về lỗi 401 Unauthorized.
                            </div>
                        </div>
                    </div>
                    
                    <h3 id="xu-ly-loi" class="hs-docs-heading">
                        Xử lý lỗi <a class="anchorjs-link" href="#xu-ly-loi" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>Khi có lỗi xảy ra trong quá trình xác thực, SePay sẽ trả về mã lỗi tương ứng. Dưới đây là một số lỗi phổ biến:</p>
                    
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Mã lỗi</th>
                                    <th>Mô tả</th>
                                    <th>Giải pháp</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>invalid_request</code></td>
                                    <td>Yêu cầu thiếu tham số bắt buộc hoặc chứa tham số không hợp lệ</td>
                                    <td>Kiểm tra lại tất cả tham số trong yêu cầu</td>
                                </tr>
                                <tr>
                                    <td><code>invalid_client</code></td>
                                    <td>Xác thực client thất bại</td>
                                    <td>Kiểm tra lại client_id và client_secret</td>
                                </tr>
                                <tr>
                                    <td><code>invalid_grant</code></td>
                                    <td>Mã ủy quyền hoặc refresh token không hợp lệ hoặc đã hết hạn</td>
                                    <td>Yêu cầu người dùng xác thực lại hoặc kiểm tra refresh token</td>
                                </tr>
                                <tr>
                                    <td><code>unauthorized_client</code></td>
                                    <td>Client không được phép yêu cầu authorization code</td>
                                    <td>Kiểm tra lại cấu hình ứng dụng</td>
                                </tr>
                                <tr>
                                    <td><code>access_denied</code></td>
                                    <td>Người dùng từ chối cấp quyền</td>
                                    <td>Thông báo cho người dùng rằng họ cần cấp quyền để sử dụng ứng dụng</td>
                                </tr>
                                <tr>
                                    <td><code>unsupported_grant_type</code></td>
                                    <td>Server không hỗ trợ kiểu grant được yêu cầu</td>
                                    <td>Kiểm tra tham số grant_type</td>
                                </tr>
                                <tr>
                                    <td><code>invalid_scope</code></td>
                                    <td>Phạm vi yêu cầu không hợp lệ, không được nhận diện hoặc vượt quá phạm vi đã đăng ký</td>
                                    <td>Kiểm tra lại phạm vi yêu cầu</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <p>Phản hồi lỗi có định dạng:</p>
                    
                    <pre><code class="language-json">{
    "error": "invalid_grant",
    "error_description": "Authorization code expired"
}</code></pre>

                    <h3 id="buoc-tiep-theo" class="hs-docs-heading">
                        Bước tiếp theo <a class="anchorjs-link" href="#buoc-tiep-theo" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>Sau khi hiểu rõ về luồng xác thực OAuth2, bạn đã sẵn sàng để triển khai trong ứng dụng của mình. Tiếp theo, hãy tìm hiểu về cách sử dụng Access Token với các API của SePay.</p>
                    
                    <div class="row g-3">
                        <div class="col-md-6">
                            <a class="card card-sm card-transition h-100" href="/oauth2/dang-ky-ung-dung.html">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <i class="bi-arrow-left fs-2"></i>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h5 class="fs-4 card-title">Đăng ký ứng dụng</h5>
                                            <p class="card-text text-body small">Quay lại cách đăng ký ứng dụng OAuth2</p>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a class="card card-sm card-transition h-100" href="/oauth2/access-token.html">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1 me-3">
                                            <h5 class="fs-4 card-title">Access Token</h5>
                                            <p class="card-text text-body small">Tìm hiểu cách sử dụng Access Token</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="bi-arrow-right fs-2"></i>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <div id="contact-box-overlay"></div>
        <div id="contact-box">
            <div class="popup">
                <a href="https://m.me/117903214582465" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/fb-messenger.png" width="50%" />
                    </div>
                    <div class="meta">
                        <p class="title">Facebook Messenger</p>
                        <small class="description">Hỗ trợ live chat 24/7</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="tel:02873059589" class="item">
                    <div class="icon" style="background-color: #22c55e; color: #fff;">
                        <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;">
                            <path
                                d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z"
                                stroke="currentColor"
                                stroke-width="1.5"
                                stroke-linecap="round"
                                fill="transparent"
                            ></path>
                        </svg>
                    </div>
                    <div class="meta">
                        <p class="title">Hotline</p>
                        <small class="description">Điện thoại hỗ trợ</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/youtube-social.png" />
                    </div>
                    <div class="meta">
                        <p class="title">Youtube</p>
                        <small class="description">Theo dõi video mới nhất của SePay</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/telegram-social.png" />
                    </div>
                    <div class="meta">
                        <p class="title">Telegram</p>
                        <small class="description">Nhận thông tin mới nhất từ SePay</small>
                    </div>
                </a>
            </div>
            <div class="container">
                <div class="dot-ping">
                    <div class="ping"></div>
                    <div class="dot"></div>
                </div>
                <div class="contact-icon">
                    <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;">
                        <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z"
                            fill="currentColor"
                        ></path>
                    </svg>
                </div>
                <span style="font-weight: bold;">Liên hệ chúng tôi</span>
            </div>
        </div>

        <a
            class="js-go-to go-to position-fixed"
            href="javascript:;"
            style="visibility: hidden;"
            data-hs-go-to-options='{
                "offsetTop": 700,
                "position": {
                    "init": {
                    "right": "2rem"
                    },
                    "show": {
                    "bottom": "2rem"
                    },
                    "hide": {
                    "bottom": "-2rem"
                    }
                }
                }'
        >
            <i class="bi-chevron-up"></i>
        </a>

        <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
        <script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
        <script src="../assets/vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js"></script>
        <script src="../assets/vendor/list.js/dist/list.min.js"></script>
        <script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>
        <script src="../assets/js/theme.min.js"></script>
        <script src="../assets/js/contact-box.js"></script>
        <script src="../assets/highlight/highlight.min.js"></script>

        <script>
            hljs.highlightAll();

            (function () {
                new HSHeader("#header").init();

                new HsNavScroller(".js-nav-scroller", {
                    delay: 400,
                    offset: 140,
                });

                const docsSearch = HSCore.components.HSList.init("#docsSearch");

                new HSGoTo(".js-go-to");
            })();

            function setCookie(cname, cvalue, exdays) {
                var d = new Date();
                d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000);
                var expires = "expires=" + d.toUTCString();
                document.cookie = cname + "=" + cvalue + "; " + expires + "; path=/; domain=.sepay.vn";
            }

            let urlParams = new URLSearchParams(document.location.search);

            ["utm_source", "utm_medium", "utm_campaign", "utm_term", "utm_content"].forEach(function (param) {
                let value = urlParams.get(param);

                if (value) {
                    setCookie(param, value, 90);
                }
            });

            if (document.referrer != "" && document.cookie.indexOf("referer") === -1) {
                setCookie("referer", document.referrer, 90);
            }
        </script>
    </body>
</html>
