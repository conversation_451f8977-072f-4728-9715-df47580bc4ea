<!DOCTYPE html>
<html lang="vi">
    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <title>OAuth2 - SePay</title>
        <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet" />
        <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
        <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
        <link rel="stylesheet" href="../assets/vendor/bootstrap-icons/font/bootstrap-icons.css" />
        <link rel="stylesheet" href="../assets/css/theme.min.css" />
        <link rel="stylesheet" href="../assets/css/docs.min.css" />
        <link rel="stylesheet" href="../assets/css/contact-box.css" />
        <link rel="stylesheet" href="../assets/highlight/styles/vs2015.min.css" />
        <style>
            .docs-navbar-sidebar-aside-body {
                padding-top: 3.5rem !important;
            }
            pre {
                border-radius: 0.5rem;
            }
        </style>
        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                dataLayer.push(arguments);
            }
            gtag("js", new Date());

            gtag("config", "G-J8DLMQTKSQ");
        </script>
    </head>

    <body class="navbar-sidebar-aside-lg">
        <header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
            <div class="container-fluid">
                <nav class="navbar-nav-wrap">
                    <div class="row flex-grow-1">
                        <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
                            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                                <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo" />
                            </a>
                        </div>

                        <div class="col-md px-lg-0">
                            <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
                                <div class="d-none d-md-block">
                                    <div class="d-none">
                                        <div id="searchTemplate" class="dropdown-item">
                                            <a class="d-block link" href="#">
                                                <span class="category d-block fw-normal text-muted mb-1"></span>
                                                <span class="component text-dark"></span>
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <ul class="navbar-nav p-0">
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html" target="_blank"> Support <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN" target="_blank"> Youtube <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://www.facebook.com/messages/t/sepay.vn" target="_blank"> Facebook <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel" target="_blank"> Telegram <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </nav>
            </div>
        </header>
        <main id="content" role="main">
            <nav
                class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
                data-hs-nav-scroller-options='{
                    "type": "vertical",
                    "target": ".navbar-nav .active",
                    "offset": 80
                }'
            >
                <button
                    type="button"
                    class="navbar-toggler btn btn-white d-grid w-100"
                    data-bs-toggle="collapse"
                    data-bs-target="#navbarVerticalNavMenu"
                    aria-label="Toggle navigation"
                    aria-expanded="false"
                    aria-controls="navbarVerticalNavMenu"
                >
                    <span class="d-flex justify-content-between align-items-center">
                        <span class="h3 mb-0">Nav menu</span>

                        <span class="navbar-toggler-default">
                            <i class="bi-list"></i>
                        </span>

                        <span class="navbar-toggler-toggled">
                            <i class="bi-x"></i>
                        </span>
                    </span>
                </button>

                <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
                    <div class="navbar-brand-wrapper border-end" style="height: auto;">
                        <div class="d-flex align-items-center mb-3">
                            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                                <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo" />
                            </a>
                        </div>
                    </div>

                    <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
                        <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
                            <li class="nav-item">
                                <span class="nav-subtitle">SePay OAuth2</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link active" href="/oauth2/">Tổng quan</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/dang-ky-ung-dung.html">Đăng ký ứng dụng</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/luong-xac-thuc.html">Luồng xác thực</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/access-token.html">Access Token</a>
                            </li>

                            <li class="nav-item my-2 my-lg-5"></li>

                            <li class="nav-item">
                                <span class="nav-subtitle">API</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-tai-khoan-ngan-hang.html">Tài khoản ngân hàng</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-giao-dich.html">Giao dịch</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-webhooks.html">Webhook</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-nguoi-dung.html">Người dùng</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-cong-ty.html">Công ty</a>
                            </li>

                        </ul>
                    </div>
                </div>
            </nav>

            <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">
                <div style="max-width: 1200px;">
                    <div class="docs-page-header">
                        <div class="row align-items-center">
                            <div class="col-sm">
                                <h1 class="docs-page-header-title">Tổng quan OAuth2</h1>
                                <p class="docs-page-header-text">Tìm hiểu về cơ chế xác thực OAuth2 để tích hợp với SePay API.</p>
                            </div>
                        </div>
                    </div>
    
                    <h3 id="gioi-thieu" class="hs-docs-heading">Giới thiệu <a class="anchorjs-link" href="#gioi-thieu" aria-label="Anchor" data-anchorjs-icon="#"></a></h3>
    
                    <p>
                        SePay hiện đã triển khai cơ chế xác thực OAuth2, giúp đối tác tích hợp dễ dàng và bảo mật hơn với hệ thống của chúng tôi. Tài liệu này cung cấp hướng dẫn chi tiết về cách sử dụng OAuth2 để kết nối với các API endpoint
                        của SePay.
                    </p>
    
                    <h3 id="/oauth2/la-gi" class="hs-docs-heading">OAuth2 là gì? <a class="anchorjs-link" href="#/oauth2/la-gi" aria-label="Anchor" data-anchorjs-icon="#"></a></h3>
    
                    <p>OAuth2 là một giao thức ủy quyền tiêu chuẩn, cho phép ứng dụng bên thứ ba truy cập tài nguyên của người dùng mà không cần biết thông tin đăng nhập.</p>

                    <img src="/assets/img/oauth2/authorize-screen.png" alt="OAuth2 Overview" class="img-fluid mb-5" />

                    <p>OAuth2 cung cấp các lợi ích:</p>
    
                    <ul class="lh-lg">
                        <li><b>An toàn</b>: Không cần chia sẻ thông tin đăng nhập với ứng dụng bên thứ ba</li>
                        <li><b>Kiểm soát</b>: Người dùng có thể giới hạn phạm vi truy cập cho ứng dụng</li>
                        <li><b>Linh hoạt</b>: Dễ dàng thu hồi quyền truy cập mà không cần thay đổi mật khẩu</li>
                        <li><b>Tiêu chuẩn</b>: Được hỗ trợ rộng rãi và áp dụng bởi nhiều dịch vụ trực tuyến</li>
                    </ul>
    
                    <h3 id="nhung-loi-ich" class="hs-docs-heading">Những lợi ích khi sử dụng OAuth2 với SePay <a class="anchorjs-link" href="#nhung-loi-ich" aria-label="Anchor" data-anchorjs-icon="#"></a></h3>
    
                    <p>Việc triển khai OAuth2 mang lại nhiều lợi ích quan trọng khi tích hợp với SePay:</p>
    
                    <div class="row content-space-t-1">
                        <div class="col-md-6 col-lg-5 mb-3 mb-md-7">
                            <div class="d-flex pe-lg-5 aos-init aos-animate" data-aos="fade-up">
                                <div class="flex-shrink-0">
                                    <span class="svg-icon text-primary">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-shield-lock" viewBox="0 0 16 16">
                                            <path d="M5.338 1.59a61 61 0 0 0-2.837.856.48.48 0 0 0-.328.39c-.554 4.157.726 7.19 2.253 9.188a10.7 10.7 0 0 0 2.287 2.233c.346.244.652.42.893.533q.18.085.293.118a1 1 0 0 0 .101.025 1 1 0 0 0 .1-.025q.114-.034.294-.118c.24-.113.547-.29.893-.533a10.7 10.7 0 0 0 2.287-2.233c1.527-1.997 2.807-5.031 2.253-9.188a.48.48 0 0 0-.328-.39c-.651-.213-1.75-.56-2.837-.855C9.552 1.29 8.531 1.067 8 1.067c-.53 0-1.552.223-2.662.524zM5.072.56C6.157.265 7.31 0 8 0s1.843.265 2.928.56c1.11.3 2.229.655 2.887.87a1.54 1.54 0 0 1 1.044 1.262c.596 4.477-.787 7.795-2.465 9.99a11.8 11.8 0 0 1-2.517 2.453 7 7 0 0 1-1.048.625c-.28.132-.581.24-.829.24s-.548-.108-.829-.24a7 7 0 0 1-1.048-.625 11.8 11.8 0 0 1-2.517-2.453C1.928 10.487.545 7.169 1.141 2.692A1.54 1.54 0 0 1 2.185 1.43 63 63 0 0 1 5.072.56"/>
                                            <path d="M9.5 6.5a1.5 1.5 0 0 1-1 1.415l.385 1.99a.5.5 0 0 1-.491.595h-.788a.5.5 0 0 1-.49-.595l.384-1.99a1.5 1.5 0 1 1 2-1.415"/>
                                        </svg>
                                    </span>
                                </div>
                                <div class="flex-grow-1 ms-4">
                                    <h4>Bảo mật cao</h4>
                                    <p>Xác thực bảo mật mà không cần chia sẻ thông tin đăng nhập, giảm thiểu rủi ro bảo mật.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-5 mb-3 mb-md-7">
                            <div class="d-flex ps-lg-5 aos-init aos-animate" data-aos="fade-up" data-aos-delay="100">
                                <div class="flex-shrink-0">
                                    <span class="svg-icon text-primary">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-person-check" viewBox="0 0 16 16">
                                            <path d="M12.5 16a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7m1.679-4.493-1.335 2.226a.75.75 0 0 1-1.174.144l-.774-.773a.5.5 0 0 1 .708-.708l.547.548 1.17-1.951a.5.5 0 1 1 .858.514M11 5a3 3 0 1 1-6 0 3 3 0 0 1 6 0M8 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4"/>
                                            <path d="M8.256 14a4.5 4.5 0 0 1-.229-1.004H3c.001-.246.154-.986.832-1.664C4.484 10.68 5.711 10 8 10q.39 0 .74.025c.226-.341.496-.65.804-.918Q8.844 9.002 8 9c-5 0-6 3-6 4s1 1 1 1z"/>
                                        </svg>
                                    </span>
                                </div>
                                <div class="flex-grow-1 ms-4">
                                    <h4>Phân quyền chi tiết</h4>
                                    <p>Phân quyền truy cập chi tiết đến từng endpoint, giúp bạn kiểm soát dữ liệu và tính năng được truy cập.</p>
                                </div>
                            </div>
                        </div>
                        <div class="w-100"></div>
                        <div class="col-md-6 col-lg-5 mb-3 mb-md-7 mb-lg-0">
                            <div class="d-flex pe-lg-5 aos-init aos-animate" data-aos="fade-up" data-aos-delay="200">
                                <div class="flex-shrink-0">
                                    <span class="svg-icon text-primary">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-sliders" viewBox="0 0 16 16">
                                            <path fill-rule="evenodd" d="M11.5 2a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3M9.05 3a2.5 2.5 0 0 1 4.9 0H16v1h-2.05a2.5 2.5 0 0 1-4.9 0H0V3zM4.5 7a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3M2.05 8a2.5 2.5 0 0 1 4.9 0H16v1H6.95a2.5 2.5 0 0 1-4.9 0H0V8zm9.45 4a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3m-2.45 1a2.5 2.5 0 0 1 4.9 0H16v1h-2.05a2.5 2.5 0 0 1-4.9 0H0v-1z"/>
                                        </svg>
                                    </span>
                                </div>
                                <div class="flex-grow-1 ms-4">
                                    <h4>Phạm vi truy cập hạn chế</h4>
                                    <p>Hạn chế phạm vi truy cập cho ứng dụng bên thứ ba, đảm bảo chỉ cấp quyền cần thiết.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-5">
                            <div class="d-flex ps-lg-5 aos-init aos-animate" data-aos="fade-up" data-aos-delay="300">
                                <div class="flex-shrink-0">
                                    <span class="svg-icon text-primary">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-clockwise" viewBox="0 0 16 16">
                                            <path fill-rule="evenodd" d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2z"/>
                                            <path d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466"/>
                                        </svg>
                                    </span>
                                </div>
                                <div class="flex-grow-1 ms-4">
                                    <h4>Refresh token</h4>
                                    <p>Cơ chế token làm mới (refresh token) để duy trì kết nối an toàn mà không cần xác thực lại.</p>
                                </div>
                            </div>
                        </div>
                    </div>
    
                    <h3 id="scopes" class="hs-docs-heading">Các phạm vi (scopes) <a class="anchorjs-link" href="#scopes" aria-label="Anchor" data-anchorjs-icon="#"></a></h3>
    
                    <p>SePay định nghĩa các phạm vi (scopes) để kiểm soát quyền truy cập vào từng API:</p>
    
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Phạm vi</th>
                                    <th>Mô tả</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>bank-account:read</code></td>
                                    <td>Quyền đọc thông tin tài khoản ngân hàng</td>
                                </tr>
                                <tr>
                                    <td><code>transaction:read</code></td>
                                    <td>Quyền đọc thông tin giao dịch</td>
                                </tr>
                                <tr>
                                    <td><code>webhook:read</code></td>
                                    <td>Quyền đọc thông tin webhook</td>
                                </tr>
                                <tr>
                                    <td><code>webhook:write</code></td>
                                    <td>Quyền ghi thông tin webhook</td>
                                </tr>
                                <tr>
                                    <td><code>webhook:delete</code></td>
                                    <td>Quyền xóa thông tin webhook</td>
                                </tr>
                                <tr>
                                    <td><code>profile</code></td>
                                    <td>Quyền đọc thông tin người dùng</td>
                                </tr>
                                <tr>
                                    <td><code>company</code></td>
                                    <td>Quyền đọc thông tin công ty</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
    
                    <p>Khi đăng ký ứng dụng, bạn có thể yêu cầu một hoặc nhiều phạm vi. Người dùng sẽ được yêu cầu cho phép khi xác thực.</p>
    
                    <h3 id="tong-quan-quy-trinh" class="hs-docs-heading">Tổng quan quy trình OAuth2 <a class="anchorjs-link" href="#tong-quan-quy-trinh" aria-label="Anchor" data-anchorjs-icon="#"></a></h3>

                    <p>Luồng OAuth2 trong SePay tuân theo quy trình xác thực chuẩn với các bước sau:</p>

                    <div class="row g-2 g-md-4 mb-5">
                        <div class="col-md-6 col-lg-4">
                            <div class="card h-100 shadow-none border">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="flex-shrink-0">
                                            <span class="badge bg-primary d-flex align-items-center justify-content-center rounded-circle" style="line-height: 0; width: 1.75rem; height: 1.75rem;">1</span>
                                        </div>
                                        <h5 class="card-title mb-0 ms-3">Đăng ký ứng dụng</h5>
                                    </div>
                                    <p class="card-text">Nhận <code>client_id</code> và <code>client_secret</code> từ SePay thông qua Developer Portal</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="card h-100 shadow-none border">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="flex-shrink-0">
                                            <span class="badge bg-primary d-flex align-items-center justify-content-center rounded-circle" style="line-height: 0; width: 1.75rem; height: 1.75rem;">2</span>
                                        </div>
                                        <h5 class="card-title mb-0 ms-3">Yêu cầu ủy quyền</h5>
                                    </div>
                                    <p class="card-text">Chuyển hướng người dùng đến màn hình xác thực SePay để cấp quyền</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="card h-100 shadow-none border">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="flex-shrink-0">
                                            <span class="badge bg-primary d-flex align-items-center justify-content-center rounded-circle" style="line-height: 0; width: 1.75rem; height: 1.75rem;">3</span>
                                        </div>
                                        <h5 class="card-title mb-0 ms-3">Nhận mã ủy quyền</h5>
                                    </div>
                                    <p class="card-text">SePay chuyển hướng người dùng về ứng dụng của bạn kèm mã ủy quyền</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="card h-100 shadow-none border">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="flex-shrink-0">
                                            <span class="badge bg-primary d-flex align-items-center justify-content-center rounded-circle" style="line-height: 0; width: 1.75rem; height: 1.75rem;">4</span>
                                        </div>
                                        <h5 class="card-title mb-0 ms-3">Đổi mã lấy token</h5>
                                    </div>
                                    <p class="card-text">Gửi yêu cầu API đến SePay để đổi mã ủy quyền lấy access token</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="card h-100 shadow-none border">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="flex-shrink-0">
                                            <span class="badge bg-primary d-flex align-items-center justify-content-center rounded-circle" style="line-height: 0; width: 1.75rem; height: 1.75rem;">5</span>
                                        </div>
                                        <h5 class="card-title mb-0 ms-3">Sử dụng token</h5>
                                    </div>
                                    <p class="card-text">Đính kèm access token vào header của các request API để xác thực</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="card h-100 shadow-none border">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="flex-shrink-0">
                                            <span class="badge bg-primary d-flex align-items-center justify-content-center rounded-circle" style="line-height: 0; width: 1.75rem; height: 1.75rem;">6</span>
                                        </div>
                                        <h5 class="card-title mb-0 ms-3">Làm mới token</h5>
                                    </div>
                                    <p class="card-text">Sử dụng refresh token để cấp lại access token mới khi hết hạn</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info bg-soft-primary border-0">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="bi bi-info-circle-fill text-primary me-2"></i>
                            </div>
                            <div>
                                <p class="mb-0">Chi tiết về từng bước trong quy trình OAuth2 được hướng dẫn đầy đủ trong mục <a href="/oauth2/luong-xac-thuc.html" class="text-primary fw-bold">Luồng xác thực</a>.</p>
                            </div>
                        </div>
                    </div>

                    <h3 id="base-url" class="hs-docs-heading">
                        Base URL API <a class="anchorjs-link" href="#base-url" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>

                    <p>Tất cả API của SePay OAuth2 đều sử dụng chung một base URL:</p>

                    <pre><code class="language-text">https://my.sepay.vn/api/v1</code></pre>

                    <p>Khi thực hiện các yêu cầu API, bạn cần thêm đường dẫn của endpoint cụ thể vào sau base URL. Ví dụ:</p>

                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                <th>Endpoint</th>
                                <th>URL đầy đủ</th>
                                <th>Mô tả</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>/bank-accounts</code></td>
                                <td><code>https://my.sepay.vn/api/v1/bank-accounts</code></td>
                                <td>API lấy danh sách tài khoản ngân hàng</td>
                            </tr>
                            <tr>
                                <td><code>/transactions</code></td>
                                <td><code>https://my.sepay.vn/api/v1/transactions</code></td>
                                <td>API lấy danh sách giao dịch</td>
                            </tr>
                            <tr>
                                <td><code>/webhooks</code></td>
                                <td><code>https://my.sepay.vn/api/v1/webhooks</code></td>
                                <td>API quản lý webhook</td>
                            </tr>
                            <tr>
                                <td><code>/me</code></td>
                                <td><code>https://my.sepay.vn/api/v1/me</code></td>
                                <td>API lấy thông tin người dùng hiện tại</td>
                            </tr>
                            <tr>
                                <td><code>/companies</code></td>
                                <td><code>https://my.sepay.vn/api/v1/companies</code></td>
                                <td>API lấy thông tin công ty</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                    <h4>Phiên bản API</h4>

                    <p>Phần "<code>v1</code>" trong base URL đại diện cho phiên bản của API. SePay sử dụng hệ thống versioning này để đảm bảo tính tương thích khi có thay đổi trong tương lai:</p>

                    <ul>
                        <li>Phiên bản hiện tại: <code>v1</code> (<code>https://my.sepay.vn/api/v1</code>)</li>
                    </ul>

                    <p>Khi có phiên bản mới của API, SePay sẽ thông báo và cập nhật tài liệu. Chúng tôi luôn đảm bảo duy trì phiên bản API cũ trong một khoảng thời gian hợp lý để các ứng dụng có thời gian cập nhật.</p>

                    <div class="alert alert-warning">
                        <div class="d-flex align-items-baseline">
                            <div class="flex-shrink-0">
                                <i class="bi-info-circle me-1"></i>
                            </div>
                            <div class="flex-grow-1 ms-2">
                                <strong>Lưu ý:</strong> Mọi yêu cầu API đều phải bao gồm Access Token trong header Authorization như đã hướng dẫn trong mục <a href="/oauth2/access-token.html">Access Token</a>.
                            </div>
                        </div>
                    </div>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <a class="card card-sm card-transition h-100" href="/">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <i class="bi-arrow-left fs-2"></i>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h5 class="fs-4 card-title">Quay lại trang chủ</h5>
                                            <p class="card-text text-body small">Trở về trang chủ SePay</p>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a class="card card-sm card-transition h-100" href="/oauth2/dang-ky-ung-dung.html">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1 me-3">
                                            <h5 class="fs-4 card-title">Đăng ký ứng dụng</h5>
                                            <p class="card-text text-body small">Bắt đầu tích hợp với SePay</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="bi-arrow-right fs-2"></i>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <div id="contact-box-overlay"></div>
        <div id="contact-box">
            <div class="popup">
                <a href="https://m.me/117903214582465" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/fb-messenger.png" width="50%" />
                    </div>
                    <div class="meta">
                        <p class="title">Facebook Messenger</p>
                        <small class="description">Hỗ trợ live chat 24/7</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="tel:02873059589" class="item">
                    <div class="icon" style="background-color: #22c55e; color: #fff;">
                        <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;">
                            <path
                                d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z"
                                stroke="currentColor"
                                stroke-width="1.5"
                                stroke-linecap="round"
                                fill="transparent"
                            ></path>
                        </svg>
                    </div>
                    <div class="meta">
                        <p class="title">Hotline</p>
                        <small class="description">Điện thoại hỗ trợ</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/youtube-social.png" />
                    </div>
                    <div class="meta">
                        <p class="title">Youtube</p>
                        <small class="description">Theo dõi video mới nhất của SePay</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/telegram-social.png" />
                    </div>
                    <div class="meta">
                        <p class="title">Telegram</p>
                        <small class="description">Nhận thông tin mới nhất từ SePay</small>
                    </div>
                </a>
            </div>
            <div class="container">
                <div class="dot-ping">
                    <div class="ping"></div>
                    <div class="dot"></div>
                </div>
                <div class="contact-icon">
                    <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;">
                        <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z"
                            fill="currentColor"
                        ></path>
                    </svg>
                </div>
                <span style="font-weight: bold;">Liên hệ chúng tôi</span>
            </div>
        </div>

        <a
            class="js-go-to go-to position-fixed"
            href="javascript:;"
            style="visibility: hidden;"
            data-hs-go-to-options='{
                "offsetTop": 700,
                "position": {
                    "init": {
                    "right": "2rem"
                    },
                    "show": {
                    "bottom": "2rem"
                    },
                    "hide": {
                    "bottom": "-2rem"
                    }
                }
                }'
        >
            <i class="bi-chevron-up"></i>
        </a>

        <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
        <script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
        <script src="../assets/vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js"></script>
        <script src="../assets/vendor/list.js/dist/list.min.js"></script>
        <script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>
        <script src="../assets/js/theme.min.js"></script>
        <script src="../assets/js/contact-box.js"></script>
        <script src="../assets/highlight/highlight.min.js"></script>

        <script>
            hljs.highlightAll();
            (function () {
                new HSHeader("#header").init();

                new HsNavScroller(".js-nav-scroller", {
                    delay: 400,
                    offset: 140,
                });

                const docsSearch = HSCore.components.HSList.init("#docsSearch");

                new HSGoTo(".js-go-to");
            })();

            function setCookie(cname, cvalue, exdays) {
                var d = new Date();
                d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000);
                var expires = "expires=" + d.toUTCString();
                document.cookie = cname + "=" + cvalue + "; " + expires + "; path=/; domain=.sepay.vn";
            }

            let urlParams = new URLSearchParams(document.location.search);

            ["utm_source", "utm_medium", "utm_campaign", "utm_term", "utm_content"].forEach(function (param) {
                let value = urlParams.get(param);

                if (value) {
                    setCookie(param, value, 90);
                }
            });

            if (document.referrer != "" && document.cookie.indexOf("referer") === -1) {
                setCookie("referer", document.referrer, 90);
            }
        </script>
    </body>
</html>
