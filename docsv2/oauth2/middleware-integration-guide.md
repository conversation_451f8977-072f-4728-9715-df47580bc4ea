# SePay OAuth2 Middleware Integration Guide

## Table of Contents

1. [Architecture Overview & Security Model](#architecture-overview--security-model)
2. [Complete OAuth2 Implementation Flow](#complete-oauth2-implementation-flow)
3. [Technical Implementation Specifications](#technical-implementation-specifications)
4. [Production-Ready Code Examples](#production-ready-code-examples)
5. [Security Best Practices & Compliance](#security-best-practices--compliance)
6. [Operational Procedures](#operational-procedures)
7. [Troubleshooting & Maintenance](#troubleshooting--maintenance)

## Introduction

This comprehensive technical integration guide is specifically designed for middleware platforms (WordPress plugins, Haravan apps, Sapo integrations, etc.) that need to implement SePay's OAuth2 authentication flow on behalf of their merchant users while maintaining strict security standards.

**Target Audience**: Senior developers and technical architects implementing OAuth2 integrations for middleware platforms serving multiple merchants.

**Primary Objective**: Design and document a secure OAuth2 integration pattern where middleware platforms can authenticate merchants with SePay without exposing sensitive client credentials to end users or frontend applications.

## Architecture Overview & Security Model

### Three-Party Relationship

The SePay OAuth2 middleware integration involves three distinct parties:

```
SePay ↔ Middleware Platform ↔ Merchant
```

#### Party Responsibilities

**SePay (Authorization Server)**
- Issues and validates OAuth2 tokens
- Manages merchant authentication and consent
- Provides secure API endpoints for merchant data access
- Maintains audit logs for compliance

**Middleware Platform (Client Application)**
- Owns and protects client credentials (`client_id`, `client_secret`)
- Initiates OAuth2 flows on behalf of merchants
- Securely stores and manages merchant tokens
- Proxies API requests to SePay with proper authentication
- Implements security controls and monitoring

**Merchant (Resource Owner)**
- Provides consent for data access through SePay's authorization interface
- Uses middleware platform features without direct access to credentials
- Can revoke access through SePay's user interface

### Security Model Principles

#### 1. Credential Protection
- **Client credentials MUST be stored server-side only**
- Never expose `client_secret` to frontend applications or end users
- Use environment variables or secure configuration management
- Implement credential rotation procedures

#### 2. Token Isolation
- Each merchant's tokens are isolated and encrypted at rest
- Tokens are associated with specific merchant accounts in your system
- Implement proper access controls for token storage

#### 3. Scope Limitation
- Request only the minimum required scopes for your use case
- Document scope requirements clearly to merchants
- Implement scope validation in your application logic

### Security Risks of Exposing Client Credentials

**❌ Never Do This:**
```javascript
// DANGEROUS: Client-side OAuth2 flow
const clientSecret = 'your_secret_here'; // Exposed in browser!
fetch('https://my.sepay.vn/oauth/token', {
  method: 'POST',
  body: new URLSearchParams({
    client_secret: clientSecret // Visible to anyone!
  })
});
```

**✅ Correct Approach:**
```javascript
// SECURE: Server-side proxy
fetch('/your-app/api/sepay/auth/callback', {
  method: 'POST',
  body: JSON.stringify({ code: authCode })
});
```

### Data Flow and Responsibility Boundaries

```mermaid
sequenceDiagram
    participant M as Merchant
    participant MP as Middleware Platform
    participant SP as SePay

    Note over M,SP: 1. Authentication Initiation
    M->>MP: Request SePay integration
    MP->>MP: Generate state parameter
    MP->>M: Redirect to SePay authorization URL
    
    Note over M,SP: 2. Merchant Authorization
    M->>SP: Login and grant permissions
    SP->>MP: Redirect with authorization code
    
    Note over M,SP: 3. Token Exchange (Server-side only)
    MP->>SP: Exchange code for tokens (with client_secret)
    SP->>MP: Return access_token + refresh_token
    MP->>MP: Encrypt and store tokens
    
    Note over M,SP: 4. API Operations
    M->>MP: Request merchant data
    MP->>MP: Decrypt stored tokens
    MP->>SP: API call with Bearer token
    SP->>MP: Return merchant data
    MP->>M: Return processed data
```

## Complete OAuth2 Implementation Flow

### Step 1: Middleware Platform Initiates OAuth2 Flow

The middleware platform starts the OAuth2 flow when a merchant wants to connect their SePay account.

#### State Management
Generate a cryptographically secure state parameter to prevent CSRF attacks:

```javascript
const crypto = require('crypto');

function generateState() {
  return crypto.randomBytes(32).toString('hex');
}

// Store state in session or database with expiration
const state = generateState();
await storeState(merchantId, state, { expiresIn: '10m' });
```

#### Authorization URL Construction
```javascript
function buildAuthorizationUrl(merchantId, scopes = []) {
  const state = generateState();
  const params = new URLSearchParams({
    response_type: 'code',
    client_id: process.env.SEPAY_CLIENT_ID,
    redirect_uri: process.env.SEPAY_REDIRECT_URI,
    scope: scopes.join(' ') || 'profile bank-account:read transaction:read',
    state: state
  });
  
  return `https://my.sepay.vn/oauth/authorize?${params.toString()}`;
}
```

### Step 2: User Redirection to SePay Authorization Server

Redirect the merchant to SePay's authorization server with proper parameters:

```javascript
app.get('/sepay/connect', async (req, res) => {
  try {
    const merchantId = req.user.id;
    const scopes = ['profile', 'bank-account:read', 'transaction:read'];
    
    const authUrl = buildAuthorizationUrl(merchantId, scopes);
    
    // Log the initiation for audit purposes
    await logOAuthEvent(merchantId, 'oauth_initiated', { scopes });
    
    res.redirect(authUrl);
  } catch (error) {
    res.status(500).json({ error: 'Failed to initiate OAuth flow' });
  }
});
```

### Step 3: Merchant Authentication and Consent

The merchant will see SePay's authorization screen where they:
1. Log in to their SePay account (if not already logged in)
2. Review the requested permissions
3. Grant or deny access to your application

**Important**: This step happens entirely on SePay's servers - your application has no control over this process.

### Step 4: Authorization Code Callback Handling

Handle the callback from SePay with security validations:

```javascript
app.get('/sepay/callback', async (req, res) => {
  try {
    const { code, state, error } = req.query;
    
    // Handle authorization denial
    if (error) {
      await logOAuthEvent(null, 'oauth_denied', { error });
      return res.redirect('/integration-failed?error=' + encodeURIComponent(error));
    }
    
    // Validate state parameter
    const storedState = await getStoredState(state);
    if (!storedState || storedState.state !== state) {
      throw new Error('Invalid state parameter - possible CSRF attack');
    }
    
    // Exchange authorization code for tokens
    const tokens = await exchangeCodeForTokens(code);
    
    // Store tokens securely
    await storeTokensForMerchant(storedState.merchantId, tokens);
    
    // Clean up state
    await deleteStoredState(state);
    
    res.redirect('/integration-success');
  } catch (error) {
    await logOAuthEvent(null, 'oauth_error', { error: error.message });
    res.redirect('/integration-failed?error=' + encodeURIComponent(error.message));
  }
});
```

### Step 5: Backend Token Exchange

Exchange the authorization code for access and refresh tokens:

```javascript
async function exchangeCodeForTokens(authorizationCode) {
  const response = await fetch('https://my.sepay.vn/oauth/token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: new URLSearchParams({
      grant_type: 'authorization_code',
      code: authorizationCode,
      redirect_uri: process.env.SEPAY_REDIRECT_URI,
      client_id: process.env.SEPAY_CLIENT_ID,
      client_secret: process.env.SEPAY_CLIENT_SECRET, // Server-side only!
    }),
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(`Token exchange failed: ${error.error_description || error.error}`);
  }
  
  return await response.json();
}
```

### Step 6: Secure Token Storage and Merchant Account Association

Store tokens with proper encryption and association:

```javascript
const crypto = require('crypto');

async function storeTokensForMerchant(merchantId, tokens) {
  const encryptionKey = process.env.TOKEN_ENCRYPTION_KEY;
  
  // Encrypt tokens before storage
  const encryptedAccessToken = encrypt(tokens.access_token, encryptionKey);
  const encryptedRefreshToken = encrypt(tokens.refresh_token, encryptionKey);
  
  // Calculate expiration time
  const expiresAt = new Date(Date.now() + (tokens.expires_in * 1000));
  
  // Store in database
  await db.sepayTokens.upsert({
    where: { merchantId },
    update: {
      accessToken: encryptedAccessToken,
      refreshToken: encryptedRefreshToken,
      expiresAt,
      scopes: tokens.scope,
      updatedAt: new Date()
    },
    create: {
      merchantId,
      accessToken: encryptedAccessToken,
      refreshToken: encryptedRefreshToken,
      expiresAt,
      scopes: tokens.scope,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  });
  
  await logOAuthEvent(merchantId, 'tokens_stored', { scopes: tokens.scope });
}

function encrypt(text, key) {
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher('aes-256-cbc', key);
  cipher.setAutoPadding(true);
  
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  return iv.toString('hex') + ':' + encrypted;
}
```

## Error Handling and Rollback Procedures

### Common Error Scenarios

1. **Invalid Authorization Code**
   - Log the error with context
   - Show user-friendly error message
   - Provide retry mechanism

2. **State Parameter Mismatch**
   - Treat as potential security threat
   - Log with high priority
   - Block the request immediately

3. **Token Exchange Failure**
   - Retry with exponential backoff
   - Log detailed error information
   - Notify merchant of temporary issue

### Rollback Procedures

```javascript
async function rollbackFailedOAuth(merchantId, reason) {
  try {
    // Remove any partially stored data
    await db.sepayTokens.deleteMany({ where: { merchantId } });
    await db.oauthStates.deleteMany({ where: { merchantId } });
    
    // Log the rollback
    await logOAuthEvent(merchantId, 'oauth_rollback', { reason });
    
    // Notify monitoring systems
    await notifyMonitoring('oauth_rollback', { merchantId, reason });
  } catch (error) {
    // Critical error - manual intervention may be required
    await logOAuthEvent(merchantId, 'rollback_failed', { 
      originalReason: reason, 
      rollbackError: error.message 
    });
  }
}
```

This completes the first major section of the guide. The implementation flow provides a secure foundation for middleware platforms to handle OAuth2 authentication while protecting sensitive credentials and maintaining proper security boundaries.

## Technical Implementation Specifications

### SePay OAuth2 Endpoints

#### Authorization Endpoint
```
GET https://my.sepay.vn/oauth/authorize
```

**Required Parameters:**
- `response_type`: Must be `code`
- `client_id`: Your application's client ID
- `redirect_uri`: Must match registered redirect URI
- `scope`: Space-separated list of requested scopes
- `state`: CSRF protection token (recommended)

**Optional Parameters:**
- `prompt`: Set to `consent` to force re-consent

#### Token Endpoint
```
POST https://my.sepay.vn/oauth/token
Content-Type: application/x-www-form-urlencoded
```

**For Authorization Code Exchange:**
```
grant_type=authorization_code
&code=AUTHORIZATION_CODE
&redirect_uri=YOUR_REDIRECT_URI
&client_id=YOUR_CLIENT_ID
&client_secret=YOUR_CLIENT_SECRET
```

**For Refresh Token:**
```
grant_type=refresh_token
&refresh_token=REFRESH_TOKEN
&client_id=YOUR_CLIENT_ID
&client_secret=YOUR_CLIENT_SECRET
```

#### Token Revocation Endpoint
```
POST https://my.sepay.vn/oauth/revoke
Content-Type: application/x-www-form-urlencoded
Authorization: Basic base64(client_id:client_secret)

token=ACCESS_TOKEN_OR_REFRESH_TOKEN
&token_type_hint=access_token
```

### OAuth2 Parameters Reference

#### Authorization Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `response_type` | string | Yes | Must be `code` for authorization code flow |
| `client_id` | string | Yes | Your application's client identifier |
| `redirect_uri` | string | Yes | URI to redirect after authorization |
| `scope` | string | No | Space-delimited scope values |
| `state` | string | Recommended | Opaque value for CSRF protection |
| `prompt` | string | No | `consent` to force re-authorization |

#### Token Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `grant_type` | string | Yes | `authorization_code` or `refresh_token` |
| `code` | string | Yes* | Authorization code (*for code exchange) |
| `refresh_token` | string | Yes* | Refresh token (*for token refresh) |
| `redirect_uri` | string | Yes* | Must match authorization request (*for code exchange) |
| `client_id` | string | Yes | Your application's client identifier |
| `client_secret` | string | Yes | Your application's client secret |

### Scope Combinations and Use Cases

#### Basic Profile Access
```
scope=profile
```
- Access to merchant's basic profile information
- Suitable for: User identification, basic account info

#### Financial Data Read Access
```
scope=profile bank-account:read transaction:read
```
- Read access to bank accounts and transactions
- Suitable for: Accounting integrations, financial dashboards

#### Webhook Management
```
scope=profile webhook:read webhook:write webhook:delete
```
- Full webhook management capabilities
- Suitable for: Event-driven integrations, real-time notifications

#### Complete Access (Recommended for most middleware platforms)
```
scope=profile company bank-account:read transaction:read webhook:read webhook:write
```
- Comprehensive access for full-featured integrations
- Suitable for: E-commerce platforms, ERP systems, comprehensive dashboards

### Token Lifecycle Management

#### Access Token Properties
- **Lifetime**: 1 hour (3600 seconds)
- **Format**: JWT (JSON Web Token)
- **Usage**: Include in `Authorization: Bearer {token}` header
- **Scope**: Limited to granted permissions

#### Refresh Token Properties
- **Lifetime**: 30 days (2,592,000 seconds)
- **Format**: Opaque string
- **Usage**: Exchange for new access tokens
- **Security**: Single-use (new refresh token issued with each refresh)

#### Token Refresh Strategy

**Proactive Refresh (Recommended):**
```javascript
async function ensureValidToken(merchantId) {
  const tokenData = await getTokenData(merchantId);

  // Refresh if token expires within 5 minutes
  const bufferTime = 5 * 60 * 1000; // 5 minutes in milliseconds
  const expiresAt = new Date(tokenData.expiresAt);
  const now = new Date();

  if (expiresAt.getTime() - now.getTime() < bufferTime) {
    return await refreshAccessToken(merchantId);
  }

  return tokenData.accessToken;
}
```

**Reactive Refresh (Fallback):**
```javascript
async function makeApiCall(merchantId, endpoint, options = {}) {
  let token = await getAccessToken(merchantId);

  try {
    return await callSepayApi(endpoint, token, options);
  } catch (error) {
    if (error.status === 401) {
      // Token expired, refresh and retry
      token = await refreshAccessToken(merchantId);
      return await callSepayApi(endpoint, token, options);
    }
    throw error;
  }
}
```

### API Authentication Patterns

#### Standard Bearer Token Authentication
```javascript
const response = await fetch('https://my.sepay.vn/api/v1/bank-accounts', {
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json',
    'User-Agent': 'YourApp/1.0.0'
  }
});
```

#### Merchant API Proxy Implementation
```javascript
app.get('/api/sepay/:endpoint(*)', async (req, res) => {
  try {
    const merchantId = req.user.id;
    const endpoint = req.params.endpoint;

    // Ensure valid token
    const token = await ensureValidToken(merchantId);

    // Proxy request to SePay
    const sepayResponse = await fetch(`https://my.sepay.vn/api/v1/${endpoint}`, {
      method: req.method,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: req.method !== 'GET' ? JSON.stringify(req.body) : undefined
    });

    const data = await sepayResponse.json();

    // Log API usage for monitoring
    await logApiUsage(merchantId, endpoint, sepayResponse.status);

    res.status(sepayResponse.status).json(data);
  } catch (error) {
    res.status(500).json({ error: 'Proxy request failed' });
  }
});
```

### Rate Limiting and Retry Strategies

#### SePay API Rate Limits
- **Standard Rate Limit**: 100 requests per minute per access token
- **Burst Limit**: 20 requests per 10 seconds
- **Daily Limit**: 10,000 requests per day per application

#### Retry Strategy Implementation
```javascript
async function callSepayApiWithRetry(endpoint, token, options = {}, maxRetries = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch(`https://my.sepay.vn/api/v1/${endpoint}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        ...options
      });

      if (response.status === 429) {
        // Rate limited - wait and retry
        const retryAfter = parseInt(response.headers.get('Retry-After') || '60');
        await sleep(retryAfter * 1000);
        continue;
      }

      if (response.status >= 500 && attempt < maxRetries) {
        // Server error - exponential backoff
        await sleep(Math.pow(2, attempt) * 1000);
        continue;
      }

      return response;
    } catch (error) {
      if (attempt === maxRetries) throw error;
      await sleep(Math.pow(2, attempt) * 1000);
    }
  }
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}
```

## Production-Ready Code Examples

### Complete Node.js/Express Backend Implementation

#### Environment Configuration

Create a `.env` file for your application:

```bash
# SePay OAuth2 Configuration
SEPAY_CLIENT_ID=your_client_id_here
SEPAY_CLIENT_SECRET=your_client_secret_here
SEPAY_REDIRECT_URI=https://your-app.com/sepay/callback

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/your_app
REDIS_URL=redis://localhost:6379

# Security Configuration
TOKEN_ENCRYPTION_KEY=your_32_character_encryption_key_here
SESSION_SECRET=your_session_secret_here

# Application Configuration
NODE_ENV=production
PORT=3000
```

#### Database Schema

```sql
-- Merchants table
CREATE TABLE merchants (
  id SERIAL PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  name VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- SePay tokens table
CREATE TABLE sepay_tokens (
  id SERIAL PRIMARY KEY,
  merchant_id INTEGER REFERENCES merchants(id) ON DELETE CASCADE,
  access_token TEXT NOT NULL,
  refresh_token TEXT NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  scopes TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(merchant_id)
);

-- OAuth states table (for CSRF protection)
CREATE TABLE oauth_states (
  id SERIAL PRIMARY KEY,
  state VARCHAR(64) UNIQUE NOT NULL,
  merchant_id INTEGER REFERENCES merchants(id) ON DELETE CASCADE,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- API usage logs table
CREATE TABLE api_usage_logs (
  id SERIAL PRIMARY KEY,
  merchant_id INTEGER REFERENCES merchants(id) ON DELETE CASCADE,
  endpoint VARCHAR(255) NOT NULL,
  method VARCHAR(10) NOT NULL,
  status_code INTEGER NOT NULL,
  response_time_ms INTEGER,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- OAuth event logs table
CREATE TABLE oauth_event_logs (
  id SERIAL PRIMARY KEY,
  merchant_id INTEGER REFERENCES merchants(id) ON DELETE SET NULL,
  event_type VARCHAR(50) NOT NULL,
  event_data JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_sepay_tokens_merchant_id ON sepay_tokens(merchant_id);
CREATE INDEX idx_oauth_states_state ON oauth_states(state);
CREATE INDEX idx_oauth_states_expires_at ON oauth_states(expires_at);
CREATE INDEX idx_api_usage_logs_merchant_id ON api_usage_logs(merchant_id);
CREATE INDEX idx_api_usage_logs_created_at ON api_usage_logs(created_at);
CREATE INDEX idx_oauth_event_logs_merchant_id ON oauth_event_logs(merchant_id);
CREATE INDEX idx_oauth_event_logs_event_type ON oauth_event_logs(event_type);
```

#### Core OAuth2 Service Implementation

```javascript
// services/sepayOAuth.js
const crypto = require('crypto');
const fetch = require('node-fetch');
const { Pool } = require('pg');

class SepayOAuthService {
  constructor() {
    this.clientId = process.env.SEPAY_CLIENT_ID;
    this.clientSecret = process.env.SEPAY_CLIENT_SECRET;
    this.redirectUri = process.env.SEPAY_REDIRECT_URI;
    this.encryptionKey = process.env.TOKEN_ENCRYPTION_KEY;
    this.db = new Pool({ connectionString: process.env.DATABASE_URL });
  }

  // Generate secure state parameter
  generateState() {
    return crypto.randomBytes(32).toString('hex');
  }

  // Store state with expiration
  async storeState(merchantId, state) {
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    await this.db.query(
      'INSERT INTO oauth_states (state, merchant_id, expires_at) VALUES ($1, $2, $3)',
      [state, merchantId, expiresAt]
    );
  }

  // Validate and retrieve state
  async validateState(state) {
    const result = await this.db.query(
      'SELECT merchant_id FROM oauth_states WHERE state = $1 AND expires_at > NOW()',
      [state]
    );

    if (result.rows.length === 0) {
      throw new Error('Invalid or expired state parameter');
    }

    // Clean up used state
    await this.db.query('DELETE FROM oauth_states WHERE state = $1', [state]);

    return result.rows[0].merchant_id;
  }

  // Build authorization URL
  buildAuthorizationUrl(merchantId, scopes = ['profile', 'bank-account:read', 'transaction:read']) {
    const state = this.generateState();

    // Store state for later validation
    this.storeState(merchantId, state);

    const params = new URLSearchParams({
      response_type: 'code',
      client_id: this.clientId,
      redirect_uri: this.redirectUri,
      scope: scopes.join(' '),
      state: state
    });

    return `https://my.sepay.vn/oauth/authorize?${params.toString()}`;
  }

  // Exchange authorization code for tokens
  async exchangeCodeForTokens(code) {
    const response = await fetch('https://my.sepay.vn/oauth/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code: code,
        redirect_uri: this.redirectUri,
        client_id: this.clientId,
        client_secret: this.clientSecret,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Token exchange failed: ${error.error_description || error.error}`);
    }

    return await response.json();
  }

  // Refresh access token
  async refreshAccessToken(merchantId) {
    const tokenData = await this.getTokenData(merchantId);

    if (!tokenData) {
      throw new Error('No tokens found for merchant');
    }

    const decryptedRefreshToken = this.decrypt(tokenData.refresh_token);

    const response = await fetch('https://my.sepay.vn/oauth/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'refresh_token',
        refresh_token: decryptedRefreshToken,
        client_id: this.clientId,
        client_secret: this.clientSecret,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Token refresh failed: ${error.error_description || error.error}`);
    }

    const tokens = await response.json();
    await this.storeTokensForMerchant(merchantId, tokens);

    return this.decrypt(await this.getAccessToken(merchantId));
  }

  // Encrypt sensitive data
  encrypt(text) {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey);

    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    return iv.toString('hex') + ':' + encrypted;
  }

  // Decrypt sensitive data
  decrypt(encryptedText) {
    const parts = encryptedText.split(':');
    const iv = Buffer.from(parts[0], 'hex');
    const encrypted = parts[1];

    const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey);

    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }

  // Store tokens for merchant
  async storeTokensForMerchant(merchantId, tokens) {
    const encryptedAccessToken = this.encrypt(tokens.access_token);
    const encryptedRefreshToken = this.encrypt(tokens.refresh_token);
    const expiresAt = new Date(Date.now() + (tokens.expires_in * 1000));

    await this.db.query(`
      INSERT INTO sepay_tokens (merchant_id, access_token, refresh_token, expires_at, scopes)
      VALUES ($1, $2, $3, $4, $5)
      ON CONFLICT (merchant_id)
      DO UPDATE SET
        access_token = EXCLUDED.access_token,
        refresh_token = EXCLUDED.refresh_token,
        expires_at = EXCLUDED.expires_at,
        scopes = EXCLUDED.scopes,
        updated_at = CURRENT_TIMESTAMP
    `, [merchantId, encryptedAccessToken, encryptedRefreshToken, expiresAt, tokens.scope]);

    await this.logOAuthEvent(merchantId, 'tokens_stored', { scopes: tokens.scope });
  }

  // Get token data for merchant
  async getTokenData(merchantId) {
    const result = await this.db.query(
      'SELECT access_token, refresh_token, expires_at, scopes FROM sepay_tokens WHERE merchant_id = $1',
      [merchantId]
    );

    return result.rows[0] || null;
  }

  // Get valid access token (with automatic refresh)
  async ensureValidToken(merchantId) {
    const tokenData = await this.getTokenData(merchantId);

    if (!tokenData) {
      throw new Error('No tokens found for merchant');
    }

    // Check if token expires within 5 minutes
    const bufferTime = 5 * 60 * 1000;
    const expiresAt = new Date(tokenData.expires_at);
    const now = new Date();

    if (expiresAt.getTime() - now.getTime() < bufferTime) {
      return await this.refreshAccessToken(merchantId);
    }

    return this.decrypt(tokenData.access_token);
  }

  // Log OAuth events for audit
  async logOAuthEvent(merchantId, eventType, eventData = {}, req = null) {
    const ipAddress = req ? req.ip : null;
    const userAgent = req ? req.get('User-Agent') : null;

    await this.db.query(`
      INSERT INTO oauth_event_logs (merchant_id, event_type, event_data, ip_address, user_agent)
      VALUES ($1, $2, $3, $4, $5)
    `, [merchantId, eventType, JSON.stringify(eventData), ipAddress, userAgent]);
  }

  // Revoke tokens for merchant
  async revokeTokens(merchantId) {
    const tokenData = await this.getTokenData(merchantId);

    if (tokenData) {
      const accessToken = this.decrypt(tokenData.access_token);

      // Revoke token at SePay
      try {
        await fetch('https://my.sepay.vn/oauth/revoke', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': `Basic ${Buffer.from(`${this.clientId}:${this.clientSecret}`).toString('base64')}`
          },
          body: new URLSearchParams({
            token: accessToken,
            token_type_hint: 'access_token'
          })
        });
      } catch (error) {
        console.error('Failed to revoke token at SePay:', error);
      }

      // Remove from local database
      await this.db.query('DELETE FROM sepay_tokens WHERE merchant_id = $1', [merchantId]);
      await this.logOAuthEvent(merchantId, 'tokens_revoked');
    }
  }
}

module.exports = SepayOAuthService;
```

#### Express Routes Implementation

```javascript
// routes/sepayOAuth.js
const express = require('express');
const SepayOAuthService = require('../services/sepayOAuth');
const { requireAuth } = require('../middleware/auth');

const router = express.Router();
const sepayOAuth = new SepayOAuthService();

// Initiate OAuth2 flow
router.get('/connect', requireAuth, async (req, res) => {
  try {
    const merchantId = req.user.id;
    const scopes = req.query.scopes ? req.query.scopes.split(',') : undefined;

    const authUrl = sepayOAuth.buildAuthorizationUrl(merchantId, scopes);

    await sepayOAuth.logOAuthEvent(merchantId, 'oauth_initiated', {
      scopes: scopes || 'default',
      userAgent: req.get('User-Agent'),
      ipAddress: req.ip
    }, req);

    res.json({ authUrl });
  } catch (error) {
    console.error('OAuth initiation error:', error);
    res.status(500).json({ error: 'Failed to initiate OAuth flow' });
  }
});

// Handle OAuth2 callback
router.get('/callback', async (req, res) => {
  try {
    const { code, state, error } = req.query;

    // Handle authorization denial
    if (error) {
      await sepayOAuth.logOAuthEvent(null, 'oauth_denied', {
        error,
        error_description: req.query.error_description
      }, req);

      return res.redirect(`/integration-failed?error=${encodeURIComponent(error)}`);
    }

    if (!code || !state) {
      return res.redirect('/integration-failed?error=missing_parameters');
    }

    // Validate state and get merchant ID
    const merchantId = await sepayOAuth.validateState(state);

    // Exchange code for tokens
    const tokens = await sepayOAuth.exchangeCodeForTokens(code);

    // Store tokens securely
    await sepayOAuth.storeTokensForMerchant(merchantId, tokens);

    await sepayOAuth.logOAuthEvent(merchantId, 'oauth_completed', {
      scopes: tokens.scope
    }, req);

    res.redirect('/integration-success');
  } catch (error) {
    console.error('OAuth callback error:', error);

    await sepayOAuth.logOAuthEvent(null, 'oauth_error', {
      error: error.message
    }, req);

    res.redirect(`/integration-failed?error=${encodeURIComponent(error.message)}`);
  }
});

// Check connection status
router.get('/status', requireAuth, async (req, res) => {
  try {
    const merchantId = req.user.id;
    const tokenData = await sepayOAuth.getTokenData(merchantId);

    if (!tokenData) {
      return res.json({ connected: false });
    }

    res.json({
      connected: true,
      scopes: tokenData.scopes.split(' '),
      expiresAt: tokenData.expires_at,
      connectedAt: tokenData.created_at
    });
  } catch (error) {
    console.error('Status check error:', error);
    res.status(500).json({ error: 'Failed to check connection status' });
  }
});

// Disconnect SePay integration
router.post('/disconnect', requireAuth, async (req, res) => {
  try {
    const merchantId = req.user.id;

    await sepayOAuth.revokeTokens(merchantId);

    res.json({ success: true, message: 'SePay integration disconnected' });
  } catch (error) {
    console.error('Disconnect error:', error);
    res.status(500).json({ error: 'Failed to disconnect SePay integration' });
  }
});

// Proxy API requests to SePay
router.all('/api/:endpoint(*)', requireAuth, async (req, res) => {
  try {
    const merchantId = req.user.id;
    const endpoint = req.params.endpoint;
    const startTime = Date.now();

    // Ensure valid token
    const token = await sepayOAuth.ensureValidToken(merchantId);

    // Prepare request options
    const options = {
      method: req.method,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        'User-Agent': 'MiddlewarePlatform/1.0.0'
      }
    };

    // Add body for non-GET requests
    if (req.method !== 'GET' && req.body) {
      options.body = JSON.stringify(req.body);
    }

    // Make request to SePay API
    const sepayResponse = await fetch(`https://my.sepay.vn/api/v1/${endpoint}`, options);
    const responseData = await sepayResponse.json();

    // Log API usage
    const responseTime = Date.now() - startTime;
    await sepayOAuth.db.query(`
      INSERT INTO api_usage_logs (merchant_id, endpoint, method, status_code, response_time_ms)
      VALUES ($1, $2, $3, $4, $5)
    `, [merchantId, endpoint, req.method, sepayResponse.status, responseTime]);

    // Return response
    res.status(sepayResponse.status).json(responseData);
  } catch (error) {
    console.error('API proxy error:', error);

    if (error.message.includes('No tokens found')) {
      return res.status(401).json({
        error: 'SePay integration not connected',
        code: 'SEPAY_NOT_CONNECTED'
      });
    }

    res.status(500).json({ error: 'API request failed' });
  }
});

module.exports = router;
```

#### Frontend JavaScript Examples

**OAuth2 Flow Initiation with State Management:**

```javascript
// public/js/sepay-integration.js
class SepayIntegration {
  constructor() {
    this.baseUrl = '/sepay';
    this.pollInterval = null;
  }

  // Initiate OAuth2 flow
  async connect(scopes = ['profile', 'bank-account:read', 'transaction:read']) {
    try {
      // Show loading state
      this.showConnectingState();

      // Get authorization URL from backend
      const response = await fetch(`${this.baseUrl}/connect?scopes=${scopes.join(',')}`, {
        method: 'GET',
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to initiate OAuth flow');
      }

      const { authUrl } = await response.json();

      // Open popup window for OAuth flow
      const popup = window.open(
        authUrl,
        'sepay-oauth',
        'width=600,height=700,scrollbars=yes,resizable=yes'
      );

      // Monitor popup for completion
      this.monitorPopup(popup);

    } catch (error) {
      console.error('Connection error:', error);
      this.showError('Failed to connect to SePay. Please try again.');
    }
  }

  // Monitor popup window for OAuth completion
  monitorPopup(popup) {
    this.pollInterval = setInterval(() => {
      try {
        // Check if popup is closed
        if (popup.closed) {
          clearInterval(this.pollInterval);
          this.checkConnectionStatus();
          return;
        }

        // Check if popup URL indicates completion
        const popupUrl = popup.location.href;
        if (popupUrl.includes('/integration-success')) {
          popup.close();
          clearInterval(this.pollInterval);
          this.handleConnectionSuccess();
        } else if (popupUrl.includes('/integration-failed')) {
          popup.close();
          clearInterval(this.pollInterval);
          this.handleConnectionError();
        }
      } catch (error) {
        // Cross-origin error - popup is still on SePay domain
        // Continue monitoring
      }
    }, 1000);
  }

  // Check current connection status
  async checkConnectionStatus() {
    try {
      const response = await fetch(`${this.baseUrl}/status`, {
        credentials: 'include'
      });

      const status = await response.json();

      if (status.connected) {
        this.showConnectedState(status);
      } else {
        this.showDisconnectedState();
      }
    } catch (error) {
      console.error('Status check error:', error);
      this.showError('Failed to check connection status');
    }
  }

  // Disconnect from SePay
  async disconnect() {
    if (!confirm('Are you sure you want to disconnect from SePay?')) {
      return;
    }

    try {
      const response = await fetch(`${this.baseUrl}/disconnect`, {
        method: 'POST',
        credentials: 'include'
      });

      if (response.ok) {
        this.showDisconnectedState();
        this.showSuccess('Successfully disconnected from SePay');
      } else {
        throw new Error('Disconnect failed');
      }
    } catch (error) {
      console.error('Disconnect error:', error);
      this.showError('Failed to disconnect from SePay');
    }
  }

  // Make API calls through the proxy
  async apiCall(endpoint, options = {}) {
    try {
      const response = await fetch(`${this.baseUrl}/api/${endpoint}`, {
        ...options,
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        }
      });

      if (response.status === 401) {
        const errorData = await response.json();
        if (errorData.code === 'SEPAY_NOT_CONNECTED') {
          this.showDisconnectedState();
          throw new Error('SePay integration not connected');
        }
      }

      return await response.json();
    } catch (error) {
      console.error('API call error:', error);
      throw error;
    }
  }

  // UI State Management
  showConnectingState() {
    const button = document.getElementById('sepay-connect-btn');
    if (button) {
      button.disabled = true;
      button.textContent = 'Connecting...';
    }
  }

  showConnectedState(status) {
    const container = document.getElementById('sepay-integration');
    if (container) {
      container.innerHTML = `
        <div class="sepay-connected">
          <div class="status-indicator connected"></div>
          <h3>SePay Connected</h3>
          <p>Connected on ${new Date(status.connectedAt).toLocaleDateString()}</p>
          <p>Permissions: ${status.scopes.join(', ')}</p>
          <button id="sepay-disconnect-btn" class="btn btn-danger">Disconnect</button>
        </div>
      `;

      document.getElementById('sepay-disconnect-btn')
        .addEventListener('click', () => this.disconnect());
    }
  }

  showDisconnectedState() {
    const container = document.getElementById('sepay-integration');
    if (container) {
      container.innerHTML = `
        <div class="sepay-disconnected">
          <div class="status-indicator disconnected"></div>
          <h3>Connect to SePay</h3>
          <p>Connect your SePay account to access transaction data and manage payments.</p>
          <button id="sepay-connect-btn" class="btn btn-primary">Connect SePay</button>
        </div>
      `;

      document.getElementById('sepay-connect-btn')
        .addEventListener('click', () => this.connect());
    }
  }

  showError(message) {
    // Implement your error display logic
    console.error(message);
    alert(message); // Replace with better UI
  }

  showSuccess(message) {
    // Implement your success display logic
    console.log(message);
  }

  handleConnectionSuccess() {
    this.showSuccess('Successfully connected to SePay!');
    this.checkConnectionStatus();
  }

  handleConnectionError() {
    this.showError('Failed to connect to SePay. Please try again.');
    this.showDisconnectedState();
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  window.sepayIntegration = new SepayIntegration();
  window.sepayIntegration.checkConnectionStatus();
});
```

#### Environment Configuration Examples

**Development Environment (.env.development):**
```bash
NODE_ENV=development
SEPAY_CLIENT_ID=dev_client_id_here
SEPAY_CLIENT_SECRET=dev_client_secret_here
SEPAY_REDIRECT_URI=http://localhost:3000/sepay/callback
DATABASE_URL=postgresql://user:password@localhost:5432/myapp_dev
TOKEN_ENCRYPTION_KEY=dev_encryption_key_32_characters
```

**Production Environment (.env.production):**
```bash
NODE_ENV=production
SEPAY_CLIENT_ID=prod_client_id_here
SEPAY_CLIENT_SECRET=prod_client_secret_here
SEPAY_REDIRECT_URI=https://yourapp.com/sepay/callback
DATABASE_URL=***************************************/myapp_prod
TOKEN_ENCRYPTION_KEY=prod_encryption_key_32_characters
```

## Security Best Practices & Compliance

### Client Credential Protection Strategies

#### 1. Environment Variable Management
```javascript
// ❌ Never hardcode credentials
const clientSecret = 'your_secret_here';

// ✅ Use environment variables
const clientSecret = process.env.SEPAY_CLIENT_SECRET;

// ✅ Validate environment variables on startup
function validateEnvironment() {
  const required = [
    'SEPAY_CLIENT_ID',
    'SEPAY_CLIENT_SECRET',
    'SEPAY_REDIRECT_URI',
    'TOKEN_ENCRYPTION_KEY'
  ];

  for (const env of required) {
    if (!process.env[env]) {
      throw new Error(`Missing required environment variable: ${env}`);
    }
  }
}
```

#### 2. Credential Rotation Procedures
```javascript
// Implement graceful credential rotation
class CredentialManager {
  constructor() {
    this.currentCredentials = {
      clientId: process.env.SEPAY_CLIENT_ID,
      clientSecret: process.env.SEPAY_CLIENT_SECRET
    };

    // Support for credential rotation
    this.nextCredentials = {
      clientId: process.env.SEPAY_CLIENT_ID_NEXT,
      clientSecret: process.env.SEPAY_CLIENT_SECRET_NEXT
    };
  }

  async exchangeTokenWithFallback(code) {
    try {
      // Try with current credentials
      return await this.exchangeToken(code, this.currentCredentials);
    } catch (error) {
      if (error.message.includes('invalid_client') && this.nextCredentials.clientId) {
        // Fallback to next credentials during rotation
        return await this.exchangeToken(code, this.nextCredentials);
      }
      throw error;
    }
  }
}
```

### Secure Token Storage Recommendations

#### 1. Encryption at Rest
```javascript
// Use strong encryption for token storage
const crypto = require('crypto');

class TokenEncryption {
  constructor(encryptionKey) {
    this.algorithm = 'aes-256-gcm';
    this.key = crypto.scryptSync(encryptionKey, 'salt', 32);
  }

  encrypt(text) {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(this.algorithm, this.key);
    cipher.setAAD(Buffer.from('sepay-token', 'utf8'));

    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const authTag = cipher.getAuthTag();

    return {
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex'),
      encrypted: encrypted
    };
  }

  decrypt(encryptedData) {
    const decipher = crypto.createDecipher(this.algorithm, this.key);
    decipher.setAAD(Buffer.from('sepay-token', 'utf8'));
    decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));

    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }
}
```

#### 2. Database Security Configuration
```sql
-- Enable row-level security
ALTER TABLE sepay_tokens ENABLE ROW LEVEL SECURITY;

-- Create policy for merchant isolation
CREATE POLICY merchant_token_isolation ON sepay_tokens
  FOR ALL TO application_user
  USING (merchant_id = current_setting('app.current_merchant_id')::integer);

-- Encrypt sensitive columns at database level
ALTER TABLE sepay_tokens
  ALTER COLUMN access_token TYPE bytea USING pgp_sym_encrypt(access_token, 'encryption_key'),
  ALTER COLUMN refresh_token TYPE bytea USING pgp_sym_encrypt(refresh_token, 'encryption_key');
```

### HTTPS Enforcement and Certificate Validation

#### 1. Express.js HTTPS Configuration
```javascript
const express = require('express');
const https = require('https');
const fs = require('fs');

const app = express();

// Force HTTPS in production
if (process.env.NODE_ENV === 'production') {
  app.use((req, res, next) => {
    if (req.header('x-forwarded-proto') !== 'https') {
      res.redirect(`https://${req.header('host')}${req.url}`);
    } else {
      next();
    }
  });
}

// Strict Transport Security
app.use((req, res, next) => {
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  next();
});

// Certificate validation for outbound requests
const httpsAgent = new https.Agent({
  rejectUnauthorized: true,
  checkServerIdentity: (host, cert) => {
    // Additional certificate validation logic
    return undefined; // No error
  }
});
```

### CSRF Protection Using State Parameters

#### 1. Enhanced State Parameter Generation
```javascript
class CSRFProtection {
  generateState(merchantId, additionalData = {}) {
    const timestamp = Date.now();
    const nonce = crypto.randomBytes(16).toString('hex');

    const stateData = {
      merchantId,
      timestamp,
      nonce,
      ...additionalData
    };

    // Sign the state to prevent tampering
    const signature = crypto
      .createHmac('sha256', process.env.STATE_SIGNING_KEY)
      .update(JSON.stringify(stateData))
      .digest('hex');

    const state = Buffer.from(JSON.stringify({
      ...stateData,
      signature
    })).toString('base64url');

    return state;
  }

  validateState(state) {
    try {
      const stateData = JSON.parse(Buffer.from(state, 'base64url').toString());

      // Check timestamp (max 10 minutes)
      if (Date.now() - stateData.timestamp > 10 * 60 * 1000) {
        throw new Error('State expired');
      }

      // Verify signature
      const { signature, ...dataToVerify } = stateData;
      const expectedSignature = crypto
        .createHmac('sha256', process.env.STATE_SIGNING_KEY)
        .update(JSON.stringify(dataToVerify))
        .digest('hex');

      if (signature !== expectedSignature) {
        throw new Error('Invalid state signature');
      }

      return stateData;
    } catch (error) {
      throw new Error('Invalid state parameter');
    }
  }
}
```

### Token Scope Limitation Principles

#### 1. Principle of Least Privilege
```javascript
// Define scope templates for different use cases
const SCOPE_TEMPLATES = {
  basic: ['profile'],
  financial_read: ['profile', 'bank-account:read', 'transaction:read'],
  webhook_management: ['profile', 'webhook:read', 'webhook:write'],
  full_integration: [
    'profile',
    'company',
    'bank-account:read',
    'transaction:read',
    'webhook:read',
    'webhook:write'
  ]
};

// Validate requested scopes
function validateScopes(requestedScopes, allowedScopes) {
  const invalid = requestedScopes.filter(scope => !allowedScopes.includes(scope));

  if (invalid.length > 0) {
    throw new Error(`Invalid scopes requested: ${invalid.join(', ')}`);
  }

  return requestedScopes;
}

// Scope-based API access control
function requireScope(requiredScope) {
  return async (req, res, next) => {
    const merchantId = req.user.id;
    const tokenData = await sepayOAuth.getTokenData(merchantId);

    if (!tokenData) {
      return res.status(401).json({ error: 'No SePay integration found' });
    }

    const grantedScopes = tokenData.scopes.split(' ');

    if (!grantedScopes.includes(requiredScope)) {
      return res.status(403).json({
        error: 'Insufficient permissions',
        required: requiredScope,
        granted: grantedScopes
      });
    }

    next();
  };
}
```

### Audit Logging Requirements

#### 1. Comprehensive Audit Trail
```javascript
class AuditLogger {
  constructor(db) {
    this.db = db;
  }

  async logSecurityEvent(eventType, details, req = null) {
    const logEntry = {
      event_type: eventType,
      timestamp: new Date(),
      ip_address: req ? req.ip : null,
      user_agent: req ? req.get('User-Agent') : null,
      session_id: req ? req.sessionID : null,
      merchant_id: details.merchantId || null,
      details: details,
      severity: this.getSeverity(eventType)
    };

    await this.db.query(`
      INSERT INTO security_audit_logs
      (event_type, timestamp, ip_address, user_agent, session_id, merchant_id, details, severity)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    `, [
      logEntry.event_type,
      logEntry.timestamp,
      logEntry.ip_address,
      logEntry.user_agent,
      logEntry.session_id,
      logEntry.merchant_id,
      JSON.stringify(logEntry.details),
      logEntry.severity
    ]);

    // Alert on high-severity events
    if (logEntry.severity === 'HIGH') {
      await this.sendSecurityAlert(logEntry);
    }
  }

  getSeverity(eventType) {
    const severityMap = {
      'oauth_initiated': 'LOW',
      'oauth_completed': 'MEDIUM',
      'oauth_denied': 'MEDIUM',
      'oauth_error': 'HIGH',
      'token_refresh_failed': 'HIGH',
      'invalid_state': 'HIGH',
      'credential_rotation': 'MEDIUM',
      'tokens_revoked': 'MEDIUM'
    };

    return severityMap[eventType] || 'MEDIUM';
  }

  async sendSecurityAlert(logEntry) {
    // Implement your alerting mechanism
    console.error('SECURITY ALERT:', logEntry);

    // Example: Send to monitoring service
    // await monitoringService.alert(logEntry);
  }
}
```

## Operational Procedures

### Merchant Onboarding Workflow

#### 1. Pre-Integration Checklist
```javascript
class MerchantOnboarding {
  async validateMerchantEligibility(merchantId) {
    const checks = [
      this.checkAccountStatus(merchantId),
      this.checkComplianceRequirements(merchantId),
      this.checkTechnicalRequirements(merchantId)
    ];

    const results = await Promise.all(checks);

    return {
      eligible: results.every(check => check.passed),
      requirements: results.filter(check => !check.passed)
    };
  }

  async initiateOnboarding(merchantId, integrationLevel = 'standard') {
    // Log onboarding initiation
    await this.auditLogger.logSecurityEvent('onboarding_initiated', {
      merchantId,
      integrationLevel
    });

    // Create onboarding record
    const onboardingId = await this.createOnboardingRecord(merchantId, integrationLevel);

    // Send welcome email with integration guide
    await this.sendOnboardingEmail(merchantId, onboardingId);

    return onboardingId;
  }

  async completeOnboarding(merchantId, onboardingId) {
    // Verify OAuth2 connection is established
    const tokenData = await this.sepayOAuth.getTokenData(merchantId);

    if (!tokenData) {
      throw new Error('OAuth2 connection not established');
    }

    // Mark onboarding as complete
    await this.updateOnboardingStatus(onboardingId, 'completed');

    // Enable full integration features
    await this.enableIntegrationFeatures(merchantId);

    // Send completion notification
    await this.sendCompletionNotification(merchantId);

    await this.auditLogger.logSecurityEvent('onboarding_completed', {
      merchantId,
      onboardingId
    });
  }
}
```

#### 2. Automated Onboarding Flow
```javascript
// Onboarding workflow automation
const onboardingWorkflow = {
  steps: [
    {
      name: 'account_verification',
      handler: async (merchantId) => {
        return await verifyMerchantAccount(merchantId);
      },
      required: true
    },
    {
      name: 'oauth_connection',
      handler: async (merchantId) => {
        const tokenData = await sepayOAuth.getTokenData(merchantId);
        return { completed: !!tokenData };
      },
      required: true
    },
    {
      name: 'webhook_setup',
      handler: async (merchantId) => {
        return await setupDefaultWebhooks(merchantId);
      },
      required: false
    },
    {
      name: 'test_transaction',
      handler: async (merchantId) => {
        return await performTestTransaction(merchantId);
      },
      required: false
    }
  ],

  async execute(merchantId) {
    const results = {};

    for (const step of this.steps) {
      try {
        results[step.name] = await step.handler(merchantId);

        if (step.required && !results[step.name].completed) {
          throw new Error(`Required step ${step.name} failed`);
        }
      } catch (error) {
        results[step.name] = { error: error.message };

        if (step.required) {
          break; // Stop on required step failure
        }
      }
    }

    return results;
  }
};
```

### Merchant Offboarding Procedures

#### 1. Graceful Disconnection Process
```javascript
class MerchantOffboarding {
  async initiateOffboarding(merchantId, reason = 'user_requested') {
    await this.auditLogger.logSecurityEvent('offboarding_initiated', {
      merchantId,
      reason
    });

    // Create offboarding record
    const offboardingId = await this.createOffboardingRecord(merchantId, reason);

    // Notify merchant of offboarding process
    await this.sendOffboardingNotification(merchantId, reason);

    return offboardingId;
  }

  async executeOffboarding(merchantId, offboardingId) {
    try {
      // 1. Revoke OAuth2 tokens
      await this.sepayOAuth.revokeTokens(merchantId);

      // 2. Disable webhooks
      await this.disableWebhooks(merchantId);

      // 3. Archive integration data
      await this.archiveIntegrationData(merchantId);

      // 4. Clean up temporary data
      await this.cleanupTemporaryData(merchantId);

      // 5. Update merchant status
      await this.updateMerchantStatus(merchantId, 'disconnected');

      // 6. Send confirmation
      await this.sendOffboardingConfirmation(merchantId);

      await this.auditLogger.logSecurityEvent('offboarding_completed', {
        merchantId,
        offboardingId
      });

    } catch (error) {
      await this.auditLogger.logSecurityEvent('offboarding_failed', {
        merchantId,
        offboardingId,
        error: error.message
      });

      throw error;
    }
  }

  async archiveIntegrationData(merchantId) {
    // Move active data to archive tables
    await this.db.query(`
      INSERT INTO sepay_tokens_archive
      SELECT *, NOW() as archived_at
      FROM sepay_tokens
      WHERE merchant_id = $1
    `, [merchantId]);

    await this.db.query(`
      INSERT INTO api_usage_logs_archive
      SELECT *, NOW() as archived_at
      FROM api_usage_logs
      WHERE merchant_id = $1
    `, [merchantId]);

    // Delete from active tables
    await this.db.query('DELETE FROM sepay_tokens WHERE merchant_id = $1', [merchantId]);
    await this.db.query('DELETE FROM api_usage_logs WHERE merchant_id = $1', [merchantId]);
  }
}
```

### Permission Revocation Handling

#### 1. Webhook Integration for Revocation Events
```javascript
// Handle SePay webhook for token revocation
app.post('/webhooks/sepay/revocation', async (req, res) => {
  try {
    // Verify webhook signature
    const signature = req.headers['x-sepay-signature'];
    const isValid = verifyWebhookSignature(req.body, signature);

    if (!isValid) {
      return res.status(401).json({ error: 'Invalid signature' });
    }

    const { event_type, data } = req.body;

    if (event_type === 'token.revoked') {
      await handleTokenRevocation(data);
    }

    res.status(200).json({ received: true });
  } catch (error) {
    console.error('Webhook processing error:', error);
    res.status(500).json({ error: 'Webhook processing failed' });
  }
});

async function handleTokenRevocation(data) {
  const { client_id, user_id } = data;

  // Find merchant by SePay user ID
  const merchant = await findMerchantBySepayUserId(user_id);

  if (merchant) {
    // Remove tokens from local storage
    await sepayOAuth.db.query(
      'DELETE FROM sepay_tokens WHERE merchant_id = $1',
      [merchant.id]
    );

    // Log revocation event
    await sepayOAuth.logOAuthEvent(merchant.id, 'tokens_revoked_by_user', {
      sepay_user_id: user_id,
      revocation_source: 'sepay_webhook'
    });

    // Notify merchant
    await notifyMerchantOfRevocation(merchant.id);

    // Disable integration features
    await disableIntegrationFeatures(merchant.id);
  }
}
```

### Token Invalidation Procedures

#### 1. Emergency Token Invalidation
```javascript
class EmergencyTokenManager {
  async invalidateAllTokens(reason = 'security_incident') {
    const startTime = Date.now();

    try {
      // Get all active tokens
      const activeTokens = await this.db.query(`
        SELECT merchant_id, access_token, refresh_token
        FROM sepay_tokens
        WHERE expires_at > NOW()
      `);

      // Revoke tokens at SePay in batches
      const batchSize = 10;
      for (let i = 0; i < activeTokens.rows.length; i += batchSize) {
        const batch = activeTokens.rows.slice(i, i + batchSize);

        await Promise.all(batch.map(async (tokenData) => {
          try {
            await this.revokeTokenAtSepay(tokenData.access_token);
          } catch (error) {
            console.error(`Failed to revoke token for merchant ${tokenData.merchant_id}:`, error);
          }
        }));
      }

      // Clear all tokens from local database
      await this.db.query('DELETE FROM sepay_tokens');

      // Log emergency invalidation
      await this.auditLogger.logSecurityEvent('emergency_token_invalidation', {
        reason,
        tokens_affected: activeTokens.rows.length,
        duration_ms: Date.now() - startTime
      });

      // Notify all affected merchants
      await this.notifyMerchantsOfInvalidation(activeTokens.rows, reason);

    } catch (error) {
      await this.auditLogger.logSecurityEvent('emergency_invalidation_failed', {
        reason,
        error: error.message,
        duration_ms: Date.now() - startTime
      });

      throw error;
    }
  }

  async invalidateMerchantTokens(merchantId, reason = 'security_concern') {
    try {
      await this.sepayOAuth.revokeTokens(merchantId);

      await this.auditLogger.logSecurityEvent('merchant_tokens_invalidated', {
        merchantId,
        reason
      });

      await this.notifyMerchantOfInvalidation(merchantId, reason);

    } catch (error) {
      await this.auditLogger.logSecurityEvent('merchant_invalidation_failed', {
        merchantId,
        reason,
        error: error.message
      });

      throw error;
    }
  }
}
```

### Monitoring and Alerting Setup

#### 1. Health Check Endpoints
```javascript
// Health check for OAuth2 integration
app.get('/health/sepay-oauth', async (req, res) => {
  const checks = {
    database: false,
    sepay_api: false,
    token_encryption: false,
    webhook_endpoint: false
  };

  try {
    // Database connectivity
    await sepayOAuth.db.query('SELECT 1');
    checks.database = true;

    // SePay API connectivity
    const response = await fetch('https://my.sepay.vn/api/v1/health', {
      timeout: 5000
    });
    checks.sepay_api = response.ok;

    // Token encryption/decryption
    const testToken = 'test_token_12345';
    const encrypted = sepayOAuth.encrypt(testToken);
    const decrypted = sepayOAuth.decrypt(encrypted);
    checks.token_encryption = decrypted === testToken;

    // Webhook endpoint accessibility
    checks.webhook_endpoint = true; // Implement actual check

  } catch (error) {
    console.error('Health check error:', error);
  }

  const allHealthy = Object.values(checks).every(check => check);

  res.status(allHealthy ? 200 : 503).json({
    status: allHealthy ? 'healthy' : 'unhealthy',
    checks,
    timestamp: new Date().toISOString()
  });
});
```

#### 2. Metrics Collection
```javascript
class MetricsCollector {
  constructor() {
    this.metrics = {
      oauth_flows_initiated: 0,
      oauth_flows_completed: 0,
      oauth_flows_failed: 0,
      token_refreshes: 0,
      api_calls_proxied: 0,
      active_integrations: 0
    };
  }

  incrementMetric(metricName, value = 1) {
    if (this.metrics.hasOwnProperty(metricName)) {
      this.metrics[metricName] += value;
    }
  }

  async collectMetrics() {
    // Collect current active integrations
    const result = await sepayOAuth.db.query(`
      SELECT COUNT(*) as count
      FROM sepay_tokens
      WHERE expires_at > NOW()
    `);

    this.metrics.active_integrations = parseInt(result.rows[0].count);

    return this.metrics;
  }

  async exportMetrics() {
    const metrics = await this.collectMetrics();

    // Export to monitoring system (Prometheus, DataDog, etc.)
    // Example for Prometheus format:
    let output = '';
    for (const [name, value] of Object.entries(metrics)) {
      output += `sepay_oauth_${name} ${value}\n`;
    }

    return output;
  }
}
```

### Backup and Disaster Recovery

#### 1. Token Backup Strategy
```javascript
class TokenBackupManager {
  async createBackup() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupId = `sepay-tokens-${timestamp}`;

    try {
      // Export encrypted tokens
      const tokens = await this.db.query(`
        SELECT
          merchant_id,
          access_token,
          refresh_token,
          expires_at,
          scopes,
          created_at,
          updated_at
        FROM sepay_tokens
      `);

      // Create backup file
      const backupData = {
        backup_id: backupId,
        created_at: new Date(),
        version: '1.0',
        tokens: tokens.rows
      };

      // Encrypt backup data
      const encryptedBackup = this.encryptBackup(JSON.stringify(backupData));

      // Store backup (cloud storage, encrypted filesystem, etc.)
      await this.storeBackup(backupId, encryptedBackup);

      // Log backup creation
      await this.auditLogger.logSecurityEvent('backup_created', {
        backup_id: backupId,
        token_count: tokens.rows.length
      });

      return backupId;

    } catch (error) {
      await this.auditLogger.logSecurityEvent('backup_failed', {
        backup_id: backupId,
        error: error.message
      });

      throw error;
    }
  }

  async restoreFromBackup(backupId) {
    try {
      // Retrieve backup
      const encryptedBackup = await this.retrieveBackup(backupId);

      // Decrypt backup data
      const backupData = JSON.parse(this.decryptBackup(encryptedBackup));

      // Validate backup integrity
      if (!this.validateBackup(backupData)) {
        throw new Error('Backup validation failed');
      }

      // Restore tokens
      for (const tokenData of backupData.tokens) {
        await this.db.query(`
          INSERT INTO sepay_tokens
          (merchant_id, access_token, refresh_token, expires_at, scopes, created_at, updated_at)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
          ON CONFLICT (merchant_id) DO NOTHING
        `, [
          tokenData.merchant_id,
          tokenData.access_token,
          tokenData.refresh_token,
          tokenData.expires_at,
          tokenData.scopes,
          tokenData.created_at,
          tokenData.updated_at
        ]);
      }

      await this.auditLogger.logSecurityEvent('backup_restored', {
        backup_id: backupId,
        tokens_restored: backupData.tokens.length
      });

    } catch (error) {
      await this.auditLogger.logSecurityEvent('restore_failed', {
        backup_id: backupId,
        error: error.message
      });

      throw error;
    }
  }
}
```

### Testing Procedures

#### 1. Sandbox Environment Setup
```javascript
// Test configuration for sandbox environment
const testConfig = {
  sepay: {
    baseUrl: 'https://sandbox.sepay.vn',
    clientId: process.env.SEPAY_SANDBOX_CLIENT_ID,
    clientSecret: process.env.SEPAY_SANDBOX_CLIENT_SECRET,
    redirectUri: 'http://localhost:3000/sepay/callback'
  },
  database: {
    url: process.env.TEST_DATABASE_URL
  }
};

// Test merchant data
const testMerchants = [
  {
    id: 'test_merchant_1',
    email: '<EMAIL>',
    name: 'Test Merchant 1'
  },
  {
    id: 'test_merchant_2',
    email: '<EMAIL>',
    name: 'Test Merchant 2'
  }
];
```

#### 2. Automated Test Suite
```javascript
// test/oauth-integration.test.js
const { describe, it, beforeEach, afterEach } = require('mocha');
const { expect } = require('chai');
const SepayOAuthService = require('../services/sepayOAuth');

describe('SePay OAuth2 Integration', () => {
  let sepayOAuth;
  let testMerchantId;

  beforeEach(async () => {
    sepayOAuth = new SepayOAuthService();
    testMerchantId = 'test_merchant_123';

    // Clean up test data
    await sepayOAuth.db.query('DELETE FROM sepay_tokens WHERE merchant_id = $1', [testMerchantId]);
    await sepayOAuth.db.query('DELETE FROM oauth_states WHERE merchant_id = $1', [testMerchantId]);
  });

  afterEach(async () => {
    // Clean up after tests
    await sepayOAuth.db.query('DELETE FROM sepay_tokens WHERE merchant_id = $1', [testMerchantId]);
    await sepayOAuth.db.query('DELETE FROM oauth_states WHERE merchant_id = $1', [testMerchantId]);
  });

  describe('OAuth2 Flow', () => {
    it('should generate valid authorization URL', () => {
      const authUrl = sepayOAuth.buildAuthorizationUrl(testMerchantId);

      expect(authUrl).to.include('https://my.sepay.vn/oauth/authorize');
      expect(authUrl).to.include('response_type=code');
      expect(authUrl).to.include('client_id=');
      expect(authUrl).to.include('state=');
    });

    it('should validate state parameter correctly', async () => {
      const state = sepayOAuth.generateState();
      await sepayOAuth.storeState(testMerchantId, state);

      const merchantId = await sepayOAuth.validateState(state);
      expect(merchantId).to.equal(testMerchantId);
    });

    it('should reject invalid state parameter', async () => {
      try {
        await sepayOAuth.validateState('invalid_state');
        expect.fail('Should have thrown error');
      } catch (error) {
        expect(error.message).to.include('Invalid or expired state');
      }
    });
  });

  describe('Token Management', () => {
    it('should encrypt and decrypt tokens correctly', () => {
      const originalToken = 'test_access_token_12345';
      const encrypted = sepayOAuth.encrypt(originalToken);
      const decrypted = sepayOAuth.decrypt(encrypted);

      expect(decrypted).to.equal(originalToken);
      expect(encrypted).to.not.equal(originalToken);
    });

    it('should store and retrieve token data', async () => {
      const tokenData = {
        access_token: 'test_access_token',
        refresh_token: 'test_refresh_token',
        expires_in: 3600,
        scope: 'profile bank-account:read'
      };

      await sepayOAuth.storeTokensForMerchant(testMerchantId, tokenData);

      const stored = await sepayOAuth.getTokenData(testMerchantId);
      expect(stored).to.not.be.null;
      expect(stored.scopes).to.equal(tokenData.scope);
    });
  });

  describe('Error Handling', () => {
    it('should handle missing tokens gracefully', async () => {
      try {
        await sepayOAuth.ensureValidToken('nonexistent_merchant');
        expect.fail('Should have thrown error');
      } catch (error) {
        expect(error.message).to.include('No tokens found');
      }
    });
  });
});
```

## Troubleshooting & Maintenance

### Common Integration Errors

#### 1. Authorization Code Exchange Failures

**Error**: `invalid_grant` - Authorization code is invalid or expired

**Causes**:
- Authorization code used more than once
- Code expired (>5 minutes old)
- Mismatched redirect_uri
- Invalid client credentials

**Solutions**:
```javascript
// Enhanced error handling for token exchange
async function exchangeCodeForTokensWithRetry(code, maxRetries = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await sepayOAuth.exchangeCodeForTokens(code);
    } catch (error) {
      console.error(`Token exchange attempt ${attempt} failed:`, error.message);

      if (error.message.includes('invalid_grant')) {
        // Don't retry invalid_grant errors
        throw new Error('Authorization code is invalid or expired. Please restart the OAuth flow.');
      }

      if (attempt === maxRetries) {
        throw error;
      }

      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
}
```

#### 2. State Parameter Validation Errors

**Error**: Invalid or expired state parameter

**Causes**:
- CSRF attack attempt
- State parameter expired
- Session storage issues
- Multiple browser tabs/windows

**Solutions**:
```javascript
// Enhanced state validation with detailed logging
async function validateStateWithLogging(state, req) {
  try {
    const merchantId = await sepayOAuth.validateState(state);

    await sepayOAuth.logOAuthEvent(merchantId, 'state_validated', {
      state_length: state.length,
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    }, req);

    return merchantId;
  } catch (error) {
    await sepayOAuth.logOAuthEvent(null, 'state_validation_failed', {
      state_provided: !!state,
      state_length: state ? state.length : 0,
      error: error.message,
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    }, req);

    throw error;
  }
}
```

#### 3. Token Refresh Failures

**Error**: `invalid_grant` - Refresh token is invalid

**Causes**:
- Refresh token expired (>30 days)
- Token revoked by user
- Client credentials changed
- Database corruption

**Solutions**:
```javascript
// Robust token refresh with fallback
async function refreshTokenWithFallback(merchantId) {
  try {
    return await sepayOAuth.refreshAccessToken(merchantId);
  } catch (error) {
    if (error.message.includes('invalid_grant')) {
      // Refresh token is invalid - require re-authorization
      await sepayOAuth.db.query('DELETE FROM sepay_tokens WHERE merchant_id = $1', [merchantId]);

      await sepayOAuth.logOAuthEvent(merchantId, 'refresh_failed_reauth_required', {
        error: error.message
      });

      throw new Error('REAUTH_REQUIRED');
    }

    throw error;
  }
}
```

### API Response Examples

#### 1. Successful Token Exchange
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "refresh_token": "def50200a1b2c3d4e5f6...",
  "scope": "profile bank-account:read transaction:read"
}
```

#### 2. Error Responses
```json
// Invalid authorization code
{
  "error": "invalid_grant",
  "error_description": "The provided authorization grant is invalid, expired, revoked, does not match the redirection URI used in the authorization request, or was issued to another client."
}

// Invalid client credentials
{
  "error": "invalid_client",
  "error_description": "Client authentication failed"
}

// Invalid scope
{
  "error": "invalid_scope",
  "error_description": "The requested scope is invalid, unknown, or malformed"
}
```

### Debugging Checklist

#### 1. OAuth2 Flow Issues
- [ ] Verify client_id and client_secret are correct
- [ ] Check redirect_uri matches exactly (including trailing slashes)
- [ ] Ensure HTTPS is used in production
- [ ] Validate state parameter generation and storage
- [ ] Check authorization code is used within 5 minutes
- [ ] Verify scopes are valid and approved

#### 2. Token Management Issues
- [ ] Check token encryption/decryption is working
- [ ] Verify database connectivity and schema
- [ ] Ensure token refresh logic is implemented
- [ ] Check for token expiration handling
- [ ] Validate token storage security

#### 3. API Integration Issues
- [ ] Verify Bearer token format in Authorization header
- [ ] Check API endpoint URLs are correct
- [ ] Ensure proper error handling for 401/403 responses
- [ ] Validate request/response content types
- [ ] Check rate limiting implementation

### Performance Optimization Guidelines

#### 1. Database Optimization
```sql
-- Optimize token queries
CREATE INDEX CONCURRENTLY idx_sepay_tokens_expires_at ON sepay_tokens(expires_at) WHERE expires_at > NOW();
CREATE INDEX CONCURRENTLY idx_oauth_states_expires_at ON oauth_states(expires_at) WHERE expires_at > NOW();

-- Partition large tables by date
CREATE TABLE api_usage_logs_2024_01 PARTITION OF api_usage_logs
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- Clean up expired data regularly
DELETE FROM oauth_states WHERE expires_at < NOW() - INTERVAL '1 hour';
DELETE FROM api_usage_logs WHERE created_at < NOW() - INTERVAL '90 days';
```

#### 2. Caching Strategy
```javascript
const Redis = require('redis');
const redis = Redis.createClient(process.env.REDIS_URL);

class TokenCache {
  async getValidToken(merchantId) {
    // Check cache first
    const cachedToken = await redis.get(`sepay_token:${merchantId}`);

    if (cachedToken) {
      const tokenData = JSON.parse(cachedToken);

      // Check if token expires within 5 minutes
      if (new Date(tokenData.expiresAt) > new Date(Date.now() + 5 * 60 * 1000)) {
        return tokenData.accessToken;
      }
    }

    // Get fresh token from database
    const token = await sepayOAuth.ensureValidToken(merchantId);

    // Cache for 50 minutes (10 minutes before expiry)
    const tokenData = await sepayOAuth.getTokenData(merchantId);
    await redis.setex(
      `sepay_token:${merchantId}`,
      50 * 60, // 50 minutes
      JSON.stringify({
        accessToken: token,
        expiresAt: tokenData.expires_at
      })
    );

    return token;
  }

  async invalidateToken(merchantId) {
    await redis.del(`sepay_token:${merchantId}`);
  }
}
```

### Version Migration Strategies

#### 1. API Version Management
```javascript
class ApiVersionManager {
  constructor() {
    this.supportedVersions = ['v1', 'v2'];
    this.defaultVersion = 'v1';
  }

  async migrateToNewVersion(fromVersion, toVersion) {
    console.log(`Migrating from API ${fromVersion} to ${toVersion}`);

    // Get all active integrations
    const activeTokens = await this.db.query(`
      SELECT merchant_id FROM sepay_tokens WHERE expires_at > NOW()
    `);

    for (const token of activeTokens.rows) {
      try {
        await this.migrateIntegration(token.merchant_id, fromVersion, toVersion);
      } catch (error) {
        console.error(`Migration failed for merchant ${token.merchant_id}:`, error);
      }
    }
  }

  async migrateIntegration(merchantId, fromVersion, toVersion) {
    // Version-specific migration logic
    if (fromVersion === 'v1' && toVersion === 'v2') {
      // Update webhook URLs
      await this.updateWebhookUrls(merchantId);

      // Update scope mappings
      await this.updateScopeMappings(merchantId);

      // Test new API endpoints
      await this.testNewEndpoints(merchantId);
    }
  }
}
```

## Conclusion

This comprehensive guide provides middleware platforms with everything needed to implement secure, production-ready SePay OAuth2 integrations. The architecture prioritizes security by keeping client credentials server-side while providing merchants with seamless access to their SePay data.

### Key Takeaways

1. **Security First**: Never expose client credentials to frontend applications
2. **Robust Error Handling**: Implement comprehensive error handling and retry logic
3. **Monitoring & Logging**: Maintain detailed audit trails for compliance
4. **Scalable Architecture**: Design for multiple merchants with proper data isolation
5. **Operational Excellence**: Implement proper backup, monitoring, and disaster recovery procedures

### Next Steps

1. Review your current integration against this guide
2. Implement missing security measures
3. Set up monitoring and alerting
4. Test thoroughly in sandbox environment
5. Plan for production deployment with proper backup procedures

For additional support or questions about SePay OAuth2 integration, contact the SePay developer support team.
