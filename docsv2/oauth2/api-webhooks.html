<!DOCTYPE html>
<html lang="vi">
    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <title>OAuth2 API Webhooks - SePay</title>
        <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet" />
        <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
        <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
        <link rel="stylesheet" href="../assets/vendor/bootstrap-icons/font/bootstrap-icons.css" />
        <link rel="stylesheet" href="../assets/css/theme.min.css" />
        <link rel="stylesheet" href="../assets/css/docs.min.css" />
        <link rel="stylesheet" href="../assets/css/contact-box.css" />
        <link rel="stylesheet" href="../assets/highlight/styles/vs2015.min.css" />
        <style>
            .docs-navbar-sidebar-aside-body {
                padding-top: 3.5rem !important;
            }
            pre {
                border-radius: 0.5rem;
            }
        </style>
        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                dataLayer.push(arguments);
            }
            gtag("js", new Date());

            gtag("config", "G-J8DLMQTKSQ");
        </script>
    </head>

    <body class="navbar-sidebar-aside-lg">
        <header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
            <div class="container-fluid">
                <nav class="navbar-nav-wrap">
                    <div class="row flex-grow-1">
                        <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
                            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                                <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo" />
                            </a>
                        </div>

                        <div class="col-md px-lg-0">
                            <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
                                <div class="d-none d-md-block">
                                    <div class="d-none">
                                        <div id="searchTemplate" class="dropdown-item">
                                            <a class="d-block link" href="#">
                                                <span class="category d-block fw-normal text-muted mb-1"></span>
                                                <span class="component text-dark"></span>
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <ul class="navbar-nav p-0">
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html" target="_blank"> Support <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN" target="_blank"> Youtube <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://www.facebook.com/messages/t/sepay.vn" target="_blank"> Facebook <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel" target="_blank"> Telegram <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </nav>
            </div>
        </header>
        <main id="content" role="main">
            <nav
                class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
                data-hs-nav-scroller-options='{
                    "type": "vertical",
                    "target": ".navbar-nav .active",
                    "offset": 80
                }'
            >
                <button
                    type="button"
                    class="navbar-toggler btn btn-white d-grid w-100"
                    data-bs-toggle="collapse"
                    data-bs-target="#navbarVerticalNavMenu"
                    aria-label="Toggle navigation"
                    aria-expanded="false"
                    aria-controls="navbarVerticalNavMenu"
                >
                    <span class="d-flex justify-content-between align-items-center">
                        <span class="h3 mb-0">Nav menu</span>

                        <span class="navbar-toggler-default">
                            <i class="bi-list"></i>
                        </span>

                        <span class="navbar-toggler-toggled">
                            <i class="bi-x"></i>
                        </span>
                    </span>
                </button>

                <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
                    <div class="navbar-brand-wrapper border-end" style="height: auto;">
                        <div class="d-flex align-items-center mb-3">
                            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                                <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo" />
                            </a>
                        </div>
                    </div>

                    <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
                        <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
                            <li class="nav-item">
                                <span class="nav-subtitle">SePay OAuth2</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/">Tổng quan</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/dang-ky-ung-dung.html">Đăng ký ứng dụng</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/luong-xac-thuc.html">Luồng xác thực</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/access-token.html">Access Token</a>
                            </li>

                            <li class="nav-item my-2 my-lg-5"></li>

                            <li class="nav-item">
                                <span class="nav-subtitle">API</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-tai-khoan-ngan-hang.html">Tài khoản ngân hàng</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-giao-dich.html">Giao dịch</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link active" href="/oauth2/api-webhooks.html">Webhook</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-nguoi-dung.html">Người dùng</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-cong-ty.html">Công ty</a>
                            </li>

                        </ul>
                    </div>
                </div>
            </nav>

            <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">
                <div style="max-width: 1200px;">
                    <div class="docs-page-header">
                        <div class="row align-items-center">
                            <div class="col-sm">
                                <h1 class="docs-page-header-title">API Webhooks</h1>
                                <p class="docs-page-header-text">Tài liệu về cách sử dụng API webhooks thông qua OAuth2 trong SePay.</p>
                            </div>
                        </div>
                    </div>
                    
                    <h3 id="gioi-thieu" class="hs-docs-heading">
                        Giới thiệu <a class="anchorjs-link" href="#gioi-thieu" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>API Webhooks của SePay cho phép bạn quản lý các webhook để nhận thông báo thời gian thực về giao dịch. Webhook là một cách hiệu quả để tự động hóa quy trình thanh toán, giúp hệ thống của bạn nhận được thông báo ngay khi có giao dịch mới.</p>
                    
                    <div class="alert alert-soft-primary" role="alert">
                        <div class="d-flex align-items-baseline">
                            <div class="flex-shrink-0">
                                <i class="bi-info-circle me-1"></i>
                            </div>
                            <div class="flex-grow-1 ms-2">
                                Để sử dụng API này, bạn cần có các quyền tương ứng trong phạm vi (scope) của Access Token: <code>webhook:read</code> để xem, <code>webhook:write</code> để tạo/cập nhật, và <code>webhook:delete</code> để xóa webhook.
                            </div>
                        </div>
                    </div>
                    
                    <h3 id="endpoints" class="hs-docs-heading">
                        Các Endpoints <a class="anchorjs-link" href="#endpoints" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>API Webhooks cung cấp các endpoints sau:</p>
                    
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                <th>Method</th>
                                <th>Endpoint</th>
                                <th>Mô tả</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>GET</td>
                                <td><code>/api/v1/webhooks</code></td>
                                <td>Lấy danh sách webhooks với các tùy chọn lọc</td>
                            </tr>
                            <tr>
                                <td>GET</td>
                                <td><code>/api/v1/webhooks/{id}</code></td>
                                <td>Lấy thông tin chi tiết về một webhook cụ thể</td>
                            </tr>
                            <tr>
                                <td>POST</td>
                                <td><code>/api/v1/webhooks</code></td>
                                <td>Tạo webhook mới</td>
                            </tr>
                            <tr>
                                <td>PATCH</td>
                                <td><code>/api/v1/webhooks/{id}</code></td>
                                <td>Cập nhật thông tin webhook</td>
                            </tr>
                            <tr>
                                <td>DELETE</td>
                                <td><code>/api/v1/webhooks/{id}</code></td>
                                <td>Xóa webhook</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <h3 id="danh-sach-webhook" class="hs-docs-heading">
                        Lấy danh sách webhooks <a class="anchorjs-link" href="#danh-sach-webhook" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p><span class="badge bg-primary">GET</span> <code>/api/v1/webhooks</code></p>
                    
                    <p>Endpoint này trả về danh sách webhooks của công ty bạn. Bạn có thể lọc kết quả theo nhiều tiêu chí khác nhau.</p>
                    
                    <h4>Quyền yêu cầu</h4>
                    <ul>
                        <li>Scope: <code>webhook:read</code></li>
                        <li>Quyền người dùng: <strong>Webhooks</strong> (<code>Xem danh sách webhooks</code>)</li>
                    </ul>

                    <h4>Tham số truy vấn</h4>
                    
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Tham số</th>
                                    <th>Kiểu</th>
                                    <th>Mô tả</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>webhook_url</code></td>
                                    <td>string</td>
                                    <td>Lọc theo URL webhook (tìm kiếm một phần)</td>
                                </tr>
                                <tr>
                                    <td><code>api_key</code></td>
                                    <td>string</td>
                                    <td>Lọc theo API key</td>
                                </tr>
                                <tr>
                                    <td><code>active</code></td>
                                    <td>integer</td>
                                    <td>Lọc theo trạng thái hoạt động (0: không hoạt động, 1: đang hoạt động)</td>
                                </tr>
                                <tr>
                                    <td><code>page</code></td>
                                    <td>integer</td>
                                    <td>Số trang, bắt đầu từ 1</td>
                                </tr>
                                <tr>
                                    <td><code>limit</code></td>
                                    <td>integer</td>
                                    <td>Số lượng kết quả trên mỗi trang</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <h4>Yêu cầu</h4>
                    
                    <pre><code class="language-http">GET /api/v1/webhooks?active=1
Authorization: Bearer {YOUR_ACCESS_TOKEN}</code></pre>
                    
                    <h4>Phản hồi</h4>
                    
                    <pre style="max-height: 35rem;"><code class="language-json">{
    "status": "success",
    "data": [
        {
            "id": 23,
            "bank_account_id": 19,
            "name": "Tích hợp với shop online",
            "event_type": "In_only",
            "authen_type": "Api_Key",
            "webhook_url": "https://example.com/webhook/payment",
            "is_verify_payment": true,
            "skip_if_no_code": true,
            "retry_conditions": {
                "non_2xx_status_code": 0
            },
            "only_va": true,
            "active": true,
            "created_at": "2025-02-15 14:23:56",
            "api_key": "a7c3b4e5f6a7b8c9d0e1f2a3b4c5d6e7",
            "request_content_type": "Json",
            "bank_sub_account_ids": [25, 26]
        },
        {
            "id": 22,
            "bank_account_id": 18,
            "name": "Tích hợp với CRM",
            "event_type": "All",
            "authen_type": "OAuth2.0",
            "webhook_url": "https://crm.example.com/webhook/transactions",
            "is_verify_payment": false,
            "skip_if_no_code": false,
            "retry_conditions": {
                "non_2xx_status_code": 0
            },
            "only_va": false,
            "active": true,
            "created_at": "2025-02-10 09:45:32",
            "oauth2_client_id": "client_id_example",
            "oauth2_client_secret": "client_secret_example",
            "oauth2_access_token_url": "https://crm.example.com/oauth/token"
        }
    ],
    "meta": {
        "pagination": {
            "total": 5,
            "per_page": 20,
            "current_page": 1,
            "last_page": 1
        }
    }
}</code></pre>
                    
                    <h3 id="chi-tiet-webhook" class="hs-docs-heading">
                        Lấy chi tiết webhook <a class="anchorjs-link" href="#chi-tiet-webhook" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p><span class="badge bg-primary">GET</span> <code>/api/v1/webhooks/{id}</code></p>
                    
                    <p>Endpoint này trả về thông tin chi tiết của một webhook dựa trên ID.</p>
                    
                    <h4>Quyền yêu cầu</h4>
                    <ul>
                        <li>Scope: <code>webhook:read</code></li>
                        <li>Quyền người dùng: <strong>Webhooks</strong> (<code>Xem danh sách webhooks</code>)</li>
                    </ul>
                    
                    <h4>Tham số đường dẫn</h4>
                    
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Tham số</th>
                                    <th>Kiểu</th>
                                    <th>Mô tả</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>id</code></td>
                                    <td>integer</td>
                                    <td>ID của webhook</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <h4>Yêu cầu</h4>
                    
                    <pre><code class="language-http">GET /api/v1/webhooks/23
Authorization: Bearer {YOUR_ACCESS_TOKEN}</code></pre>
                    
                    <h4>Phản hồi</h4>
                    
                    <pre><code class="language-json">{
    "status": "success",
    "data": {
        "id": 23,
        "bank_account_id": 19,
        "name": "Tích hợp với shop online",
        "event_type": "In_only",
        "authen_type": "Api_Key",
        "webhook_url": "https://example.com/webhook/payment",
        "is_verify_payment": true,
        "skip_if_no_code": true,
        "retry_conditions": {
        "only_va": true,
        "active": true,
        "created_at": "2025-02-15 14:23:56",
        "api_key": "a7c3b4e5f6a7b8c9d0e1f2a3b4c5d6e7",
        "request_content_type": "Json",
        "bank_sub_account_ids": [25, 26]
    }
}</code></pre>
                    
                    <h3 id="tao-webhook" class="hs-docs-heading">
                        Tạo webhook mới <a class="anchorjs-link" href="#tao-webhook" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p><span class="badge bg-success">POST</span> <code>/api/v1/webhooks</code></p>
                    
                    <p>Endpoint này cho phép bạn tạo một webhook mới để nhận thông báo về giao dịch.</p>
                    
                    <h4>Quyền yêu cầu</h4>
                    <ul>
                        <li>Scope: <code>webhook:write</code></li>
                        <li>Quyền người dùng: <strong>Webhooks</strong> (<code>Thêm webhooks</code>)</li>
                    </ul>

                    <h4>Tham số yêu cầu</h4>

                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Tham số</th>
                                    <th>Kiểu</th>
                                    <th>Yêu cầu</th>
                                    <th>Mô tả</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>bank_account_id</code></td>
                                    <td>integer</td>
                                    <td>Bắt buộc</td>
                                    <td>ID của tài khoản ngân hàng</td>
                                </tr>
                                <tr>
                                    <td><code>name</code></td>
                                    <td>string</td>
                                    <td>Bắt buộc</td>
                                    <td>Tên của webhook</td>
                                </tr>
                                <tr>
                                    <td><code>event_type</code></td>
                                    <td>string</td>
                                    <td>Bắt buộc</td>
                                    <td>Loại sự kiện (<code>All</code>, <code>In_only</code>, <code>Out_only</code>)</td>
                                </tr>
                                <tr>
                                    <td><code>authen_type</code></td>
                                    <td>string</td>
                                    <td>Bắt buộc</td>
                                    <td>Kiểu xác thực (<code>No_Authen</code>, <code>OAuth2.0</code>, <code>Api_Key</code>)</td>
                                </tr>
                                <tr>
                                    <td><code>webhook_url</code></td>
                                    <td>string</td>
                                    <td>Bắt buộc</td>
                                    <td>URL nhận webhook</td>
                                </tr>
                                <tr>
                                    <td><code>is_verify_payment</code></td>
                                    <td>integer</td>
                                    <td>Bắt buộc</td>
                                    <td>Có xác thực thanh toán không (0: không, 1: có)</td>
                                </tr>
                                <tr>
                                    <td><code>skip_if_no_code</code></td>
                                    <td>integer</td>
                                    <td>Tùy chọn</td>
                                    <td>Bỏ qua nếu không có mã thanh toán (0: không, 1: có)</td>
                                </tr>
                                <tr>
                                    <td><code>active</code></td>
                                    <td>integer</td>
                                    <td>Tùy chọn</td>
                                    <td>Trạng thái hoạt động (0: không hoạt động, 1: đang hoạt động)</td>
                                </tr>
                                <tr>
                                    <td><code>retry_conditions</code></td>
                                    <td>array</td>
                                    <td>Tùy chọn</td>
                                    <td>Điều kiện thử lại khi gặp lỗi</td>
                                </tr>
                                <tr>
                                    <td><code>only_va</code></td>
                                    <td>integer</td>
                                    <td>Tùy chọn</td>
                                    <td>Chỉ nhận giao dịch từ tài khoản ảo (0: không, 1: có)</td>
                                </tr>
                                <tr>
                                    <td><code>bank_sub_account_ids</code></td>
                                    <td>array</td>
                                    <td>Bắt buộc nếu only_va=1</td>
                                    <td>Danh sách ID tài khoản ảo</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <p>Thêm tham số cho từng kiểu xác thực:</p>

                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Kiểu xác thực</th>
                                    <th>Tham số bổ sung</th>
                                    <th>Yêu cầu</th>
                                    <th>Mô tả</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td rowspan="3"><code>OAuth2.0</code></td>
                                    <td><code>oauth2_access_token_url</code></td>
                                    <td>Bắt buộc</td>
                                    <td>URL để lấy access token</td>
                                </tr>
                                <tr>
                                    <td><code>oauth2_client_id</code></td>
                                    <td>Bắt buộc</td>
                                    <td>Client ID</td>
                                </tr>
                                <tr>
                                    <td><code>oauth2_client_secret</code></td>
                                    <td>Bắt buộc</td>
                                    <td>Client Secret</td>
                                </tr>
                                <tr>
                                    <td rowspan="2"><code>Api_Key</code></td>
                                    <td><code>api_key</code></td>
                                    <td>Bắt buộc</td>
                                    <td>API Key</td>
                                </tr>
                                <tr>
                                    <td><code>request_content_type</code></td>
                                    <td>Bắt buộc</td>
                                    <td>Kiểu nội dung yêu cầu (<code>Json</code>, <code>multipart_form-data</code>)</td>
                                </tr>
                                <tr>
                                    <td><code>No_Authen</code></td>
                                    <td><code>request_content_type</code></td>
                                    <td>Bắt buộc</td>
                                    <td>Kiểu nội dung yêu cầu (<code>Json</code>, <code>multipart_form-data</code>)</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <h4>Yêu cầu</h4>
                    
                    <pre><code class="language-http">POST /api/v1/webhooks
Content-Type: application/json
Authorization: Bearer {YOUR_ACCESS_TOKEN}

{
    "bank_account_id": 19,
    "name": "Tích hợp với shop online",
    "event_type": "In_only",
    "authen_type": "Api_Key",
    "webhook_url": "https://example.com/webhook/payment",
    "is_verify_payment": 1,
    "skip_if_no_code": 1,
    "active": 1,
    "only_va": 1,
    "bank_sub_account_ids": [25, 26],
    "retry_conditions": {
        "non_2xx_status_code": 1
    },
    "api_key": "a7c3b4e5f6a7b8c9d0e1f2a3b4c5d6e7",
    "request_content_type": "Json"
}</code></pre>
                    
                    <h4>Phản hồi</h4>
                    
                    <pre><code class="language-json">{
    "message": "Thêm WebHooks thành công",
    "id": 23,
}</code></pre>
                    
                    <h3 id="cap-nhat-webhook" class="hs-docs-heading">
                        Cập nhật webhook <a class="anchorjs-link" href="#cap-nhat-webhook" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p><span class="badge bg-success">PATCH</span> <code>/api/v1/webhooks/{id}</code></p>
                    
                    <p>Endpoint này cho phép bạn cập nhật thông tin của một webhook hiện có.</p>
                    
                    <h4>Quyền yêu cầu</h4>
                    <ul>
                        <li>Scope: <code>webhook:write</code></li>
                        <li>Quyền người dùng: <strong>Webhooks</strong> (<code>Sửa webhooks</code>)</li>
                    </ul>
                    
                    <h4>Tham số đường dẫn</h4>
                    
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                <th>Tham số</th>
                                <th>Kiểu</th>
                                <th>Mô tả</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>id</code></td>
                                <td>integer</td>
                                <td>ID của webhook</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <h4>Tham số yêu cầu</h4>
                    
                    <p>Tham số yêu cầu giống như khi tạo webhook, nhưng tất cả đều là tùy chọn. Bạn chỉ cần cung cấp các tham số cần thay đổi.</p>
                    
                    <h4>Yêu cầu</h4>
                    
                    <pre><code class="language-http">PATCH /api/v1/webhooks/23
Content-Type: application/json
Authorization: Bearer {YOUR_ACCESS_TOKEN}

{
    "active": 0,
    "skip_if_no_code": 0
}</code></pre>
                    
                    <h4>Phản hồi</h4>
                    
                    <pre><code class="language-json">{
    "message": "Cập nhật WebHooks thành công",
    "id": "23",
}</code></pre>
                    
                    <h3 id="xoa-webhook" class="hs-docs-heading">
                        Xóa webhook <a class="anchorjs-link" href="#xoa-webhook" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p><span class="badge bg-danger">DELETE</span> <code>/api/v1/webhooks/{id}</code></p>
                    
                    <p>Endpoint này cho phép bạn xóa một webhook.</p>
                    
                    <h4>Quyền yêu cầu</h4>
                    <ul>
                        <li>Scope: <code>webhook:delete</code></li>
                        <li>Quyền người dùng: <strong>Webhooks</strong> (<code>Xóa webhooks</code>)</li>
                    </ul>
                    
                    <h4>Tham số đường dẫn</h4>
                    
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Tham số</th>
                                    <th>Kiểu</th>
                                    <th>Mô tả</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>id</code></td>
                                    <td>integer</td>
                                    <td>ID của webhook</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <h4>Yêu cầu</h4>
                    
                    <pre><code class="language-http">DELETE /api/v1/webhooks/23
Authorization: Bearer {YOUR_ACCESS_TOKEN}</code></pre>
                    
                    <h4>Phản hồi</h4>
                    
                    <pre><code class="language-json">{
    "message": "Xóa WebHooks thành công"
}</code></pre>

                    <h3 id="vi-du-su-dung" class="hs-docs-heading">
                        Ví dụ sử dụng <a class="anchorjs-link" href="#vi-du-su-dung" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>

                    <div class="mb-3">
                        <nav>
                            <div class="nav nav-tabs" id="nav-tab" role="tablist">
                                <button class="nav-link active" id="nav-curl-tab" data-bs-toggle="tab" data-bs-target="#nav-curl" type="button" role="tab" aria-controls="nav-curl" aria-selected="true">cURL</button>
                                <button class="nav-link" id="nav-php-tab" data-bs-toggle="tab" data-bs-target="#nav-php" type="button" role="tab" aria-controls="nav-php" aria-selected="false">PHP</button>
                                <button class="nav-link" id="nav-nodejs-tab" data-bs-toggle="tab" data-bs-target="#nav-nodejs" type="button" role="tab" aria-controls="nav-nodejs" aria-selected="false">Node.js</button>
                            </div>
                        </nav>
                        <div class="tab-content" id="nav-tabContent">
                            <div class="tab-pane fade show active" id="nav-curl" role="tabpanel" aria-labelledby="nav-curl-tab">
                                <pre style="border-top-left-radius: 0; max-height: 35rem;"><code class="language-bash"># Lấy danh sách webhooks
curl -X GET "https://my.sepay.vn/api/v1/webhooks" \
        -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Lấy chi tiết webhook
curl -X GET "https://my.sepay.vn/api/v1/webhooks/23" \
        -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Tạo webhook mới
curl -X POST "https://my.sepay.vn/api/v1/webhooks" \
        -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "bank_account_id": 19,
            "name": "Tích hợp với shop online",
            "event_type": "In_only",
            "authen_type": "Api_Key",
            "webhook_url": "https://example.com/webhook/payment",
            "is_verify_payment": 1,
            "skip_if_no_code": 1,
            "active": 1,
            "api_key": "a7c3b4e5f6a7b8c9d0e1f2a3b4c5d6e7",
            "request_content_type": "Json"
        }'

# Cập nhật webhook
curl -X PUT "https://my.sepay.vn/api/v1/webhooks/23" \
        -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "active": 0
        }'

# Xóa webhook
curl -X DELETE "https://my.sepay.vn/api/v1/webhooks/23" \
        -H "Authorization: Bearer YOUR_ACCESS_TOKEN"</code></pre>
                            </div>
                            <div class="tab-pane fade" id="nav-php" role="tabpanel" aria-labelledby="nav-php-tab">
                                <pre style="border-top-left-radius: 0; max-height: 35rem;"><code class="language-php">&lt;?php

$accessToken = 'YOUR_ACCESS_TOKEN';
$baseUrl = 'https://my.sepay.vn/api/v1/webhooks';

// Hàm lấy danh sách webhooks
function getWebhooks($accessToken, $filters = []) {
    global $baseUrl;
    
    $queryString = http_build_query($filters);
    $url = $queryString ? "$baseUrl?$queryString" : $baseUrl;
    
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $accessToken
    ]);
    
    $response = curl_exec($ch);
    $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($status == 200) {
        return json_decode($response, true);
    } else {
        return ['error' => $status, 'message' => $response];
    }
}

// Hàm lấy chi tiết webhook
function getWebhookDetails($accessToken, $webhookId) {
    global $baseUrl;
    
    $ch = curl_init("$baseUrl/$webhookId");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $accessToken
    ]);
    
    $response = curl_exec($ch);
    $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($status == 200) {
        return json_decode($response, true);
    } else {
        return ['error' => $status, 'message' => $response];
    }
}

// Hàm tạo webhook mới
function createWebhook($accessToken, $data) {
    global $baseUrl;
    
    $ch = curl_init($baseUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $accessToken,
        'Content-Type: application/json'
    ]);
    
    $response = curl_exec($ch);
    $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return json_decode($response, true);
}

// Hàm cập nhật webhook
function updateWebhook($accessToken, $webhookId, $data) {
    global $baseUrl;
    
    $ch = curl_init("$baseUrl/$webhookId");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $accessToken,
        'Content-Type: application/json'
    ]);
    
    $response = curl_exec($ch);
    $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return json_decode($response, true);
}

// Hàm xóa webhook
function deleteWebhook($accessToken, $webhookId) {
    global $baseUrl;
    
    $ch = curl_init("$baseUrl/$webhookId");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $accessToken
    ]);
    
    $response = curl_exec($ch);
    $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return json_decode($response, true);
}

// Ví dụ sử dụng
$webhooks = getWebhooks($accessToken, ['active' => 1]);
$webhookDetails = getWebhookDetails($accessToken, 23);

// Tạo webhook mới
$newWebhook = createWebhook($accessToken, [
    'bank_account_id' => 19,
    'name' => 'Tích hợp với shop online',
    'event_type' => 'In_only',
    'authen_type' => 'Api_Key',
    'webhook_url' => 'https://example.com/webhook/payment',
    'is_verify_payment' => 1,
    'skip_if_no_code' => 1,
    'active' => 1,
    'api_key' => 'a7c3b4e5f6a7b8c9d0e1f2a3b4c5d6e7',
    'request_content_type' => 'Json'
]);

// Cập nhật webhook
$updateResult = updateWebhook($accessToken, 23, [
    'active' => 0
]);

// Xóa webhook
$deleteResult = deleteWebhook($accessToken, 23);</code></pre>
                            </div>
                            <div class="tab-pane fade" id="nav-nodejs" role="tabpanel" aria-labelledby="nav-nodejs-tab">
                                <pre style="border-top-left-radius: 0; max-height: 35rem;"><code class="language-javascript">const accessToken = 'YOUR_ACCESS_TOKEN';
const baseUrl = 'https://my.sepay.vn/api/v1/webhooks';

// Hàm lấy danh sách webhooks
async function getWebhooks(filters = {}) {
    try {
        // Tạo query string từ filters
        const queryParams = new URLSearchParams();
        for (const [key, value] of Object.entries(filters)) {
            if (value !== undefined && value !== null) {
                queryParams.append(key, value);
            }
        }
        
        const url = queryParams.toString() 
            ? `${baseUrl}?${queryParams.toString()}` 
            : baseUrl;
        
        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('Error fetching webhooks:', error.message);
        throw error;
    }
}

// Hàm lấy chi tiết webhook
async function getWebhookDetails(webhookId) {
    try {
        const response = await fetch(`${baseUrl}/${webhookId}`, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error(`Error fetching webhook ${webhookId}:`, error.message);
        throw error;
    }
}

// Hàm tạo webhook mới
async function createWebhook(webhookData) {
    try {
        const response = await fetch(baseUrl, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(webhookData)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('Error creating webhook:', error.message);
        throw error;
    }
}

// Hàm cập nhật webhook
async function updateWebhook(webhookId, webhookData) {
    try {
        const response = await fetch(`${baseUrl}/${webhookId}`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(webhookData)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error(`Error updating webhook ${webhookId}:`, error.message);
        throw error;
    }
}

// Hàm xóa webhook
async function deleteWebhook(webhookId) {
    try {
        const response = await fetch(`${baseUrl}/${webhookId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error(`Error deleting webhook ${webhookId}:`, error.message);
        throw error;
    }
}

// Ví dụ sử dụng
async function exampleUsage() {
    try {
        // Lấy danh sách webhooks
        const webhooks = await getWebhooks({active: 1});
        console.log('Webhooks:', webhooks);
        
        // Lấy chi tiết webhook
        const webhookDetails = await getWebhookDetails(23);
        console.log('Webhook details:', webhookDetails);
        
        // Tạo webhook mới
        const newWebhook = await createWebhook({
            bank_account_id: 19,
            name: 'Tích hợp với shop online',
            event_type: 'In_only',
            authen_type: 'Api_Key',
            webhook_url: 'https://example.com/webhook/payment',
            is_verify_payment: 1,
            skip_if_no_code: 1,
            active: 1,
            api_key: 'a7c3b4e5f6a7b8c9d0e1f2a3b4c5d6e7',
            request_content_type: 'Json'
        });
        console.log('New webhook created:', newWebhook);
        
        // Cập nhật webhook
        const updateResult = await updateWebhook(23, {
            active: 0
        });
        console.log('Update result:', updateResult);
        
        // Xóa webhook
        const deleteResult = await deleteWebhook(23);
        console.log('Delete result:', deleteResult);
    } catch (error) {
        console.error('Example usage error:', error);
    }
}

exampleUsage();</code></pre>
                            </div>
                        </div>
                    </div>

                    <h3 id="ma-loi" class="hs-docs-heading">
                        Mã lỗi <a class="anchorjs-link" href="#ma-loi" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>

                    <p>Dưới đây là các mã lỗi có thể gặp khi sử dụng API webhooks:</p>

                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                <th>Mã HTTP</th>
                                <th>Mã lỗi</th>
                                <th>Mô tả</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>400</td>
                                <td><code>validation_error</code></td>
                                <td>Lỗi xác thực dữ liệu đầu vào</td>
                            </tr>
                            <tr>
                                <td>401</td>
                                <td><code>unauthorized</code></td>
                                <td>Token không hợp lệ hoặc hết hạn</td>
                            </tr>
                            <tr>
                                <td>403</td>
                                <td><code>forbidden</code></td>
                                <td>Không có quyền truy cập vào tài nguyên này</td>
                            </tr>
                            <tr>
                                <td>404</td>
                                <td><code>not_found</code></td>
                                <td>Không tìm thấy webhook</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                    <h3 id="buoc-tiep-theo" class="hs-docs-heading">
                        Bước tiếp theo <a class="anchorjs-link" href="#buoc-tiep-theo" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>

                    <p>Sau khi đã tạo webhook, bạn cần chuẩn bị máy chủ của mình để xử lý dữ liệu webhook được gửi từ SePay. Ngoài ra, bạn có thể tìm hiểu thêm về cách sử dụng API Webhook kết hợp với các API khác để tạo ra các giải pháp tích hợp hoàn chỉnh.</p>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <a class="card card-sm card-transition h-100" href="/oauth2/api-giao-dich.html">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <i class="bi-arrow-left fs-2"></i>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h5 class="fs-4 card-title">API Giao dịch</h5>
                                            <p class="card-text text-body small">Quay lại API giao dịch</p>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a class="card card-sm card-transition h-100" href="/oauth2/api-nguoi-dung.html">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1 me-3">
                                            <h5 class="fs-4 card-title">API Người dùng</h5>
                                            <p class="card-text text-body small">Tìm hiểu thêm về API người dùng</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="bi-arrow-right fs-2"></i>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <div id="contact-box-overlay"></div>
        <div id="contact-box">
            <div class="popup">
                <a href="https://m.me/117903214582465" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/fb-messenger.png" width="50%" />
                    </div>
                    <div class="meta">
                        <p class="title">Facebook Messenger</p>
                        <small class="description">Hỗ trợ live chat 24/7</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="tel:02873059589" class="item">
                    <div class="icon" style="background-color: #22c55e; color: #fff;">
                        <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;">
                            <path
                                d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z"
                                stroke="currentColor"
                                stroke-width="1.5"
                                stroke-linecap="round"
                                fill="transparent"
                            ></path>
                        </svg>
                    </div>
                    <div class="meta">
                        <p class="title">Hotline</p>
                        <small class="description">Điện thoại hỗ trợ</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/youtube-social.png" />
                    </div>
                    <div class="meta">
                        <p class="title">Youtube</p>
                        <small class="description">Theo dõi video mới nhất của SePay</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/telegram-social.png" />
                    </div>
                    <div class="meta">
                        <p class="title">Telegram</p>
                        <small class="description">Nhận thông tin mới nhất từ SePay</small>
                    </div>
                </a>
            </div>
            <div class="container">
                <div class="dot-ping">
                    <div class="ping"></div>
                    <div class="dot"></div>
                </div>
                <div class="contact-icon">
                    <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;">
                        <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z"
                            fill="currentColor"
                        ></path>
                    </svg>
                </div>
                <span style="font-weight: bold;">Liên hệ chúng tôi</span>
            </div>
        </div>

        <a
            class="js-go-to go-to position-fixed"
            href="javascript:;"
            style="visibility: hidden;"
            data-hs-go-to-options='{
                "offsetTop": 700,
                "position": {
                    "init": {
                    "right": "2rem"
                    },
                    "show": {
                    "bottom": "2rem"
                    },
                    "hide": {
                    "bottom": "-2rem"
                    }
                }
                }'
        >
            <i class="bi-chevron-up"></i>
        </a>

        <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
        <script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
        <script src="../assets/vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js"></script>
        <script src="../assets/vendor/list.js/dist/list.min.js"></script>
        <script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>
        <script src="../assets/js/theme.min.js"></script>
        <script src="../assets/js/contact-box.js"></script>
        <script src="../assets/highlight/highlight.min.js"></script>

        <script>
            hljs.highlightAll();

            (function () {
                new HSHeader("#header").init();

                new HsNavScroller(".js-nav-scroller", {
                    delay: 400,
                    offset: 140,
                });

                const docsSearch = HSCore.components.HSList.init("#docsSearch");

                new HSGoTo(".js-go-to");
            })();

            function setCookie(cname, cvalue, exdays) {
                var d = new Date();
                d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000);
                var expires = "expires=" + d.toUTCString();
                document.cookie = cname + "=" + cvalue + "; " + expires + "; path=/; domain=.sepay.vn";
            }

            let urlParams = new URLSearchParams(document.location.search);

            ["utm_source", "utm_medium", "utm_campaign", "utm_term", "utm_content"].forEach(function (param) {
                let value = urlParams.get(param);

                if (value) {
                    setCookie(param, value, 90);
                }
            });

            if (document.referrer != "" && document.cookie.indexOf("referer") === -1) {
                setCookie("referer", document.referrer, 90);
            }
        </script>
    </body>
</html>
