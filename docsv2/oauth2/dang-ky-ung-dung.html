<!DOCTYPE html>
<html lang="vi">
    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <title>OAuth2 Đăng ký <PERSON>ng dụng - SePay</title>
        <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet" />
        <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
        <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
        <link rel="stylesheet" href="../assets/vendor/bootstrap-icons/font/bootstrap-icons.css" />
        <link rel="stylesheet" href="../assets/css/theme.min.css" />
        <link rel="stylesheet" href="../assets/css/docs.min.css" />
        <link rel="stylesheet" href="../assets/css/contact-box.css" />
        <style>
            .docs-navbar-sidebar-aside-body {
                padding-top: 3.5rem !important;
            }
        </style>
        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                dataLayer.push(arguments);
            }
            gtag("js", new Date());

            gtag("config", "G-J8DLMQTKSQ");
        </script>
    </head>

    <body class="navbar-sidebar-aside-lg">
        <header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
            <div class="container-fluid">
                <nav class="navbar-nav-wrap">
                    <div class="row flex-grow-1">
                        <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
                            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                                <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo" />
                            </a>
                        </div>

                        <div class="col-md px-lg-0">
                            <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
                                <div class="d-none d-md-block">
                                    <div class="d-none">
                    <div id="searchTemplate" class="dropdown-item">
                        <a class="d-block link" href="#">
                            <span class="category d-block fw-normal text-muted mb-1"></span>
                            <span class="component text-dark"></span>
                        </a>
                    </div>
                                    </div>
                                </div>

                                <ul class="navbar-nav p-0">
                                    <li class="nav-item">
                    <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html" target="_blank"> Support <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                    <a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN" target="_blank"> Youtube <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                    <a class="btn btn-ghost-secondary btn-sm" href="https://www.facebook.com/messages/t/sepay.vn" target="_blank"> Facebook <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                    <a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel" target="_blank"> Telegram <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </nav>
            </div>
        </header>
        <main id="content" role="main">
            <nav
                class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
                data-hs-nav-scroller-options='{
                    "type": "vertical",
                    "target": ".navbar-nav .active",
                    "offset": 80
                }'
            >
                <button
                    type="button"
                    class="navbar-toggler btn btn-white d-grid w-100"
                    data-bs-toggle="collapse"
                    data-bs-target="#navbarVerticalNavMenu"
                    aria-label="Toggle navigation"
                    aria-expanded="false"
                    aria-controls="navbarVerticalNavMenu"
                >
                    <span class="d-flex justify-content-between align-items-center">
                        <span class="h3 mb-0">Nav menu</span>

                        <span class="navbar-toggler-default">
                            <i class="bi-list"></i>
                        </span>

                        <span class="navbar-toggler-toggled">
                            <i class="bi-x"></i>
                        </span>
                    </span>
                </button>

                <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
                    <div class="navbar-brand-wrapper border-end" style="height: auto;">
                        <div class="d-flex align-items-center mb-3">
                            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                                <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo" />
                            </a>
                        </div>
                    </div>

                    <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
                        <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
                            <li class="nav-item">
                                <span class="nav-subtitle">SePay OAuth2</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/">Tổng quan</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link active" href="/oauth2/dang-ky-ung-dung.html">Đăng ký ứng dụng</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/luong-xac-thuc.html">Luồng xác thực</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/access-token.html">Access Token</a>
                            </li>

                            <li class="nav-item my-2 my-lg-5"></li>

                            <li class="nav-item">
                                <span class="nav-subtitle">API</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-tai-khoan-ngan-hang.html">Tài khoản ngân hàng</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-giao-dich.html">Giao dịch</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-webhooks.html">Webhook</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-nguoi-dung.html">Người dùng</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2/api-cong-ty.html">Công ty</a>
                            </li>

                        </ul>
                    </div>
                </div>
            </nav>

            <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">
                <div style="max-width: 1200px;">
                    <div class="docs-page-header">
                        <div class="row align-items-center">
                            <div class="col-sm">
                                <h1 class="docs-page-header-title">Đăng ký ứng dụng</h1>
                                <p class="docs-page-header-text">Hướng dẫn cách đăng ký ứng dụng để lấy client_id và client_secret từ SePay.</p>
                            </div>
                        </div>
                    </div>

                    <h3 id="dang-ky" class="hs-docs-heading">
                        Cách đăng ký ứng dụng <a class="anchorjs-link" href="#dang-ky" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>

                    <p>Trước khi bắt đầu tích hợp OAuth2, bạn cần đăng ký ứng dụng của mình trên SePay để lấy <code>client_id</code> và <code>client_secret</code>. Đây là những thông tin cần thiết để thực hiện xác thực OAuth2.</p>
                    
                    <div class="alert alert-soft-primary" role="alert">
                        <div class="d-flex align-items-baseline">
                            <div class="flex-shrink-0">
                                <i class="bi-info-circle me-1"></i>
                            </div>
                            <div class="flex-grow-1 ms-2">
                                Lưu ý: Hiện tại bạn chưa thể tự tạo ứng ứng được. Bạn phải liên hệ SePay để được hỗ trợ phê duyệt và tạo ứng dụng ở tài khoản của bạn. 
                            </div>
                        </div>
                    </div>

                    <p>Các bước đăng ký ứng dụng như sau:</p>

                    <ol class="lh-lg">
                        <li><b>Đăng nhập</b> vào tài khoản SePay của bạn.</li>
                        <li>
                            <p>Truy cập menu <i class="bi bi-user"></i> <b>Tài khoản</b> -> <b>Ứng dụng OAuth</b>.</p>
                            <img src="../assets/img/oauth2/create-oauth-app-1.png" class="img-fluid mb-3" alt="Tạo ứng dụng OAuth2"> 
                        </li>
                        <li>
                            <p>Nhấn vào nút <b>Tạo ứng dụng mới</b></p>
                            <img src="../assets/img/oauth2/create-oauth-app-2.png" class="img-fluid mb-3" alt="Tạo ứng dụng OAuth2"> 
                        </li>
                    </ol>
                    
                    <img src="../assets/img/oauth2/create-oauth-app-3.png" class="img-fluid" alt="Tạo ứng dụng OAuth2">
                    
                    <h3 id="thong-tin-ung-dung" class="hs-docs-heading">
                        Thông tin ứng dụng <a class="anchorjs-link" href="#thong-tin-ung-dung" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>Khi tạo ứng dụng mới, bạn cần điền các thông tin sau:</p>
                    
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Thông tin</th>
                                    <th>Mô tả</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><b>Tên ứng dụng</b></td>
                                    <td>Tên hiển thị của ứng dụng. Người dùng sẽ nhìn thấy tên này khi được yêu cầu cấp quyền.</td>
                                </tr>
                                <tr>
                                    <td><b>Phạm vi yêu cầu</b></td>
                                    <td>Các quyền mà ứng dụng yêu cầu. Chỉ chọn những quyền cần thiết cho ứng dụng của bạn.</td>
                                </tr>
                                <tr>
                                    <td><b>URL chuyển hướng</b></td>
                                    <td>URL mà SePay sẽ chuyển hướng người dùng đến sau khi họ đồng ý hoặc từ chối cấp quyền.</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <h3 id="phạm-vi-scopes" class="hs-docs-heading">
                        Phạm vi (Scopes) <a class="anchorjs-link" href="#phạm-vi-scopes" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>

                    <p>Khi đăng ký ứng dụng, bạn cần chọn các phạm vi (scopes) mà ứng dụng cần truy cập. Các phạm vi bao gồm:</p>

                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Phạm vi</th>
                                    <th>Mô tả</th>
                                    <th>Quyền</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>bank-account:read</code></td>
                                    <td>Truy cập thông tin tài khoản ngân hàng</td>
                                    <td>Xem danh sách tài khoản, số dư, thông tin chi tiết từng tài khoản</td>
                                </tr>
                                <tr>
                                    <td><code>transaction:read</code></td>
                                    <td>Truy cập thông tin giao dịch</td>
                                    <td>Xem lịch sử giao dịch, chi tiết giao dịch, đếm số lượng giao dịch</td>
                                </tr>
                                <tr>
                                    <td><code>webhook:read</code></td>
                                    <td>Truy cập thông tin webhook</td>
                                    <td>Xem danh sách webhook, thông tin chi tiết từng webhook</td>
                                </tr>
                                <tr>
                                    <td><code>webhook:write</code></td>
                                    <td>Quản lý webhook</td>
                                    <td>Tạo mới, cập nhật webhook</td>
                                </tr>
                                <tr>
                                    <td><code>webhook:delete</code></td>
                                    <td>Xóa webhook</td>
                                </tr>
                                <tr>
                                    <td><code>profile</code></td>
                                    <td>Truy cập thông tin người dùng</td>
                                    <td>Xem thông tin cá nhân người dùng</td>
                                </tr>
                                <tr>
                                    <td><code>company</code></td>
                                    <td>Truy cập thông tin công ty</td>
                                    <td>Xem thông tin chi tiết về công ty</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="alert alert-soft-info mb-4" role="alert">
                        <div class="d-flex align-items-baseline">
                            <div class="flex-shrink-0">
                                <i class="bi-shield-check me-1"></i>
                            </div>
                            <div class="flex-grow-1 ms-2">
                                <b>Nguyên tắc quan trọng</b>: Chỉ yêu cầu những phạm vi thực sự cần thiết cho ứng dụng của bạn. Yêu cầu quá nhiều quyền không cần thiết sẽ làm giảm tỷ lệ người dùng chấp nhận cấp quyền.
                            </div>
                        </div>
                    </div>

                    <h3 id="client-credentials" class="hs-docs-heading">
                        Client ID và Client Secret <a class="anchorjs-link" href="#client-credentials" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>Sau khi đăng ký thành công, hệ thống sẽ cấp cho bạn:</p>
                    
                    <ul class="lh-lg">
                        <li><b>Client ID</b>: Định danh công khai của ứng dụng</li>
                        <li><b>Client Secret</b>: Khóa bí mật dùng để xác thực ứng dụng</li>
                    </ul>
                    
                    <div class="alert alert-soft-danger" role="alert">
                        <div class="d-flex align-items-baseline">
                            <div class="flex-shrink-0">
                                <i class="bi-exclamation-triangle me-1"></i>
                            </div>
                            <div class="flex-grow-1 ms-2">
                                <b>Quan trọng</b>: Client Secret là thông tin nhạy cảm và phải được bảo mật. Không được chia sẻ hoặc để lộ Client Secret trong mã nguồn công khai hoặc ứng dụng phía máy khách.
                            </div>
                        </div>
                    </div>
                    
                    <p>Client ID và Client Secret sẽ được hiển thị như hình dưới đây:</p>
                    
                    <img src="../assets/img/oauth2/client-credentials.png" class="img-fluid rounded" alt="Client ID và Client Secret">
                    
                    <h3 id="quan-ly-ung-dung" class="hs-docs-heading">
                        Quản lý ứng dụng <a class="anchorjs-link" href="#quan-ly-ung-dung" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>Sau khi tạo ứng dụng, bạn có thể quản lý các ứng dụng đã đăng ký tại menu <i class="bi bi-user"></i> <b>Tài khoản</b> -> <b>Ứng dụng OAuth</b> để quản lý các ứng dụng OAuth đã tạo</p>
                    
                    <ul class="lh-lg">
                        <li><b>Chỉnh sửa thông tin ứng dụng</b>: Cập nhật tên, URL chuyển hướng</li>
                        <li><b>Cập nhật phạm vi</b>: Thay đổi các quyền yêu cầu</li>
                        <li><b>Xóa ứng dụng</b>: Xóa hoàn toàn ứng dụng và thu hồi tất cả token đã cấp</li>
                    </ul>

                    <h3 id="buoc-tiep-theo" class="hs-docs-heading">
                        Bước tiếp theo <a class="anchorjs-link" href="#buoc-tiep-theo" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>Sau khi đăng ký ứng dụng thành công và lấy Client ID và Client Secret, bạn đã sẵn sàng để triển khai luồng xác thực OAuth2. Hãy tiếp tục đến trang <a href="/oauth2/luong-xac-thuc.html">Luồng xác thực</a> để tìm hiểu các bước tiếp theo.</p>
                    
                    <div class="row g-3">
                        <div class="col-md-6">
                            <a class="card card-sm card-transition h-100" href="/oauth2/">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <i class="bi-arrow-left fs-2"></i>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h5 class="fs-4 card-title">Tổng quan OAuth2</h5>
                                            <p class="card-text text-body small">Quay lại tổng quan về OAuth2</p>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a class="card card-sm card-transition h-100" href="/oauth2/luong-xac-thuc.html">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1 me-3">
                                            <h5 class="fs-4 card-title">Luồng xác thực</h5>
                                            <p class="card-text text-body small">Tìm hiểu về luồng xác thực OAuth2</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="bi-arrow-right fs-2"></i>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <div id="contact-box-overlay"></div>
        <div id="contact-box">
            <div class="popup">
                <a href="https://m.me/117903214582465" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/fb-messenger.png" width="50%" />
                    </div>
                    <div class="meta">
                        <p class="title">Facebook Messenger</p>
                        <small class="description">Hỗ trợ live chat 24/7</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="tel:02873059589" class="item">
                    <div class="icon" style="background-color: #22c55e; color: #fff;">
                        <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;">
                            <path
                                d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z"
                                stroke="currentColor"
                                stroke-width="1.5"
                                stroke-linecap="round"
                                fill="transparent"
                            ></path>
                        </svg>
                    </div>
                    <div class="meta">
                        <p class="title">Hotline</p>
                        <small class="description">Điện thoại hỗ trợ</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/youtube-social.png" />
                    </div>
                    <div class="meta">
                        <p class="title">Youtube</p>
                        <small class="description">Theo dõi video mới nhất của SePay</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/telegram-social.png" />
                    </div>
                    <div class="meta">
                        <p class="title">Telegram</p>
                        <small class="description">Nhận thông tin mới nhất từ SePay</small>
                    </div>
                </a>
            </div>
            <div class="container">
                <div class="dot-ping">
                    <div class="ping"></div>
                    <div class="dot"></div>
                </div>
                <div class="contact-icon">
                    <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;">
                        <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z"
                            fill="currentColor"
                        ></path>
                    </svg>
                </div>
                <span style="font-weight: bold;">Liên hệ chúng tôi</span>
            </div>
        </div>

        <a
            class="js-go-to go-to position-fixed"
            href="javascript:;"
            style="visibility: hidden;"
            data-hs-go-to-options='{
                "offsetTop": 700,
                "position": {
                    "init": {
                    "right": "2rem"
                    },
                    "show": {
                    "bottom": "2rem"
                    },
                    "hide": {
                    "bottom": "-2rem"
                    }
                }
                }'
        >
            <i class="bi-chevron-up"></i>
        </a>

        <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
        <script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
        <script src="../assets/vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js"></script>
        <script src="../assets/vendor/list.js/dist/list.min.js"></script>
        <script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>
        <script src="../assets/js/theme.min.js"></script>
        <script src="../assets/js/contact-box.js"></script>

        <script>
            (function () {
                new HSHeader("#header").init();

                new HsNavScroller(".js-nav-scroller", {
                    delay: 400,
                    offset: 140,
                });

                const docsSearch = HSCore.components.HSList.init("#docsSearch");

                new HSGoTo(".js-go-to");
            })();

            function setCookie(cname, cvalue, exdays) {
                var d = new Date();
                d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000);
                var expires = "expires=" + d.toUTCString();
                document.cookie = cname + "=" + cvalue + "; " + expires + "; path=/; domain=.sepay.vn";
            }

            let urlParams = new URLSearchParams(document.location.search);

            ["utm_source", "utm_medium", "utm_campaign", "utm_term", "utm_content"].forEach(function (param) {
                let value = urlParams.get(param);

                if (value) {
                    setCookie(param, value, 90);
                }
            });

            if (document.referrer != "" && document.cookie.indexOf("referer") === -1) {
                setCookie("referer", document.referrer, 90);
            }
        </script>
    </body>
</html>
