<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <title>Hướng dẫn tích hợp SePay với Woo<PERSON>om<PERSON>ce | SePay</title>
        <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet" />
        <link rel="stylesheet" href="../assets/vendor/bootstrap-icons/font/bootstrap-icons.css" />
        <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
        <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
        <link rel="stylesheet" href="../assets/css/theme.min.css" />
        <link rel="stylesheet" href="../assets/css/docs.min.css" />
        <link rel="stylesheet" href="../assets/css/contact-box.css" />
        <link rel="stylesheet" href="../assets/vendor/hs-video-bg/dist/hs-video-bg.min.css" />
        <style>
            .docs-navbar-sidebar-aside-body {
                padding-top: 3.5rem !important;
            }
			.hs-docs-heading {
				margin-top: unset;
			}
			.hs-docs-heading~p {
				margin-top: unset;
			}
        </style>
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                dataLayer.push(arguments);
            }
            gtag("js", new Date());

            gtag("config", "G-J8DLMQTKSQ");
        </script>
    </head>

    <body class="navbar-sidebar-aside-lg">
        <header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
            <div class="container-fluid">
                <nav class="navbar-nav-wrap">
                    <div class="row flex-grow-1">
                        <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
                            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                                <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo" />
                            </a>
                        </div>

                        <div class="col-md px-lg-0">
                            <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
                                <div class="d-none d-md-block">
                                    <div class="d-none">
                                        <div id="searchTemplate" class="dropdown-item">
                                            <a class="d-block link" href="#">
                                                <span class="category d-block fw-normal text-muted mb-1"></span>
                                                <span class="component text-dark"></span>
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <ul class="navbar-nav p-0">
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html" target="_blank"> Support <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN" target="_blank"> Youtube <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://www.facebook.com/messages/t/sepay.vn" target="_blank"> Facebook <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel" target="_blank"> Telegram <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </nav>
            </div>
        </header>

        <main id="content" role="main">
            <nav
                class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
                data-hs-nav-scroller-options='{
					"type": "vertical",
					"target": ".navbar-nav .active",
					"offset": 80
				}'
            >
                <button
                    type="button"
                    class="navbar-toggler btn btn-white d-grid w-100"
                    data-bs-toggle="collapse"
                    data-bs-target="#navbarVerticalNavMenu"
                    aria-label="Toggle navigation"
                    aria-expanded="false"
                    aria-controls="navbarVerticalNavMenu"
                >
                    <span class="d-flex justify-content-between align-items-center">
                        <span class="h3 mb-0">Nav menu</span>
                        <span class="navbar-toggler-default">
                            <i class="bi-list"></i>
                        </span>
                        <span class="navbar-toggler-toggled">
                            <i class="bi-x"></i>
                        </span>
                    </span>
                </button>

                <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
                    <div class="navbar-brand-wrapper border-end" style="height: auto;">
                        <div class="d-flex align-items-center mb-3">
                            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                                <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo" />
                            </a>
                        </div>
                    </div>

                    <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
                        <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
                            <li class="nav-item">
                                <span class="nav-subtitle">Giới thiệu</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="sepay-la-gi.html">SePay là gì?</a>
                            </li>
                            <li class="nav-item my-2 mt-lg-5">
                                <span class="nav-subtitle">Gói dịch vụ</span>
                            </li>
                  
                            <li class="nav-item">
                                <a class="nav-link" href="goi-theo-diem-ban">Gói theo điểm bán</a>
                            </li>
                            <li class="nav-item my-2 my-lg-5"></li>

                            <li class="nav-item">
                                <span class="nav-subtitle">Hướng dẫn chung</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="dang-ky-sepay.html">Đăng ký SePay</a>
                            </li>

                        <li class="nav-item">
                            <a class="nav-link" href="them-tai-khoan-ngan-hang.html">Thêm tài khoản ngân hàng</a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link " href="xem-giao-dich.html">Xem giao dịch</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="nguoi-dung-va-phan-quyen.html">Người dùng & Phân quyền</a>
                        </li>
 
                        <li class="nav-item">
                            <a class="nav-link" href="tai-khoan-phu.html">Tài khoản phụ</a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="cau-hinh-tai-khoan-ngan-hang.html">Cấu hình TK ngân hàng</a>
                        </li>

                        
                        <li class="nav-item my-2 my-lg-5"></li>

                            <li class="nav-item">
                                <span class="nav-subtitle">Cấu hình công ty</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="goi-dich-vu.html">Gói dịch vụ</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="hoa-don-va-thanh-toan.html">Hóa đơn & thanh toán</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="cau-hinh-chung.html">Cấu hình chung</a>
                            </li>

                            <li class="nav-item my-2 my-lg-5"></li>

                        <li class="nav-item">
                            <span class="nav-subtitle">Chia sẻ biến động số dư</span>
                        </li>
              
                        <li class="nav-item">
                            <a class="nav-link " href="tich-hop-telegram.html">Tích hợp Telegram <i class="bi bi-telegram ms-2 text-info"></i></a>
                          </li>
              
                          <li class="nav-item">
                            <a class="nav-link" href="tich-hop-lark-messenger.html">Tích hợp Lark Messenger <img src="assets/img/lark/lark-icon.png" class="img-fluid" style="max-height:25px"></a>
                        </li>
              
                        <li class="nav-item">
                            <a class="nav-link" href="tich-hop-viber.html">Tích hợp Viber <img src="assets/img/viber/viber-icon.png" class="img-fluid rounded ms-2" style="height:25px;width:25px;"></a>
                        </li>
              
                        <li class="nav-item">
                            <a class="nav-link d-flex align-items-center" href="mobile-app.html">Mobile App <i class="bi bi-phone-vibrate ms-2 fs-3 text-info"></i> </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="tich-hop-loa-thanh-toan.html">Tích hợp Loa thanh toán 
                                <svg class="mb-0 ms-1" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#377dff" class="bi bi-speaker" viewBox="0 0 16 16">
                                    <path d="M12 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1zM4 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2z"></path>
                                    <path d="M8 4.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5M8 6a2 2 0 1 0 0-4 2 2 0 0 0 0 4m0 3a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3m-3.5 1.5a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0"></path>
                                  </svg>
                                </a>
                          </li>
                <li class="nav-item my-2 my-lg-5"></li>

                            <li class="nav-item">
                                <span class="nav-subtitle">Tích hợp web</span>
                            </li>

                <li class="nav-item">
                    <a class="nav-link" href="tich-hop-shopify.html">Tích hợp Shopify <img class="ms-2" src="assets/img/others/shopify-icon.png" style="width:22px; height: 22px;"></a>
                  </li>
                  
                <li class="nav-item">
                    <a class="nav-link" href="tich-hop-sapo.html">Tích hợp Sapo <img class="ms-2" src="assets/img/others/sapo-icon.png" style="width:18px; height: 18px;"></a>
                </li>
               
    
                <li class="nav-item">
                    <a class="nav-link" href="tich-hop-haravan.html">Tích hợp Haravan <img class="ms-2" src="assets/img/others/haravan-icon.png" style="width:22px; height: 22px;"></a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="tich-hop-gohighlevel.html">Tích hợp GoHighLevel <img class="ms-2" src="assets/img/others/gohighlevel-icon.png" style="width:22px; height: 22px;"></a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="woocommerce.html">Tích hợp WooCommerce <img class="ms-2" src="assets/img/others/woocommerce-icon.png" style="width:22px; height: 22px;"></a>
                </li>
                            <li class="nav-item">
                                <a class="nav-link" href="tich-hop-google-sheets.html">Tích hợp Google Sheets <img class="ms-2" src="assets/img/others/google-sheets-icon.png" style="width: 22px; height: 22px;" /></a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="tich-hop-hostbill.html">Tích hợp HostBill <img class="ms-2" src="assets/img/others/hostbill-icon.png" style="height: 22px;" /></a>
                            </li>

                            <li class="nav-item my-2 my-lg-5"></li>

                            <li class="nav-item">
                                <span class="nav-subtitle">Lập trình & Tích hợp</span>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="tich-hop-webhooks.html">Tích hợp WebHooks</a>
                            </li>

            <li class="nav-item ">
                <a class="nav-link" href="lap-trinh-webhooks.html">Lập trình WebHooks</a>
            </li>
            <li class="nav-item ">
              <a class="nav-link" href="gia-lap-giao-dich.html">Giả lập giao dịch</a>
          </li>
          <li class="nav-item ">
            <a class="nav-link" href="tao-qr-code-vietqr-dong.html">Tạo & nhúng QR Code</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="/oauth2">OAuth2</a>
        </li>

                            <li class="nav-item my-2 my-lg-5"></li>

                            <li class="nav-item">
                                <span class="nav-subtitle">SePay API</span>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="gioi-thieu-api.html">Giới thiệu API</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="tao-api-token.html">Tạo API Token</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="api-giao-dich.html">API Giao dịch</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="api-tai-khoan-ngan-hang.html">API Tài khoản ngân hàng</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="api-va-theo-don-hang.html">API VA theo Đơn hàng</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>

            <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">
                <div style="max-width: 800px;">
					<div class="docs-page-header">
						<div class="row align-items-center">
							<div class="col-sm">
								<h1 class="docs-page-header-title">
									Hướng dẫn tích hợp WooCommerce
									<img src="assets/img/others/woocommerce-icon.png" style="width: 30px;" />
								</h1>
								<p class="docs-page-header-text">
									Tích hợp SePay vào WooCommerce giúp xác nhận thanh toán ngay sau khi khách hàng chuyển khoản. Đơn hàng cũng sẽ chuyển sang trạng thái đã thanh toán.
								</p>
							</div>
						</div>
					</div>
					<img class="img-fluid rounded-1 mb-2" src="assets/img/woocommerce/image.png" />
					<p>
						Nếu bạn đang sử dụng website WordPress và WooCommerce, bài viết này sẽ hướng dẫn bạn tích hợp SePay vào WordPress WooCommerce giúp tự động hóa việc xác nhận thanh toán qua chuyển khoản.
					</p>
                    <div class="text-center mb-5">
                        <a class="btn btn-primary btn-transition mb-2" href="https://wordpress.org/plugins/sepay-gateway/" target="_blank">
                            <i class="bi bi-cloud-download"></i> Tải Plugin WordPress SePay Payment Gateway
                        </a>
                    </div>
                    <h2 id="ket-qua-sau-khi-tich-hop" class="hs-docs-heading">
                        Kết quả sau khi tích hợp
                        <a class="anchorjs-link" href="#ket-qua-sau-khi-tich-hop" aria-label="Anchor" data-anchorjs-icon="#"></a>
					</h2>
					<div class="ratio ratio-16x9 mb-3">
						<iframe src="https://www.youtube.com/embed/28jVZt__fto" title="SePay WooCommerce Demo" allowfullscreen class="rounded-1"></iframe>
					</div>
					<ol>
						<li><strong>Phía người dùng mua hàng trực tuyến</strong></li>
					</ol>
					<p>Khi khách đặt hàng và thanh toán, sẽ có thêm tùy chọn Chuyển khoản ngân hàng.</p>
					<img class="img-fluid rounded-1 mb-2" src="assets/img/woocommerce/image-1.png" />
					<p>Mã QR code kèm thông tin thanh toán sẽ hiện lên sau khi chọn phương thức thanh toán trên.</p>
					<img class="img-fluid rounded-1 mb-2" src="assets/img/woocommerce/image-2.png" />
					<p>Website sẽ hiện&nbsp;<strong>Bạn đã thanh toán thành công</strong>&nbsp;sau khi khách hàng thanh toán chỉ vài giây sau khi chuyển tiền hoàn tất.</p>
					<img class="img-fluid rounded-1 mb-2" src="assets/img/woocommerce/image-3.png" />
					<ol start="2">
						<li><strong>Phía giao diện Quản lý Đơn hàng của WooCommerce</strong></li>
					</ol>
					<p>Đơn hàng sẽ tự động ghi nhận thanh toán. Và tự chuyển sang trạng thái từ Tạm giữ (On-Hold) sang Đang xử lý (Processing) nếu khách thanh toán đủ.</p>
					<img class="img-fluid rounded-1 mb-3" src="assets/img/woocommerce/image-4.png" />
					<h2 id="huong-dan-tich-hop" class="hs-docs-heading">
						Hướng dẫn tích hợp
						<a class="anchorjs-link" href="#huong-dan-tich-hop" aria-label="Anchor" data-anchorjs-icon="#"></a>
					</h2>
					<h3 id="buoc-1-cai-dat-plugin-sepay-gateway-vao-website-wordpress" class="hs-docs-heading">
						Bước 1: Cài đặt plugin SePay Gateway vào website WordPress
						<a class="anchorjs-link" href="#buoc-1-cai-dat-plugin-sepay-gateway-vao-website-wordpress" aria-label="Anchor" data-anchorjs-icon="#"></a>
					</h3>
					<p>Để cài đặt plugin SePay Gateway vào website WordPress của bạn, thực hiện các bước sau:</p>
					<p>Truy cập vào <strong>Trang quản trị</strong>, vào <strong>Plugin</strong> → <strong>Cài Plugin</strong>, sau đó tìm từ khóa “sepay” trên thanh tìm kiếm, nhấn vào nút <strong>Cài đặt ngay</strong> và <strong>Kích hoạt</strong> để cài đặt plugin.</p>
					<img class="img-fluid rounded-1 mb-2" src="assets/img/woocommerce/image-5.png" />
					<p>Sau khi cài xong plugin, bạn sẽ được chuyển hướng đến trang thiết lập plugin <strong>SePay Gateway.</strong></p>
					<h3 id="buoc-2-ket-noi-voi-tai-khoan-sepay" class="hs-docs-heading">
						Bước 2: Kết nối với tài khoản SePay
						<a class="anchorjs-link" href="#buoc-2-ket-noi-voi-tai-khoan-sepay" aria-label="Anchor" data-anchorjs-icon="#"></a>
					</h3>
					<p>Đây là giao diện thiết lập của plugin <strong>SePay Gateway</strong>, nhấn vào nút <strong>Kết nối tài khoản</strong> để bắt đầu thiết lập.</p>
					<img class="img-fluid rounded-1 mb-2" src="assets/img/woocommerce/image-6.png" />
					<p>Bạn sẽ được chuyển sang trang ủy quyền <strong>WooCommerce</strong> truy cập vào <strong>tài khoản SePay của bạn</strong> như hình bên dưới, bạn sẽ cần đăng nhập nếu chưa đăng nhập vào <strong>my.sepay.vn</strong>.</p>
					<img class="img-fluid rounded-1 mb-2" src="assets/img/woocommerce/image-7.png" />
					<p>Nhấn vào nút <strong>Cho phép</strong> để tiếp tục chuyển sang bước cấu hình chọn tài khoản ngân hàng.</p>
					<h3 id="buoc-3-cau-hinh-tai-khoan-ngan-hang" class="hs-docs-heading">
						Bước 3: Cấu hình tài khoản ngân hàng
						<a class="anchorjs-link" href="#buoc-3-cau-hinh-tai-khoan-ngan-hang" aria-label="Anchor" data-anchorjs-icon="#"></a>
					</h3>
					<p>Ở bước này bạn cần chọn tài khoản ngân hàng nào để hiển thị ngoài trang thanh toán và nhận tiền.</p>
					<p>Nếu bạn chưa có ngân hàng nào tại <a href="http://my.sepay.vn">my.sepay.vn</a> thì truy cập vào trang <a href="https://my.sepay.vn/bankaccount/connect">Kết nối ngân hàng</a> tại SePay để thêm ngân hàng mới.</p>
					<img class="img-fluid rounded-1 mb-2" src="assets/img/woocommerce/image-8.png" />
					<p>Chọn tài khoản ngân hàng cần tích hợp và nhấn vào nút <strong>Hoàn tất thiết lập</strong> để hoàn tất.</p>
					<img class="img-fluid rounded-1 mb-2" src="assets/img/woocommerce/image-9.png" />
					<p>Sau khi hoàn tất, hệ thống sẽ tự động tạo một tích hợp <a href="https://docs.sepay.vn/tich-hop-webhooks.html">Webhooks</a> tương ứng với tài khoản ngân hàng bạn chọn trên <a href="https://quocdat.my.sepay.dev/webhooks">SePay</a> để gửi thông báo qua trang web WordPress của bạn khi có giao dịch chuyển tiền vào tài khoản ngân hàng đó.</p>
					<img class="img-fluid rounded-1 mb-2" src="assets/img/woocommerce/image-10.png" />
					<p>Đến đây bạn đã hoàn tất tích hợp thanh toán SePay với WooCommerce.</p>
					<h2 id="cau-hinh-sepay-gateway" class="hs-docs-heading">
						Cấu hình SePay Gateway
						<a class="anchorjs-link" href="#cau-hinh-sepay-gateway" aria-label="Anchor" data-anchorjs-icon="#"></a>
					</h2>
					<p>Trong phần này sẽ hướng dẫn chi tiết về các thiết lập cần thiết trong giao diện quản trị của plugin.</p>
					<img class="img-fluid rounded-1 mb-2" src="assets/img/woocommerce/image-11.png" />
					<h3 id="giai-thich-cac-cau-hinh" class="hs-docs-heading">
						Giải thích các cấu hình
						<a class="anchorjs-link" href="#giai-thich-cac-cau-hinh" aria-label="Anchor" data-anchorjs-icon="#"></a>
					</h3>
					<p><strong>1. Bật/Tắt</strong></p>
					<p>Bật tắt phương thức thanh toán.</p>
					<img class="img-fluid rounded-1 mb-2" src="assets/img/woocommerce/image-12.png" />
					<p><strong>2. Tiêu đề</strong></p>
					<p>Tên hiển thị cho phương thức thanh toán.</p>
					<img class="img-fluid rounded-1 mb-2" src="assets/img/woocommerce/image-13.png" />
					<p><strong>3. Mô tả</strong></p>
					<p>Thông tin mô tả phương thức thanh toán.</p>
					<img class="img-fluid rounded-1 mb-2" src="assets/img/woocommerce/image-14.png" />
					<p><strong>4. Tài khoản ngân hàng</strong></p>
					<p>Chọn tài khoản ngân hàng để hiển thị trên trang thanh toán để khách chuyển khoản tới và SePay thực hiện xác thực thanh toán bằng tài khoản ngân hàng đó.</p>
					<img class="img-fluid rounded-1 mb-2" src="assets/img/woocommerce/image-15.png" />
					<p><strong>5. Tài khoản VA</strong></p>
					<p>Đối với các tài khoản ngân hàng chỉ hỗ trợ xác thực thanh toán thông qua số VA như BIDV, OCB, MSB, KienLongBank. Bạn cần chọn một tài khoản VA tương ứng với tài khoản ngân hàng đã chọn.</p>
					<img class="img-fluid rounded-1 mb-2" src="assets/img/woocommerce/image-16.png" />
					<p><strong>6. Tiền tố mã thanh toán</strong></p>
					<p>Là chuỗi từ 2-5 ký tự nằm ở trước mã thanh toán, SePay sử dụng để phân biệt và xác thực thanh toán cho đơn hàng của bạn.</p>
					<img class="img-fluid rounded-1 mb-2" src="assets/img/woocommerce/image-17.png" />
					<p>Ví dụ tiền tố mã thanh toán là <strong>DH</strong>, thì khi thanh toán khách sẽ quét mã QR chuyển tiền có nội dung là <strong>DH1234.</strong> trong đó <strong>DH</strong> là tiền tố mã thanh toán đã chọn, <strong>1234</strong> là mã đơn hàng WooCommerce.</p>
					<p>Vào <strong>Cấu hình Công ty </strong>→ <strong><a href="https://my.sepay.vn/company/configuration">Cấu hình chung</a></strong> để quản lý cấu trúc mã thanh toán của bạn.</p>
					<p><strong>7. Thông báo thành công</strong></p>
					<p>Nội dung thông báo trên website sau khi khách hàng thanh toán thành công.</p>
					<img class="img-fluid rounded-1 mb-2" src="assets/img/woocommerce/image-18.png" />
					<p>Hỗ trợ định dạng chữ thuần, HTML và JavaScript. Nếu bạn muốn thêm code JavaScript để bắn sự kiện lên các trang tracking như Google Analytics, bạn có thể chèn mã JavaScript tại đây.</p>
					<p><strong>8. Trạng thái đơn hàng khi hoàn tất</strong></p>
					<p>Là Trạng thái đơn hàng tại WooCommerce sau khi khách thanh toán đủ. Nếu không chỉ định, trạng thái này sẽ do WooCommerce quyết định.</p>
					<p>Hoặc bạn có thể chỉ định là&nbsp;<strong>Đang xử lý (Processing)</strong>&nbsp;hoặc&nbsp;<strong>Đã hoàn tất (Completed).</strong></p>
					<img class="img-fluid rounded-1 mb-2" src="assets/img/woocommerce/image-19.png" />
					<p><strong>9. Chế độ tải xuống</strong></p>
					<p>Tùy chọn này dành cho các đơn hàng sản phẩm số, bạn có thể chỉ định cách mà người dùng tải xuống sản phẩm thông qua hình thức&nbsp;<strong>Thủ công</strong>&nbsp;hoặc&nbsp;<strong>Tự động</strong>.</p>
					<img class="img-fluid rounded-1 mb-2" src="assets/img/woocommerce/image-20.png" />
					<p><strong>10. Hiển thị tên ngân hàng</strong></p>
					<p>Chế độ hiện thị tên ngân hàng trên màn hình thanh toán, SePay hỗ trợ ba tùy chọn gồm:&nbsp;<strong>Tên đầy đủ</strong>,&nbsp;<strong>Tên viết tắt</strong>&nbsp;hoặc&nbsp;<strong>Tên đầy đủ kèm tên viết tắt</strong>.</p>
					<img class="img-fluid rounded-1 mb-2" src="assets/img/woocommerce/image-21.png" />
					<p><strong>11. Logo</strong></p>
					<p>URL của logo để hiển thị logo trên phương thức thanh toán.</p>
					<img class="img-fluid rounded-1 mb-2" src="assets/img/woocommerce/image-22.png" />
					<h3 id="kiem-tra-cau-hinh-cua-ban" class="hs-docs-heading">
						Kiểm tra cấu hình của bạn
						<a class="anchorjs-link" href="#kiem-tra-cau-hinh-cua-ban" aria-label="Anchor" data-anchorjs-icon="#"></a>
					</h3>
					<p>Để biết mọi thứ bạn đã cấu hình có hoạt động đúng hay không, bạn có thể thực hiện như sau:</p>
					<ul>
						<li>Thử đặt hàng trên website của bạn, hãy chọn đơn hàng giá trị nhỏ nhất (ví dụ 2000).</li>
					</ul>
					<ul>
						<li>Đến bước Thanh toán bạn chọn hình thức thanh toán là&nbsp;<strong>Chuyển khoản ngân hàng (Quét mã QR)</strong>.</li>
					</ul>
					<ul>
						<li>Nếu mã QR code hiện ra sau khi đặt hàng nghĩa là bạn đã cấu hình thành công.</li>
					</ul>
					<ul>
						<li>
							Thử thanh toán, nếu sau khi hoàn tất thanh toán, giao diện thanh toán hiện lên Bạn đã thanh toán thành công nghĩa là việc cấu hình đã chính xác. Đồng thời xem tại giao diện quản lý đơn hàng, bạn sẽ thấy đơn hàng được chuyển sang
							Đang xử lý (Processing).
						</li>
					</ul>
					<img class="img-fluid rounded-1 mb-3" src="assets/img/woocommerce/image-23.png" />

                    <h2 id="huong-dan-nang-cap" class="hs-docs-heading">
                        Hướng dẫn nâng cấp từ phiên bản 1.0.x lên 1.1.0
                        <a class="anchorjs-link" href="#huong-dan-nang-cap" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h2>

                    <p>Phiên bản 1.1.0 giới thiệu tính năng kết nối tài khoản SePay trực tiếp, giúp đồng bộ dữ liệu tài khoản ngân hàng, tiền tố mã thanh toán và webhook tự động. Dưới đây là hướng dẫn nâng cấp an toàn:</p>
                    
                    <h3 id="buoc-1-sao-luu-du-lieu" class="hs-docs-heading">
                        Bước 1: Sao lưu dữ liệu
                        <a class="anchorjs-link" href="#buoc-1-sao-luu-du-lieu" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>Trước khi nâng cấp, hãy sao lưu:</p>
                    <ul>
                        <li>Website WordPress của bạn (files và database)</li>
                        <li>Ghi chú các cài đặt hiện tại của plugin SePay Gateway</li>
                    </ul>
                    
                    <h3 id="buoc-2-cai-dat-phien-ban-moi" class="hs-docs-heading">
                        Bước 2: Cài đặt phiên bản mới
                        <a class="anchorjs-link" href="#buoc-2-cai-dat-phien-ban-moi" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <h4>Cách 1: Tự động cập nhật</h4>
                    <p>Nếu bạn cài đặt plugin từ WordPress.org, vào <strong>Plugins</strong> → <strong>Đã cài đặt</strong> và kiểm tra cập nhật cho "SePay Gateway".</p>
                    
                    <h4>Cách 2: Cài đặt thủ công</h4>
                    <ol>
                        <li>Tải phiên bản 1.1.0 từ <a href="https://wordpress.org/plugins/sepay-gateway/" target="_blank">WordPress.org</a></li>
                        <li>Vào <strong>Plugins</strong> → <strong>Đã cài đặt</strong></li>
                        <li>Tắt plugin "SePay Gateway" hiện tại</li>
                        <li>Vào <strong>Plugins</strong> → <strong>Thêm mới</strong> → <strong>Tải plugin lên</strong> và chọn file zip bạn đã tải về</li>
                        <li>Khi WordPress hỏi về việc ghi đè plugin cũ, chọn <strong>Đồng ý</strong> để ghi đè</li>
                        <li>Kích hoạt plugin sau khi cài đặt hoàn tất</li>
                    </ol>

                    <h3 id="buoc-3-ket-noi-tai-khoan-sepay" class="hs-docs-heading">
                        Bước 3: Kết nối tài khoản SePay
                        <a class="anchorjs-link" href="#buoc-3-ket-noi-tai-khoan-sepay" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <ol>
                        <li>Sau khi kích hoạt plugin, vào <strong>WooCommerce</strong> → <strong>Cài đặt</strong> → <strong>Thanh toán</strong> → <strong>SePay</strong></li>
                        <li>Bạn sẽ thấy nút <strong>Kết nối tài khoản</strong> mới - nhấp vào đó</li>
                        <li>Đăng nhập vào tài khoản SePay của bạn nếu được yêu cầu</li>
                        <li>Cấp quyền cho WooCommerce truy cập vào tài khoản SePay</li>
                        <li>Chọn tài khoản ngân hàng bạn muốn sử dụng và hoàn tất thiết lập</li>
                    </ol>
                    
                    <h3 id="buoc-4-kiem-tra-cau-hinh" class="hs-docs-heading">
                        Bước 4: Kiểm tra cấu hình
                        <a class="anchorjs-link" href="#buoc-4-kiem-tra-cau-hinh" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>Sau khi kết nối, hãy kiểm tra các cài đặt sau đã được đồng bộ đúng:</p>
                    <ul>
                        <li>Tài khoản ngân hàng đã được chọn</li>
                        <li>Tiền tố mã thanh toán</li>
                        <li>Webhook đã được tạo tự động trên SePay</li>
                    </ul>
                    
                    <h3 id="buoc-5-kiem-tra-hoat-dong" class="hs-docs-heading">
                        Bước 5: Kiểm tra hoạt động
                        <a class="anchorjs-link" href="#buoc-5-kiem-tra-hoat-dong" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>Tạo một đơn hàng thử với giá trị nhỏ và thực hiện thanh toán để đảm bảo:</p>
                    <ol>
                        <li>Mã QR hiển thị đúng</li>
                        <li>Sau khi thanh toán, đơn hàng được cập nhật trạng thái đúng</li>
                    </ol>
                    
                    <h3 id="xu-ly-su-co" class="hs-docs-heading">
                        Xử lý sự cố
                        <a class="anchorjs-link" href="#xu-ly-su-co" aria-label="Anchor" data-anchorjs-icon="#"></a>
                    </h3>
                    
                    <p>Nếu gặp vấn đề sau khi nâng cấp:</p>

                    <p><strong>1. Không thể kết nối với tài khoản SePay</strong></p>
                    <ul>
                        <li>Kiểm tra kết nối internet</li>
                        <li>Đảm bảo tài khoản SePay của bạn đang hoạt động</li>
                        <li>Thử đăng xuất và đăng nhập lại vào SePay</li>
                    </ul>
                    
                    <p><strong>2. Webhook không hoạt động</strong></p>
                    <ul>
                        <li>Kiểm tra webhook trong trang quản trị SePay</li>
                        <li>Đảm bảo URL callback được phép truy cập từ internet</li>
                    </ul>
                    
                    <p><strong>3. Khôi phục phiên bản cũ</strong></p>
                    <p>Nếu cần quay lại phiên bản cũ, hãy gỡ cài đặt plugin hiện tại và cài đặt lại phiên bản 1.0.x.</p>
					<h2 id="change-log" class="hs-docs-heading">
						Change log
						<a class="anchorjs-link" href="#change-log" aria-label="Anchor" data-anchorjs-icon="#"></a>
					</h2>
					<p><strong>v1.1.0 - 11/03/2025</strong></p>
					<ul>
						<li>[Tính năng mới] Cho phép WooCommerce kết nối với tài khoản của khách trên SePay để đồng bộ dữ liệu tài khoản ngân hàng, tiền tố mã thanh toán và webhook.</li>
					</ul>
					<p><strong>29/03/2024</strong></p>
					<ul>
						<li>[Cập nhật] Hỗ trợ Cart &amp; Checkout blocks cho&nbsp;<a href="https://docs.sepay.vn/woocommerce.html">SePay WooCommerce Payment plugin</a>.</li>
					</ul>
					<p><strong>15/11/2023</strong></p>
					<ul>
						<li>[Tính năg mới] Cho phép tuỳ chỉnh hiển thị tên ngân hàng. Bao gồm hiện: Tên viết tắt, tên đầy đủ, tên đầy đủ kèm tên viết tắt</li>
					</ul>
					<ul>
						<li>[Fix lỗi] Fix lỗi không xác thực được giao dịch khi dùng VA.</li>
					</ul>
					<p><strong>07/11/2023</strong></p>
					<ul>
						<li>[Cập nhật] Tối ưu giao diện CSS để tương thích với nhiều giao diện WordPress.</li>
					</ul>
					<ul>
						<li>[Tính năg mới] Hỗ trợ Digital/Downloadable product. Cho phép download sau khi thanh toán.</li>
					</ul>
					<ul>
						<li>[Fix lỗi] Fix lỗi json response.</li>
					</ul>
					<p><strong>04/10/2023</strong></p>
					<ul>
						<li>[Thay đổi]: Đổi trạng thái ghi chú cho đơn hàng từ ghi chú cho Khách hàng sang ghi chú cho Admin. Như vậy ghi chú tự động tạo bởi SePay sẽ không còn gửi email cho khách hàng.</li>
					</ul>
					<ul>
						<li>
							[Tính năng mới]: Cho phép tuỳ chỉnh thông điệp sau khi khách hàng thanh toán thành công. Hỗ trợ chữ thuần, HTML và JavaScript. Nếu bạn muốn thêm code JavaScript để bắn sự kiện lên các trang tracking như Google Analytics, bạn có
							thể chèn mã JavaScript tại đây.
						</li>
					</ul>
					<ul>
						<li>
							[Tính năng mới]: Tuỳ chỉnh trạng thái đơn hàng sau khi khách thanh toán đủ. Nếu không chỉ định, trạng thái này sẽ do WooCommerce quyết định. Hoặc bạn có thể chỉ định là Đang xử lý (Processing) hoặc Đã hoàn tất (Completed).
						</li>
					</ul>
					<p>
						Đọc tiếp:
						<a href="tich-hop-google-sheets.html">
							Hướng dẫn tích hợp Google Sheets
							<i class="bi bi-chevron-right"></i>
						</a>
					</p>
				</div>
            </div>
        </main>

        <div id="contact-box-overlay"></div>
        <div id="contact-box">
            <div class="popup">
                <a href="https://m.me/117903214582465" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/fb-messenger.png" width="50%" />
                    </div>
                    <div class="meta">
                        <p class="title">Facebook Messenger</p>
                        <small class="description">Hỗ trợ live chat 24/7</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="tel:02873059589" class="item">
                    <div class="icon" style="background-color: #22c55e; color: #fff;">
                        <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;">
                            <path
                                d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z"
                                stroke="currentColor"
                                stroke-width="1.5"
                                stroke-linecap="round"
                                fill="transparent"
                            ></path>
                        </svg>
                    </div>
                    <div class="meta">
                        <p class="title">Hotline</p>
                        <small class="description">Điện thoại hỗ trợ</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/youtube-social.png" />
                    </div>
                    <div class="meta">
                        <p class="title">Youtube</p>
                        <small class="description">Theo dõi video mới nhất của SePay</small>
                    </div>
                </a/div>
                <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/telegram-social.png" />
                    </div>
                    <div class="meta">
                        <p class="title">Telegram</p>
                        <small class="description">Nhận thông tin mới nhất từ SePay</small>
                    </div>
                </a>
            </div>
            <div class="container">
                <div class="dot-ping">
                    <div class="ping"></div>
                    <div class="dot"></div>
                </div>
                <div class="contact-icon">
                    <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;">
                        <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z"
                            fill="currentColor"
                        ></path>
                    </svg>
                </div>
                <span style="font-weight: bold;">Liên hệ chúng tôi</span>
            </div>
        </div>

        <a
            class="js-go-to go-to position-fixed"
            href="javascript:;"
            style="visibility: hidden;"
            data-hs-go-to-options='{
				"offsetTop": 700,
				"position": {
					"init": {
						"right": "2rem"
					},
					"show": {
						"bottom": "2rem"
					},
					"hide": {
						"bottom": "-2rem"
					}
				}
			}'
        >
            <i class="bi-chevron-up"></i>
        </a>

        <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
        <script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
        <script src="../assets/vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js"></script>
        <script src="../assets/vendor/list.js/dist/list.min.js"></script>
        <script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>
        <script src="../assets/vendor/hs-video-bg/dist/hs-video-bg.min.js"></script>
        <script src="../assets/js/theme.min.js"></script>
        <script src="../assets/js/contact-box.js"></script>

        <script>
            (function () {
                new HSHeader("#header").init();

                new HsNavScroller(".js-nav-scroller", {
                    delay: 400,
                    offset: 140,
                });
                const docsSearch = HSCore.components.HSList.init("#docsSearch");

                new HSGoTo(".js-go-to");
            })();

            function setCookie(cname, cvalue, exdays) {
                var d = new Date();
                d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000);
                var expires = "expires=" + d.toUTCString();
                document.cookie = cname + "=" + cvalue + "; " + expires + "; path=/; domain=.sepay.vn";
            }

            let urlParams = new URLSearchParams(document.location.search);

            ["utm_source", "utm_medium", "utm_campaign", "utm_term", "utm_content"].forEach(function (param) {
                let value = urlParams.get(param);

                if (value) {
                    setCookie(param, value, 90);
                }
            });

            if (document.referrer != "" && document.cookie.indexOf("referer") === -1) {
                setCookie("referer", document.referrer, 90);
            }
        </script>
    </body>
</html>
