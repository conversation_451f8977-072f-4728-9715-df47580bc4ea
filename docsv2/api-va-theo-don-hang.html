<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />

        <title>API VA theo Đơn hàng | SePay</title>

		<link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />

		<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">
		<meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
		<meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />

		<link rel="stylesheet" href="../assets/vendor/bootstrap-icons/bootstrap-icons.css">
		<link rel="stylesheet" href="../assets/css/theme.min.css">
		<link rel="stylesheet" href="../assets/css/docs.min.css">
		<link rel="stylesheet" href="../assets/css/contact-box.css">

		<style>
			.docs-navbar-sidebar-aside-body {
				padding-top: 3.5rem !important;
			}

			.navbar-expand .nav-item:not(:last-child) {
				margin-right: 0;
			}
		</style>

		<script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
		<script>
			window.dataLayer = window.dataLayer || [];
			function gtag() {
				dataLayer.push(arguments);
			}
			gtag('js', new Date());

			gtag('config', 'G-J8DLMQTKSQ');
		</script>
    </head>

    <body class="navbar-sidebar-aside-lg">
		<header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
			<div class="container-fluid">
				<nav class="navbar-nav-wrap">
					<div class="row flex-grow-1">
						<div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
							<a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
								<img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="SePay" />
							</a>
						</div>
						<div class="col-md px-lg-0">
							<div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
								<div class="d-none d-md-block">
									<div class="d-none">
										<div id="searchTemplate" class="dropdown-item">
											<a class="d-block link" href="#">
												<span class="category d-block fw-normal text-muted mb-1"></span>
												<span class="component text-dark"></span>
											</a>
										</div>
									</div>
								</div>
		
								<ul class="navbar-nav p-0">
									<li class="nav-item">
										<a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html" target="_blank"> Support <i class="bi-box-arrow-up-right ms-1"></i> </a>
									</li>
									<li class="nav-item">
										<a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN" target="_blank"> Youtube <i class="bi-box-arrow-up-right ms-1"></i> </a>
									</li>
									<li class="nav-item">
										<a class="btn btn-ghost-secondary btn-sm" href="https://www.facebook.com/messages/t/sepay.vn" target="_blank"> Facebook <i class="bi-box-arrow-up-right ms-1"></i> </a>
									</li>
									<li class="nav-item">
										<a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel" target="_blank"> Telegram <i class="bi-box-arrow-up-right ms-1"></i> </a>
									</li>
								</ul>
							</div>
						</div>
					</div>
				</nav>
			</div>
		</header>

        <main id="content" role="main">
			<nav
				class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
				data-hs-nav-scroller-options='{
					"type": "vertical",
					"target": ".navbar-nav .active",
					"offset": 80
				}'
			>
				<button type="button" class="navbar-toggler btn btn-white d-grid w-100" data-bs-toggle="collapse" data-bs-target="#navbarVerticalNavMenu" aria-label="Toggle navigation" aria-expanded="false" aria-controls="navbarVerticalNavMenu">
					<span class="d-flex justify-content-between align-items-center">
						<span class="h3 mb-0">Nav menu</span>
			
						<span class="navbar-toggler-default">
							<i class="bi-list"></i>
						</span>
			
						<span class="navbar-toggler-toggled">
							<i class="bi-x"></i>
						</span>
					</span>
				</button>

				<div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
					<div class="navbar-brand-wrapper border-end" style="height: auto;">
						<div class="d-flex align-items-center mb-3">
							<a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
								<img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo" />
							</a>
						</div>
					</div>
					<div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
						<ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
							<li class="nav-item">
								<span class="nav-subtitle">Giới thiệu</span>
							</li>
			
							<li class="nav-item">
								<a class="nav-link" href="sepay-la-gi.html">SePay là gì?</a>
							</li>
							<li class="nav-item my-2 mt-lg-5">
								<span class="nav-subtitle">Gói dịch vụ</span>
							</li>
	
							<li class="nav-item">
								<a class="nav-link" href="goi-theo-diem-ban">Gói theo điểm bán</a>
							</li>
							<li class="nav-item my-2 my-lg-5"></li>
			
							<li class="nav-item">
								<span class="nav-subtitle">Hướng dẫn chung</span>
							</li>
			
							<li class="nav-item">
								<a class="nav-link" href="dang-ky-sepay.html">Đăng ký SePay</a>
							</li>
			
							<li class="nav-item">
								<a class="nav-link" href="them-tai-khoan-ngan-hang.html">Thêm tài khoản ngân hàng</a>
							</li>
			
							<li class="nav-item">
								<a class="nav-link" href="xem-giao-dich.html">Xem giao dịch</a>
							</li>
							<li class="nav-item">
								<a class="nav-link" href="nguoi-dung-va-phan-quyen.html">Người dùng & Phân quyền</a>
							</li>
			
							<li class="nav-item">
								<a class="nav-link" href="tai-khoan-phu.html">Tài khoản phụ</a>
							</li>

							<li class="nav-item">
								<a class="nav-link" href="cau-hinh-tai-khoan-ngan-hang.html">Cấu hình TK ngân hàng</a>
							</li>
			
							<li class="nav-item my-2 my-lg-5"></li>
			
							<li class="nav-item">
								<span class="nav-subtitle">Cấu hình công ty</span>
							</li>
			
							<li class="nav-item">
								<a class="nav-link" href="goi-dich-vu.html">Gói dịch vụ</a>
							</li>
							<li class="nav-item">
								<a class="nav-link" href="hoa-don-va-thanh-toan.html">Hóa đơn & thanh toán</a>
							</li>
			
							<li class="nav-item">
								<a class="nav-link" href="cau-hinh-chung.html">Cấu hình chung</a>
							</li>
			
							<li class="nav-item my-2 my-lg-5"></li>
			
							<li class="nav-item">
								<span class="nav-subtitle">Chia sẻ biến động số dư</span>
							</li>
			
							<li class="nav-item">
								<a class="nav-link" href="tich-hop-telegram.html">Tích hợp Telegram <i class="bi bi-telegram ms-2 text-info"></i></a>
							</li>
			
							<li class="nav-item">
								<a class="nav-link" href="tich-hop-lark-messenger.html">Tích hợp Lark Messenger <img src="assets/img/lark/lark-icon.png" class="img-fluid" style="max-height: 25px;" /></a>
							</li>

							<li class="nav-item">
								<a class="nav-link" href="tich-hop-viber.html">Tích hợp Viber <img src="assets/img/viber/viber-icon.png" class="img-fluid rounded ms-2" style="height:25px;width:25px;"></a>
							</li>
			
							<li class="nav-item">
								<a class="nav-link d-flex align-items-center" href="mobile-app.html">Mobile App <i class="bi bi-phone-vibrate ms-2 fs-3 text-info"></i> </a>
							</li>
							<li class="nav-item">
								<a class="nav-link " href="tich-hop-loa-thanh-toan.html">Tích hợp Loa thanh toán 
									<svg class="mb-0 ms-1" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#377dff" class="bi bi-speaker" viewBox="0 0 16 16">
										<path d="M12 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1zM4 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2z"></path>
										<path d="M8 4.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5M8 6a2 2 0 1 0 0-4 2 2 0 0 0 0 4m0 3a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3m-3.5 1.5a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0"></path>
									  </svg>
									</a>
							  </li>
							<li class="nav-item my-2 my-lg-5"></li>
			
							<li class="nav-item">
								<span class="nav-subtitle">Tích hợp web</span>
							</li>
			
							<li class="nav-item">
								<a class="nav-link" href="tich-hop-shopify.html">Tích hợp Shopify <img class="ms-2" src="assets/img/others/shopify-icon.png" style="width: 22px; height: 22px;" /></a>
							</li>
			
							<li class="nav-item">
								<a class="nav-link" href="tich-hop-sapo.html">Tích hợp Sapo <img class="ms-2" src="assets/img/others/sapo-icon.png" style="width: 18px; height: 18px;" /></a>
							</li>
			
							<li class="nav-item">
								<a class="nav-link" href="tich-hop-haravan.html">Tích hợp Haravan <img class="ms-2" src="assets/img/others/haravan-icon.png" style="width: 22px; height: 22px;" /></a>
							</li>
							<li class="nav-item">
								<a class="nav-link" href="tich-hop-gohighlevel.html">Tích hợp GoHighLevel <img class="ms-2" src="assets/img/others/gohighlevel-icon.png" style="width:22px; height: 22px;"></a>
							</li>
							<li class="nav-item">
								<a class="nav-link" href="woocommerce.html">Tích hợp WooCommerce <img class="ms-2" src="assets/img/others/woocommerce-icon.png" style="width: 22px; height: 22px;" /></a>
							</li>
							<li class="nav-item">
								<a class="nav-link" href="tich-hop-google-sheets.html">Tích hợp Google Sheets <img class="ms-2" src="assets/img/others/google-sheets-icon.png" style="width: 22px; height: 22px;" /></a>
							</li>
							<li class="nav-item">
								<a class="nav-link" href="tich-hop-hostbill.html">Tích hợp HostBill <img class="ms-2" src="assets/img/others/hostbill-icon.png" style="width: 22px; height: 22px;" /></a>
							</li>
							<li class="nav-item my-2 my-lg-5"></li>
			
							<li class="nav-item">
								<span class="nav-subtitle">Lập trình & Tích hợp</span>
							</li>
							<li class="nav-item">
								<a class="nav-link" href="tich-hop-webhooks.html">Tích hợp WebHooks</a>
							</li>
			
							<li class="nav-item">
								<a class="nav-link" href="lap-trinh-webhooks.html">Lập trình WebHooks</a>
							</li>
							<li class="nav-item">
								<a class="nav-link" href="gia-lap-giao-dich.html">Giả lập giao dịch</a>
							</li>
							<li class="nav-item">
								<a class="nav-link" href="tao-qr-code-vietqr-dong.html">Tạo & nhúng QR Code</a>
							</li>
							<li class="nav-item">
                                <a class="nav-link" href="/oauth2">OAuth2</a>
                            </li>
			
							<li class="nav-item my-2 my-lg-5"></li>
			
							<li class="nav-item">
								<span class="nav-subtitle">SePay API</span>
							</li>
							<li class="nav-item">
								<a class="nav-link" href="gioi-thieu-api.html">Giới thiệu API</a>
							</li>
							<li class="nav-item">
								<a class="nav-link" href="tao-api-token.html">Tạo API Token</a>
							</li>
							<li class="nav-item">
								<a class="nav-link" href="api-giao-dich.html">API Giao dịch</a>
							</li>
							<li class="nav-item">
								<a class="nav-link" href="api-tai-khoan-ngan-hang.html">API Tài khoản ngân hàng</a>
							</li>
							<li class="nav-item">
								<a class="nav-link active" href="api-va-theo-don-hang.html">API VA theo Đơn hàng</a>
							</li>
						</ul>
					</div>
				</div>
			</nav>

			<div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">
                <div class="docs-page-header">
                    <div class="row align-items-center">
                        <div class="col-sm">
                            <h1 class="docs-page-header-title">API VA theo Đơn hàng</h1>
                            <p class="docs-page-header-text">API VA (Virtual Account) theo đơn hàng là giải pháp tự động hóa xác nhận thanh toán. Thay vì sử dụng một VA cố định, mỗi đơn hàng sẽ được cấp một VA riêng với số tiền khớp chính xác.</p>
                        </div>
                    </div>
                </div>

				<div style="max-width: 1200px;">
					<img src="assets/img/api-va-theo-don-hang/va-order-flow.png" class="img-fluid" alt="API VA theo Đơn hàng cho BIDV doanh nghiệp">
					<h2 id="giai-thich-luong-hoat-dong" class="hs-docs-heading">
						Giải thích luồng hoạt động
					</h2>
					<p>Khi khách hàng thực hiện thanh toán qua VA (Virtual Account), toàn bộ quá trình được xử lý tự động theo các bước sau:</p>
					<ol>
						<li>Sau khi khách hàng hoàn tất đơn hàng trên website của bạn, hệ thống sẽ tự động kết nối với SePay thông qua API để tạo một đơn hàng mới. SePay sẽ trả về một số VA duy nhất cho đơn hàng này.</li>
						<li>Website của bạn ngay lập tức hiển thị thông tin thanh toán cho khách hàng, bao gồm:
							<ul>
								<li>Số tài khoản VA cần chuyển khoản</li>
								<li>Số tiền cần thanh toán chính xác</li>
								<li>Thời gian VA có hiệu lực</li>
								<li>Mã QR để quét thanh toán nhanh</li>
							</ul>
						</li>
						<li>Khách hàng có thể dễ dàng thanh toán bằng cách quét mã QR hoặc chuyển khoản trực tiếp đến số VA được cấp.</li>
						<li>Ngay khi giao dịch được thực hiện thành công, ngân hàng sẽ tự động gửi thông báo đến SePay để xác nhận thanh toán.</li>
						<li>SePay nhận được thông báo và ngay lập tức chuyển tiếp thông tin này đến website của bạn thông qua <a href="tich-hop-webhooks.html" target="_blank">Webhook</a></li>
						<li>Hệ thống của bạn xử lý thông tin nhận được, cập nhật trạng thái đơn hàng và hiển thị thông báo xác nhận thanh toán thành công cho khách hàng.</li>
					</ol>
					<h2 id="uu-diem-chinh" class="hs-docs-heading">
						Ưu điểm chính <a class="anchorjs-link" href="#uu-diem-chinh" aria-label="Anchor" data-anchorjs-icon="#"></a>
					</h2>
					<div class="row content-space-t-1">
						<div class="col-md-6 col-lg-5 mb-3 mb-md-7">
							<div class="d-flex pe-lg-5 aos-init aos-animate" data-aos="fade-up">
								<div class="flex-shrink-0">
									<span class="svg-icon text-primary">
										<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
											<path fill-rule="evenodd" clip-rule="evenodd" d="M15 19.5229C15 20.265 15.9624 20.5564 16.374 19.9389L22.2227 11.166C22.5549 10.6676 22.1976 10 21.5986 10H17V4.47708C17 3.73503 16.0376 3.44363 15.626 4.06106L9.77735 12.834C9.44507 13.3324 9.80237 14 10.4014 14H15V19.5229Z" fill="#035A4B"></path>
											<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M3 6.5C3 5.67157 3.67157 5 4.5 5H9.5C10.3284 5 11 5.67157 11 6.5C11 7.32843 10.3284 8 9.5 8H4.5C3.67157 8 3 7.32843 3 6.5ZM3 18.5C3 17.6716 3.67157 17 4.5 17H9.5C10.3284 17 11 17.6716 11 18.5C11 19.3284 10.3284 20 9.5 20H4.5C3.67157 20 3 19.3284 3 18.5ZM2.5 11C1.67157 11 1 11.6716 1 12.5C1 13.3284 1.67157 14 2.5 14H6.5C7.32843 14 8 13.3284 8 12.5C8 11.6716 7.32843 11 6.5 11H2.5Z" fill="#035A4B"></path>
										</svg>
									</span>
								</div>
								<div class="flex-grow-1 ms-4">
									<h4>Chính xác tuyệt đối</h4>
									<p>VA chỉ chấp nhận thanh toán đúng số tiền của đơn hàng, nếu khách hàng chuyển sai số tiền thì app bank sẽ báo lỗi ngay lập tức.</p>
								</div>
							</div>
						</div>
						<div class="col-md-6 col-lg-5 mb-3 mb-md-7">
							<div class="d-flex ps-lg-5 aos-init aos-animate" data-aos="fade-up" data-aos-delay="100">
								<div class="flex-shrink-0">
									<span class="svg-icon text-primary">
										<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
											<path opacity="0.3" d="M18 10V20C18 20.6 18.4 21 19 21C19.6 21 20 20.6 20 20V10H18Z" fill="#035A4B"></path>
											<path opacity="0.3" d="M11 10V17H6V10H4V20C4 20.6 4.4 21 5 21H12C12.6 21 13 20.6 13 20V10H11Z" fill="#035A4B"></path>
											<path opacity="0.3" d="M10 10C10 11.1 9.1 12 8 12C6.9 12 6 11.1 6 10H10Z" fill="#035A4B"></path>
											<path opacity="0.3" d="M18 10C18 11.1 17.1 12 16 12C14.9 12 14 11.1 14 10H18Z" fill="#035A4B"></path>
											<path opacity="0.3" d="M14 4H10V10H14V4Z" fill="#035A4B"></path>
											<path opacity="0.3" d="M17 4H20L22 10H18L17 4Z" fill="#035A4B"></path>
											<path opacity="0.3" d="M7 4H4L2 10H6L7 4Z" fill="#035A4B"></path>
											<path d="M6 10C6 11.1 5.1 12 4 12C2.9 12 2 11.1 2 10H6ZM10 10C10 11.1 10.9 12 12 12C13.1 12 14 11.1 14 10H10ZM18 10C18 11.1 18.9 12 20 12C21.1 12 22 11.1 22 10H18ZM19 2H5C4.4 2 4 2.4 4 3V4H20V3C20 2.4 19.6 2 19 2ZM12 17C12 16.4 11.6 16 11 16H6C5.4 16 5 16.4 5 17C5 17.6 5.4 18 6 18H11C11.6 18 12 17.6 12 17Z" fill="#035A4B"></path>
										  </svg>
									</span>
								</div>
								<div class="flex-grow-1 ms-4">
									<h4>Độc lập hoàn toàn</h4>
									<p>Mỗi đơn hàng có một VA riêng nên không phụ thuộc vào nội dung chuyển khoản, tránh được các lỗi do khách hàng điền sai nội dung.</p>
								</div>
							</div>
						</div>
						<div class="w-100"></div>
						<div class="col-md-6 col-lg-5 mb-3 mb-md-7 mb-lg-0">
							<div class="d-flex pe-lg-5 aos-init aos-animate" data-aos="fade-up" data-aos-delay="200">
								<div class="flex-shrink-0">
									<span class="svg-icon text-primary">
										<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
											<g id="com/com014">
											  <path id="vector" opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M4 4L11.6314 2.56911C11.875 2.52343 12.125 2.52343 12.3686 2.56911L20 4V11.9033C20 15.696 18.0462 19.2211 14.83 21.2313L12.53 22.6687C12.2057 22.8714 11.7943 22.8714 11.47 22.6687L9.17001 21.2313C5.95382 19.2211 4 15.696 4 11.9033L4 4Z" fill="#035A4B"></path>
											  <path id="group" fill-rule="evenodd" clip-rule="evenodd" d="M9.5 10.5C9.5 9.11929 10.6193 8 12 8C13.3807 8 14.5 9.11929 14.5 10.5V11C15.0523 11 15.5 11.4477 15.5 12V15C15.5 15.5523 15.0523 16 14.5 16H9.5C8.94772 16 8.5 15.5523 8.5 15V12C8.5 11.4477 8.94772 11 9.5 11V10.5ZM12 9C11.1716 9 10.5 9.67157 10.5 10.5V11H13.5V10.5C13.5 9.67157 12.8284 9 12 9Z" fill="#035A4B"></path>
											</g>
										</svg>
									</span>
								</div>
								<div class="flex-grow-1 ms-4">
									<h4>An toàn tuyệt đối</h4>
									<p>VA tự động hủy sau khi thanh toán thành công hoặc hết hạn, tránh hoàn toàn tình trạng thanh toán trùng lặp.</p>
								</div>
							</div>
						</div>
						<div class="col-md-6 col-lg-5">
							<div class="d-flex ps-lg-5 aos-init aos-animate" data-aos="fade-up" data-aos-delay="300">
								<div class="flex-shrink-0">
									<span class="svg-icon text-primary">
										<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
											<path d="M17.2718 8.68537C16.8933 8.28319 16.9125 7.65032 17.3146 7.2718C17.7168 6.89329 18.3497 6.91246 18.7282 7.31464L22.7282 11.5646C23.0906 11.9497 23.0906 12.5503 22.7282 12.9354L18.7282 17.1854C18.3497 17.5875 17.7168 17.6067 17.3146 17.2282C16.9125 16.8497 16.8933 16.2168 17.2718 15.8146L20.6268 12.25L17.2718 8.68537Z" fill="#035A4B"></path>
											<path d="M6.7282 8.68537C7.10671 8.28319 7.08754 7.65032 6.68536 7.2718C6.28319 6.89329 5.65031 6.91246 5.2718 7.31464L1.2718 11.5646C0.909397 11.9497 0.909397 12.5503 1.2718 12.9354L5.2718 17.1854C5.65031 17.5875 6.28319 17.6067 6.68536 17.2282C7.08754 16.8497 7.10671 16.2168 6.7282 15.8146L3.37325 12.25L6.7282 8.68537Z" fill="#035A4B"></path>
											<rect opacity="0.3" x="12.7388" y="3.97168" width="2" height="16" rx="1" transform="rotate(12.3829 12.7388 3.97168)" fill="#035A4B"></rect>
										</svg>
									</span>
								</div>
								<div class="flex-grow-1 ms-4">
									<h4>Tích hợp dễ dàng</h4>
									<p>API RESTful được thiết kế chuyên nghiệp với tài liệu đầy đủ, dễ dàng tích hợp vào mọi nền tảng.</p>
								</div>
							</div>
						</div>
					</div>
				</div>

				<h2 id="ngan-hang-ho-tro" class="hs-docs-heading">
					Ngân hàng hỗ trợ <a class="anchorjs-link" href="#ngan-hang-ho-tro" aria-label="Anchor" data-anchorjs-icon="#"></a>
				</h2>

				<ul class="lh-lg ps-0" style="list-style: none;">
					<li class="d-flex align-items-start">
						<img src="https://my.sepay.vn/assets/images/banklogo/bidv-icon.png" class="me-2 mt-2" style="width: 18px; height: 18px;" />
						<a href="api-va-theo-don-hang-bidv.html">Tài liệu API VA theo Đơn hàng cho doanh nghiệp</a>
					</li>
				</ul>
            </div>
        </main>

		<div id="contact-box-overlay"></div>
		<div id="contact-box">
			<div class="popup">
				<a href="https://m.me/***************" target="_blank" class="item">
					<div class="logo">
						<img src="../assets/img/others/fb-messenger.png" width="50%" />
					</div>
					<div class="meta">
						<p class="title">Facebook Messenger</p>
						<small class="description">Hỗ trợ live chat 24/7</small>
					</div>
				</a>
				<div class="divide"></div>
				<a href="tel:***********" class="item">
					<div class="icon" style="background-color: #22c55e; color: #fff;">
						<svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;">
							<path
								d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z"
								stroke="currentColor"
								stroke-width="1.5"
								stroke-linecap="round"
								fill="transparent"
							></path>
						</svg>
					</div>
					<div class="meta">
						<p class="title">Hotline</p>
						<small class="description">Điện thoại hỗ trợ</small>
					</div>
				</a>
				<div class="divide"></div>
				<a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
					<div class="logo">
						<img src="../assets/img/others/youtube-social.png" />
					</div>
					<div class="meta">
						<p class="title">Youtube</p>
						<small class="description">Theo dõi video mới nhất của SePay</small>
					</div>
				</a>
				<div class="divide"></div>
				<a href="https://t.me/s/sepaychannel" target="_blank" class="item">
					<div class="logo">
						<img src="../assets/img/others/telegram-social.png" />
					</div>
					<div class="meta">
						<p class="title">Telegram</p>
						<small class="description">Nhận thông tin mới nhất từ SePay</small>
					</div>
				</a>
			</div>
			<div class="container">
				<div class="dot-ping">
					<div class="ping"></div>
					<div class="dot"></div>
				</div>
				<div class="contact-icon">
					<svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;">
						<path
							fill-rule="evenodd"
							clip-rule="evenodd"
							d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z"
							fill="currentColor"
						></path>
					</svg>
				</div>
				<span style="font-weight: bold;">Liên hệ chúng tôi</span>
			</div>
		</div>

        <a
            class="js-go-to go-to position-fixed"
            href="javascript:;"
            style="visibility: hidden;"
            data-hs-go-to-options='{
				"offsetTop": 700,
				"position": {
					"init": {
					"right": "2rem"
					},
					"show": {
					"bottom": "2rem"
					},
					"hide": {
					"bottom": "-2rem"
					}
				}
			}'
        >
            <i class="bi-chevron-up"></i>
        </a>

        <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
		<script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
		<script src="../assets/vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js"></script>
		<script src="../assets/vendor/list.js/dist/list.min.js"></script>
		<script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>
		<script src="../assets/js/theme.min.js"></script>
		<script src="../assets/js/contact-box.js"></script>

		<script>
			(function () {
				new HSHeader("#header").init();

				new HsNavScroller('.js-nav-scroller', {
					delay: 400,
					offset: 140,
				});

				const docsSearch = HSCore.components.HSList.init('#docsSearch');
				new HSGoTo('.js-go-to');

				function setCookie(cname, cvalue, exdays) {
					var d = new Date();
					d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
					var expires = "expires=" + d.toUTCString();
					document.cookie = cname + "=" + cvalue + "; " + expires + "; path=/; domain=.sepay.vn";
				}
				let urlParams = new URLSearchParams(document.location.search);
				['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content'].forEach(function(param) {
					let value = urlParams.get(param);
					if (value) {
						setCookie(param, value, 90);
					}
				});
				if (document.referrer != '' && document.cookie.indexOf('referer') === -1) {
					setCookie('referer', document.referrer, 90);
				}
			})();
		</script>
    </body>
</html>
