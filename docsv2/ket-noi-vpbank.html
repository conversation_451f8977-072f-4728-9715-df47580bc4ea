<!DOCTYPE html>
<html lang="en">

<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

  <!-- Title -->
  <title>Hướng dẫn kết nối <PERSON> Hàng VPBank cá nhân</title>

  <!-- Favicon -->
  <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">

  <!-- CSS Implementing Plugins -->
  <link rel="stylesheet" href="../assets/vendor/bootstrap-icons/font/bootstrap-icons.css">
  <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
  <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />

  <!-- CSS Front Template -->
  <link rel="stylesheet" href="../assets/css/theme.min.css">
  <link rel="stylesheet" href="../assets/css/docs.min.css">

  <!--contact-box-css-->
  <link rel="stylesheet" href="../assets/css/contact-box.css">
  <!--/contact-box-css-->

  <style>
    .docs-navbar-sidebar-aside-body {
      padding-top: 3.5rem !important;
    }
  </style>
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());

    gtag('config', 'G-J8DLMQTKSQ');
  </script>
</head>

<body class="navbar-sidebar-aside-lg">
  <!-- ========== HEADER ========== -->
  <header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
    <div class="container-fluid">
      <nav class="navbar-nav-wrap">
        <div class="row flex-grow-1">
          <!-- Default Logo -->
          <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
              <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo">
            </a>

          </div>
          <!-- End Default Logo -->

          <div class="col-md px-lg-0">
            <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
              <div class="d-none d-md-block">


                <!-- List Item Template -->
                <div class="d-none">
                  <div id="searchTemplate" class="dropdown-item">
                    <a class="d-block link" href="#">
                      <span class="category d-block fw-normal text-muted mb-1"></span>
                      <span class="component text-dark"></span>
                    </a>
                  </div>
                </div>
                <!-- End List Item Template -->
              </div>

              <!-- Navbar -->
              <ul class="navbar-nav p-0">
                <li class="nav-item">
                  <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html" target="_blank">
                    Support <i class="bi-box-arrow-up-right ms-1"></i>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN" target="_blank">
                    Youtube <i class="bi-box-arrow-up-right ms-1"></i>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="btn btn-ghost-secondary btn-sm" href="https://www.facebook.com/messages/t/sepay.vn"
                    target="_blank">
                    Facebook <i class="bi-box-arrow-up-right ms-1"></i>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel" target="_blank">
                    Telegram <i class="bi-box-arrow-up-right ms-1"></i>
                  </a>
                </li>

              </ul>
              <!-- End Navbar -->
            </div>
          </div>
          <!-- End Col -->
        </div>
        <!-- End Row -->
      </nav>
    </div>
  </header>
  <!-- ========== END HEADER ========== -->

  <!-- ========== MAIN CONTENT ========== -->
  <main id="content" role="main">
    <!-- Navbar -->
    <nav class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
      data-hs-nav-scroller-options='{
            "type": "vertical",
            "target": ".navbar-nav .active",
            "offset": 80
           }'>
      <!-- Navbar Toggle -->
      <button type="button" class="navbar-toggler btn btn-white d-grid w-100" data-bs-toggle="collapse"
        data-bs-target="#navbarVerticalNavMenu" aria-label="Toggle navigation" aria-expanded="false"
        aria-controls="navbarVerticalNavMenu">
        <span class="d-flex justify-content-between align-items-center">
          <span class="h3 mb-0">Nav menu</span>

          <span class="navbar-toggler-default">
            <i class="bi-list"></i>
          </span>

          <span class="navbar-toggler-toggled">
            <i class="bi-x"></i>
          </span>
        </span>
      </button>
      <!-- End Navbar Toggle -->

      <!-- Navbar Collapse -->
      <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
        <div class="navbar-brand-wrapper border-end" style="height: auto;">
          <!-- Default Logo -->
          <div class="d-flex align-items-center mb-3">
            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
              <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo">
            </a>

          </div>
          <!-- End Default Logo -->


        </div>

        <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
          <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
            <li class="nav-item">
              <span class="nav-subtitle">Giới thiệu</span>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="sepay-la-gi.html">SePay là gì?</a>
            </li>
            <li class="nav-item my-2 mt-lg-5">
              <span class="nav-subtitle">Gói dịch vụ</span>
          </li>

          <li class="nav-item">
              <a class="nav-link" href="goi-theo-diem-ban">Gói theo điểm bán</a>
          </li>
            <li class="nav-item my-2 my-lg-5"></li>

            <li class="nav-item">
              <span class="nav-subtitle">Hướng dẫn chung</span>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="dang-ky-sepay.html">Đăng ký SePay</a>
            </li>

            <li class="nav-item">
              <a class="nav-link active" href="them-tai-khoan-ngan-hang.html">Thêm tài khoản ngân hàng</a>
            </li>

            
            <li class="nav-item">
              <a class="nav-link " href="xem-giao-dich.html">Xem giao dịch</a>
            </li>
            <li class="nav-item">
              <a class="nav-link " href="nguoi-dung-va-phan-quyen.html">Người dùng & Phân quyền</a>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="tai-khoan-phu.html">Tài khoản phụ</a>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="cau-hinh-tai-khoan-ngan-hang.html">Cấu hình TK ngân hàng</a>
            </li>

            <li class="nav-item my-2 my-lg-5"></li>

            <li class="nav-item">
              <span class="nav-subtitle">Cấu hình công ty</span>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="goi-dich-vu.html">Gói dịch vụ</a>
            </li>
            <li class="nav-item">
              <a class="nav-link " href="hoa-don-va-thanh-toan.html">Hóa đơn & thanh toán</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="cau-hinh-chung.html">Cấu hình chung</a>
            </li>

            <li class="nav-item my-2 my-lg-5"></li>

                        <li class="nav-item">
                            <span class="nav-subtitle">Chia sẻ biến động số dư</span>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link " href="tich-hop-telegram.html">Tích hợp Telegram <i class="bi bi-telegram ms-2 text-info"></i></a>
                          </li>
              
            <li class="nav-item">
              <a class="nav-link" href="tich-hop-lark-messenger.html">Tích hợp Lark Messenger <img src="assets/img/lark/lark-icon.png" class="img-fluid" style="max-height:25px"></a>
          </li>

                        <li class="nav-item">
                            <a class="nav-link" href="tich-hop-viber.html">Tích hợp Viber <img src="assets/img/viber/viber-icon.png" class="img-fluid rounded ms-2" style="height:25px;width:25px;"></a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link d-flex align-items-center" href="mobile-app.html">Mobile App <i class="bi bi-phone-vibrate ms-2 fs-3 text-info"></i> </a>
                        </li>
                        <li class="nav-item">
                          <a class="nav-link " href="tich-hop-loa-thanh-toan.html">Tích hợp Loa thanh toán 
                              <svg class="mb-0 ms-1" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#377dff" class="bi bi-speaker" viewBox="0 0 16 16">
                                  <path d="M12 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1zM4 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2z"></path>
                                  <path d="M8 4.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5M8 6a2 2 0 1 0 0-4 2 2 0 0 0 0 4m0 3a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3m-3.5 1.5a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0"></path>
                                </svg>
                          </a>
                        </li>
            <li class="nav-item my-2 my-lg-5"></li>

            <li class="nav-item">
              <span class="nav-subtitle">Tích hợp web</span>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="tich-hop-shopify.html">Tích hợp Shopify <img class="ms-2" src="assets/img/others/shopify-icon.png" style="width:22px; height: 22px;"></a>
            </li>
            
            <li class="nav-item">
              <a class="nav-link" href="tich-hop-sapo.html">Tích hợp Sapo <img class="ms-2"
                  src="assets/img/others/sapo-icon.png" style="width:18px; height: 18px;"></a>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="tich-hop-haravan.html">Tích hợp Haravan <img class="ms-2"
                  src="assets/img/others/haravan-icon.png" style="width:22px; height: 22px;"></a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="tich-hop-gohighlevel.html">Tích hợp GoHighLevel <img class="ms-2" src="assets/img/others/gohighlevel-icon.png" style="width:22px; height: 22px;"></a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="woocommerce.html">Tích hợp WooCommerce <img class="ms-2"
                  src="assets/img/others/woocommerce-icon.png" style="width:22px; height: 22px;"></a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="tich-hop-google-sheets.html">Tích hợp Google Sheets <img class="ms-2"
                  src="assets/img/others/google-sheets-icon.png" style="width:22px; height: 22px;"></a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="tich-hop-hostbill.html">Tích hợp HostBill <img class="ms-2"
                      src="assets/img/others/hostbill-icon.png" style="height: 22px;"></a>
          </li>
            <li class="nav-item my-2 my-lg-5"></li>

            <li class="nav-item">
              <span class="nav-subtitle">Lập trình & Tích hợp</span>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="tich-hop-webhooks.html">Tích hợp WebHooks</a>
            </li>

            <li class="nav-item ">
              <a class="nav-link" href="lap-trinh-webhooks.html">Lập trình WebHooks</a>
            </li>
            <li class="nav-item ">
              <a class="nav-link" href="gia-lap-giao-dich.html">Giả lập giao dịch</a>
            </li>
            <li class="nav-item ">
              <a class="nav-link" href="tao-qr-code-vietqr-dong.html">Tạo & nhúng QR Code</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/oauth2">OAuth2</a>
            </li>


            <li class="nav-item my-2 my-lg-5"></li>

            <li class="nav-item">
              <span class="nav-subtitle">SePay API</span>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="gioi-thieu-api.html">Giới thiệu API</a>
            </li>
            <li class="nav-item">
              <a class="nav-link " href="tao-api-token.html">Tạo API Token</a>
            </li>
            <li class="nav-item">
              <a class="nav-link " href="api-giao-dich.html">API Giao dịch</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="api-tai-khoan-ngan-hang.html">API Tài khoản ngân hàng</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="api-va-theo-don-hang.html">API VA theo Đơn hàng</a>
            </li>


          </ul>
        </div>
      </div>
      <!-- End Navbar Collapse -->
    </nav>
    <!-- End Navbar -->

    <!-- Content -->
    <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">

      <div class="docs-page-header">
        <div class="row align-items-center">
          <div class="col-sm">
            <h1 class="docs-page-header-title">Hướng dẫn kết nối tài khoản VPBank cá nhân vào SePay</h1>
            <p class="docs-page-header-text">Bài viết này sẽ hướng dẫn bạn kết nối tài khoản Ngân Hàng TMCP Việt Nam Thịnh Vượng vào
              SePay thông qua API.</p>
          </div>
        </div>
      </div>

      <p>SePay chính thức hợp tác mở API cùng Ngân hàng VPBank. Chỉ 5 phút mở tài khoản VPBank qua app VPBank NEO, 3 phút liên kết qua SePay, quý khách đã có thể nhận và quản lý giao dịch ngay tức thì.</p>

      <div class="ratio ratio-16x9" style="max-width: 800px;">
        <iframe src="https://www.youtube.com/embed/08EVRuHlkS8?si=KEJlW-FMz2yuXRBJ" title="Hướng dẫn kết nối tài khoản VPBank cá nhân" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>  
      </div>

      <p class="mt-3">Sau đây là các bước kết nối API Ngân hàng VPBank:</p>
      <p><b>Bước 1:</b> Truy cập <a href="https://my.sepay.vn" target="_blank">my.sepay.vn</a> -> Chọn menu <b>Ngân
          hàng</b> -> Chọn <b>Kết nối tài khoản</b></p>
      <p class="px-3 px-sm-1"><img src="assets/img/vpbank-connect/vpbank-connect-1.png" class="img-fluid"
          style="max-height: 500px; margin: -1.5rem -2rem;"></p>
      <p><b>Bước 2:</b> Tại giao diện kết nối API ngân hàng:</p>
      <p>Chọn mục <b>Cá nhân</b>, sau đó nhấn chọn kết nối tại ngân hàng VPBank.</p>
      <p class="px-3 px-sm-1"><img src="assets/img/vpbank-connect/vpbank-connect-2.png" class="img-fluid"
          style="max-height: 500px; margin: -1.5rem -2rem;"></p>

      <p><b>Bước 3:</b> Bắt đầu đăng nhập tài khoản VPBank NEO của bạn bằng cách nhấp vào nút <b>Đăng nhập ngay</b></p>

      <p class="px-3 px-sm-1"><img src="assets/img/vpbank-connect/vpbank-connect-3.png" class="img-fluid"
        style="max-height: 500px; margin: -1.5rem -2rem;"></p>

      <p>Cửa sổ đăng nhập VPBank NEO hiện ra, nhập thông tin tài khoản của bạn và tiến hành đăng nhập</p>

      <p class="px-3 px-sm-1"><img src="assets/img/vpbank-connect/vpbank-connect-4.png" class="img-fluid"
        style="max-height: 500px; margin: -1.5rem -2rem;"></p>

      <div class="alert alert-soft-dark" role="alert">
        <div class="d-flex align-items-baseline">
          <div class="flex-shrink-0">
            <i class="bi-info-circle me-1"></i>
          </div>
          <div class="flex-grow-1 ms-2">
            Nếu chưa có tài khoản, bạn có thể xem <a href="https://sepay.vn/blog/huong-dan-mo-tai-khoan-vpbank-ca-nhan-online-don-gian" target="_blank">hướng dẫn mở tài khoản trực tuyến qua app VPBank NEO</a>.
          </div>
        </div>
      </div>

      <p><b>Bước 4:</b> Nhập các thông tin cần thiết bao gồm:
      <ul>
        <li>Số tài khoản ngân hàng VPBank cá nhân</li>
        <li>Tên thụ hưởng của tài khoản ngân hàng</li>
        <li>Số CCCD/CMND đã dùng đăng ký tài khoản ngân hàng</li>
      </ul>
      <p>Sau đó nhấp nút <b>Tiếp tục</b></p>

      <p class="px-3 px-sm-1"><img src="assets/img/vpbank-connect/vpbank-connect-5.png" class="img-fluid"
          style="max-height: 500px; margin: -1.5rem -2rem;"></p>

      <p><b>Bước 5:</b> Nhấp vào nút <b>Xác thực OTP</b></p>

      <p class="px-3 px-sm-1"><img src="assets/img/vpbank-connect/vpbank-connect-6.png" class="img-fluid"
          style="max-height: 500px; margin: -1.5rem -2rem;"></p>

      <p>Một sổ trình duyệt ngân hàng VPBank hiện ra, bạn thực hiện theo hướng dẫn để xác thực OTP.</p>

      <ul>
        <li>Chọn tài khoản cần liên kết và nhấp <b>Tiếp tục</b>, nhấp <b>Đồng ý</b>, một mã OTP xác thực sẽ gửi về số điện thoại mà bạn đã đăng ký cho số tài khoản ngân hàng VPBank.</li>
        <li>Nhập mã OTP, sau đó nhấp <b>Xác nhận</b> để hoàn tất xác thực</li>
      </ul>

      <div class="d-block d-md-flex align-items-center w-100">
        <p class="px-3 px-sm-1"><img src="assets/img/vpbank-connect/vpbank-connect-7.png" class="img-fluid"
          style="max-height: 600px; margin: -1.5rem -2rem;"></p>
        <p class="px-3 px-sm-1"><img src="assets/img/vpbank-connect/vpbank-connect-8.png" class="img-fluid"
          style="max-height: 600px; margin: -1.5rem -2rem;"></p>
        <p class="px-3 px-sm-1"><img src="assets/img/vpbank-connect/vpbank-connect-9.png" class="img-fluid"
            style="max-height: 600px; margin: -1.5rem -2rem;"></p>
      </div>

      <div class="alert alert-soft-dark" role="alert">
        <div class="d-flex align-items-baseline">
          <div class="flex-shrink-0">
            <i class="bi-info-circle me-1"></i>
          </div>
          <div class="flex-grow-1 ms-2">
            Trong trường hợp không nhận mã OTP hoặc mã hết hiệu lực, vui lòng nhấn vào nút <b>Gửi lại</b>.
          </div>
        </div>
      </div>

      <p><b>Bước 6:</b> Sau khi liên kết API thành công, bạn thực hiện thử một giao dịch chuyển tiền tới số tài khoản VPBank
        vừa liên kết.</p>
      <p class="px-3 px-sm-1"><img src="assets/img/vpbank-connect/vpbank-connect-10.png" class="img-fluid"
          style="max-height: 500px; margin: -1.5rem -2rem;"></p>
      <p>Nếu nhận được thông báo giao dịch mới như hình bên dưới thì việc liên kết API tài khoản VPBank của bạn đã hoàn tất.
      </p>
      <p class="px-3 px-sm-1"><img src="assets/img/vpbank-connect/vpbank-connect-17.png" class="img-fluid"
          style="max-height: 500px;"></p>

      <div class="alert alert-soft-dark" role="alert" id="ho-tro-nhan-sms-tien-ra">
        <div class="d-sm-flex">
          <div class="flex-shrink-0">
            <img class="avatar avatar-xl mb-2 mb-sm-0" src="../assets/svg/illustrations/oc-megaphone.svg"
              alt="Image Description">
          </div>

          <div class="flex-grow-1 ms-sm-4">
            <h3>Lưu ý:</h3>
            <p></p>Khi kết nối ngân hàng VPBank cá nhân thông qua API, mặc định SePay nhận các giao dịch báo có tại số tài khoản chính từ ngân hàng VPBank cung cấp. Bạn có thể tạo các tài khoản ảo với tiền tố <code>TKP</code> để xác thực thanh toán thông qua nội dung thanh toán.</p>
          </div>
        </div>
      </div>

      <h3 class="hs-docs-heading" id="them-va">Thêm tài khoản ảo (VA) <a class="anchorjs-link" href="#them-va" aria-label="Anchor" data-anchorjs-icon="#"></a></h3>
      <p>Hiện tại bạn có thể quản lý dòng tiền của tài khoản thông qua <a href="https://sepay.vn/blog/tai-khoan-ngan-hang-ao-la-gi-phan-loai-va-ung-dung/" target="_blank">tài khoản VA theo nội dụng</a>.</p>

      <p><b>Bước 1:</b> Chọn tài khoản ngân hàng VPBank đã liên kết API với SePay cần thêm VA mới.</p>

      <p><b>Bước 2:</b> Tại trang chi tiết tài khoản, bảng quản lý tài khoản ảo (VA), chọn nút <b>Thêm ngay</b>.</p>

      <p class="px-3 px-sm-1"><img src="assets/img/vpbank-connect/vpbank-connect-11.png" class="img-fluid"
        style="max-height: 500px; margin: -1rem -2rem;"></p>

      <p>Một hộp thoại hiện ra, bạn có thể đặt tên gợi nhớ để dễ dàng tìm kiếm VA sau này. Nhấn vào nút <b>Tạo VA</b>.</p>

      <p class="px-3 px-sm-1"><img src="assets/img/vpbank-connect/vpbank-connect-14.png" class="img-fluid" style="max-height: 500px;"></p>
  
      <p><b>Bước 3:</b> Sau khi tạo VA thành công, bạn có thể thực hiện thử một giao dịch chuyển tiền tới số VA vừa tạo để kiểm tra.</p>

      <h3 class="hs-docs-heading" id="chuyen-doi-phuong-thuc-ket-noi">Chuyển đổi phương thức kết nối <a class="anchorjs-link" href="#chuyen-doi-phuong-thuc-ket-noi" aria-label="Anchor" data-anchorjs-icon="#"></a></h3>
      <p>Hiện tại với ngân hàng VPBank cá nhân, SePay hỗ trợ chuyển đổi linh hoạt phương thức kết nối giữa API Banking và SMS
        Banking cho các trường hợp:</p>

      <ul>
        <li>Bạn đang sử dụng tài khoản VPBank với phương thức kết nối SMS Banking trước đó và muốn chuyển sang phương thức
          API Banking để tăng chất lượng dịch vụ.</li>
        <li>Trong trường hợp hệ thống ngân hàng VPBank gián đoạn hoặc bảo trì, bạn muốn chuyển sang phương thức kết nối SMS
          Banking để không ảnh hưởng đến việc nhận thông báo biến động số dư của tài khoản.</li>
      </ul>

      <h4 class="hs-docs-heading" id="sms-banking-sang-api-banking" class="d-flex align-items-center mb-4 d-flex"><span class="badge bg-primary rounded-pill me-2">1</span>Chuyển phương thức kết nối từ SMS Banking -> API Banking<a class="anchorjs-link" href="#sms-banking-sang-api-banking" aria-label="Anchor" data-anchorjs-icon="#"></a></h4>

      <p><b>Bước 1:</b> Chọn tài khoản ngân hàng VPBank bạn muốn chuyển đổi</p>

      <p><b>Bước 2:</b> Tại trang quản lý tài khoản ngân hàng VP, nhấp vào nút <b>Chuyển sang phương thức kết nối API
          Banking</b></p>
          
      <p class="px-3 px-sm-1 mt-2"><img src="assets/img/vpbank-connect/vpbank-connect-16.png" class="img-fluid"
          style="max-height: 500px; margin: -1.5rem -2rem;"></p>

      <p><b>Bước 3:</b> Tiếp tục thực hiện các bước liên kết để hoàn tất quá trình chuyển đổi</p>

      <p>Các bước khác thực hiện tương tự như việc thêm tài khoản ngân hàng VPBank cá nhân với hình thức liên kết API. Trường hợp đã liên kết API trước đó, bạn không cần phải xác thực OTP lại nữa.</p>
      <p>Sau khi chuyển đổi sang phương thức kết nối API Banking thành công, vui lòng thử thực hiện một giao dịch báo có để kiểm tra.</p>

      <h4 class="hs-docs-heading" id="api-banking-sang-sms-banking" class="d-flex align-items-center mb-4 d-flex"><span class="badge bg-primary rounded-pill me-2">2</span>Chuyển phương thức kết nối từ API Banking -> SMS Banking<a class="anchorjs-link" href="#api-banking-sang-sms-banking" aria-label="Anchor" data-anchorjs-icon="#"></a></h4>

      <div class="alert alert-soft-dark" role="alert">
        <div class="d-flex align-items-baseline">
          <div class="flex-shrink-0">
            <i class="bi-info-circle me-1"></i>
          </div>
          <div class="flex-grow-1 ms-2">
            Điều kiện để chuyển sang phương thức kết nối SMS Banking bắt buộc gói dịch vụ bạn đang sử dụng phải hỗ trợ SMS
            Banking. Xem thêm thông tin tại <a href="https://sepay.vn/bang-gia.html" target="_blank">Bảng giá</a>.
          </div>
        </div>
      </div>

      <p><b>Bước 1:</b> Chọn tài khoản ngân hàng VPBank bạn muốn chuyển đổi</p>

      <p><b>Bước 2:</b> Tại trang quản lý tài khoản ngân hàng VPBanh, nhấp vào nút <b>Chuyển đổi</b></p>
      <p class="px-3 px-sm-1 mt-2"><img src="assets/img/vpbank-connect/vpbank-connect-13.png" class="img-fluid"
          style="max-height: 530px; margin: -1.5rem -2rem;"></p>

      <p><b>Bước 3:</b> Tại hộp thoại xác nhận, vui lòng chọn SIM bạn muốn kết nối với tài khoản, sau đó nhấn vào nút
        <b>Chuyển đổi</b></p>
      <p><img src="assets/img/vpbank-connect/vpbank-connect-15.png" class="img-fluid" style="max-height: 500px;"></p>

      <div class="alert alert-soft-dark" role="alert" id="ho-tro-nhan-sms-tien-ra">
        <div class="d-sm-flex">
          <div class="flex-shrink-0">
            <img class="avatar avatar-xl mb-2 mb-sm-0" src="../assets/svg/illustrations/oc-megaphone.svg"
              alt="Image Description">
          </div>

          <div class="flex-grow-1 ms-sm-4">
            <h3>Lưu ý:</h3>
            <p>Nếu SIM chưa được đăng ký nhận thông báo biến động số dư thông qua SMS tại ngân hàng VPBank trước đó, vui lòng tiến hành đăng ký số điện thoại đã chọn tại <b>Bước 3</b> với ngân hàng VPBank để nhận SMS biến động số dư cho tài khoản.</p>
            <p>Trường hợp đã đăng ký số điện thoại nhận SMS biến động số dư trước đó, vui lòng thực hiện một giao dịch báo có/báo nợ bất kỳ để kiểm tra.</p>
          </div>
        </div>
      </div>

      <p>Ngoài ra, tại my.sepay.vn, bạn có thể:</p>
      <ul>
        <li><a href="https://my.sepay.vn/transactions">Xem danh sách giao dịch</a></li>
        <li><a href="https://my.sepay.vn/bankaccount">Xem danh sách ngân hàng đã kết nối hoặc tạo thêm VA.</a></li>

      </ul>
      <p>Chúc bạn thành công!</p>

      <div class="my-2 mt-5">
        <p>Đọc tiếp: <a href="xem-giao-dich.html">Xem giao dịch<i class="bi bi-chevron-right"></i></a></p>
      </div>

    </div>
    <!-- End Content -->
  </main>
  <!-- ========== END MAIN CONTENT ========== -->

  <!-- ========== SECONDARY CONTENTS ========== -->
  <!--contact-box-html-docs-->
  <div id="contact-box-overlay"></div>
  <div id="contact-box">
    <div class="popup">
      <a href="https://m.me/***************" target="_blank" class="item">
        <div class="logo">
          <img src="../assets/img/others/fb-messenger.png" width="50%" />
        </div>
        <div class="meta">
          <p class="title">Facebook Messenger</p>
          <small class="description">Hỗ trợ live chat 24/7</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="tel:***********" class="item">
        <div class="icon" style="background-color: #22c55e; color: #fff;">
          <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20"
            style="color: currentcolor;">
            <path
              d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z"
              stroke="currentColor" stroke-width="1.5" stroke-linecap="round" fill="transparent"></path>
          </svg>
        </div>
        <div class="meta">
          <p class="title">Hotline</p>
          <small class="description">Điện thoại hỗ trợ</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
        <div class="logo">
          <img src="../assets/img/others/youtube-social.png" />
        </div>
        <div class="meta">
          <p class="title">Youtube</p>
          <small class="description">Theo dõi video mới nhất của SePay</small>
        </div>
      </a>
      <div class="divide"></div>
      <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
        <div class="logo">
          <img src="../assets/img/others/telegram-social.png" />
        </div>
        <div class="meta">
          <p class="title">Telegram</p>
          <small class="description">Nhận thông tin mới nhất từ SePay</small>
        </div>
      </a>
    </div>
    <div class="container">
      <div class="dot-ping">
        <div class="ping"></div>
        <div class="dot"></div>
      </div>
      <div class="contact-icon">
        <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20"
          style="color: currentcolor;">
          <path fill-rule="evenodd" clip-rule="evenodd"
            d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z"
            fill="currentColor"></path>
        </svg>
      </div>
      <span style="font-weight: bold;">Liên hệ chúng tôi</span>
    </div>
  </div>
  <!--/contact-box-html-docs-->

  <!-- Go To -->
  <a class="js-go-to go-to position-fixed" href="javascript:;" style="visibility: hidden;" data-hs-go-to-options='{
       "offsetTop": 700,
       "position": {
         "init": {
           "right": "2rem"
         },
         "show": {
           "bottom": "2rem"
         },
         "hide": {
           "bottom": "-2rem"
         }
       }
     }'>
    <i class="bi-chevron-up"></i>
  </a>
  <!-- ========== END SECONDARY CONTENTS ========== -->

  <!-- JS Global Compulsory  -->
  <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

  <!-- JS Implementing Plugins -->
  <script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
  <script src="../assets/vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js"></script>
  <script src="../assets/vendor/list.js/dist/list.min.js"></script>
  <script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>


  <!-- JS Front -->
  <script src="../assets/js/theme.min.js"></script>

  <!-- contact-box-js -->
  <script src="../assets/js/contact-box.js"></script>
  <!-- /contact-box-js -->

  <!-- JS Plugins Init. -->
  <script>
    (function () {
      // INITIALIZATION OF HEADER
      // =======================================================
      new HSHeader('#header').init()

      // INITIALIZATION OF NAV SCROLLER
      // =======================================================
      new HsNavScroller('.js-nav-scroller', {
        delay: 400,
        offset: 140
      })

      // INITIALIZATION OF LISTJS COMPONENT
      // =======================================================
      const docsSearch = HSCore.components.HSList.init('#docsSearch')




      // INITIALIZATION OF GO TO
      // =======================================================
      new HSGoTo('.js-go-to')
        })()

        function setCookie(cname, cvalue, exdays) {
            var d = new Date();
            d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
            var expires = "expires=" + d.toUTCString();
            document.cookie = cname + "=" + cvalue + "; " + expires + "; path=/; domain=.sepay.vn";
        }

        let urlParams = new URLSearchParams(document.location.search);

        ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content'].forEach(function(param) {
            let value = urlParams.get(param);

            if (value) {
                setCookie(param, value, 90);
            }
        });

        if (document.referrer != '' && document.cookie.indexOf('referer') === -1) {
            setCookie('referer', document.referrer, 90);
        }
    </script>
</body>

</html>