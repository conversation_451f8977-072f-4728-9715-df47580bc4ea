<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Required Meta Tags Always Come First -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- Title -->
    <title>API Tài khoản ngân hàng | SePay</title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />

    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">
    <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
    <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
  
    <!-- CSS Implementing Plugins -->
    <link rel="stylesheet" href="../assets/css/bootstrap-icons/font/bootstrap-icons.css">

    <!-- CSS Front Template -->
    <link rel="stylesheet" href="../assets/css/theme.min.css">
    <link rel="stylesheet" href="../assets/css/docs.min.css">

    <!--contact-box-css-->
    <link rel="stylesheet" href="../assets/css/contact-box.css">
    <!--/contact-box-css-->

    <style>
        .docs-navbar-sidebar-aside-body {
            padding-top: 3.5rem !important;
        }
    </style>
      <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-J8DLMQTKSQ');
  </script>
</head>

<body class="navbar-sidebar-aside-lg">
    <!-- ========== HEADER ========== -->
    <header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
        <div class="container-fluid">
            <nav class="navbar-nav-wrap">
                <div class="row flex-grow-1">
                    <!-- Default Logo -->
                    <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
                        <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                            <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png"
                                alt="Logo">
                        </a>

                    </div>
                    <!-- End Default Logo -->

                    <div class="col-md px-lg-0">
                        <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
                            <div class="d-none d-md-block">


                                <!-- List Item Template -->
                                <div class="d-none">
                                    <div id="searchTemplate" class="dropdown-item">
                                        <a class="d-block link" href="#">
                                            <span class="category d-block fw-normal text-muted mb-1"></span>
                                            <span class="component text-dark"></span>
                                        </a>
                                    </div>
                                </div>
                                <!-- End List Item Template -->
                            </div>

                            <!-- Navbar -->
                            <ul class="navbar-nav p-0">
                                <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html"
                                        target="_blank">
                                        Support <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN" target="_blank">
                                      Youtube <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                  </li>
                                  <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://www.facebook.com/messages/t/sepay.vn" target="_blank">
                                      Facebook <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                  </li>
                                  <li class="nav-item">
                                    <a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel" target="_blank">
                                      Telegram <i class="bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                  </li>

                            </ul>
                            <!-- End Navbar -->
                        </div>
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Row -->
            </nav>
        </div>
    </header>
    <!-- ========== END HEADER ========== -->

    <!-- ========== MAIN CONTENT ========== -->
    <main id="content" role="main">
        <!-- Navbar -->
        <nav class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
            data-hs-nav-scroller-options='{
            "type": "vertical",
            "target": ".navbar-nav .active",
            "offset": 80
           }'>
            <!-- Navbar Toggle -->
            <button type="button" class="navbar-toggler btn btn-white d-grid w-100" data-bs-toggle="collapse"
                data-bs-target="#navbarVerticalNavMenu" aria-label="Toggle navigation" aria-expanded="false"
                aria-controls="navbarVerticalNavMenu">
                <span class="d-flex justify-content-between align-items-center">
                    <span class="h3 mb-0">Nav menu</span>

                    <span class="navbar-toggler-default">
                        <i class="bi-list"></i>
                    </span>

                    <span class="navbar-toggler-toggled">
                        <i class="bi-x"></i>
                    </span>
                </span>
            </button>
            <!-- End Navbar Toggle -->

            <!-- Navbar Collapse -->
            <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
                <div class="navbar-brand-wrapper border-end" style="height: auto;">
                    <!-- Default Logo -->
                    <div class="d-flex align-items-center mb-3">
                        <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                            <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png"
                                alt="Logo">
                        </a>

                    </div>
                    <!-- End Default Logo -->


                </div>

                <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
                    <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
                        <li class="nav-item">
                            <span class="nav-subtitle">Giới thiệu</span>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="sepay-la-gi.html">SePay là gì?</a>
                        </li>

                        <li class="nav-item my-2 mt-lg-5">
                            <span class="nav-subtitle">Gói dịch vụ</span>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="goi-theo-diem-ban">Gói theo điểm bán</a>
                        </li>

                        <li class="nav-item my-2 my-lg-5"></li>

                        <li class="nav-item">
                            <span class="nav-subtitle">Hướng dẫn chung</span>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="dang-ky-sepay.html">Đăng ký SePay</a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="them-tai-khoan-ngan-hang.html">Thêm tài khoản ngân hàng</a>
                        </li>

                        
                        <li class="nav-item">
                            <a class="nav-link " href="xem-giao-dich.html">Xem giao dịch</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="nguoi-dung-va-phan-quyen.html">Người dùng & Phân quyền</a>
                        </li>
 
                        <li class="nav-item">
                            <a class="nav-link" href="tai-khoan-phu.html">Tài khoản phụ</a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="cau-hinh-tai-khoan-ngan-hang.html">Cấu hình TK ngân hàng</a>
                        </li>

                        <li class="nav-item my-2 my-lg-5"></li>

                        <li class="nav-item">
                            <span class="nav-subtitle">Cấu hình công ty</span>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link " href="goi-dich-vu.html">Gói dịch vụ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="hoa-don-va-thanh-toan.html">Hóa đơn & thanh toán</a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link " href="cau-hinh-chung.html">Cấu hình chung</a>
                        </li>

                        <li class="nav-item my-2 my-lg-5"></li>

                        <li class="nav-item">
                            <span class="nav-subtitle">Chia sẻ biến động số dư</span>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link " href="tich-hop-telegram.html">Tích hợp Telegram <i class="bi bi-telegram ms-2 text-info"></i></a>
                          </li>
              
            <li class="nav-item">
              <a class="nav-link" href="tich-hop-lark-messenger.html">Tích hợp Lark Messenger <img src="assets/img/lark/lark-icon.png" class="img-fluid" style="max-height:25px"></a>
          </li>

                        <li class="nav-item">
                            <a class="nav-link" href="tich-hop-viber.html">Tích hợp Viber <img src="assets/img/viber/viber-icon.png" class="img-fluid rounded ms-2" style="height:25px;width:25px;"></a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link d-flex align-items-center" href="mobile-app.html">Mobile App <i class="bi bi-phone-vibrate ms-2 fs-3 text-info"></i> </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link " href="tich-hop-loa-thanh-toan.html">Tích hợp Loa thanh toán 
                                <svg class="mb-0 ms-1" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#377dff" class="bi bi-speaker" viewBox="0 0 16 16">
                                    <path d="M12 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1zM4 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2z"></path>
                                    <path d="M8 4.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5M8 6a2 2 0 1 0 0-4 2 2 0 0 0 0 4m0 3a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3m-3.5 1.5a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0"></path>
                                  </svg>
                                </a>
                          </li>
                        <li class="nav-item my-2 my-lg-5"></li>

                        <li class="nav-item">
                            <span class="nav-subtitle">Tích hợp web</span>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="tich-hop-shopify.html">Tích hợp Shopify <img class="ms-2" src="assets/img/others/shopify-icon.png" style="width:22px; height: 22px;"></a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="tich-hop-sapo.html">Tích hợp Sapo <img class="ms-2" src="assets/img/others/sapo-icon.png" style="width:18px; height: 18px;"></a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="tich-hop-haravan.html">Tích hợp Haravan <img class="ms-2" src="assets/img/others/haravan-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="tich-hop-gohighlevel.html">Tích hợp GoHighLevel <img class="ms-2" src="assets/img/others/gohighlevel-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="woocommerce.html">Tích hợp WooCommerce <img class="ms-2" src="assets/img/others/woocommerce-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="tich-hop-google-sheets.html">Tích hợp Google Sheets <img class="ms-2" src="assets/img/others/google-sheets-icon.png" style="width:22px; height: 22px;"></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="tich-hop-hostbill.html">Tích hợp HostBill <img class="ms-2"
                                    src="assets/img/others/hostbill-icon.png" style="height: 22px;"></a>
                        </li>

            <li class="nav-item my-2 my-lg-5"></li>

            <li class="nav-item">
                <span class="nav-subtitle">Lập trình & Tích hợp</span>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="tich-hop-webhooks.html">Tích hợp WebHooks</a>
            </li>

            <li class="nav-item ">
                <a class="nav-link" href="lap-trinh-webhooks.html">Lập trình WebHooks</a>
            </li>
            <li class="nav-item ">
              <a class="nav-link" href="gia-lap-giao-dich.html">Giả lập giao dịch</a>
          </li>
          <li class="nav-item ">
            <a class="nav-link" href="tao-qr-code-vietqr-dong.html">Tạo & nhúng QR Code</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="/oauth2">OAuth2</a>
        </li>

        <li class="nav-item my-2 my-lg-5"></li>

        <li class="nav-item">
            <span class="nav-subtitle">SePay API</span>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="gioi-thieu-api.html">Giới thiệu API</a>
        </li>
        <li class="nav-item">
            <a class="nav-link " href="tao-api-token.html">Tạo API Token</a>
        </li>
        <li class="nav-item">
            <a class="nav-link " href="api-giao-dich.html">API Giao dịch</a>
        </li>
        <li class="nav-item">
            <a class="nav-link  active" href="api-tai-khoan-ngan-hang.html">API Tài khoản ngân hàng</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="api-va-theo-don-hang.html">API VA theo Đơn hàng</a>
        </li>
      
      


                    </ul>
                </div>
            </div>
            <!-- End Navbar Collapse -->
        </nav>
        <!-- End Navbar -->

        <!-- Content -->
        <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">

            <div class="docs-page-header">
                <div class="row align-items-center">
                    <div class="col-sm">
                        <h1 class="docs-page-header-title">API Tài khoản ngân hàng</h1>
                        <p class="docs-page-header-text">API tài khoản ngân hàng giúp lấy danh sách các tài khoản ngân hàng đã được thêm vào SePay. Truy vấn thông tin chi tiết từng tài khoản, số dư từng tài khoản.</p>
                    </div>
                </div>
            </div>

            <h3 id="ban-co-the" class="hs-docs-heading">
                Bạn có thể làm gì với API này? <a class="anchorjs-link" href="#ban-co-the" aria-label="Anchor" data-anchorjs-icon="#"></a>
              </h3>

             

              <p>SePay cho phép bạn thực hiện những truy vấn sau với Tài khoản ngân hàng:</p>

              <ul class="lh-lg">
                <li>GET <code>https://my.sepay.vn/userapi/bankaccounts/details/{bank_account_id}</code>
                    <ul>
                        <li><a href="api-tai-khoan-ngan-hang.html#bankaccount-details">Lấy chi tiết một tài khoản ngân hàng</a></li>
                    </ul>
                </li>
                <li>GET <code>https://my.sepay.vn/userapi/bankaccounts/list</code>
                    <ul>
                        <li><a href="api-tai-khoan-ngan-hang.html#bankaccounts-list">Lấy danh sách các tài khoản ngân hàng</a></li>
                    </ul>
                </li>
                <li>GET <code>https://my.sepay.vn/userapi/bankaccounts/count</code>
                    <ul>
                        <li><a href="api-tai-khoan-ngan-hang.html#bankaccounts-count">Đếm số lượng tài khoản ngân hàng</a></li>
                    </ul>
                </li>
              </ul>


              <h3 id="bankaccount-details" class="hs-docs-heading mt-5">
                Lấy chi tiết một tài khoản ngân hàng <a class="anchorjs-link" href="#bankaccount-details" aria-label="Anchor" data-anchorjs-icon="#"></a>
              </h3>
              <p><span class="badge bg-primary">GET</span> <span class="text-primary">https://my.sepay.vn/userapi/bankaccounts/details/{bank_account_id}</span></p>
              <p class="mt-3" >Lấy chi tiết thông tin một tài khoản ngân hàng theo ID</p>
              <p>Ví dụ:</p> 
              <ul>
                <li>GET <code>https://my.sepay.vn/userapi/bankaccounts/details/18</code></li>
            </ul>
<pre>
HTTP/1.1 200 OK
----
{
    "status": 200,
    "error": null,
    "messages": {
        "success": true
    },
    "bankaccount":{
        "id": "18",                                // ID của tài khoản ngân hàng trên SePay
        "account_holder_name": "NGUYEN VAN A",     // Tên chủ tài khoản
        "account_number": "*************",         // Số tài khoản
        "accumulated": "**********.00",            // Số dư (VND)
        "last_transaction": "2023-08-09 07:59:48", // Giao dịch gần nhất lúc
        "label": "",                                // Tên gợi nhớ
        "active": "1",                              // 1 là tài khoản đang hoạt động, 0 là tạm khóa
        "created_at": "2023-02-12 20:05:47",        // Ngày tạo tài khoản trên SePay
        "bank_short_name": "Vietcombank",           // Tên ngắn của ngân hàng (short_name)
        "bank_full_name": "Ngân hàng TMCP Ngoại Thương Việt Nam", // Tên đầy đủ của ngân hàng
        "bank_bin": "970436",                       // BIN code của ngân hàng
        "bank_code": "VCB"                          // Bank code của ngân hàng
    }
}

</pre>

<h3 id="bankaccounts-list" class="hs-docs-heading mt-5">
    Lấy danh sách tài khoản ngân hàng <a class="anchorjs-link" href="#bankaccounts-list" aria-label="Anchor" data-anchorjs-icon="#"></a>
  </h3>
  <p><span class="badge bg-primary">GET</span> <span class="text-primary">https://my.sepay.vn/userapi/bankaccounts/list</span></p>
  <p class="mt-3" >Lấy danh sách tài khoản ngân hàng. Bạn có thể lọc theo các tham số sau:</p>
  <table class="table">
    
    <tbody>
        <tr>
            <td>short_name</td>
            <td>Tên ngân hàng, tương ứng với trường <b>short_name</b> <a href="https://qr.sepay.vn/banks.json">tại đây</a></td>
        </tr>
        <tr>
            <td>last_transaction_date_min</td>
            <td>Lọc tài khoản có giao dịch gần nhất sau thời gian (>=). Định dạng yyyy-mm-dd</td>
        </tr>
        <tr>
            <td>last_transaction_date_max</td>
            <td>Lọc tài khoản có giao dịch gần nhất trước thời gian (<=). Định dạng yyyy-mm-dd</td>
        </tr>
        <tr>
            <td>since_id</td>
            <td>Hiển thị tài khoản ngân hàng từ ID chỉ định (>=)</td>
        </tr>
        <tr>
            <td>limit</td>
            <td>Giới hạn tài khoản ngân hàng trả về. Mặc định là 100.</td>
        </tr>
        <tr>
            <td>accumulated_min</td>
            <td>Lọc tài khoản ngân hàng có số dư lớn hơn hoặc bằng (>=)</td>
        </tr>
        <tr>
            <td>accumulated_max</td>
            <td>Lọc tài khoản ngân hàng có số dư nhỏ hơn hoặc bằng (>=)</td>
        </tr>
         
    </tbody>
  </table>
  <hr>
  <p>Lấy tất cả tài khoản ngân hàng. Mặc định chỉ hiển thị 100 tài khoản ngân hàng được thêm gần nhất.</p> 
  <ul>
    <li>GET <code>https://my.sepay.vn/userapi/bankaccounts/list</code></li>
</ul>
<pre>
HTTP/1.1 200 OK
----
{
    "status": 200,
    "error": null,
    "messages": {
        "success": true
    },
    "bankaccounts":[
    {
        "id": "25",
        "account_holder_name": "NGUYEN THI B",
        "account_number": "************",
        "accumulated": "2525493.00",
        "last_transaction": "2023-02-16 16:16:00",
        "label": "",
        "active": "1",
        "created_at": "2023-02-16 15:57:26",
        "bank_short_name": "VietinBank",
        "bank_full_name": "Ngân hàng TMCP Công Thương Việt Nam",
        "bank_bin": "970415",
        "bank_code": "ICB"
        },
        {
        "id": "24",
        "account_holder_name": "NGUYEN VAN A",
        "account_number": "***************",
        "accumulated": "551564.00",
        "last_transaction": "2023-02-16 15:38:50",
        "label": "",
        "active": "1",
        "created_at": "2023-02-16 15:33:46",
        "bank_short_name": "HDBank",
        "bank_full_name": "Ngân hàng TMCP Phát triển Thành phố Hồ Chí Minh",
        "bank_bin": "970437",
        "bank_code": "HDB"
        },
        {
        "id": "21",
        "account_holder_name": "TRAN VAN VIET",
        "account_number": "*********",
        "accumulated": "********.00",
        "last_transaction": "2023-04-21 15:25:47",
        "label": "",
        "active": "1",
        "created_at": "2023-02-16 11:26:58",
        "bank_short_name": "VPB",
        "bank_full_name": "Ngân hàng TMCP Việt Nam Thịnh Vượng",
        "bank_bin": "970432",
        "bank_code": "VPB"
        },
        {
        "id": "19",
        "account_holder_name": "CONG TY TNHH DEMO",
        "account_number": "*************",
        "accumulated": "**********.00",
        "last_transaction": "2023-08-09 11:59:47",
        "label": "Cty Demo",
        "active": "1",
        "created_at": "2023-02-12 21:09:49",
        "bank_short_name": "Vietcombank",
        "bank_full_name": "Ngân hàng TMCP Ngoại Thương Việt Nam",
        "bank_bin": "970436",
        "bank_code": "VCB"
        },
        {
        "id": "18",
        "account_holder_name": "NGUYEN VAN A",
        "account_number": "*************",
        "accumulated": "**********.00",
        "last_transaction": "2023-08-09 07:59:48",
        "label": "",
        "active": "1",
        "created_at": "2023-02-12 20:05:47",
        "bank_short_name": "Vietcombank",
        "bank_full_name": "Ngân hàng TMCP Ngoại Thương Việt Nam",
        "bank_bin": "970436",
        "bank_code": "VCB"
        }
    ]
}

</pre>

  <p class="mt-5">Lấy tài khoản ngân hàng có giao dịch sau 08h00 ngày 30/04/2023 và trước 12h00 ngày 02/05/2023.</p> 
  <ul>
    <li>GET <code>https://my.sepay.vn/userapi/bankaccounts/list?last_transaction_date_min=2023-04-30 08:00:00&last_transaction_date_max=2023-05-02 12:00:00</code></li>
</ul>

  <p class="mt-5">Lấy tài khoản ngân hàng từ ID 20 trở về sau.</p> 
  <ul>
    <li>GET <code>https://my.sepay.vn/userapi/bankaccounts/list?since_id=20</code></li>
</ul>
<p class="mt-5">Lấy 20 tài khoản của ngân hàng Vietcombank.</p> 
  <ul>
    <li>GET <code>https://my.sepay.vn/userapi/bankaccounts/list?short_name=Vietcombank&limit=20</code></li>
</ul>
<p class="mt-5">Lọc tài khoản ngân hàng có số dư >= 1 VND</p> 
  <ul>
    <li>GET <code>https://my.sepay.vn/userapi/bankaccounts/list?accumulated_min=1</code></li>
</ul>
 


<h3 id="bankaccounts-count" class="hs-docs-heading mt-5">
    Đếm số lượng tài khoản ngân hàng <a class="anchorjs-link" href="#bankaccounts-count" aria-label="Anchor" data-anchorjs-icon="#"></a>
  </h3>
  <p><span class="badge bg-primary">GET</span> <span class="text-primary">https://my.sepay.vn/userapi/bankaccounts/count</span></p>
  <p class="mt-3" >Đếm số lượng tài khoản ngân hàng. Bạn có thể lọc theo các tham số sau:</p>
  <table class="table">
    
    <tbody>
        <tr>
            <td>short_name</td>
            <td>Tên ngân hàng, tương ứng với trường <b>short_name</b> <a href="https://qr.sepay.vn/banks.json">tại đây</a></td>
        </tr>
        <tr>
            <td>last_transaction_date_min</td>
            <td>Lọc tài khoản có giao dịch gần nhất sau thời gian (>=). Định dạng yyyy-mm-dd</td>
        </tr>
        <tr>
            <td>last_transaction_date_max</td>
            <td>Lọc tài khoản có giao dịch gần nhất trước thời gian (<=). Định dạng yyyy-mm-dd</td>
        </tr>
        <tr>
            <td>since_id</td>
            <td>Hiển thị tài khoản ngân hàng từ ID chỉ định (>=)</td>
        </tr>
        
        <tr>
            <td>accumulated_min</td>
            <td>Lọc tài khoản ngân hàng có số dư lớn hơn hoặc bằng (>=)</td>
        </tr>
        <tr>
            <td>accumulated_max</td>
            <td>Lọc tài khoản ngân hàng có số dư nhỏ hơn hoặc bằng (>=)</td>
        </tr>
         
    </tbody>
  </table>
  <hr>
  <p>Đếm tổng số lượng tài khoản ngân hàng đang có.</p> 
  <ul>
    <li>GET <code>https://my.sepay.vn/userapi/bankaccounts/count</code></li>
</ul>
<pre>
HTTP/1.1 200 OK
----
{
    "status": 200,
    "error": null,
    "messages": {
        "success": true
    },
    "count_bankaccounts": 9
}

</pre>
<p>Đếm tổng số lượng tài khoản ngân hàng của tài khoản ngân hàng có số dư >=1.</p> 
  <ul>
    <li>GET <code>https://my.sepay.vn/userapi/bankaccounts/count?accumulated_min=1</code></li>
</ul>
        </div>
        <!-- End Content -->
    </main>
    <!-- ========== END MAIN CONTENT ========== -->

    <!-- ========== SECONDARY CONTENTS ========== -->
    <!--contact-box-html-docs-->
  <div id="contact-box-overlay"></div>
  <div id="contact-box">
      <div class="popup">
          <a href="https://m.me/***************" target="_blank" class="item">
              <div class="logo">
                  <img src="../assets/img/others/fb-messenger.png" width="50%" />
              </div>
              <div class="meta">
                  <p class="title">Facebook Messenger</p>
                  <small class="description">Hỗ trợ live chat 24/7</small>
              </div>
          </a>
          <div class="divide"></div>
          <a href="tel:***********" class="item">
              <div class="icon" style="background-color: #22c55e; color: #fff;">
                  <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;"><path d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" fill="transparent"></path></svg>
              </div>
              <div class="meta">
                  <p class="title">Hotline</p>
                  <small class="description">Điện thoại hỗ trợ</small>
              </div>
          </a>
          <div class="divide"></div>
          <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
              <div class="logo">
                  <img src="../assets/img/others/youtube-social.png" />
              </div>
              <div class="meta">
                  <p class="title">Youtube</p>
                  <small class="description">Theo dõi video mới nhất của SePay</small>
              </div>
          </a>
          <div class="divide"></div>
          <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
              <div class="logo">
                  <img src="../assets/img/others/telegram-social.png" />
              </div>
              <div class="meta">
                  <p class="title">Telegram</p>
                  <small class="description">Nhận thông tin mới nhất từ SePay</small>
              </div>
          </a>
      </div>
      <div class="container">
          <div class="dot-ping">
              <div class="ping"></div>
              <div class="dot"></div>
          </div>
          <div class="contact-icon">
              <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;"><path fill-rule="evenodd" clip-rule="evenodd" d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z" fill="currentColor"></path></svg>
          </div>
          <span style="font-weight: bold;">Liên hệ chúng tôi</span>
      </div>
  </div>
  <!--/contact-box-html-docs-->

    <!-- Go To -->
    <a class="js-go-to go-to position-fixed" href="javascript:;" style="visibility: hidden;" data-hs-go-to-options='{
       "offsetTop": 700,
       "position": {
         "init": {
           "right": "2rem"
         },
         "show": {
           "bottom": "2rem"
         },
         "hide": {
           "bottom": "-2rem"
         }
       }
     }'>
        <i class="bi-chevron-up"></i>
    </a>
    <!-- ========== END SECONDARY CONTENTS ========== -->

    <!-- JS Global Compulsory  -->
    <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

    <!-- JS Implementing Plugins -->
    <script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
    <script src="../assets/vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js"></script>
    <script src="../assets/vendor/list.js/dist/list.min.js"></script>
    <script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>
    

    <!-- JS Front -->
    <script src="../assets/js/theme.min.js"></script>

    <!-- contact-box-js -->
    <script src="../assets/js/contact-box.js"></script>
    <!-- /contact-box-js -->

    <!-- JS Plugins Init. -->
    <script>
        (function () {
            // INITIALIZATION OF HEADER
            // =======================================================
            new HSHeader('#header').init()

            // INITIALIZATION OF NAV SCROLLER
            // =======================================================
            new HsNavScroller('.js-nav-scroller', {
                delay: 400,
                offset: 140
            })
            // INITIALIZATION OF LISTJS COMPONENT
            // =======================================================
            const docsSearch = HSCore.components.HSList.init('#docsSearch')




            // INITIALIZATION OF GO TO
            // =======================================================
            new HSGoTo('.js-go-to')
        })()

        function setCookie(cname, cvalue, exdays) {
            var d = new Date();
            d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
            var expires = "expires=" + d.toUTCString();
            document.cookie = cname + "=" + cvalue + "; " + expires + "; path=/; domain=.sepay.vn";
        }

        let urlParams = new URLSearchParams(document.location.search);

        ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content'].forEach(function(param) {
            let value = urlParams.get(param);

            if (value) {
                setCookie(param, value, 90);
            }
        });

        if (document.referrer != '' && document.cookie.indexOf('referer') === -1) {
            setCookie('referer', document.referrer, 90);
        }
    </script>
</body>

</html>