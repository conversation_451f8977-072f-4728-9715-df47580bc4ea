<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <title>Hướng dẫn tích hợp Viber | SePay</title>
        <link rel="shortcut icon" href="https://sepay.vn/assets/img/logo/favicon.png" type="image/x-icon" />
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet" />
        <link rel="stylesheet" href="../assets/vendor/bootstrap-icons/font/bootstrap-icons.css" />

        <meta property="og:locale" content="vi_VN" />
        <link rel="canonical" href="https://docs.sepay.vn/tich-hop-viber.html" />
        <meta name="description" content="SePay bắn giao dịch ngân hàng lên kênh Viber của bạn, nhân viên của bạn dễ dàng nắm bắt thông tin nhanh chóng." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://docs.sepay.vn/tich-hop-viber.html" />
        <meta property="og:title" content="Hướng dẫn tích hợp Viber" />
        <meta property="og:description" content="SePay bắn giao dịch ngân hàng lên kênh Viber của bạn, nhân viên của bạn dễ dàng nắm bắt thông tin nhanh chóng." />

        <meta property="og:site_name" content="Hướng dẫn tích hợp Viber" />
        <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
        <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />

        <link rel="stylesheet" href="../assets/css/theme.min.css" />
        <link rel="stylesheet" href="../assets/css/docs.min.css" />
        <link rel="stylesheet" href="../assets/css/contact-box.css" />

        <style>
            .docs-navbar-sidebar-aside-body {
                padding-top: 3.5rem !important;
            }
        </style>
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                dataLayer.push(arguments);
            }
            gtag("js", new Date());

            gtag("config", "G-J8DLMQTKSQ");
        </script>
    </head>

    <body class="navbar-sidebar-aside-lg">
        <header id="header" class="navbar navbar-expand navbar-fixed navbar-end navbar-light navbar-sticky-lg-top bg-white">
            <div class="container-fluid">
                <nav class="navbar-nav-wrap">
                    <div class="row flex-grow-1">
                        <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
                            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                                <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo" />
                            </a>
                        </div>

                        <div class="col-md px-lg-0">
                            <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
                                <div class="d-none d-md-block">
                                    <div class="d-none">
                                        <div id="searchTemplate" class="dropdown-item">
                                            <a class="d-block link" href="#">
                                                <span class="category d-block fw-normal text-muted mb-1"></span>
                                                <span class="component text-dark"></span>
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <ul class="navbar-nav p-0">
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://sepay.vn/lien-he.html" target="_blank"> Support <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://www.youtube.com/@SePayVN" target="_blank"> Youtube <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://www.facebook.com/messages/t/sepay.vn" target="_blank"> Facebook <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="btn btn-ghost-secondary btn-sm" href="https://t.me/s/sepaychannel" target="_blank"> Telegram <i class="bi-box-arrow-up-right ms-1"></i> </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </nav>
            </div>
        </header>

        <main id="content" role="main">
            <nav
                class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end"
                data-hs-nav-scroller-options='{
                    "type": "vertical",
                    "target": ".navbar-nav .active",
                    "offset": 80
                }'
            >
                <button
                    type="button"
                    class="navbar-toggler btn btn-white d-grid w-100"
                    data-bs-toggle="collapse"
                    data-bs-target="#navbarVerticalNavMenu"
                    aria-label="Toggle navigation"
                    aria-expanded="false"
                    aria-controls="navbarVerticalNavMenu"
                >
                    <span class="d-flex justify-content-between align-items-center">
                        <span class="h3 mb-0">Nav menu</span>

                        <span class="navbar-toggler-default">
                            <i class="bi-list"></i>
                        </span>

                        <span class="navbar-toggler-toggled">
                            <i class="bi-x"></i>
                        </span>
                    </span>
                </button>

                <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
                    <div class="navbar-brand-wrapper border-end" style="height: auto;">
                        <div class="d-flex align-items-center mb-3">
                            <a class="navbar-brand" href="https://docs.sepay.vn" aria-label="Space">
                                <img class="navbar-brand-logo" src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" alt="Logo" />
                            </a>
                        </div>
                    </div>

                    <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
                        <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
                            <li class="nav-item">
                                <span class="nav-subtitle">Giới thiệu</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="sepay-la-gi.html">SePay là gì?</a>
                            </li>
                            <li class="nav-item my-2 mt-lg-5">
                                <span class="nav-subtitle">Gói dịch vụ</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="goi-theo-diem-ban">Gói theo điểm bán</a>
                            </li>
                            <li class="nav-item my-2 my-lg-5"></li>

                            <li class="nav-item">
                                <span class="nav-subtitle">Hướng dẫn chung</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="dang-ky-sepay.html">Đăng ký SePay</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="them-tai-khoan-ngan-hang.html">Thêm tài khoản ngân hàng</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="xem-giao-dich.html">Xem giao dịch</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="nguoi-dung-va-phan-quyen.html">Người dùng & Phân quyền</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="tai-khoan-phu.html">Tài khoản phụ</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="cau-hinh-tai-khoan-ngan-hang.html">Cấu hình TK ngân hàng</a>
                            </li>

                            <li class="nav-item my-2 my-lg-5"></li>

                            <li class="nav-item">
                                <span class="nav-subtitle">Cấu hình công ty</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="goi-dich-vu.html">Gói dịch vụ</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="hoa-don-va-thanh-toan.html">Hóa đơn & thanh toán</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="cau-hinh-chung.html">Cấu hình chung</a>
                            </li>

                            <li class="nav-item my-2 my-lg-5"></li>

                            <li class="nav-item">
                                <span class="nav-subtitle">Chia sẻ biến động số dư</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="tich-hop-telegram.html">Tích hợp Telegram <i class="bi bi-telegram ms-2 text-info"></i></a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="tich-hop-lark-messenger.html">Tích hợp Lark Messenger <img src="assets/img/lark/lark-icon.png" class="img-fluid" style="max-height: 25px;" /></a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link active" href="tich-hop-viber.html">Tích hợp Viber <img src="assets/img/viber/viber-icon.png" class="img-fluid rounded ms-2" style="height: 25px; width: 25px;" /></a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link d-flex align-items-center" href="mobile-app.html">Mobile App <i class="bi bi-phone-vibrate ms-2 fs-3 text-info"></i> </a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="tich-hop-loa-thanh-toan.html">
                                    Tích hợp Loa thanh toán
                                    <svg class="mb-0 ms-1" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#377dff" class="bi bi-speaker" viewBox="0 0 16 16">
                                        <path d="M12 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1zM4 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2z"></path>
                                        <path d="M8 4.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5M8 6a2 2 0 1 0 0-4 2 2 0 0 0 0 4m0 3a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3m-3.5 1.5a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0"></path>
                                    </svg>
                                </a>
                            </li>
                            <li class="nav-item my-2 my-lg-5"></li>

                            <li class="nav-item">
                                <span class="nav-subtitle">Tích hợp web</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="tich-hop-shopify.html">Tích hợp Shopify <img class="ms-2" src="assets/img/others/shopify-icon.png" style="width: 22px; height: 22px;" /></a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="tich-hop-sapo.html">Tích hợp Sapo <img class="ms-2" src="assets/img/others/sapo-icon.png" style="width: 18px; height: 18px;" /></a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="tich-hop-haravan.html">Tích hợp Haravan <img class="ms-2" src="assets/img/others/haravan-icon.png" style="width: 22px; height: 22px;" /></a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="tich-hop-gohighlevel.html">Tích hợp GoHighLevel <img class="ms-2" src="assets/img/others/gohighlevel-icon.png" style="width: 22px; height: 22px;" /></a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="woocommerce.html">Tích hợp WooCommerce <img class="ms-2" src="assets/img/others/woocommerce-icon.png" style="width: 22px; height: 22px;" /></a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="tich-hop-google-sheets.html">Tích hợp Google Sheets <img class="ms-2" src="assets/img/others/google-sheets-icon.png" style="width: 22px; height: 22px;" /></a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="tich-hop-hostbill.html">Tích hợp HostBill <img class="ms-2" src="assets/img/others/hostbill-icon.png" style="height: 22px;" /></a>
                            </li>
                            <li class="nav-item my-2 my-lg-5"></li>

                            <li class="nav-item">
                                <span class="nav-subtitle">Lập trình & Tích hợp</span>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="tich-hop-webhooks.html">Tích hợp WebHooks</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="lap-trinh-webhooks.html">Lập trình WebHooks</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="gia-lap-giao-dich.html">Giả lập giao dịch</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="tao-qr-code-vietqr-dong.html">Tạo & nhúng QR Code</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/oauth2">OAuth2</a>
                            </li>
                            <li class="nav-item my-2 my-lg-5"></li>

                            <li class="nav-item">
                                <span class="nav-subtitle">SePay API</span>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="gioi-thieu-api.html">Giới thiệu API</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="tao-api-token.html">Tạo API Token</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="api-giao-dich.html">API Giao dịch</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="api-tai-khoan-ngan-hang.html">API Tài khoản ngân hàng</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="api-va-theo-don-hang.html">API VA theo Đơn hàng</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>

            <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10" style="max-width: 1200px;">
                <div class="docs-page-header">
                    <div class="row align-items-center">
                        <div class="col-sm">
                            <h1 class="docs-page-header-title">Hướng dẫn tích hợp Viber <img src="assets/img/viber/viber-icon.png" class="img-fluid rounded ms-2" style="max-height:35px"></h1>
                            <p class="docs-page-header-text">Tích hợp <a href="https://www.viber.com/" target="_blank">Viber</a> giúp SePay báo có giao dịch ngân hàng lên kênh Viber của bạn, nhân viên dễ dàng nắm bắt thông tin nhanh chóng.</p>
                        </div>
                    </div>
                </div>

                <img src="assets/img/viber/viber-integration-overview.png" class="img-fluid mb-5" alt="Viber Integration Overview" />

                <h2 id="bat-dau-tich-hop" class="hs-docs-heading">
                    Bắt đầu tích hợp
                    <a class="anchorjs-link" href="#bat-dau-tich-hop" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h2>
                <p class="mt-3">Truy cập vào menu <a href="https://my.sepay.vn/notificationviber">Tích hợp Viber</a></p>
                <p>Chọn vào button <a class="btn btn-primary btn-xs">Bắt đầu tích hợp</a> ở chính giữa màn hình</p>
                <img src="assets/img/viber/start-integration.png" class="img-fluid mb-5" alt="Bắt đầu tích hợp" />

                <h3 id="lay-auth-token" class="hs-docs-heading">
                    Lấy Auth Token từ Viber Channel
                    <a class="anchorjs-link" href="#lay-auth-token" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h3>

                <div class="alert alert-warning" role="alert">
                    <strong>Lưu ý quan trọng về bảo mật:</strong>
                    <ul class="mb-0 mt-2">
                        <li>Bạn phải là <strong>Super Admin</strong> của Viber Channel</li>
                        <li>Viber Channel mặc định là riêng tư, tuy nhiên để đảm bảo an toàn tối đa, <strong>hãy tắt tính năng "Allow all subscribers to share"</strong> trong cài đặt Channel link theo <a href="#cau-hinh-bao-mat">hướng dẫn bên dưới</a></li>
                        <li>Điều này giúp tránh việc thành viên chia sẻ link Channel một cách tùy tiện, đảm bảo chỉ Admin mới có thể kiểm soát việc mời thành viên mới</li>
                    </ul>
                </div>

                <p>Mở ứng dụng Viber trên điện thoại hoặc máy tính, tìm và mở kênh mà bạn là Super Admin hoặc tạo kênh mới nếu chưa có.</p>
                <p>Nhấn vào tên kênh để vào màn hình thông tin kênh.</p>
                <img src="assets/img/viber/auth-token-1.png" class="img-fluid rounded mb-3" alt="Vào thông tin kênh" />
                <p>Trong màn hình thông tin kênh, tìm và nhấn vào mục <strong>Developer tools</strong>.</p>
                <img src="assets/img/viber/auth-token-2.png" class="img-fluid rounded mb-3" alt="Developer tools" />
                <p>Trong màn hình Developer tools, bạn sẽ thấy mã <strong>Authentication token</strong> của kênh của bạn.</p>
                <p>Sau đó nhấn <strong>Copy</strong> để lấy mã này.</p>
                <img src="assets/img/viber/auth-token-3.png" class="img-fluid rounded mb-3" alt="Copy Auth Token" />

                <h3 id="thu-ket-noi-voi-sepay" class="hs-docs-heading">
                    Thử kết nối với SePay
                    <a class="anchorjs-link" href="#thu-ket-noi-voi-sepay" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h3>

                <p>Nhập mã <strong>Authentication token</strong> vào ô <strong>Viber Auth Token</strong> và nhấn <strong>Kiểm tra kết nối</strong> để kiểm tra kết nối.</p>
                <img src="assets/img/viber/step1-connect.png" class="img-fluid mb-5" alt="Kết nối SePay với Viber" />
                <p>Nếu kết nối thành công, bạn sẽ thấy thông báo <strong>Kết nối thành công</strong>. Sau đó bạn có thể cấu hình chọn <strong>Người gửi tin nhắn</strong> và nhấn <strong>Tiếp tục</strong> để qua bước tiếp theo.</p>
                <img src="assets/img/viber/step1-connect-success.png" class="img-fluid mb-5" alt="Kết nối thành công" />

                <h3 id="cau-hinh-tich-hop" class="hs-docs-heading">
                    Cấu hình tích hợp
                    <a class="anchorjs-link" href="#cau-hinh-tich-hop" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h3>
                <p>Tại bước này, bạn sẽ cấu hình các điều kiện để SePay biết khi nào cần gửi thông báo lên Viber.</p>
                <img src="assets/img/viber/step2-overview.png" class="img-fluid mb-4" alt="Cấu hình tài khoản và điều kiện" />
                <p>Theo mặc định, tất cả các giao dịch từ tài khoản ngân hàng đã liên kết vào SePay sẽ được gửi thông báo lên kênh Viber. Bạn có thể cấu hình tài khoản ngân hàng và tài khoản ảo (VA) cần chia sẻ biến động số dư.</p>
                <p>Ngoài ra, bạn có thể cấu hình điều kiện nhận thông báo theo các sự kiện sau:</p>

                <ul class="lh-lg list-unstyled">
                    <li><span class="badge bg-primary rounded-pill">1</span> <b>Loại giao dịch</b>: Tùy chọn thông báo cho <code>Tiền vào</code>, <code>Tiền ra</code> hoặc <code>Cả hai</code></li>
                    <li><span class="badge bg-primary rounded-pill">2</span> <b>Trạng thái webhook</b>: Kiểm soát thông báo dựa trên kết quả gửi và xác thực webhook</li>
                    <li><span class="badge bg-primary rounded-pill">3</span> <b>Ngưỡng giao dịch</b>: Đặt giới hạn số tiền tối thiểu và tối đa cho thông báo</li>
                    <li><span class="badge bg-primary rounded-pill">4</span> <b>Bộ lọc nội dung</b>: Tùy chỉnh thông báo dựa trên từ khóa trong nội dung giao dịch</li>
                </ul>

                <img src="assets/img/viber/step2-filters.png" class="img-fluid mb-5" alt="Cấu hình bộ lọc" />

                <p>Bạn có thể để mặc định hoặc cấu hình theo ý mình và nhấn tiếp tục để qua bước tiếp theo.</p>

                <h3 id="cau-hinh-template" class="hs-docs-heading">
                    Tùy chỉnh template tin nhắn
                    <a class="anchorjs-link" href="#cau-hinh-template" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h3>

                <p>SePay cung cấp 3 template có sẵn và cho phép bạn tùy chỉnh template riêng theo ý muốn.</p>

                <img src="assets/img/viber/step3-template.png" class="img-fluid mb-5" alt="Preview template" />

                <p>Bạn có thể tùy chỉnh template tin nhắn theo ý mình và nhấn <strong>Tiếp tục</strong> để hoàn tất quá trình tích hợp.</p>

                <div class="alert alert-soft-success mt-2" role="alert">
                    <div class="d-flex align-items-baseline">
                        <div class="flex-shrink-0">
                            <i class="bi-check-circle me-1"></i>
                        </div>
                        <div class="flex-grow-1 ms-2">Qua được bước này, bạn đã hoàn tất tích hợp Viber.</div>
                    </div>
                </div>

                <img src="assets/img/viber/step4-complete.png" class="img-fluid mb-4" alt="Hoàn tất tích hợp" />

                <h3 id="quan-ly" class="hs-docs-heading">
                    Quản lý tích hợp Viber <a class="anchorjs-link" href="#quan-ly" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h3>
                <p>Sau khi tạo thành công, bạn có thể:</p>
                <ul>
                    <li><strong>Xem danh sách:</strong> Tất cả tích hợp Viber đã tạo</li>
                    <li><strong>Chỉnh sửa:</strong> Cập nhật cấu hình, template, điều kiện</li>
                    <li><strong>Bật/Tắt:</strong> Kích hoạt hoặc tạm dừng tích hợp</li>
                    <li><strong>Xóa:</strong> Xóa tích hợp không cần thiết</li>
                </ul>

                <img src="assets/img/viber/management-overview.png" class="img-fluid mb-4" alt="Quản lý tích hợp" />

                <h3 id="kiem-tra" class="hs-docs-heading">
                    Kiểm tra hoạt động <a class="anchorjs-link" href="#kiem-tra" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h3>
                <p>Bạn có thể kiểm tra hoạt động của tích hợp bằng cách chuyển khoản tới tài khoản ngân hàng bạn đã cấu hình. Nếu có tin nhắn gửi tới nhóm Viber Channel có nghĩa là bạn đã tích hợp thành công.</p>
                <p>Ngoài ra, bạn có thể xem tin nhắn Viber đã gửi theo từng giao dịch tại menu <a href="https://my.sepay.vn/transactions"><i class="bi bi-arrow-left-right"></i> Giao dịch</a> -> cột Tự động -> chọn vào biểu tượng Chat.</p>

                <h3 id="cau-hinh-bao-mat" class="hs-docs-heading">
                    Cấu hình bảo mật Channel (Khuyến nghị)
                    <a class="anchorjs-link" href="#cau-hinh-bao-mat" aria-label="Anchor" data-anchorjs-icon="#"></a>
                </h3>

                <p>Để đảm bảo an toàn tối đa cho Channel của bạn, hãy thực hiện các bước sau để tắt tính năng chia sẻ link cho thành viên:</p>

                <p><strong>Bước 1:</strong> Trong màn hình thông tin Channel, nhấn vào <strong>Channel link</strong>.</p>
                <img src="assets/img/viber/security-settings-1.png" class="img-fluid rounded mb-3" alt="Vào Channel link" />

                <p><strong>Bước 2:</strong> Tắt tùy chọn <strong>"Allow all subscribers to share"</strong>.</p>
                <img src="assets/img/viber/security-settings-2.png" class="img-fluid rounded mb-3" alt="Tắt Allow all subscribers to share" />

                <div class="alert alert-success" role="alert">
                    <strong>Hoàn tất!</strong> Bây giờ chỉ có Admin và Super Admin mới có thể chia sẻ link Channel, đảm bảo kiểm soát tốt hơn về quyền truy cập và bảo mật.
                </div>

                <div class="my-2">
                    <p>
                        <strong>Đọc tiếp:</strong> 
                        <a href="mobile-app.html">Tích hợp Mobile App <i class="bi bi-chevron-right"></i></a>
                    </p>
                </div>

            </div>
        </main>

        <div id="contact-box-overlay"></div>
        <div id="contact-box">
            <div class="popup">
                <a href="https://m.me/117903214582465" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/fb-messenger.png" width="50%" />
                    </div>
                    <div class="meta">
                        <p class="title">Facebook Messenger</p>
                        <small class="description">Hỗ trợ live chat 24/7</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="tel:02873059589" class="item">
                    <div class="icon" style="background-color: #22c55e; color: #fff;">
                        <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;"><path d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" fill="transparent"></path></svg>
                    </div>
                    <div class="meta">
                        <p class="title">Hotline</p>
                        <small class="description">Điện thoại hỗ trợ</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/youtube-social.png" />
                    </div>
                    <div class="meta">
                        <p class="title">Youtube</p>
                        <small class="description">Theo dõi video mới nhất của SePay</small>
                    </div>
                </a>
                <div class="divide"></div>
                <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
                    <div class="logo">
                        <img src="../assets/img/others/telegram-social.png" />
                    </div>
                    <div class="meta">
                        <p class="title">Telegram</p>
                        <small class="description">Nhận thông tin mới nhất từ SePay</small>
                    </div>
                </a>
            </div>
            <div class="container">
                <div class="dot-ping">
                    <div class="ping"></div>
                    <div class="dot"></div>
                </div>
                <div class="contact-icon">
                    <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;"><path fill-rule="evenodd" clip-rule="evenodd" d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z" fill="currentColor"></path></svg>
                </div>
                <span style="font-weight: bold;">Liên hệ chúng tôi</span>
            </div>
        </div>

        <a
            class="js-go-to go-to position-fixed"
            href="javascript:;"
            style="visibility: hidden;"
            data-hs-go-to-options='{
                "offsetTop": 700,
                "position": {
                    "init": {
                    "right": "2rem"
                    },
                    "show": {
                    "bottom": "2rem"
                    },
                    "hide": {
                    "bottom": "-2rem"
                    }
                }
                }'
        >
            <i class="bi-chevron-up"></i>
        </a>

        <script src="../assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
        <script src="../assets/vendor/hs-header/dist/hs-header.min.js"></script>
        <script src="../assets/vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js"></script>
        <script src="../assets/vendor/list.js/dist/list.min.js"></script>
        <script src="../assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>
        <script src="../assets/js/theme.min.js"></script>
        <script src="../assets/js/contact-box.js"></script>

        <script>
            (function () {
                new HSHeader("#header").init();

                new HsNavScroller(".js-nav-scroller", {
                    delay: 400,
                    offset: 140,
                });

                const docsSearch = HSCore.components.HSList.init("#docsSearch");

                new HSGoTo(".js-go-to");
            })();

            function setCookie(cname, cvalue, exdays) {
                var d = new Date();
                d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000);
                var expires = "expires=" + d.toUTCString();
                document.cookie = cname + "=" + cvalue + "; " + expires + "; path=/; domain=.sepay.vn";
            }

            let urlParams = new URLSearchParams(document.location.search);

            ["utm_source", "utm_medium", "utm_campaign", "utm_term", "utm_content"].forEach(function (param) {
                let value = urlParams.get(param);

                if (value) {
                    setCookie(param, value, 90);
                }
            });

            if (document.referrer != "" && document.cookie.indexOf("referer") === -1) {
                setCookie("referer", document.referrer, 90);
            }
        </script>
    </body>
</html> 