<svg width="200" height="144" viewBox="0 0 200 144" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0)">
<path d="M196 0H4C1.79086 0 0 1.79086 0 4V140C0 142.209 1.79086 144 4 144H196C198.209 144 200 142.209 200 140V4C200 1.79086 198.209 0 196 0Z" fill="#F3F4F6"/>
<g filter="url(#filter0_d)">
<path d="M27 24C27 21.7909 28.7909 20 31 20H169C171.209 20 173 21.7909 173 24V144H27V24Z" fill="white"/>
<path d="M31 20.5H169C170.933 20.5 172.5 22.067 172.5 24V143.5H27.5V24C27.5 22.067 29.067 20.5 31 20.5Z" stroke="#E7EAF3"/>
</g>
<rect x="66" y="43" width="18" height="3" rx="1.5" fill="#D9DDEA"/>
<rect x="37" y="57" width="15" height="2" rx="1" fill="#D9DDEA"/>
<rect x="37" y="79" width="15" height="2" rx="1" fill="#D9DDEA"/>
<rect x="103" y="57" width="15" height="2" rx="1" fill="#D9DDEA"/>
<g filter="url(#filter1_d)">
<rect x="80" y="120" width="41" height="9" rx="2" fill="#377dff"/>
</g>
<rect x="37.5" y="62.5" width="59" height="10" rx="1.5" stroke="#D9DDEA"/>
<rect x="37.5" y="84.5" width="125" height="28" rx="1.5" stroke="#D9DDEA"/>
<rect x="103.5" y="62.5" width="59" height="10" rx="1.5" stroke="#D9DDEA"/>
<rect x="92" y="124" width="17" height="1" rx="0.5" fill="white"/>
<rect x="88" y="43" width="21" height="3" rx="1.5" fill="#D9DDEA"/>
<rect x="113" y="43" width="21" height="3" rx="1.5" fill="#D9DDEA"/>
<path d="M0 104H200V140C200 142.209 198.209 144 196 144H4C1.79086 144 0 142.209 0 140V104Z" fill="url(#paint0_linear)"/>
<path d="M63.5582 32.6822H65.1317V37H66.0465V32.6822H67.62V31.9091H63.5582V32.6822ZM69.4999 37.0746C70.3898 37.0746 71.0013 36.6396 71.1604 35.9759L70.3202 35.8814C70.1984 36.2045 69.9001 36.3736 69.5123 36.3736C68.9306 36.3736 68.5453 35.9908 68.5379 35.337H71.1977V35.0611C71.1977 33.7212 70.3923 33.1321 69.4526 33.1321C68.3589 33.1321 67.6455 33.935 67.6455 35.1133C67.6455 36.3114 68.3489 37.0746 69.4999 37.0746ZM68.5403 34.7305C68.5677 34.2433 68.9281 33.8331 69.4651 33.8331C69.9821 33.8331 70.3301 34.2109 70.3351 34.7305H68.5403ZM72.8588 31.9091H71.9589V37H72.8588V31.9091ZM74.684 31.9091H73.7841V37H74.684V31.9091ZM79.7793 35.3942C79.7793 35.9759 79.3641 36.2642 78.9664 36.2642C78.5339 36.2642 78.2455 35.9585 78.2455 35.4737V33.1818H77.3457V35.6129C77.3457 36.5302 77.8677 37.0497 78.6184 37.0497C79.1901 37.0497 79.5928 36.7489 79.7668 36.3214H79.8066V37H80.6791V33.1818H79.7793V35.3942ZM84.6222 34.1911C84.4979 33.5447 83.9809 33.1321 83.086 33.1321C82.1662 33.1321 81.5398 33.5845 81.5423 34.2905C81.5398 34.8473 81.8829 35.2152 82.6162 35.3668L83.2674 35.5036C83.6179 35.5806 83.782 35.7223 83.782 35.9386C83.782 36.1996 83.4986 36.396 83.0711 36.396C82.6584 36.396 82.39 36.217 82.3129 35.8739L81.4354 35.9585C81.5473 36.6594 82.1364 37.0746 83.0736 37.0746C84.0281 37.0746 84.7017 36.5799 84.7042 35.8565C84.7017 35.3121 84.3513 34.979 83.6304 34.8224L82.9791 34.6832C82.5913 34.5962 82.4372 34.462 82.4397 34.2408C82.4372 33.9822 82.7231 33.8033 83.0984 33.8033C83.5135 33.8033 83.7323 34.0295 83.8019 34.2805L84.6222 34.1911ZM88.2813 37.0771C88.8804 37.0771 89.2383 36.7962 89.4024 36.4755H89.4322V37H90.2973V34.4446C90.2973 33.4354 89.4745 33.1321 88.7461 33.1321C87.9432 33.1321 87.3267 33.4901 87.1279 34.1861L87.9681 34.3054C88.0576 34.0444 88.3111 33.8207 88.7511 33.8207C89.1687 33.8207 89.3974 34.0344 89.3974 34.4098V34.4247C89.3974 34.6832 89.1265 34.6957 88.4528 34.7678C87.712 34.8473 87.0036 35.0685 87.0036 35.9286C87.0036 36.6793 87.553 37.0771 88.2813 37.0771ZM88.515 36.4158C88.1396 36.4158 87.8711 36.2443 87.8711 35.9137C87.8711 35.5682 88.1719 35.424 88.5746 35.3668C88.8108 35.3345 89.2831 35.2749 89.3999 35.1804V35.6303C89.3999 36.0554 89.0569 36.4158 88.515 36.4158ZM91.2419 37H92.1268V36.3984H92.179C92.3207 36.6768 92.6165 37.0671 93.2728 37.0671C94.1726 37.0671 94.8463 36.3537 94.8463 35.0959C94.8463 33.8232 94.1527 33.1321 93.2703 33.1321C92.5966 33.1321 92.3157 33.5373 92.179 33.8132H92.1417V31.9091H91.2419V37ZM92.1243 35.0909C92.1243 34.3501 92.4425 33.8704 93.0217 33.8704C93.6208 33.8704 93.929 34.38 93.929 35.0909C93.929 35.8068 93.6158 36.3288 93.0217 36.3288C92.4475 36.3288 92.1243 35.8317 92.1243 35.0909ZM97.2768 37.0746C98.3954 37.0746 99.1063 36.2866 99.1063 35.1058C99.1063 33.9226 98.3954 33.1321 97.2768 33.1321C96.1582 33.1321 95.4472 33.9226 95.4472 35.1058C95.4472 36.2866 96.1582 37.0746 97.2768 37.0746ZM97.2817 36.3537C96.6628 36.3537 96.3595 35.8018 96.3595 35.1033C96.3595 34.4048 96.6628 33.8455 97.2817 33.8455C97.8908 33.8455 98.194 34.4048 98.194 35.1033C98.194 35.8018 97.8908 36.3537 97.2817 36.3537ZM102.304 35.3942C102.304 35.9759 101.889 36.2642 101.491 36.2642C101.058 36.2642 100.77 35.9585 100.77 35.4737V33.1818H99.8701V35.6129C99.8701 36.5302 100.392 37.0497 101.143 37.0497C101.715 37.0497 102.117 36.7489 102.291 36.3214H102.331V37H103.204V33.1818H102.304V35.3942ZM106.035 33.1818H105.282V32.267H104.382V33.1818H103.841V33.8778H104.382V36.0007C104.377 36.7191 104.899 37.0721 105.576 37.0522C105.832 37.0447 106.008 36.995 106.105 36.9627L105.953 36.2592C105.904 36.2717 105.802 36.294 105.69 36.294C105.464 36.294 105.282 36.2145 105.282 35.8516V33.8778H106.035V33.1818ZM109.084 38.4318C109.785 38.4318 110.19 38.0714 110.406 37.4624L111.94 33.1868L110.978 33.1818L110.095 36.0653H110.056L109.176 33.1818H108.221L109.606 37.0795L109.529 37.2859C109.362 37.7209 109.099 37.7507 108.706 37.6364L108.497 38.3374C108.619 38.3871 108.838 38.4318 109.084 38.4318ZM114.1 37.0746C115.219 37.0746 115.93 36.2866 115.93 35.1058C115.93 33.9226 115.219 33.1321 114.1 33.1321C112.981 33.1321 112.27 33.9226 112.27 35.1058C112.27 36.2866 112.981 37.0746 114.1 37.0746ZM114.105 36.3537C113.486 36.3537 113.183 35.8018 113.183 35.1033C113.183 34.4048 113.486 33.8455 114.105 33.8455C114.714 33.8455 115.017 34.4048 115.017 35.1033C115.017 35.8018 114.714 36.3537 114.105 36.3537ZM119.127 35.3942C119.127 35.9759 118.712 36.2642 118.314 36.2642C117.882 36.2642 117.593 35.9585 117.593 35.4737V33.1818H116.693V35.6129C116.693 36.5302 117.215 37.0497 117.966 37.0497C118.538 37.0497 118.94 36.7489 119.114 36.3214H119.154V37H120.027V33.1818H119.127V35.3942ZM120.952 37H121.852V34.7553C121.852 34.2706 122.217 33.9276 122.712 33.9276C122.864 33.9276 123.053 33.9549 123.13 33.9798V33.152C123.048 33.1371 122.906 33.1271 122.807 33.1271C122.369 33.1271 122.004 33.3757 121.864 33.8182H121.825V33.1818H120.952V37ZM126.745 34.1911C126.621 33.5447 126.104 33.1321 125.209 33.1321C124.289 33.1321 123.663 33.5845 123.665 34.2905C123.663 34.8473 124.006 35.2152 124.739 35.3668L125.39 35.5036C125.741 35.5806 125.905 35.7223 125.905 35.9386C125.905 36.1996 125.622 36.396 125.194 36.396C124.781 36.396 124.513 36.217 124.436 35.8739L123.558 35.9585C123.67 36.6594 124.259 37.0746 125.197 37.0746C126.151 37.0746 126.825 36.5799 126.827 35.8565C126.825 35.3121 126.474 34.979 125.753 34.8224L125.102 34.6832C124.714 34.5962 124.56 34.462 124.563 34.2408C124.56 33.9822 124.846 33.8033 125.221 33.8033C125.637 33.8033 125.855 34.0295 125.925 34.2805L126.745 34.1911ZM129.26 37.0746C130.15 37.0746 130.761 36.6396 130.92 35.9759L130.08 35.8814C129.958 36.2045 129.66 36.3736 129.272 36.3736C128.69 36.3736 128.305 35.9908 128.298 35.337H130.957V35.0611C130.957 33.7212 130.152 33.1321 129.212 33.1321C128.119 33.1321 127.405 33.935 127.405 35.1133C127.405 36.3114 128.109 37.0746 129.26 37.0746ZM128.3 34.7305C128.327 34.2433 128.688 33.8331 129.225 33.8331C129.742 33.8331 130.09 34.2109 130.095 34.7305H128.3ZM132.619 31.9091H131.719V37H132.619V31.9091ZM135.478 33.1818H134.685V32.881C134.685 32.5827 134.809 32.4162 135.145 32.4162C135.286 32.4162 135.406 32.4485 135.48 32.4709L135.662 31.7749C135.545 31.7351 135.289 31.6705 134.978 31.6705C134.314 31.6705 133.785 32.0508 133.785 32.8214V33.1818H133.221V33.8778H133.785V37H134.685V33.8778H135.478V33.1818Z" fill="#555A60"/>
</g>
<defs>
<filter id="filter0_d" x="22" y="18" width="156" height="134" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="3"/>
<feGaussianBlur stdDeviation="2.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.596078 0 0 0 0 0.643137 0 0 0 0.125 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter1_d" x="78" y="119" width="45" height="13" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.215686 0 0 0 0 0.490196 0 0 0 0 1 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="100" y1="154" x2="100" y2="92.5" gradientUnits="userSpaceOnUse">
<stop offset="0.256387" stop-color="#F3F4F6"/>
<stop offset="0.749415" stop-color="#F3F4F6" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0">
<rect width="200" height="144" fill="white"/>
</clipPath>
</defs>
</svg>
