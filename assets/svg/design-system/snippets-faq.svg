<svg width="200" height="144" viewBox="0 0 200 144" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0)">
<path d="M196 0H4C1.79086 0 0 1.79086 0 4V140C0 142.209 1.79086 144 4 144H196C198.209 144 200 142.209 200 140V4C200 1.79086 198.209 0 196 0Z" fill="#F3F4F6"/>
<g filter="url(#filter0_d)">
<path d="M27 24C27 21.7909 28.7909 20 31 20H169C171.209 20 173 21.7909 173 24V144H27V24Z" fill="white"/>
<path d="M31 20.5H169C170.933 20.5 172.5 22.067 172.5 24V143.5H27.5V24C27.5 22.067 29.067 20.5 31 20.5Z" stroke="#E7EAF3"/>
</g>
<path d="M51.6506 52H52.5728V49.8374H54.6882V49.0643H52.5728V47.6822H54.9119V46.9091H51.6506V52ZM55.6577 52H56.5575V49.7553C56.5575 49.2706 56.9229 48.9276 57.4176 48.9276C57.5692 48.9276 57.7582 48.9549 57.8352 48.9798V48.152C57.7532 48.1371 57.6115 48.1271 57.5121 48.1271C57.0746 48.1271 56.7092 48.3757 56.57 48.8182H56.5302V48.1818H55.6577V52ZM59.9662 52.0746C60.8561 52.0746 61.4676 51.6396 61.6267 50.9759L60.7865 50.8814C60.6647 51.2045 60.3664 51.3736 59.9786 51.3736C59.3969 51.3736 59.0116 50.9908 59.0042 50.337H61.664V50.0611C61.664 48.7212 60.8586 48.1321 59.9189 48.1321C58.8252 48.1321 58.1118 48.935 58.1118 50.1133C58.1118 51.3114 58.8153 52.0746 59.9662 52.0746ZM59.0067 49.7305C59.034 49.2433 59.3944 48.8331 59.9314 48.8331C60.4484 48.8331 60.7964 49.2109 60.8014 49.7305H59.0067ZM65.873 53.4318V48.1818H64.9881V48.8132H64.9359C64.7992 48.5373 64.5183 48.1321 63.8446 48.1321C62.9622 48.1321 62.2686 48.8232 62.2686 50.0959C62.2686 51.3537 62.9423 52.0671 63.8422 52.0671C64.4984 52.0671 64.7942 51.6768 64.9359 51.3984H64.9732V53.4318H65.873ZM64.9906 50.0909C64.9906 50.8317 64.6674 51.3288 64.0932 51.3288C63.4991 51.3288 63.1859 50.8068 63.1859 50.0909C63.1859 49.38 63.4941 48.8704 64.0932 48.8704C64.6724 48.8704 64.9906 49.3501 64.9906 50.0909ZM69.2338 50.3942C69.2338 50.9759 68.8187 51.2642 68.421 51.2642C67.9885 51.2642 67.7001 50.9585 67.7001 50.4737V48.1818H66.8002V50.6129C66.8002 51.5302 67.3223 52.0497 68.073 52.0497C68.6447 52.0497 69.0474 51.7489 69.2214 51.3214H69.2612V52H70.1337V48.1818H69.2338V50.3942ZM72.7494 52.0746C73.6393 52.0746 74.2508 51.6396 74.4099 50.9759L73.5697 50.8814C73.4479 51.2045 73.1496 51.3736 72.7618 51.3736C72.1801 51.3736 71.7948 50.9908 71.7874 50.337H74.4472V50.0611C74.4472 48.7212 73.6418 48.1321 72.7021 48.1321C71.6084 48.1321 70.895 48.935 70.895 50.1133C70.895 51.3114 71.5985 52.0746 72.7494 52.0746ZM71.7899 49.7305C71.8172 49.2433 72.1776 48.8331 72.7146 48.8331C73.2316 48.8331 73.5796 49.2109 73.5846 49.7305H71.7899ZM76.1083 49.7628C76.1083 49.2109 76.4414 48.8928 76.9162 48.8928C77.381 48.8928 77.6594 49.1985 77.6594 49.7081V52H78.5593V49.5689C78.5618 48.6541 78.0398 48.1321 77.2518 48.1321C76.68 48.1321 76.2873 48.4055 76.1133 48.8306H76.0685V48.1818H75.2085V52H76.1083V49.7628ZM81.3807 48.1818H80.6275V47.267H79.7276V48.1818H79.1857V48.8778H79.7276V51.0007C79.7227 51.7191 80.2447 52.0721 80.9208 52.0522C81.1768 52.0447 81.3533 51.995 81.4503 51.9627L81.2987 51.2592C81.2489 51.2717 81.147 51.294 81.0352 51.294C80.8089 51.294 80.6275 51.2145 80.6275 50.8516V48.8778H81.3807V48.1818ZM83.1151 46.9091H82.2153V52H83.1151V46.9091ZM84.5998 53.4318C85.3008 53.4318 85.706 53.0714 85.9222 52.4624L87.456 48.1868L86.494 48.1818L85.6115 51.0653H85.5717L84.6918 48.1818H83.7372L85.1218 52.0795L85.0447 52.2859C84.8782 52.7209 84.6147 52.7507 84.2219 52.6364L84.0131 53.3374C84.1349 53.3871 84.3537 53.4318 84.5998 53.4318ZM90.5104 52L90.9305 50.7447H92.8445L93.2671 52H94.2515L92.4568 46.9091H91.3183L89.526 52H90.5104ZM91.1791 50.0039L91.8676 47.9531H91.9074L92.596 50.0039H91.1791ZM97.8385 49.1911C97.7142 48.5447 97.1972 48.1321 96.3023 48.1321C95.3825 48.1321 94.7561 48.5845 94.7586 49.2905C94.7561 49.8473 95.0992 50.2152 95.8325 50.3668L96.4838 50.5036C96.8343 50.5806 96.9983 50.7223 96.9983 50.9386C96.9983 51.1996 96.7149 51.396 96.2874 51.396C95.8747 51.396 95.6063 51.217 95.5292 50.8739L94.6517 50.9585C94.7636 51.6594 95.3527 52.0746 96.2899 52.0746C97.2444 52.0746 97.9181 51.5799 97.9205 50.8565C97.9181 50.3121 97.5676 49.979 96.8467 49.8224L96.1954 49.6832C95.8076 49.5962 95.6535 49.462 95.656 49.2408C95.6535 48.9822 95.9394 48.8033 96.3147 48.8033C96.7298 48.8033 96.9486 49.0295 97.0182 49.2805L97.8385 49.1911ZM98.6626 52H99.5624V50.7173L99.8905 50.3668L101.059 52H102.135L100.569 49.8299L102.048 48.1818H100.997L99.6246 49.7156H99.5624V46.9091H98.6626V52ZM104.181 52.0746C105.071 52.0746 105.682 51.6396 105.842 50.9759L105.001 50.8814C104.88 51.2045 104.581 51.3736 104.193 51.3736C103.612 51.3736 103.226 50.9908 103.219 50.337H105.879V50.0611C105.879 48.7212 105.073 48.1321 104.134 48.1321C103.04 48.1321 102.327 48.935 102.327 50.1133C102.327 51.3114 103.03 52.0746 104.181 52.0746ZM103.222 49.7305C103.249 49.2433 103.609 48.8331 104.146 48.8331C104.663 48.8331 105.011 49.2109 105.016 49.7305H103.222ZM108.057 52.0671C108.713 52.0671 109.009 51.6768 109.151 51.3984H109.205V52H110.09V46.9091H109.188V48.8132H109.151C109.014 48.5373 108.733 48.1321 108.059 48.1321C107.177 48.1321 106.483 48.8232 106.483 50.0959C106.483 51.3537 107.157 52.0671 108.057 52.0671ZM108.308 51.3288C107.714 51.3288 107.401 50.8068 107.401 50.0909C107.401 49.38 107.709 48.8704 108.308 48.8704C108.887 48.8704 109.205 49.3501 109.205 50.0909C109.205 50.8317 108.882 51.3288 108.308 51.3288ZM117.374 49.4545C117.374 47.8089 116.37 46.8395 115.035 46.8395C113.698 46.8395 112.696 47.8089 112.696 49.4545C112.696 51.0977 113.698 52.0696 115.035 52.0696C115.358 52.0696 115.662 52.0124 115.938 51.9031L116.358 52.4375H117.253L116.547 51.5327C117.059 51.0852 117.374 50.3768 117.374 49.4545ZM114.722 50.2898L115.411 51.1946C115.291 51.2269 115.167 51.2418 115.035 51.2418C114.207 51.2418 113.623 50.6129 113.623 49.4545C113.623 48.2962 114.207 47.6673 115.035 47.6673C115.866 47.6673 116.447 48.2962 116.447 49.4545C116.447 50.081 116.278 50.5508 115.99 50.8516L115.557 50.2898H114.722ZM120.647 50.3942C120.647 50.9759 120.232 51.2642 119.834 51.2642C119.402 51.2642 119.113 50.9585 119.113 50.4737V48.1818H118.213V50.6129C118.213 51.5302 118.735 52.0497 119.486 52.0497C120.058 52.0497 120.46 51.7489 120.634 51.3214H120.674V52H121.547V48.1818H120.647V50.3942ZM124.162 52.0746C125.052 52.0746 125.664 51.6396 125.823 50.9759L124.983 50.8814C124.861 51.2045 124.563 51.3736 124.175 51.3736C123.593 51.3736 123.208 50.9908 123.2 50.337H125.86V50.0611C125.86 48.7212 125.055 48.1321 124.115 48.1321C123.021 48.1321 122.308 48.935 122.308 50.1133C122.308 51.3114 123.012 52.0746 124.162 52.0746ZM123.203 49.7305C123.23 49.2433 123.591 48.8331 124.128 48.8331C124.645 48.8331 124.993 49.2109 124.998 49.7305H123.203ZM129.639 49.1911C129.515 48.5447 128.998 48.1321 128.103 48.1321C127.183 48.1321 126.557 48.5845 126.559 49.2905C126.557 49.8473 126.9 50.2152 127.633 50.3668L128.285 50.5036C128.635 50.5806 128.799 50.7223 128.799 50.9386C128.799 51.1996 128.516 51.396 128.088 51.396C127.676 51.396 127.407 51.217 127.33 50.8739L126.453 50.9585C126.564 51.6594 127.153 52.0746 128.091 52.0746C129.045 52.0746 129.719 51.5799 129.721 50.8565C129.719 50.3121 129.368 49.979 128.647 49.8224L127.996 49.6832C127.608 49.5962 127.454 49.462 127.457 49.2408C127.454 48.9822 127.74 48.8033 128.116 48.8033C128.531 48.8033 128.749 49.0295 128.819 49.2805L129.639 49.1911ZM132.37 48.1818H131.617V47.267H130.717V48.1818H130.175V48.8778H130.717V51.0007C130.712 51.7191 131.234 52.0721 131.91 52.0522C132.166 52.0447 132.343 51.995 132.44 51.9627L132.288 51.2592C132.238 51.2717 132.136 51.294 132.024 51.294C131.798 51.294 131.617 51.2145 131.617 50.8516V48.8778H132.37V48.1818ZM133.123 52H134.022V48.1818H133.123V52ZM133.575 47.6399C133.861 47.6399 134.094 47.4212 134.094 47.1527C134.094 46.8817 133.861 46.663 133.575 46.663C133.287 46.663 133.053 46.8817 133.053 47.1527C133.053 47.4212 133.287 47.6399 133.575 47.6399ZM136.613 52.0746C137.732 52.0746 138.443 51.2866 138.443 50.1058C138.443 48.9226 137.732 48.1321 136.613 48.1321C135.495 48.1321 134.784 48.9226 134.784 50.1058C134.784 51.2866 135.495 52.0746 136.613 52.0746ZM136.618 51.3537C135.999 51.3537 135.696 50.8018 135.696 50.1033C135.696 49.4048 135.999 48.8455 136.618 48.8455C137.227 48.8455 137.53 49.4048 137.53 50.1033C137.53 50.8018 137.227 51.3537 136.618 51.3537ZM140.106 49.7628C140.106 49.2109 140.439 48.8928 140.914 48.8928C141.379 48.8928 141.657 49.1985 141.657 49.7081V52H142.557V49.5689C142.56 48.6541 142.038 48.1321 141.25 48.1321C140.678 48.1321 140.285 48.4055 140.111 48.8306H140.067V48.1818H139.206V52H140.106V49.7628ZM146.49 49.1911C146.366 48.5447 145.849 48.1321 144.954 48.1321C144.034 48.1321 143.407 48.5845 143.41 49.2905C143.407 49.8473 143.751 50.2152 144.484 50.3668L145.135 50.5036C145.486 50.5806 145.65 50.7223 145.65 50.9386C145.65 51.1996 145.366 51.396 144.939 51.396C144.526 51.396 144.258 51.217 144.181 50.8739L143.303 50.9585C143.415 51.6594 144.004 52.0746 144.941 52.0746C145.896 52.0746 146.569 51.5799 146.572 50.8565C146.569 50.3121 146.219 49.979 145.498 49.8224L144.847 49.6832C144.459 49.5962 144.305 49.462 144.307 49.2408C144.305 48.9822 144.591 48.8033 144.966 48.8033C145.381 48.8033 145.6 49.0295 145.67 49.2805L146.49 49.1911Z" fill="#555A60"/>
<rect x="66" y="69" width="18" height="2" rx="1" fill="#71869D"/>
<rect x="88" y="69" width="21" height="2" rx="1" fill="#71869D"/>
<rect x="113" y="69" width="21" height="2" rx="1" fill="#71869D"/>
<rect x="66" y="74" width="12" height="2" rx="1" fill="#71869D"/>
<rect x="82" y="74" width="14" height="2" rx="1" fill="#71869D"/>
<rect x="100" y="74" width="14" height="2" rx="1" fill="#71869D"/>
<g filter="url(#filter1_d)">
<rect x="59" y="83" width="82" height="31" rx="4" fill="white"/>
<rect x="59.5" y="83.5" width="81" height="30" rx="3.5" stroke="#E7EAF3"/>
</g>
<rect x="66" y="90" width="18" height="2" rx="1" fill="#377dff"/>
<rect x="88" y="90" width="21" height="2" rx="1" fill="#377dff"/>
<rect x="113" y="90" width="21" height="2" rx="1" fill="#377dff"/>
<rect x="66" y="99" width="18" height="2" rx="1" fill="#D9DDEA"/>
<rect x="88" y="99" width="21" height="2" rx="1" fill="#D9DDEA"/>
<rect x="113" y="99" width="21" height="2" rx="1" fill="#D9DDEA"/>
<rect x="66" y="105" width="12" height="2" rx="1" fill="#D9DDEA"/>
<rect x="82" y="105" width="14" height="2" rx="1" fill="#D9DDEA"/>
<rect x="100" y="105" width="14" height="2" rx="1" fill="#D9DDEA"/>
<rect x="66" y="121" width="18" height="2" rx="1" fill="#D9DDEA"/>
<rect x="88" y="121" width="21" height="2" rx="1" fill="#D9DDEA"/>
<rect x="113" y="121" width="21" height="2" rx="1" fill="#D9DDEA"/>
<rect x="66" y="126" width="12" height="2" rx="1" fill="#D9DDEA"/>
<rect x="82" y="126" width="14" height="2" rx="1" fill="#D9DDEA"/>
<rect x="100" y="126" width="14" height="2" rx="1" fill="#D9DDEA"/>
<path d="M0 104H200V140C200 142.209 198.209 144 196 144H4C1.79086 144 0 142.209 0 140V104Z" fill="url(#paint0_linear)"/>
</g>
<defs>
<filter id="filter0_d" x="22" y="18" width="156" height="134" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="3"/>
<feGaussianBlur stdDeviation="2.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.596078 0 0 0 0 0.643137 0 0 0 0.125 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter1_d" x="54" y="81" width="92" height="41" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="3"/>
<feGaussianBlur stdDeviation="2.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.596078 0 0 0 0 0.643137 0 0 0 0.125 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="100" y1="154" x2="100" y2="92.5" gradientUnits="userSpaceOnUse">
<stop offset="0.256387" stop-color="#F3F4F6"/>
<stop offset="0.749415" stop-color="#F3F4F6" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0">
<rect width="200" height="144" fill="white"/>
</clipPath>
</defs>
</svg>
