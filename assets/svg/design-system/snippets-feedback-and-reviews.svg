<svg width="200" height="144" viewBox="0 0 200 144" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0)">
<path d="M196 0H4C1.79086 0 0 1.79086 0 4V140C0 142.209 1.79086 144 4 144H196C198.209 144 200 142.209 200 140V4C200 1.79086 198.209 0 196 0Z" fill="#F3F4F6"/>
<g filter="url(#filter0_d)">
<path d="M27 24C27 21.7909 28.7909 20 31 20H169C171.209 20 173 21.7909 173 24V144H27V24Z" fill="white"/>
<path d="M31 20.5H169C170.933 20.5 172.5 22.067 172.5 24V143.5H27.5V24C27.5 22.067 29.067 20.5 31 20.5Z" stroke="#E7EAF3"/>
</g>
<rect x="37" y="30" width="37" height="16" rx="2" fill="#377dff"/>
<path d="M49.1971 39.9205H51.9727V41H52.9783V39.9205H53.7227V39.054H52.9783V35.1818H51.6658L49.1971 39.0824V39.9205ZM51.984 39.054H50.2766V39.0085L51.9386 36.375H51.984V39.054ZM55.1112 41.0625C55.4464 41.0625 55.7333 40.7841 55.7362 40.4375C55.7333 40.0966 55.4464 39.8182 55.1112 39.8182C54.7646 39.8182 54.4833 40.0966 54.4862 40.4375C54.4833 40.7841 54.7646 41.0625 55.1112 41.0625ZM58.636 41.0795C59.8945 41.0795 60.7951 40.3864 60.7979 39.4403C60.7951 38.7131 60.2582 38.1051 59.582 37.9915V37.9517C60.1701 37.821 60.5792 37.2926 60.582 36.6619C60.5792 35.767 59.7553 35.1023 58.636 35.1023C57.5082 35.1023 56.6843 35.7642 56.6871 36.6619C56.6843 37.2926 57.0877 37.821 57.6871 37.9517V37.9915C56.9996 38.1051 56.4684 38.7131 56.4712 39.4403C56.4684 40.3864 57.3661 41.0795 58.636 41.0795ZM58.636 40.267C57.9769 40.267 57.5508 39.9034 57.5565 39.3665C57.5508 38.8097 58.0082 38.4148 58.636 38.4148C59.2553 38.4148 59.7099 38.8125 59.7156 39.3665C59.7099 39.9034 59.2866 40.267 58.636 40.267ZM58.636 37.6165C58.0962 37.6165 57.7099 37.267 57.7156 36.7614C57.7099 36.2614 58.0849 35.9261 58.636 35.9261C59.1786 35.9261 59.5508 36.2614 59.5565 36.7614C59.5508 37.2699 59.1673 37.6165 58.636 37.6165Z" fill="white"/>
<rect x="37" y="50" width="23" height="2" rx="1" fill="#D9DDEA"/>
<rect x="85" y="30" width="15" height="5" rx="1" fill="#E7EAF3"/>
<rect x="140" y="30" width="23" height="5" rx="1" fill="#D9DDEA"/>
<rect x="36.5334" y="56.4445" width="9.06667" height="1.51111" rx="0.755556" fill="#D9DDEA"/>
<rect x="70.5334" y="56.4445" width="3.02222" height="1.51111" rx="0.755556" fill="#D9DDEA"/>
<rect x="48.6223" y="56.4445" width="18.8889" height="1.51111" rx="0.755556" fill="#D9DDEA"/>
<rect x="48.6223" y="56.4445" width="17.3778" height="1.51111" rx="0.755556" fill="#377dff"/>
<rect x="36.5334" y="60.2222" width="9.06667" height="1.51111" rx="0.755556" fill="#D9DDEA"/>
<rect x="70.5334" y="60.2222" width="3.02222" height="1.51111" rx="0.755556" fill="#D9DDEA"/>
<rect x="48.6223" y="60.2222" width="18.8889" height="1.51111" rx="0.755556" fill="#D9DDEA"/>
<rect x="48.6223" y="60.2222" width="4.53333" height="1.51111" rx="0.755556" fill="#377dff"/>
<rect x="36.5334" y="64" width="9.06667" height="1.51111" rx="0.755556" fill="#D9DDEA"/>
<rect x="70.5334" y="64" width="3.02222" height="1.51111" rx="0.755556" fill="#D9DDEA"/>
<rect x="48.6223" y="64" width="18.8889" height="1.51111" rx="0.755556" fill="#D9DDEA"/>
<rect x="48.6223" y="64" width="1.51111" height="1.51111" rx="0.755556" fill="#377dff"/>
<rect x="36.5334" y="67.7778" width="9.06667" height="1.51111" rx="0.755556" fill="#D9DDEA"/>
<rect x="70.5334" y="67.7778" width="3.02222" height="1.51111" rx="0.755556" fill="#D9DDEA"/>
<rect x="48.6223" y="67.7778" width="18.8889" height="1.51111" rx="0.755556" fill="#D9DDEA"/>
<rect x="48.6223" y="67.7778" width="0.755556" height="1.51111" rx="0.377778" fill="#377dff"/>
<rect x="36.5334" y="71.5555" width="9.06667" height="1.51111" rx="0.755556" fill="#D9DDEA"/>
<rect x="70.5334" y="71.5555" width="3.02222" height="1.51111" rx="0.755556" fill="#D9DDEA"/>
<rect x="48.6223" y="71.5555" width="18.8889" height="1.51111" rx="0.755556" fill="#D9DDEA"/>
<path d="M104.203 43.6318L104.515 44.296C104.529 44.3101 104.542 44.3242 104.556 44.3242L105.248 44.4232C105.288 44.4232 105.302 44.4797 105.275 44.508L104.773 45.0167C104.759 45.0308 104.759 45.045 104.759 45.0591L104.881 45.7798C104.895 45.8222 104.841 45.8504 104.814 45.8363L104.176 45.5113C104.162 45.5113 104.149 45.5113 104.135 45.5113L103.511 45.8504C103.47 45.8646 103.43 45.8363 103.443 45.7939L103.565 45.0732C103.565 45.0591 103.565 45.045 103.552 45.0308L103.036 44.508C103.009 44.4797 103.023 44.4373 103.063 44.4373L103.755 44.3384C103.769 44.3384 103.782 44.3242 103.796 44.3101L104.108 43.6318C104.135 43.5894 104.189 43.5894 104.203 43.6318Z" fill="#F5CA99"/>
<path d="M107.226 43.6353L107.538 44.2995C107.551 44.3136 107.565 44.3278 107.578 44.3278L108.27 44.4267C108.311 44.4267 108.325 44.4832 108.297 44.5115L107.796 45.0202C107.782 45.0343 107.782 45.0485 107.782 45.0626L107.904 45.7833C107.918 45.8257 107.863 45.854 107.836 45.8398L107.199 45.5148C107.185 45.5148 107.171 45.5148 107.158 45.5148L106.534 45.854C106.493 45.8681 106.452 45.8398 106.466 45.7974L106.588 45.0767C106.588 45.0626 106.588 45.0485 106.575 45.0343L106.059 44.5115C106.032 44.4832 106.045 44.4408 106.086 44.4408L106.778 44.3419C106.792 44.3419 106.805 44.3278 106.819 44.3136L107.131 43.6353C107.158 43.5929 107.212 43.5929 107.226 43.6353Z" fill="#F5CA99"/>
<path d="M110.249 43.6389L110.561 44.303C110.574 44.3172 110.588 44.3313 110.601 44.3313L111.293 44.4302C111.334 44.4302 111.348 44.4867 111.32 44.515L110.818 45.0237C110.805 45.0379 110.805 45.052 110.805 45.0661L110.927 45.7868C110.941 45.8292 110.886 45.8575 110.859 45.8434L110.222 45.5183C110.208 45.5183 110.194 45.5183 110.181 45.5183L109.557 45.8575C109.516 45.8716 109.475 45.8434 109.489 45.801L109.611 45.0803C109.611 45.0661 109.611 45.052 109.598 45.0379L109.082 44.515C109.055 44.4867 109.068 44.4443 109.109 44.4443L109.801 44.3454C109.815 44.3454 109.828 44.3313 109.842 44.3172L110.154 43.6389C110.181 43.5965 110.235 43.5965 110.249 43.6389Z" fill="#F5CA99"/>
<path d="M113.271 43.6424L113.583 44.3066C113.597 44.3207 113.611 44.3348 113.624 44.3348L114.316 44.4338C114.357 44.4338 114.37 44.4903 114.343 44.5185L113.841 45.0273C113.828 45.0414 113.828 45.0555 113.828 45.0697L113.95 45.7904C113.963 45.8328 113.909 45.861 113.882 45.8469L113.244 45.5219C113.231 45.5219 113.217 45.5219 113.204 45.5219L112.58 45.861C112.539 45.8752 112.498 45.8469 112.512 45.8045L112.634 45.0838C112.634 45.0697 112.634 45.0555 112.62 45.0414L112.105 44.5185C112.078 44.4903 112.091 44.4479 112.132 44.4479L112.824 44.349C112.837 44.349 112.851 44.3348 112.864 44.3207L113.176 43.6424C113.204 43.6 113.258 43.6 113.271 43.6424Z" fill="#F5CA99"/>
<path d="M116.294 43.6459L116.606 44.3101C116.62 44.3242 116.633 44.3383 116.647 44.3383L117.339 44.4373C117.379 44.4373 117.393 44.4938 117.366 44.5221L116.864 45.0308C116.85 45.0449 116.85 45.0591 116.85 45.0732L116.972 45.7939C116.986 45.8363 116.932 45.8645 116.905 45.8504L116.267 45.5254C116.253 45.5254 116.24 45.5254 116.226 45.5254L115.602 45.8645C115.562 45.8787 115.521 45.8504 115.534 45.808L115.656 45.0873C115.656 45.0732 115.656 45.0591 115.643 45.0449L115.127 44.5221C115.1 44.4938 115.114 44.4514 115.155 44.4514L115.846 44.3525C115.86 44.3525 115.874 44.3383 115.887 44.3242L116.199 43.6459C116.226 43.6035 116.281 43.6035 116.294 43.6459Z" fill="#F5CA99"/>
<rect x="103.022" y="48.1333" width="18.1333" height="2.26667" rx="1.13333" fill="#D9DDEA"/>
<rect x="103.022" y="54.1778" width="42.3111" height="2.26667" rx="1.13333" fill="#D9DDEA"/>
<g filter="url(#filter1_d)">
<rect x="87.1555" y="44.3556" width="9.06667" height="9.06667" rx="4.53333" fill="white"/>
</g>
<path d="M91.6888 48.4922C92.346 48.4922 92.8788 47.9595 92.8788 47.3022C92.8788 46.645 92.346 46.1122 91.6888 46.1122C91.0316 46.1122 90.4988 46.645 90.4988 47.3022C90.4988 47.9595 91.0316 48.4922 91.6888 48.4922Z" fill="#71869D"/>
<path d="M93.9214 50.5436C93.0034 51.7789 91.2637 52.0339 90.0284 51.1216C89.8074 50.9572 89.6147 50.7646 89.4504 50.5436C89.3711 50.4246 89.3597 50.2716 89.4221 50.1469L89.5071 49.9712C89.7054 49.5519 90.1247 49.2856 90.5894 49.2856H92.7937C93.2471 49.2856 93.6607 49.5462 93.8647 49.9542L93.9554 50.1412C94.0121 50.2716 94.0064 50.4246 93.9214 50.5436Z" fill="#71869D"/>
<path d="M104.203 66.0318L104.515 66.696C104.529 66.7101 104.542 66.7242 104.556 66.7242L105.248 66.8232C105.288 66.8232 105.302 66.8797 105.275 66.9079L104.773 67.4167C104.759 67.4308 104.759 67.4449 104.759 67.4591L104.881 68.1798C104.895 68.2222 104.841 68.2504 104.814 68.2363L104.176 67.9113C104.162 67.9113 104.149 67.9113 104.135 67.9113L103.511 68.2504C103.47 68.2646 103.43 68.2363 103.443 68.1939L103.565 67.4732C103.565 67.4591 103.565 67.4449 103.552 67.4308L103.036 66.9079C103.009 66.8797 103.023 66.8373 103.063 66.8373L103.755 66.7384C103.769 66.7384 103.782 66.7242 103.796 66.7101L104.108 66.0318C104.135 65.9894 104.189 65.9894 104.203 66.0318Z" fill="#F5CA99"/>
<path d="M107.226 66.0353L107.538 66.6995C107.551 66.7136 107.565 66.7278 107.578 66.7278L108.27 66.8267C108.311 66.8267 108.325 66.8832 108.297 66.9115L107.796 67.4202C107.782 67.4344 107.782 67.4485 107.782 67.4626L107.904 68.1833C107.918 68.2257 107.863 68.254 107.836 68.2399L107.199 67.9148C107.185 67.9148 107.171 67.9148 107.158 67.9148L106.534 68.254C106.493 68.2681 106.452 68.2399 106.466 68.1975L106.588 67.4767C106.588 67.4626 106.588 67.4485 106.575 67.4344L106.059 66.9115C106.032 66.8832 106.045 66.8408 106.086 66.8408L106.778 66.7419C106.792 66.7419 106.805 66.7278 106.819 66.7136L107.131 66.0353C107.158 65.9929 107.212 65.9929 107.226 66.0353Z" fill="#F5CA99"/>
<path d="M110.249 66.0388L110.561 66.703C110.574 66.7172 110.588 66.7313 110.601 66.7313L111.293 66.8302C111.334 66.8302 111.348 66.8867 111.32 66.915L110.818 67.4237C110.805 67.4379 110.805 67.452 110.805 67.4661L110.927 68.1868C110.941 68.2292 110.886 68.2575 110.859 68.2434L110.222 67.9183C110.208 67.9183 110.194 67.9183 110.181 67.9183L109.557 68.2575C109.516 68.2716 109.475 68.2434 109.489 68.201L109.611 67.4803C109.611 67.4661 109.611 67.452 109.598 67.4379L109.082 66.915C109.055 66.8867 109.068 66.8443 109.109 66.8443L109.801 66.7454C109.815 66.7454 109.828 66.7313 109.842 66.7172L110.154 66.0388C110.181 65.9965 110.235 65.9965 110.249 66.0388Z" fill="#F5CA99"/>
<path d="M113.271 66.0424L113.583 66.7066C113.597 66.7207 113.611 66.7348 113.624 66.7348L114.316 66.8338C114.357 66.8338 114.37 66.8903 114.343 66.9185L113.841 67.4273C113.828 67.4414 113.828 67.4555 113.828 67.4697L113.95 68.1904C113.963 68.2328 113.909 68.261 113.882 68.2469L113.244 67.9219C113.231 67.9219 113.217 67.9219 113.204 67.9219L112.58 68.261C112.539 68.2752 112.498 68.2469 112.512 68.2045L112.634 67.4838C112.634 67.4697 112.634 67.4555 112.62 67.4414L112.105 66.9185C112.078 66.8903 112.091 66.8479 112.132 66.8479L112.824 66.749C112.837 66.749 112.851 66.7348 112.864 66.7207L113.176 66.0424C113.204 66 113.258 66 113.271 66.0424Z" fill="#F5CA99"/>
<path d="M116.294 66.0459L116.606 66.7101C116.62 66.7242 116.633 66.7383 116.647 66.7383L117.339 66.8373C117.379 66.8373 117.393 66.8938 117.366 66.922L116.864 67.4308C116.85 67.4449 116.85 67.459 116.85 67.4732L116.972 68.1939C116.986 68.2363 116.932 68.2645 116.905 68.2504L116.267 67.9254C116.253 67.9254 116.24 67.9254 116.226 67.9254L115.602 68.2645C115.562 68.2787 115.521 68.2504 115.534 68.208L115.656 67.4873C115.656 67.4732 115.656 67.459 115.643 67.4449L115.127 66.922C115.1 66.8938 115.114 66.8514 115.155 66.8514L115.846 66.7525C115.86 66.7525 115.874 66.7383 115.887 66.7242L116.199 66.0459C116.226 66.0035 116.281 66.0035 116.294 66.0459Z" fill="#D9DDEA"/>
<rect x="103.022" y="70.5333" width="18.1333" height="2.26667" rx="1.13333" fill="#D9DDEA"/>
<rect x="103.022" y="76.5778" width="42.3111" height="2.26667" rx="1.13333" fill="#D9DDEA"/>
<g filter="url(#filter2_d)">
<rect x="87.1555" y="66.7556" width="9.06667" height="9.06667" rx="4.53333" fill="white"/>
</g>
<path d="M91.6888 70.8922C92.346 70.8922 92.8788 70.3595 92.8788 69.7022C92.8788 69.045 92.346 68.5122 91.6888 68.5122C91.0316 68.5122 90.4988 69.045 90.4988 69.7022C90.4988 70.3595 91.0316 70.8922 91.6888 70.8922Z" fill="#71869D"/>
<path d="M93.9214 72.9436C93.0034 74.1789 91.2637 74.4339 90.0284 73.5216C89.8074 73.3572 89.6147 73.1646 89.4504 72.9436C89.3711 72.8246 89.3597 72.6716 89.4221 72.5469L89.5071 72.3712C89.7054 71.9519 90.1247 71.6856 90.5894 71.6856H92.7937C93.2471 71.6856 93.6607 71.9462 93.8647 72.3542L93.9554 72.5412C94.0121 72.6716 94.0064 72.8246 93.9214 72.9436Z" fill="#71869D"/>
<path d="M104.203 88.1873L104.515 88.8515C104.529 88.8657 104.542 88.8798 104.556 88.8798L105.248 88.9787C105.288 88.9787 105.302 89.0352 105.275 89.0635L104.773 89.5722C104.759 89.5864 104.759 89.6005 104.759 89.6146L104.881 90.3353C104.895 90.3777 104.841 90.406 104.814 90.3919L104.176 90.0668C104.162 90.0668 104.149 90.0668 104.135 90.0668L103.511 90.406C103.47 90.4201 103.43 90.3919 103.443 90.3495L103.565 89.6288C103.565 89.6146 103.565 89.6005 103.552 89.5864L103.036 89.0635C103.009 89.0352 103.023 88.9928 103.063 88.9928L103.755 88.8939C103.769 88.8939 103.782 88.8798 103.796 88.8657L104.108 88.1873C104.135 88.1449 104.189 88.1449 104.203 88.1873Z" fill="#F5CA99"/>
<path d="M107.226 88.1909L107.538 88.8551C107.551 88.8692 107.565 88.8833 107.578 88.8833L108.27 88.9822C108.311 88.9822 108.325 89.0388 108.297 89.067L107.796 89.5758C107.782 89.5899 107.782 89.604 107.782 89.6182L107.904 90.3389C107.918 90.3813 107.863 90.4095 107.836 90.3954L107.199 90.0704C107.185 90.0704 107.171 90.0704 107.158 90.0704L106.534 90.4095C106.493 90.4237 106.452 90.3954 106.466 90.353L106.588 89.6323C106.588 89.6182 106.588 89.604 106.575 89.5899L106.059 89.067C106.032 89.0388 106.045 88.9964 106.086 88.9964L106.778 88.8975C106.792 88.8975 106.805 88.8833 106.819 88.8692L107.131 88.1909C107.158 88.1485 107.212 88.1485 107.226 88.1909Z" fill="#F5CA99"/>
<path d="M110.249 88.1944L110.561 88.8586C110.574 88.8727 110.588 88.8868 110.601 88.8868L111.293 88.9858C111.334 88.9858 111.348 89.0423 111.32 89.0705L110.818 89.5793C110.805 89.5934 110.805 89.6075 110.805 89.6217L110.927 90.3424C110.941 90.3848 110.886 90.413 110.859 90.3989L110.222 90.0739C110.208 90.0739 110.194 90.0739 110.181 90.0739L109.557 90.413C109.516 90.4272 109.475 90.3989 109.489 90.3565L109.611 89.6358C109.611 89.6217 109.611 89.6075 109.598 89.5934L109.082 89.0705C109.055 89.0423 109.068 88.9999 109.109 88.9999L109.801 88.901C109.815 88.901 109.828 88.8868 109.842 88.8727L110.154 88.1944C110.181 88.152 110.235 88.152 110.249 88.1944Z" fill="#F5CA99"/>
<path d="M113.271 88.1979L113.583 88.8621C113.597 88.8762 113.611 88.8904 113.624 88.8904L114.316 88.9893C114.357 88.9893 114.37 89.0458 114.343 89.0741L113.841 89.5828C113.828 89.597 113.828 89.6111 113.828 89.6252L113.95 90.3459C113.963 90.3883 113.909 90.4166 113.882 90.4024L113.244 90.0774C113.231 90.0774 113.217 90.0774 113.204 90.0774L112.58 90.4166C112.539 90.4307 112.498 90.4024 112.512 90.3601L112.634 89.6393C112.634 89.6252 112.634 89.6111 112.62 89.597L112.105 89.0741C112.078 89.0458 112.091 89.0034 112.132 89.0034L112.824 88.9045C112.837 88.9045 112.851 88.8904 112.864 88.8762L113.176 88.1979C113.204 88.1555 113.258 88.1555 113.271 88.1979Z" fill="#F5CA99"/>
<g clip-path="url(#clip1)">
<path d="M117.364 89.0554C117.391 89.0273 117.378 88.9712 117.337 88.9712L116.645 88.8729C116.631 88.8729 116.618 88.8729 116.604 88.8448L116.292 88.1847C116.278 88.1707 116.265 88.1707 116.251 88.1707V90.0526C116.251 90.0526 116.265 90.0526 116.278 90.0526L116.916 90.3756C116.943 90.3896 116.998 90.3615 116.984 90.3194L116.862 89.6032C116.862 89.5891 116.862 89.5751 116.876 89.561L117.364 89.0554Z" fill="#E7EAF3"/>
<path d="M116.251 88.1707C116.224 88.1707 116.21 88.1847 116.197 88.1847L115.885 88.8729C115.871 88.8729 115.857 88.8869 115.844 88.8869L115.152 88.9852C115.111 88.9852 115.097 89.0554 115.124 89.0835L115.64 89.5751C115.654 89.5891 115.654 89.6172 115.654 89.6172L115.532 90.3335C115.518 90.3896 115.559 90.4177 115.6 90.3896L116.224 90.0526H116.237V88.1707H116.251Z" fill="#F5CA99"/>
</g>
<rect x="103.022" y="92.6889" width="18.1333" height="2.26667" rx="1.13333" fill="#D9DDEA"/>
<rect x="103.022" y="98.7333" width="42.3111" height="2.26667" rx="1.13333" fill="#D9DDEA"/>
<g filter="url(#filter3_d)">
<rect x="87.1555" y="88.9111" width="9.06667" height="9.06667" rx="4.53333" fill="white"/>
</g>
<path d="M91.6888 93.0478C92.346 93.0478 92.8788 92.515 92.8788 91.8578C92.8788 91.2006 92.346 90.6678 91.6888 90.6678C91.0316 90.6678 90.4988 91.2006 90.4988 91.8578C90.4988 92.515 91.0316 93.0478 91.6888 93.0478Z" fill="#71869D"/>
<path d="M93.9214 95.0991C93.0034 96.3345 91.2637 96.5895 90.0284 95.6771C89.8074 95.5128 89.6147 95.3201 89.4504 95.0991C89.3711 94.9801 89.3597 94.8271 89.4221 94.7025L89.5071 94.5268C89.7054 94.1075 90.1247 93.8411 90.5894 93.8411H92.7937C93.2471 93.8411 93.6607 94.1018 93.8647 94.5098L93.9554 94.6968C94.0121 94.8271 94.0064 94.9801 93.9214 95.0991Z" fill="#71869D"/>
<path d="M0 105H200V141C200 143.209 198.209 145 196 145H4C1.79086 145 0 143.209 0 141V105Z" fill="url(#paint0_linear)"/>
</g>
<defs>
<filter id="filter0_d" x="22" y="18" width="156" height="134" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="3"/>
<feGaussianBlur stdDeviation="2.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.596078 0 0 0 0 0.643137 0 0 0 0.125 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter1_d" x="85.1555" y="43.3556" width="13.0667" height="13.0667" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.596078 0 0 0 0 0.643137 0 0 0 0.18 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter2_d" x="85.1555" y="65.7556" width="13.0667" height="13.0667" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.596078 0 0 0 0 0.643137 0 0 0 0.18 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter3_d" x="85.1555" y="87.9111" width="13.0667" height="13.0667" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.596078 0 0 0 0 0.643137 0 0 0 0.18 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="100" y1="155" x2="100" y2="93.5" gradientUnits="userSpaceOnUse">
<stop offset="0.256387" stop-color="#F3F4F6"/>
<stop offset="0.749415" stop-color="#F3F4F6" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0">
<rect width="200" height="144" fill="white"/>
</clipPath>
<clipPath id="clip1">
<rect width="2.26667" height="2.25156" fill="white" transform="translate(115.111 88.1707)"/>
</clipPath>
</defs>
</svg>
