<svg width="200" height="144" viewBox="0 0 200 144" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0)">
<path d="M196 0H4C1.79086 0 0 1.79086 0 4V140C0 142.209 1.79086 144 4 144H196C198.209 144 200 142.209 200 140V4C200 1.79086 198.209 0 196 0Z" fill="#F3F4F6"/>
<g filter="url(#filter0_d)">
<path d="M27 24C27 21.7909 28.7909 20 31 20H169C171.209 20 173 21.7909 173 24V144H27V24Z" fill="white"/>
<path d="M31 20.5H169C170.933 20.5 172.5 22.067 172.5 24V143.5H27.5V24C27.5 22.067 29.067 20.5 31 20.5Z" stroke="#E7EAF3"/>
</g>
<rect x="41" y="34" width="22" height="3" rx="1.5" fill="#71869D"/>
<rect x="41" y="51" width="10" height="2" rx="1" fill="##377dff"/>
<rect x="46" y="61" width="49" height="1" rx="0.5" fill="##377dff"/>
<rect x="105" y="51" width="10" height="2" rx="1" fill="#D9DDEA"/>
<rect x="144" y="51" width="9" height="2" rx="1" fill="#D9DDEA"/>
<rect x="41.5" y="56.5" width="58" height="10" rx="1.5" stroke="##377dff"/>
<rect x="105.5" y="56.5" width="33" height="10" rx="1.5" stroke="#D9DDEA"/>
<rect x="144.5" y="56.5" width="14" height="10" rx="1.5" stroke="#D9DDEA"/>
<rect x="144" y="95" width="9" height="2" rx="1" fill="#D9DDEA"/>
<rect x="144.5" y="100.5" width="14" height="10" rx="1.5" stroke="#D9DDEA"/>
<rect x="41" y="73" width="10" height="2" rx="1" fill="#D9DDEA"/>
<rect x="41" y="95" width="10" height="2" rx="1" fill="#D9DDEA"/>
<rect x="121" y="73" width="9" height="2" rx="1" fill="#D9DDEA"/>
<rect x="41.5" y="78.5" width="74" height="10" rx="1.5" stroke="#D9DDEA"/>
<rect x="121.5" y="78.5" width="37" height="10" rx="1.5" stroke="#D9DDEA"/>
<rect x="41.5" y="100.5" width="97" height="10" rx="1.5" stroke="#D9DDEA"/>
<path d="M0 104H200V140C200 142.209 198.209 144 196 144H4C1.79086 144 0 142.209 0 140V104Z" fill="url(#paint0_linear)"/>
</g>
<defs>
<filter id="filter0_d" x="22" y="18" width="156" height="134" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="3"/>
<feGaussianBlur stdDeviation="2.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.596078 0 0 0 0 0.643137 0 0 0 0.125 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="100" y1="154" x2="100" y2="92.5" gradientUnits="userSpaceOnUse">
<stop offset="0.256387" stop-color="#F3F4F6"/>
<stop offset="0.749415" stop-color="#F3F4F6" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0">
<rect width="200" height="144" fill="white"/>
</clipPath>
</defs>
</svg>
