<svg width="200" height="144" viewBox="0 0 200 144" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0)">
<path d="M196 0H4C1.79086 0 0 1.79086 0 4V140C0 142.209 1.79086 144 4 144H196C198.209 144 200 142.209 200 140V4C200 1.79086 198.209 0 196 0Z" fill="#F3F4F6"/>
<g filter="url(#filter0_d)">
<path d="M27 24C27 21.7909 28.7909 20 31 20H169C171.209 20 173 21.7909 173 24V144H27V24Z" fill="white"/>
<path d="M31 20.5H169C170.933 20.5 172.5 22.067 172.5 24V143.5H27.5V24C27.5 22.067 29.067 20.5 31 20.5Z" stroke="#E7EAF3"/>
</g>
<rect x="66" y="43" width="18" height="3" rx="1.5" fill="#D9DDEA"/>
<rect x="37" y="57" width="15" height="2" rx="1" fill="#D9DDEA"/>
<rect x="37" y="79" width="15" height="2" rx="1" fill="#D9DDEA"/>
<rect x="103" y="57" width="15" height="2" rx="1" fill="#377dff"/>
<rect x="127" y="79" width="15" height="2" rx="1" fill="#D9DDEA"/>
<g filter="url(#filter1_d)">
<rect x="142" y="106" width="21" height="9" rx="2" fill="#377dff"/>
</g>
<g filter="url(#filter2_d)">
<rect x="37" y="106" width="21" height="9" rx="2" fill="white"/>
<rect x="37.5" y="106.5" width="20" height="8" rx="1.5" stroke="#D9DDEA"/>
</g>
<rect x="37.5" y="62.5" width="59" height="10" rx="1.5" stroke="#D9DDEA"/>
<rect x="37.5" y="84.5" width="83" height="10" rx="1.5" stroke="#D9DDEA"/>
<rect x="103.5" y="62.5" width="59" height="10" rx="1.5" stroke="#377dff"/>
<rect x="127.5" y="84.5" width="35" height="10" rx="1.5" stroke="#D9DDEA"/>
<rect x="148" y="110" width="9" height="1" rx="0.5" fill="white"/>
<rect x="43" y="110" width="9" height="1" rx="0.5" fill="#D9DDEA"/>
<rect x="109" y="67" width="48" height="1" rx="0.5" fill="#377dff"/>
<rect x="88" y="43" width="21" height="3" rx="1.5" fill="#D9DDEA"/>
<rect x="113" y="43" width="21" height="3" rx="1.5" fill="#D9DDEA"/>
<path d="M0 104H200V140C200 142.209 198.209 144 196 144H4C1.79086 144 0 142.209 0 140V104Z" fill="url(#paint0_linear)"/>
<path d="M81.1005 33.6268C80.9438 32.4957 80.0738 31.8395 78.9453 31.8395C77.6154 31.8395 76.6136 32.8089 76.6136 34.4545C76.6136 36.0977 77.6029 37.0696 78.9453 37.0696C80.1559 37.0696 80.9588 36.2866 81.1005 35.3146L80.1708 35.3097C80.0539 35.9062 79.5667 36.2418 78.9577 36.2418C78.1324 36.2418 77.5408 35.6229 77.5408 34.4545C77.5408 33.3061 78.1274 32.6673 78.9602 32.6673C79.5791 32.6673 80.0639 33.0178 80.1708 33.6268H81.1005ZM83.5707 37.0746C84.6893 37.0746 85.4003 36.2866 85.4003 35.1058C85.4003 33.9226 84.6893 33.1321 83.5707 33.1321C82.4521 33.1321 81.7412 33.9226 81.7412 35.1058C81.7412 36.2866 82.4521 37.0746 83.5707 37.0746ZM83.5757 36.3537C82.9567 36.3537 82.6535 35.8018 82.6535 35.1033C82.6535 34.4048 82.9567 33.8455 83.5757 33.8455C84.1847 33.8455 84.488 34.4048 84.488 35.1033C84.488 35.8018 84.1847 36.3537 83.5757 36.3537ZM87.0639 34.7628C87.0639 34.2109 87.397 33.8928 87.8718 33.8928C88.3366 33.8928 88.615 34.1985 88.615 34.7081V37H89.5149V34.5689C89.5174 33.6541 88.9953 33.1321 88.2073 33.1321C87.6356 33.1321 87.2429 33.4055 87.0688 33.8306H87.0241V33.1818H86.164V37H87.0639V34.7628ZM92.3362 33.1818H91.5831V32.267H90.6832V33.1818H90.1413V33.8778H90.6832V36.0007C90.6782 36.7191 91.2002 37.0721 91.8764 37.0522C92.1324 37.0447 92.3089 36.995 92.4059 36.9627L92.2542 36.2592C92.2045 36.2717 92.1026 36.294 91.9907 36.294C91.7645 36.294 91.5831 36.2145 91.5831 35.8516V33.8778H92.3362V33.1818ZM94.1875 37.0771C94.7866 37.0771 95.1446 36.7962 95.3086 36.4755H95.3385V37H96.2035V34.4446C96.2035 33.4354 95.3807 33.1321 94.6524 33.1321C93.8495 33.1321 93.233 33.4901 93.0341 34.1861L93.8743 34.3054C93.9638 34.0444 94.2174 33.8207 94.6574 33.8207C95.075 33.8207 95.3037 34.0344 95.3037 34.4098V34.4247C95.3037 34.6832 95.0327 34.6957 94.3591 34.7678C93.6183 34.8473 92.9098 35.0685 92.9098 35.9286C92.9098 36.6793 93.4592 37.0771 94.1875 37.0771ZM94.4212 36.4158C94.0459 36.4158 93.7774 36.2443 93.7774 35.9137C93.7774 35.5682 94.0782 35.424 94.4809 35.3668C94.717 35.3345 95.1893 35.2749 95.3062 35.1804V35.6303C95.3062 36.0554 94.9631 36.4158 94.4212 36.4158ZM98.7738 37.0746C99.7334 37.0746 100.345 36.5053 100.409 35.695H99.5494C99.4723 36.1051 99.1765 36.3413 98.7813 36.3413C98.2195 36.3413 97.8566 35.8714 97.8566 35.0909C97.8566 34.3203 98.227 33.858 98.7813 33.858C99.2138 33.858 99.4798 34.1364 99.5494 34.5043H100.409C100.347 33.6765 99.701 33.1321 98.7689 33.1321C97.6503 33.1321 96.9443 33.94 96.9443 35.1058C96.9443 36.2617 97.6329 37.0746 98.7738 37.0746ZM103.055 33.1818H102.302V32.267H101.402V33.1818H100.86V33.8778H101.402V36.0007C101.397 36.7191 101.919 37.0721 102.595 37.0522C102.851 37.0447 103.028 36.995 103.125 36.9627L102.973 36.2592C102.923 36.2717 102.821 36.294 102.709 36.294C102.483 36.294 102.302 36.2145 102.302 35.8516V33.8778H103.055V33.1818ZM108.562 34.1911C108.437 33.5447 107.92 33.1321 107.025 33.1321C106.106 33.1321 105.479 33.5845 105.482 34.2905C105.479 34.8473 105.822 35.2152 106.556 35.3668L107.207 35.5036C107.557 35.5806 107.721 35.7223 107.721 35.9386C107.721 36.1996 107.438 36.396 107.011 36.396C106.598 36.396 106.329 36.217 106.252 35.8739L105.375 35.9585C105.487 36.6594 106.076 37.0746 107.013 37.0746C107.968 37.0746 108.641 36.5799 108.644 35.8565C108.641 35.3121 108.291 34.979 107.57 34.8224L106.919 34.6832C106.531 34.5962 106.377 34.462 106.379 34.2408C106.377 33.9822 106.663 33.8033 107.038 33.8033C107.453 33.8033 107.672 34.0295 107.741 34.2805L108.562 34.1911ZM110.484 37.0771C111.083 37.0771 111.441 36.7962 111.606 36.4755H111.635V37H112.5V34.4446C112.5 33.4354 111.678 33.1321 110.949 33.1321C110.146 33.1321 109.53 33.4901 109.331 34.1861L110.171 34.3054C110.261 34.0444 110.514 33.8207 110.954 33.8207C111.372 33.8207 111.601 34.0344 111.601 34.4098V34.4247C111.601 34.6832 111.33 34.6957 110.656 34.7678C109.915 34.8473 109.207 35.0685 109.207 35.9286C109.207 36.6793 109.756 37.0771 110.484 37.0771ZM110.718 36.4158C110.343 36.4158 110.074 36.2443 110.074 35.9137C110.074 35.5682 110.375 35.424 110.778 35.3668C111.014 35.3345 111.486 35.2749 111.603 35.1804V35.6303C111.603 36.0554 111.26 36.4158 110.718 36.4158ZM114.305 31.9091H113.405V37H114.305V31.9091ZM116.921 37.0746C117.811 37.0746 118.422 36.6396 118.581 35.9759L117.741 35.8814C117.619 36.2045 117.321 36.3736 116.933 36.3736C116.352 36.3736 115.966 35.9908 115.959 35.337H118.619V35.0611C118.619 33.7212 117.813 33.1321 116.874 33.1321C115.78 33.1321 115.066 33.935 115.066 35.1133C115.066 36.3114 115.77 37.0746 116.921 37.0746ZM115.961 34.7305C115.989 34.2433 116.349 33.8331 116.886 33.8331C117.403 33.8331 117.751 34.2109 117.756 34.7305H115.961ZM122.398 34.1911C122.273 33.5447 121.756 33.1321 120.861 33.1321C119.942 33.1321 119.315 33.5845 119.318 34.2905C119.315 34.8473 119.658 35.2152 120.392 35.3668L121.043 35.5036C121.393 35.5806 121.557 35.7223 121.557 35.9386C121.557 36.1996 121.274 36.396 120.846 36.396C120.434 36.396 120.165 36.217 120.088 35.8739L119.211 35.9585C119.323 36.6594 119.912 37.0746 120.849 37.0746C121.803 37.0746 122.477 36.5799 122.48 35.8565C122.477 35.3121 122.127 34.979 121.406 34.8224L120.754 34.6832C120.367 34.5962 120.213 34.462 120.215 34.2408C120.213 33.9822 120.498 33.8033 120.874 33.8033C121.289 33.8033 121.508 34.0295 121.577 34.2805L122.398 34.1911Z" fill="#555A60"/>
</g>
<defs>
<filter id="filter0_d" x="22" y="18" width="156" height="134" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="3"/>
<feGaussianBlur stdDeviation="2.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.596078 0 0 0 0 0.643137 0 0 0 0.125 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter1_d" x="140" y="105" width="25" height="13" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.215686 0 0 0 0 0.490196 0 0 0 0 1 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter2_d" x="35" y="105" width="25" height="13" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.596078 0 0 0 0 0.643137 0 0 0 0.18 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="100" y1="154" x2="100" y2="92.5" gradientUnits="userSpaceOnUse">
<stop offset="0.256387" stop-color="#F3F4F6"/>
<stop offset="0.749415" stop-color="#F3F4F6" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0">
<rect width="200" height="144" fill="white"/>
</clipPath>
</defs>
</svg>
