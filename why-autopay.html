<!DOCTYPE html>
<html lang="en" dir="">

<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

  <!-- Title -->
  <title>Tại sao chọn SePay?</title>

  <!-- Favicon -->
  <link rel="shortcut icon" href="./assets/img/logo/favicon.png" type="image/x-icon" />

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">

  <!-- CSS Implementing Plugins -->
  <link rel="stylesheet" href="./assets/vendor/bootstrap-icons/font/bootstrap-icons.css">
  <link rel="stylesheet" href="./assets/vendor/hs-mega-menu/dist/hs-mega-menu.min.css">
  <link rel="stylesheet" href="./assets/vendor/aos/dist/aos.css">

  <!-- CSS Front Template -->
  <link rel="stylesheet" href="./assets/css/theme.min.css">

<!-- meta tag -->
<meta property="og:locale" content="vi_VN" />
<link rel="canonical" href="https://sepay.vn/why-autopay.html" />
<meta name="description" content="SePay giúp giảm workload kế toán, giảm chi phí cổng thanh toán, tăng trải nghiệm mua sắm cho khách hàng."/>
<meta property="og:type" content="article" />
<meta property="og:url" content="https://sepay.vn/why-autopay.html" />
<meta property="og:title" content="Tại sao chọn SePay?" />
<meta property="og:description" content="SePay giúp giảm workload kế toán, giảm chi phí cổng thanh toán, tăng trải nghiệm mua sắm cho khách hàng." />

<meta property="og:site_name" content="Tại sao chọn SePay?" />
<meta property="og:image" content="https://sepay.vn/assets/img/others/autopay-dashboard.png" />
<meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/autopay-dashboard.png" />
<!-- meta tag -->

</head>

<body>
  <!-- ========== HEADER ========== -->
  <header id="header" class="navbar navbar-expand-lg navbar-end navbar-absolute-top navbar-light navbar-show-hide navbar-scrolled"
    data-hs-header-options='{
            "fixMoment": 1000,
            "fixEffect": "slide"
          }'>


    <div class="container">
      <nav class="js-mega-menu navbar-nav-wrap">
        <!-- Default Logo -->
        <a class="navbar-brand" href="https://sepay.vn" aria-label="Front">
          <img class="navbar-brand-logo" src="./assets/img/logo/autopay-blue.svg" alt="Logo">
        </a>
        <!-- End Default Logo -->

        <!-- Toggler -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown"
          aria-controls="navbarNavDropdown" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-default">
            <i class="bi-list"></i>
          </span>
          <span class="navbar-toggler-toggled">
            <i class="bi-x"></i>
          </span>
        </button>
        <!-- End Toggler -->

        <!-- Collapse -->
        <div class="collapse navbar-collapse" id="navbarNavDropdown">
          <div class="navbar-absolute-top-scroller">
            <ul class="navbar-nav">
              <!-- Home page -->
              <li class="nav-item">
                <a id="" class="nav-link" href="https://sepay.vn">Trang chủ</a>


              </li>
              <!-- End home page -->

              <!-- Features -->
              <li class="hs-has-sub-menu nav-item">
                <a id="coursesMegaMenu" class="hs-mega-menu-invoker nav-link dropdown-toggle" href="#" role="button"> Tính năng</a>

                <!-- Mega Menu -->
                <div class="hs-sub-menu dropdown-menu" aria-labelledby="coursesMegaMenu" style="min-width: 20rem;">


                  <a class="navbar-dropdown-menu-media-link" href="chia-se-bien-dong-so-du.html">
                    <div class="d-flex">
                      <div class="flex-shrink-0">
                        <span class="svg-icon svg-icon-sm text-primary">
                          <span class="svg-icon svg-icon-sm text-primary">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M22.1671 18.1421C22.4827 18.4577 23.0222 18.2331 23.0206 17.7868L23.0039 13.1053V5.52632C23.0039 4.13107 21.8729 3 20.4776 3H8.68815C7.2929 3 6.16183 4.13107 6.16183 5.52632V9H13C14.6568 9 16 10.3431 16 12V15.6316H19.6565L22.1671 18.1421Z"
                                fill="#035A4B" />
                              <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd"
                                d="M1.98508 18V13C1.98508 11.8954 2.88051 11 3.98508 11H11.9851C13.0896 11 13.9851 11.8954 13.9851 13V18C13.9851 19.1046 13.0896 20 11.9851 20H4.10081L2.85695 21.1905C2.53895 21.4949 2.01123 21.2695 2.01123 20.8293V18.3243C1.99402 18.2187 1.98508 18.1104 1.98508 18ZM5.99999 14.5C5.99999 14.2239 6.22385 14 6.49999 14H11.5C11.7761 14 12 14.2239 12 14.5C12 14.7761 11.7761 15 11.5 15H6.49999C6.22385 15 5.99999 14.7761 5.99999 14.5ZM9.49999 16C9.22385 16 8.99999 16.2239 8.99999 16.5C8.99999 16.7761 9.22385 17 9.49999 17H11.5C11.7761 17 12 16.7761 12 16.5C12 16.2239 11.7761 16 11.5 16H9.49999Z"
                                fill="#035A4B" />
                            </svg>

                          </span>

                        </span>
                      </div>

                      <div class="flex-grow-1 ms-3">
                        <span class="navbar-dropdown-menu-media-title">Chia sẻ biến động số dư</span>
                        <p class="navbar-dropdown-menu-media-desc">Báo lên group Telegram khi nhận chuyển khoản</p>
                      </div>
                    </div>
                  </a>

                  <div class="dropdown-divider"></div>

                  <a class="navbar-dropdown-menu-media-link" href="xac-thuc-thanh-toan.html">
                    <div class="d-flex">
                      <div class="flex-shrink-0">
                        <span class="svg-icon svg-icon-sm text-primary">
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path opacity="0.3" d="M18 10V20C18 20.6 18.4 21 19 21C19.6 21 20 20.6 20 20V10H18Z"
                              fill="#035A4B" />
                            <path opacity="0.3"
                              d="M11 10V17H6V10H4V20C4 20.6 4.4 21 5 21H12C12.6 21 13 20.6 13 20V10H11Z"
                              fill="#035A4B" />
                            <path opacity="0.3" d="M10 10C10 11.1 9.1 12 8 12C6.9 12 6 11.1 6 10H10Z" fill="#035A4B" />
                            <path opacity="0.3" d="M18 10C18 11.1 17.1 12 16 12C14.9 12 14 11.1 14 10H18Z"
                              fill="#035A4B" />
                            <path opacity="0.3" d="M14 4H10V10H14V4Z" fill="#035A4B" />
                            <path opacity="0.3" d="M17 4H20L22 10H18L17 4Z" fill="#035A4B" />
                            <path opacity="0.3" d="M7 4H4L2 10H6L7 4Z" fill="#035A4B" />
                            <path
                              d="M6 10C6 11.1 5.1 12 4 12C2.9 12 2 11.1 2 10H6ZM10 10C10 11.1 10.9 12 12 12C13.1 12 14 11.1 14 10H10ZM18 10C18 11.1 18.9 12 20 12C21.1 12 22 11.1 22 10H18ZM19 2H5C4.4 2 4 2.4 4 3V4H20V3C20 2.4 19.6 2 19 2ZM12 17C12 16.4 11.6 16 11 16H6C5.4 16 5 16.4 5 17C5 17.6 5.4 18 6 18H11C11.6 18 12 17.6 12 17Z"
                              fill="#035A4B" />
                          </svg>

                        </span>
                      </div>

                      <div class="flex-grow-1 ms-3">
                        <span class="navbar-dropdown-menu-media-title">Xác thực thanh toán</span>
                        <p class="navbar-dropdown-menu-media-desc">Tự động xác nhận thanh toán khi khách hàng chuyển
                          tiền</p>
                      </div>
                    </div>
                  </a>

                  <div class="dropdown-divider"></div>

                  <a class="navbar-dropdown-menu-media-link" href="thong-ke-dong-tien.html">
                    <div class="d-flex">
                      <div class="flex-shrink-0">
                        <span class="svg-icon svg-icon-sm text-primary">
                          <span class="svg-icon svg-icon-sm text-primary">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd"
                                d="M12 17C16.4183 17 20 13.4183 20 9C20 4.58172 16.4183 1 12 1C7.58172 1 4 4.58172 4 9C4 13.4183 7.58172 17 12 17Z"
                                fill="#035A4B" />
                              <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd"
                                d="M6.53819 9L10.568 19.3624L11.976 18.8149L13.3745 19.3674L17.4703 9H6.53819ZM9.46188 11H14.5298L11.9759 17.4645L9.46188 11Z"
                                fill="#035A4B" />
                              <path opacity="0.3"
                                d="M10 22H14V22C14 23.1046 13.1046 24 12 24V24C10.8954 24 10 23.1046 10 22V22Z"
                                fill="#035A4B" />
                              <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M8 17C8 16.4477 8.44772 16 9 16H15C15.5523 16 16 16.4477 16 17C16 17.5523 15.5523 18 15 18C15.5523 18 16 18.4477 16 19C16 19.5523 15.5523 20 15 20C15.5523 20 16 20.4477 16 21C16 21.5523 15.5523 22 15 22H9C8.44772 22 8 21.5523 8 21C8 20.4477 8.44772 20 9 20C8.44772 20 8 19.5523 8 19C8 18.4477 8.44772 18 9 18C8.44772 18 8 17.5523 8 17Z"
                                fill="#035A4B" />
                            </svg>

                          </span>

                        </span>
                      </div>

                      <div class="flex-grow-1 ms-3">
                        <span class="navbar-dropdown-menu-media-title">Thống kê dòng tiền</span>
                        <p class="navbar-dropdown-menu-media-desc">Nắm bắt được dòng tiền tất cả tài khoản</p>
                      </div>
                    </div>
                  </a>

                </div>

              </li>
              <!-- End Features -->




              <li class="nav-item">
                <a class="nav-link" href="why-autopay.html">Why SePay?</a>
              </li>

            

              <li class="nav-item">
                <a class="nav-link" href="bang-gia.html">Bảng giá</a>
              </li>
             
 
              <!-- Button -->
              <li class="nav-item mt-5 mt-lg-0">
                <a class="btn btn-outline-dark btn-transition" href="https://my.sepay.vn">Đăng nhập</a>
                <a class="btn btn-primary btn-transition"
                  href="#"
                  target="_blank">Đăng ký</a>
              </li>
              <!-- End Button -->
            </ul>
          </div>
        </div>
        <!-- End Collapse -->
      </nav>
    </div>
  </header>

  <!-- ========== END HEADER ========== -->

  <!-- ========== MAIN CONTENT ========== -->
  <main id="content" role="main">
    <!-- Hero -->
   <!-- <div class="container content-space-t-4 content-space-t-lg-5 content-space-b-2 content-space-b-lg-3">--> 
    <div class="position-relative overflow-hidden">
      <div class="container content-space-t-4 content-space-b-lg-1">
      <div class="row justify-content-lg-between align-items-lg-center mb-10">
        <div class="col-md-6 col-lg-5">
          <!-- Heading -->
          <div class="mb-5">
            <h1>Tại sao chọn SePay?</h1>
            <p>SePay giúp bạn giảm workload kế toán, giảm chi phí cổng thanh toán, tăng trải nghiệm khách hàng.</p>
          </div>
          <!-- End Title & Description -->

          <div class="d-grid d-sm-flex gap-3">
            <a class="btn btn-primary btn-transition" href="bang-gia.html">Đăng ký ngay</a>
            <a class="btn btn-link" href="lien-he.html#contact">Liên hệ tư vấn <i class="bi-chevron-right small ms-1"></i></a>
          </div>
        </div>
        <!-- End Col -->

        <div class="col-sm-7 col-md-6 d-none d-md-block">
          <img class="img-fluid" src="./assets/svg/illustrations/oc-building-apps.svg" alt="Image Description">
        </div>
        <!-- End Col -->
      </div>
      <!-- End Row -->
    </div>
 
    </div>
    <!-- End Hero -->

 
    <!-- Features -->
    <div class="container content-space-2 content-space-lg-3">
        <div class="row align-items-md-center">
          <div class="col-md-7 order-md-2 mb-7 mb-md-0">
            <div class="w-sm-75 mx-sm-auto" data-aos="fade-up">
              <!-- Card -->
              <div class="card bg-soft-success text-center">
               
                <div class="w-75 mx-auto">
                  <img class="img-fluid rounded-top-2" src="./assets/img/others/autopay-telegram2.jpg?v=2" alt="Image Description">
                </div>
              </div>
              <!-- End Card -->
            </div>
          </div>
          <!-- End Col -->
  
          <div class="col-md-5">
            <!-- Heading -->
            <div class="mb-5">
              <h2 class="mb-3">Giảm workload kế toán</h2>
              <p>Nhân viên bán hàng của bạn hoàn toàn có thể kiểm tra thanh toán mà không cần kế toán. Giao dịch ghi có của ngân hàng được báo lên nhóm Telegram một cách tự động.</p>
            </div>
            <!-- End Heading -->
  
            <!-- List Checked -->
            <ul class="list-checked list-checked-soft-bg-primary list-checked-lg">
              <li class="list-checked-item">Tối ưu chi phí nhân sự</li>
              <li class="list-checked-item">Tăng thời gian kiểm tra giao dịch</li>
              <li class="list-checked-item">Không phụ thuộc vào internet banking</li>
            </ul>
            <!-- End List Checked -->
          </div>
          <!-- End Col -->
        </div>
        <!-- End Row -->
      </div>
      <!-- End Features -->
    

       <!-- Features -->
    <div class="container content-space-t-2 content-space-b-2 content-space-b-lg-3">
        <div class="row align-items-md-center">
          <div class="col-md-7 mb-7 mb-md-0">
            <div class="w-sm-75 mx-sm-auto" data-aos="fade-up">
              <!-- Card -->
              <div class="card bg-soft-primary text-center">
                
  
                <div class="w-75 mx-auto">
                  <img class="img-fluid rounded-top-2" src="./assets/svg/illustrations/pro-account.svg" alt="Image Description">
                </div>
              </div>
              <!-- End Card -->
            </div>
          </div>
          <!-- End Col -->
  
          <div class="col-md-5 order-md-2">
            <!-- Heading -->
            <div class="mb-5">
              <h2 class="mb-3">Giảm chi phí cho cổng thanh toán</h2>
              <p>Nhận giao dịch qua chuyển khoản ngân hàng không tốn phí. Tiền vào ngay lập tức và bạn không cần phải trả chi phí cho cổng thanh toán.</p>
            </div>
            <!-- End Heading -->
  
            <!-- List Checked -->
            <ul class="list-checked list-checked-soft-bg-primary list-checked-lg">
              <li class="list-checked-item">Chuyển khoản nhanh bằng QR Code</li>
              <li class="list-checked-item">Nhận tiền ngay sau 10 giây</li>
              <li class="list-checked-item">Không mất phí cả người mua lẫn người bán</li>
            </ul>
            <!-- End List Checked -->
          </div>
          <!-- End Col -->
        </div>
        <!-- End Row -->
      </div>
      <!-- End Features -->
  

    <!-- Features -->
    <div class="container content-space-2 content-space-lg-3">
        <div class="row align-items-md-center">
          <div class="col-md-7 order-md-2 mb-7 mb-md-0">
            <div class="position-relative mx-auto" style="max-width: 20rem;">
                <!-- Mobile Device -->
                <figure class="device-mobile mx-auto">
                  <div class="device-mobile-frame">
                    <img class="device-mobile-img" src="./assets/img/others/autopay-qrcode.jpg?v=1" alt="Image Description">
                  </div>
                </figure>
                <!-- End Mobile Device -->
                  
              
  
                <!-- Image -->
                <div class="position-absolute bottom-0 start-0 zi-2 ms-n10 mb-10" style="width: 16rem;">
                  <img class="img-fluid shadow-lg rounded-2" src="./assets/img/others/qrcode-pay.png" alt="Image Description">
                </div>
                <!-- End Image -->
  
                <!-- SVG Shape -->
                <div class="position-absolute bottom-0 end-0 zi-n1 mx-auto" style="width: 20rem;">
                  <img class="img-fluid" src="./assets/svg/components/shape-1.svg" alt="SVG">
                </div>
                <!-- End SVG Shape -->
              </div>
          </div>
          <!-- End Col -->
  
          <div class="col-md-5">
            <!-- Heading -->
            <div class="mb-5">
              <h2 class="mb-3">Tăng trải nghiệm khách hàng</h2>
              <p>Khách hàng quét QR code để thanh toán, tất cả thông tin chuyển khoản tự động điền, bấm tiếp tục và hoàn tất thanh toán, rất nhanh và tiện lợi</p>
            </div>
            <!-- End Heading -->
  
            <!-- List Checked -->
            <ul class="list-checked list-checked-soft-bg-primary list-checked-lg">
              <li class="list-checked-item">Thanh toán trong 10 giây</li>
              <li class="list-checked-item">Xác nhận thanh toán trong 5 giây</li>
              <li class="list-checked-item">Hoàn toàn tự động</li>
            </ul>
            <!-- End List Checked -->
          </div>
          <!-- End Col -->
        </div>
        <!-- End Row -->
      </div>
      <!-- End Features -->
 

    <!-- Testimonials -->
    <div class="container content-space-2 content-space-lg-3">
      <div class="text-center mb-5">
        <img class="avatar avatar-lg avatar-4x3" src="./assets/svg/illustrations/oc-person-2.svg" alt="Illustration">
      </div>
      
      <!-- Blockquote -->
      <figure class="w-md-75 text-center mx-md-auto">
        <blockquote class="blockquote">“Bán một cái laptop 20 triệu, cổng thanh toán thu ngay 200.000đ tiền phí, quá cao!”</blockquote>

        <figcaption class="blockquote-footer text-center">
          Mr. Đạt Nguyễn
          <span class="blockquote-footer-source">CEO | Di Động Việt</span>
        </figcaption>
      </figure>
      <!-- End Blockquote -->
    </div>
    <!-- End Testimonials -->

    <!-- Contacts -->
    <div class="position-relative" id="contact">
      <div class="bg-dark" style="background-image: url(./assets/svg/components/wave-pattern-light.svg);">
        <div class="container content-space-t-2 content-space-t-lg-3 content-space-b-1">
          <!-- Heading -->
          <div class="w-lg-50 text-center mx-lg-auto mb-7">
            <span class="text-cap text-white-70">Liên hệ</span>
            <h2 class="text-white lh-base">Chúng tôi luôn sẵn sàng để tư vấn bạn <span class="text-warning">Liên hệ ngay.</span></h2>
          </div>
          <!-- End Heading -->

          <div class="mx-auto" style="max-width: 35rem;">
            <!-- Card -->
            <div class="card zi-2">
              <div class="card-body">
                <!-- Form -->
                <form>
                  <div class="row">
                    <div class="col-sm-6">
                      <!-- Form -->
                      <div class="mb-4">
                        <label class="form-label" for="hireUsFormFirstName">Tên</label>
                        <input type="text" class="form-control form-control-lg" name="hireUsFormNameFirstName" id="hireUsFormFirstName" placeholder="First name" aria-label="First name">
                      </div>
                      <!-- End Form -->
                    </div>
                    <!-- End Col -->

                    <div class="col-sm-6">
                      <!-- Form -->
                      <div class="mb-4">
                        <label class="form-label" for="hireUsFormLasttName">Họ và đệm</label>
                        <input type="text" class="form-control form-control-lg" name="hireUsFormNameLastName" id="hireUsFormLasttName" placeholder="Last name" aria-label="Last name">
                      </div>
                      <!-- End Form -->
                    </div>
                    <!-- End Col -->
                  </div>
                  <!-- End Row -->

                  <div class="mb-3">
                    <label class="form-label" for="signupModalFormLoginPhone">Số điện thoại</label>
                    <input type="number" class="form-control form-control-lg" name="phone" id="signupModalFormLoginPhone"
                        placeholder="0909999999" required>
                    <span class="invalid-feedback">Please enter a valid phone number.</span>
                </div>

                  <!-- Form -->
                  <div class="mb-4">
                    <label class="form-label" for="hireUsFormWorkEmail">Email</label>
                    <input type="email" class="form-control form-control-lg" name="hireUsFormNameWorkEmail" id="hireUsFormWorkEmail" placeholder="<EMAIL>" aria-label="<EMAIL>">
                  </div>
                  <!-- End Form -->

                  <!-- Form -->
                  <div class="mb-4">
                    <label class="form-label" for="hireUsFormCompanyName">Tên công ty <span class="form-label-secondary">(Optional)</span></label>
                    <input type="text" class="form-control form-control-lg" name="hireUsFormNameCompanyName" id="hireUsFormCompanyName" placeholder="" aria-label="Htmlstream">
                  </div>
                  <!-- End Form -->
 

                  <!-- Form -->
                  <div class="mb-4">
                    <label class="form-label" for="hireUsFormDetails">Chi tiết yêu cầu</label>
                    <textarea class="form-control form-control-lg" name="hireUsFormNameDetails" id="hireUsFormDetails" placeholder="Tell us about your requirement" aria-label="Tell us about your requirement" rows="4"></textarea>
                  </div>
                  <!-- End Form -->

                

                  <div class="d-grid mb-2">
                    <button type="submit" class="btn btn-primary btn-lg">Gửi đi</button>
                  </div>

                
                </form>
                <!-- End Form -->
              </div>
            </div>
            <!-- End Card -->
          </div>
        </div>
      </div>

      <!-- Shape -->
      <div class="shape shape-bottom zi-1">
        <svg width="3000" height="500" viewBox="0 0 3000 500" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M0 500H3000V0L0 500Z" fill="#fff"/>
        </svg>
      </div>
      <!-- End Shape -->
    </div>
    <!-- End Contacts -->
  </main>
  <!-- ========== END MAIN CONTENT ========== -->


   <!-- ========== FOOTER ========== -->
   <footer class="container content-space-2 content-space-t-lg-3">
    <!-- Links -->
    <div class="row mb-5">
      <div class="col-md-3 mb-5 mb-md-0">
        <!-- Logo -->
        <div class="mb-3">
          <a class="navbar-brand" href="./index.html" aria-label="Space">
            <img class="navbar-brand-logo" src="./assets/img/logo/sepay-blue-154x50.png" alt="Image Description">
          </a>
        </div>
        <!-- End Logo -->

        <!-- List -->
        <ul class="list-unstyled list-py-1">
          <li><b>Công Ty Cổ Phần SePay</b></li>
          <li><a class="link-sm link-secondary" href="https://goo.gl/maps/HGZVhsbv3NhR7kan8" target="_blank"><i
                class="bi-geo-alt-fill me-1"></i> 168 Đường số 2, Vạn Phúc City, P. Hiệp Bình Phước, Thủ Đức, TP. Hồ Chí
              Minh, Việt Nam</a></li>
          <li><a class="link-sm link-secondary" href="tel:02873059589"><i class="bi-telephone-inbound-fill me-1"></i>
              02873.059.589</a></li>
          <li><span class="text-secondary fs-6"><i class="bi bi-envelope-fill me-2"></i> <EMAIL></span></li>
          <li><span class="text-secondary fs-6">MST: 0317887567, cấp ngày 19/06/2023 tại Sở Kế hoạch và Đầu tư
              TPHCM</span></li>

        </ul>
        <!-- End List -->
        <div class="col-sm-auto">
          <!-- Socials -->
          <ul class="list-inline mb-0">
            <li class="list-inline-item">
              <a class="btn btn-soft-secondary btn-xs btn-icon" href="https://www.facebook.com/sepay.vn">
                <i class="bi-facebook"></i>
              </a>
            </li>
            <li class="list-inline-item">
              <a class="btn btn-soft-secondary btn-xs btn-icon" href="https://www.youtube.com/@SePayVN">
                <i class="bi-youtube"></i>
              </a>
            </li>
            <li class="list-inline-item">
              <a class="btn btn-soft-secondary btn-xs btn-icon" href="https://www.pinterest.com/sepayvn/">
                <i class="bi-pinterest"></i>
              </a>
            </li>




          </ul>
          <!-- End Socials -->
        </div>
      </div>

      <div class="col-sm-2 col-md-2 col-6 mb-3 mb-sm-0">
        <h5 class=" mb-3">THÔNG TIN</h5>

        <!-- List -->
        <ul class="list-unstyled list-py-1">
          <li><a class="text-body" href="gioi-thieu.html">Giới thiệu</a></li>
          <li><a class="text-body" href="bang-gia.html">Bảng giá</a></li>

          <li><a class="text-body" href="https://sepay.vn/blog">Blog</a></li>
          <li><a class="text-body" href="lien-he.html">Liên hệ</a></li>
          <li><a class="text-body" target="_blank" href="https://docs.sepay.vn/tich-hop-webhooks.html">WebHooks</a></li>

          <li><a class="text-body" target="_blank" href="https://docs.sepay.vn/gioi-thieu-api.html">API</a></li>

          <li><a class="text-body" href="faq.html">FAQ</a></li>
          <li><a class="text-body" href="changelog.html">Changelog</a></li>

        </ul>
        <div><img loading="lazy" src="/assets/img/others/RapidSSL-SEAL.gif" class="img-fluid"> <a
            href="https://secure.sitelock.com/public/verify/sepay.vn" target="_blank" rel=nofollow><img
              src="https://shield.sitelock.com/shield/sepay.vn" loading="lazy" class="img-fluid"
              style="max-width: 90px;"></a> <a href="http://online.gov.vn/Home/WebDetails/107586" target="_blank"
            rel=nofollow><img src="https://sepay.vn/assets/img/others/ttbct.png" loading="lazy" class="img-fluid"
              style="max-width: 150px;"></a></div>
        <!-- End List -->
      </div>

      <div class="col-sm-3 col-md-3 col-6 mb-3 mb-sm-0">
        <h5 class=" mb-3">HƯỚNG DẪN & TÍNH NĂNG</h5>

        <!-- List -->
        <ul class="list-unstyled list-py-1">
          <li>
            <a class="text-body" href="https://docs.sepay.vn/">
              Hướng dẫn sử dụng
            </a>
          </li>
          <li><a class="text-body" href="thanh-toan.html">Hướng dẫn thanh toán</a></li>
          <li><a class="text-body" href="terms-of-service.html">Quy định sử dụng</a></li>
          <li><a class="text-body" href="hoan-tien.html">Quy định hoàn tiền</a></li>
          <li><a class="text-body" href="privacy.html">Chính sách bảo mật</a></li>
          <li>
            <a class="text-body" href="ngan-hang.html">
              Ngân hàng kết nối
            </a>
          </li>


          <li>
            <a class="text-body" href="affiliate.html">
              Tiếp thị liên kết
            </a>
          </li>
          <li>
            <a class="text-body" href="chia-se-bien-dong-so-du.html">
              Chia sẻ biến động số dư
            </a>
          </li>
          <li>
            <a class="text-body" href="cong-thanh-toan-truc-tuyen.html">
              Cổng thanh toán trực tuyến
            </a>
          </li>
          <li>
            <a class="text-body" href="thong-ke-dong-tien.html">
              Thống kê dòng tiền
            </a>
          </li>
        </ul>
        <!-- End List -->
      </div>

      <div class="col-sm-2 col-md-2 col-6 mb-3 mb-sm-0 mx-auto">
        <h5 class=" mb-3">TÍCH HỢP</h5>
        <!-- List -->
        <ul class="list-unstyled list-py-1">

          <li>
            <a class="text-body" href="https://docs.sepay.vn/tich-hop-haravan.html">
              <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/haravan-icon.png"
                style="width:22px; height: 22px;"> Haravan
            </a>
          </li>

          <li>
            <a class="text-body" href="https://docs.sepay.vn/tich-hop-sapo.html">
              <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/sapo-icon.png"
                style="width:22px; height: 22px;"> Sapo
            </a>
          </li>
          <li>
            <a class="text-body" href="https://docs.sepay.vn/woocommerce.html">
              <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/woocommerce-icon.png"
                style="width:22px; height: 22px;"> WooCommerce
            </a>
          </li>
          <li>
            <a class="text-body" href="https://docs.sepay.vn/tich-hop-google-sheets.html">
              <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/google-sheets-icon.png"
                style="width:22px; height: 22px;"> Google Sheets
            </a>
          </li>
          <li>
            <a class="text-body" href="https://docs.sepay.vn/tich-hop-lark-messenger.html">
              <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/lark/lark-icon.png"
                style="width:22px; height: 22px;"> Lark
            </a>
          </li>
          <li>
            <a class="text-body" href="https://docs.sepay.vn/tich-hop-telegram.html">
              <i class="bi bi-telegram me-1 text-info fs-3"></i> Telegram
            </a>
          </li>




          
      </div>
      <div class="col-sm-2 col-md-2 col-6 mb-3 mb-sm-0 mx-auto">
        <h5 class=" mb-3">KẾT NỐI VỚI SEPAY</h5>
         <!-- List -->
         <ul class="list-unstyled list-py-1">

          <li>
            <a class="text-body" href="https://www.facebook.com/sepay.vn">
              <i class="bi bi-facebook me-1"></i> Facebook
            </a>
          </li>
          <li>
            <a class="text-body" href="https://t.me/s/sepaychannel">
              <i class="bi bi-telegram me-1"></i> Telegram
            </a>
          </li>
          <li>
            <a class="text-body" href="https://www.youtube.com/@SePayVN">
              <i class="bi bi-youtube me-1"></i> Youtube
            </a>
          </li>

          <li>
            <a class="text-body" href="https://www.pinterest.com/sepayvn/">
              <i class="bi bi-pinterest me-1"></i> Pinterest
            </a>
          </li>
          <li>
            <a class="text-body" href="https://github.com/sepayvn">
              <i class="bi bi-github me-1"></i> GitHub
            </a>
          </li>



        </ul>
        <!-- End List -->
         
      </div>
    </div>
    <!-- End Links -->
    <div class="border-top border-10 my-5"></div>
    <div class="row align-items-sm-center">
      <div class="col-12 mb-4 mb-sm-0">
        <p class="small mb-0 text-center">&copy; SePay 2023. All rights reserved.</p>
      </div>
      <!-- End Col -->


      <!-- End Col -->
    </div>
    <!-- End Row -->
  </footer>
  <!-- ========== END FOOTER ========== -->

  <!-- ========== SECONDARY CONTENTS ========== -->
  <!-- Sign Up -->
  <div class="modal fade" id="signupModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
      <div class="modal-content">
        <!-- Header -->
        <div class="modal-close">
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <!-- End Header -->

        <!-- Body -->
        <div class="modal-body">
          <!-- Log in -->
          <div id="signupModalFormLogin" style="display: none; opacity: 0;">
            <!-- Heading -->
            <div class="text-center mb-7">
              <h2>Log in</h2>
              <p>Don't have an account yet?
                <a class="js-animation-link link" href="javascript:;" role="button" data-hs-show-animation-options='{
                         "targetSelector": "#signupModalFormSignup",
                         "groupName": "idForm"
                       }'>Sign up</a>
              </p>
            </div>
            <!-- End Heading -->

            <div class="d-grid gap-2">
              <a class="btn btn-white btn-lg" href="#">
                <span class="d-flex justify-content-center align-items-center">
                  <img class="avatar avatar-xss me-2" src="./assets/svg/brands/google-icon.svg" alt="Image Description">
                  Log in with Google
                </span>
              </a>

              <a class="js-animation-link btn btn-primary btn-lg" href="#" data-hs-show-animation-options='{
                       "targetSelector": "#signupModalFormLoginWithEmail",
                       "groupName": "idForm"
                     }'>Log in with Email</a>
            </div>
          </div>
          <!-- End Log in -->

          <!-- Log in with Modal -->
          <div id="signupModalFormLoginWithEmail" style="display: none; opacity: 0;">
            <!-- Heading -->
            <div class="text-center mb-7">
              <h2>Log in</h2>
              <p>Don't have an account yet?
                <a class="js-animation-link link" href="javascript:;" role="button" data-hs-show-animation-options='{
                         "targetSelector": "#signupModalFormSignup",
                         "groupName": "idForm"
                       }'>Sign up</a>
              </p>
            </div>
            <!-- End Heading -->

            <form class="js-validate needs-validation" novalidate>
              <!-- Form -->
              <div class="mb-3">
                <label class="form-label" for="signupModalFormLoginEmail">Your email</label>
                <input type="email" class="form-control form-control-lg" name="email" id="signupModalFormLoginEmail"
                  placeholder="<EMAIL>" aria-label="<EMAIL>" required>
                <span class="invalid-feedback">Please enter a valid email address.</span>
              </div>

            
              <!-- End Form -->
 

              <!-- Form -->
              <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center">
                  <label class="form-label" for="signupModalFormLoginPassword">Password</label>

                  <a class="js-animation-link form-label-link" href="javascript:;" data-hs-show-animation-options='{
                       "targetSelector": "#signupModalFormResetPassword",
                       "groupName": "idForm"
                     }'>Forgot Password?</a>
                </div>

                <input type="password" class="form-control form-control-lg" name="password"
                  id="signupModalFormLoginPassword" placeholder="8+ characters required"
                  aria-label="8+ characters required" required minlength="8">
                <span class="invalid-feedback">Please enter a valid password.</span>
              </div>
              <!-- End Form -->

              <div class="d-grid mb-3">
                <button type="submit" class="btn btn-primary form-control-lg">Log in</button>
              </div>
            </form>
          </div>
          <!-- End Log in with Modal -->

          <!-- Sign up -->
          <div id="signupModalFormSignup">
            <!-- Heading -->
            <div class="text-center mb-7">
              <h2>Sign up</h2>
              <p>Already have an account?
                <a class="js-animation-link link" href="javascript:;" role="button" data-hs-show-animation-options='{
                         "targetSelector": "#signupModalFormLogin",
                         "groupName": "idForm"
                       }'>Log in</a>
              </p>
            </div>
            <!-- End Heading -->

            <div class="d-grid gap-3">
              <a class="btn btn-white btn-lg" href="#">
                <span class="d-flex justify-content-center align-items-center">
                  <img class="avatar avatar-xss me-2" src="./assets/svg/brands/google-icon.svg" alt="Image Description">
                  Sign up with Google
                </span>
              </a>

              <a class="js-animation-link btn btn-primary btn-lg" href="#" data-hs-show-animation-options='{
                       "targetSelector": "#signupModalFormSignupWithEmail",
                       "groupName": "idForm"
                     }'>Sign up with Email</a>

              <div class="text-center">
                <p class="small mb-0">By continuing you agree to our <a href="#">Terms and Conditions</a></p>
              </div>
            </div>
          </div>
          <!-- End Sign up -->

          <!-- Sign up with Modal -->
          <div id="signupModalFormSignupWithEmail" style="display: none; opacity: 0;">
            <!-- Heading -->
            <div class="text-center mb-7">
              <h2>Sign up</h2>
              <p>Already have an account?
                <a class="js-animation-link link" href="javascript:;" role="button" data-hs-show-animation-options='{
                         "targetSelector": "#signupModalFormLogin",
                         "groupName": "idForm"
                       }'>Log in</a>
              </p>
            </div>
            <!-- End Heading -->

            <form class="js-validate need-validate" novalidate>
              <!-- Form -->
              <div class="mb-3">
                <label class="form-label" for="signupModalFormSignupEmail">Your email</label>
                <input type="email" class="form-control form-control-lg" name="email" id="signupModalFormSignupEmail"
                  placeholder="<EMAIL>" aria-label="<EMAIL>" required>
                <span class="invalid-feedback">Please enter a valid email address.</span>
              </div>
              <!-- End Form -->

              <!-- Form -->
              <div class="mb-3">
                <label class="form-label" for="signupModalFormSignupPassword">Password</label>
                <input type="password" class="form-control form-control-lg" name="password"
                  id="signupModalFormSignupPassword" placeholder="8+ characters required"
                  aria-label="8+ characters required" required>
                <span class="invalid-feedback">Your password is invalid. Please try again.</span>
              </div>
              <!-- End Form -->

              <!-- Form -->
              <div class="mb-3" data-hs-validation-validate-class>
                <label class="form-label" for="signupModalFormSignupConfirmPassword">Confirm password</label>
                <input type="password" class="form-control form-control-lg" name="confirmPassword"
                  id="signupModalFormSignupConfirmPassword" placeholder="8+ characters required"
                  aria-label="8+ characters required" required
                  data-hs-validation-equal-field="#signupModalFormSignupPassword">
                <span class="invalid-feedback">Password does not match the confirm password.</span>
              </div>
              <!-- End Form -->

              <div class="d-grid mb-3">
                <button type="submit" class="btn btn-primary form-control-lg">Sign up</button>
              </div>

              <div class="text-center">
                <p class="small mb-0">By continuing you agree to our <a href="#">Terms and Conditions</a></p>
              </div>
            </form>
          </div>
          <!-- End Sign up with Modal -->

          <!-- Reset Password -->
          <div id="signupModalFormResetPassword" style="display: none; opacity: 0;">
            <!-- Heading -->
            <div class="text-center mb-7">
              <h2>Forgot password?</h2>
              <p>Enter the email address you used when you joined and we'll send you instructions to reset your
                password.</p>
            </div>
            <!-- En dHeading -->

            <form class="js-validate need-validate" novalidate>
              <div class="mb-3">
                <!-- Form -->
                <div class="d-flex justify-content-between align-items-center">
                  <label class="form-label" for="signupModalFormResetPasswordEmail" tabindex="0">Your email</label>

                  <a class="js-animation-link form-label-link" href="javascript:;" data-hs-show-animation-options='{
                         "targetSelector": "#signupModalFormLogin",
                         "groupName": "idForm"
                       }'>
                    <i class="bi-chevron-left small"></i> Back to Log in
                  </a>
                </div>

                <input type="email" class="form-control form-control-lg" name="email"
                  id="signupModalFormResetPasswordEmail" tabindex="1" placeholder="Enter your email address"
                  aria-label="Enter your email address" required>
                <span class="invalid-feedback">Please enter a valid email address.</span>
                <!-- End Form -->
              </div>

              <div class="d-grid">
                <button type="submit" class="btn btn-primary form-control-lg">Submit</button>
              </div>
            </form>
          </div>
          <!-- End Reset Password -->
        </div>
        <!-- End Body -->

        <!-- Footer -->
        <div class="modal-footer d-block text-center py-sm-5">
          <small class="text-cap mb-4">Trusted by the world's best teams</small>

          <div class="w-85 mx-auto">
            <div class="row justify-content-between">
              <div class="col">
                <img class="img-fluid" src="./assets/svg/brands/gitlab-gray.svg" alt="Logo">
              </div>
              <!-- End Col -->

              <div class="col">
                <img class="img-fluid" src="./assets/svg/brands/fitbit-gray.svg" alt="Logo">
              </div>
              <!-- End Col -->

              <div class="col">
                <img class="img-fluid" src="./assets/svg/brands/flow-xo-gray.svg" alt="Logo">
              </div>
              <!-- End Col -->

              <div class="col">
                <img class="img-fluid" src="./assets/svg/brands/layar-gray.svg" alt="Logo">
              </div>
              <!-- End Col -->
            </div>
          </div>
          <!-- End Row -->
        </div>
        <!-- End Footer -->
      </div>
    </div>
  </div>

  <!-- Go To -->
  <a class="js-go-to go-to position-fixed" href="javascript:;" style="visibility: hidden;" data-hs-go-to-options='{
       "offsetTop": 700,
       "position": {
         "init": {
           "right": "2rem"
         },
         "show": {
           "bottom": "2rem"
         },
         "hide": {
           "bottom": "-2rem"
         }
       }
     }'>
    <i class="bi-chevron-up"></i>
  </a>
  <!-- ========== END SECONDARY CONTENTS ========== -->

  <!-- JS Global Compulsory  -->
  <script src="./assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

  <!-- JS Implementing Plugins -->
  <script src="./assets/vendor/hs-header/dist/hs-header.min.js"></script>
  <script src="./assets/vendor/hs-mega-menu/dist/hs-mega-menu.min.js"></script>
  <script src="./assets/vendor/hs-show-animation/dist/hs-show-animation.min.js"></script>
  <script src="./assets/vendor/hs-go-to/dist/hs-go-to.min.js"></script>
  <script src="./assets/vendor/aos/dist/aos.js"></script>
  <script src="./assets/vendor/typed.js/lib/typed.min.js"></script>
  <script src="./assets/vendor/fslightbox/index.js"></script>
  <script src="./assets/vendor/hs-toggle-switch/dist/hs-toggle-switch.min.js"></script>
  <!-- JS Front -->
  <script src="./assets/js/theme.min.js"></script>

  <!-- JS Plugins Init. -->
  <script>
    (function () {
      // INITIALIZATION OF HEADER
      // =======================================================
      new HSHeader('#header').init()


      // INITIALIZATION OF MEGA MENU
      // =======================================================
      new HSMegaMenu('.js-mega-menu', {
        desktop: {
          position: 'left'
        }
      })


      // INITIALIZATION OF SHOW ANIMATIONS
      // =======================================================
      new HSShowAnimation('.js-animation-link')


      // INITIALIZATION OF BOOTSTRAP VALIDATION
      // =======================================================
      HSBsValidation.init('.js-validate', {
        onSubmit: data => {
          data.event.preventDefault()
          alert('Submited')
        }
      })


      // INITIALIZATION OF BOOTSTRAP DROPDOWN
      // =======================================================
      HSBsDropdown.init()


      // INITIALIZATION OF GO TO
      // =======================================================
      new HSGoTo('.js-go-to')


      // INITIALIZATION OF AOS
      // =======================================================
      AOS.init({
        duration: 650,
        once: true
      });

      // INITIALIZATION OF TOGGLE SWITCH
      // =======================================================
      new HSToggleSwitch('.js-toggle-switch');


      // INITIALIZATION OF TEXT ANIMATION (TYPING)
      // =======================================================
      HSCore.components.HSTyped.init('.js-typedjs');
    })()
    
  </script>
</body>

</html>