<!DOCTYPE html>
<html lang="en" dir="">

<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

  <!-- Title -->
  <title>SePay - KienLongBank:  Tự hào là đối tác ch<PERSON>h thức</title>

  <!-- Favicon -->
  <link rel="shortcut icon" href="./assets/img/logo/favicon.png" type="image/x-icon" />

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">

  <!-- CSS Implementing Plugins -->
  <link rel="stylesheet" href="./assets/vendor/bootstrap-icons/font/bootstrap-icons.css">
  <link rel="stylesheet" href="./assets/vendor/hs-mega-menu/dist/hs-mega-menu.min.css">


  <!-- CSS Front Template -->
  <link rel="stylesheet" href="./assets/css/theme.min.css">

  <!--contact-box-css-->
  <link rel="stylesheet" href="./assets/css/contact-box.css">
  <!--/contact-box-css-->

  <!-- meta tag -->
  <meta property="og:locale" content="vi_VN" />
  <link rel="canonical" href="https://sepay.vn/kien-long-bank.html" />
  <meta name="description" content="SePay - KienLongBank: Tự hào là đối tác chính thức của Open Banking. Cung cấp dịch vụ chia sẻ biến động số dư và xác nhận thanh toán tự động." />
  <meta property="og:type" content="article" />
  <meta property="og:url" content="https://sepay.vn/kien-long-bank.html" />
  <meta property="og:title" content="SePay - KienLongBank: Tự hào là đối tác chính thức của Open Banking" />
  <meta property="og:description"
    content="SePay - KienLongBank: Tự hào là đối tác chính thức của Open Banking. Cung cấp dịch vụ chia sẻ biến động số dư và xác nhận thanh toán tự động." />

  <meta property="og:site_name" content="SePay - KienLongBank: Tự hào là đối tác chính thức của Open Banking" />
  <meta property="og:image" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
  <meta property="og:image:secure_url" content="https://sepay.vn/assets/img/others/sepay-og-image.jpg" />
  <!-- meta tag -->


  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-J8DLMQTKSQ"></script>
  <script>
    window.dataLayer = window.dataLayer || [];

    function gtag() {
      dataLayer.push(arguments);
    }
    gtag('js', new Date());

    gtag('config', 'G-J8DLMQTKSQ');
  </script>

</head>

<body>
  <!-- ========== HEADER ========== -->
  <header id="header" class="navbar navbar-expand-lg navbar-end navbar-absolute-top navbar-dark navbar-show-hide"
    data-hs-header-options='{
            "fixMoment": 1000,
            "fixEffect": "slide"
          }'>


    <div class="container">
      <nav class="js-mega-menu navbar-nav-wrap">
        <!-- Default Logo -->
        <a class="navbar-brand" href="https://sepay.vn" aria-label="Front">
          <img class="navbar-brand-logo" src="./assets/img/logo/sepay-white-154x50.png" alt="Logo">
        </a>
        <!-- End Default Logo -->

        <!-- Toggler -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown"
          aria-controls="navbarNavDropdown" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-default">
            <i class="bi-list"></i>
          </span>
          <span class="navbar-toggler-toggled">
            <i class="bi-x"></i>
          </span>
        </button>
        <!-- End Toggler -->

        <!-- Collapse -->
        <div class="collapse navbar-collapse" id="navbarNavDropdown">
          <div class="navbar-absolute-top-scroller">
            <ul class="navbar-nav">
            
              <!-- Features -->
              <li class="hs-has-sub-menu nav-item">
                <a id="coursesMegaMenu" class="hs-mega-menu-invoker nav-link dropdown-toggle" href="#" role="button">
                  Tính năng</a>

                <!-- Mega Menu -->
                <div class="hs-sub-menu dropdown-menu" aria-labelledby="coursesMegaMenu" style="min-width: 20rem;">


                  <a class="navbar-dropdown-menu-media-link" href="/chia-se-bien-dong-so-du.html">
                    <div class="d-flex">
                      <div class="flex-shrink-0">
                        <span class="svg-icon svg-icon-sm text-primary">
                          <span class="svg-icon svg-icon-sm text-primary">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M22.1671 18.1421C22.4827 18.4577 23.0222 18.2331 23.0206 17.7868L23.0039 13.1053V5.52632C23.0039 4.13107 21.8729 3 20.4776 3H8.68815C7.2929 3 6.16183 4.13107 6.16183 5.52632V9H13C14.6568 9 16 10.3431 16 12V15.6316H19.6565L22.1671 18.1421Z"
                                fill="#035A4B" />
                              <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd"
                                d="M1.98508 18V13C1.98508 11.8954 2.88051 11 3.98508 11H11.9851C13.0896 11 13.9851 11.8954 13.9851 13V18C13.9851 19.1046 13.0896 20 11.9851 20H4.10081L2.85695 21.1905C2.53895 21.4949 2.01123 21.2695 2.01123 20.8293V18.3243C1.99402 18.2187 1.98508 18.1104 1.98508 18ZM5.99999 14.5C5.99999 14.2239 6.22385 14 6.49999 14H11.5C11.7761 14 12 14.2239 12 14.5C12 14.7761 11.7761 15 11.5 15H6.49999C6.22385 15 5.99999 14.7761 5.99999 14.5ZM9.49999 16C9.22385 16 8.99999 16.2239 8.99999 16.5C8.99999 16.7761 9.22385 17 9.49999 17H11.5C11.7761 17 12 16.7761 12 16.5C12 16.2239 11.7761 16 11.5 16H9.49999Z"
                                fill="#035A4B" />
                            </svg>

                          </span>

                        </span>
                      </div>

                      <div class="flex-grow-1 ms-3">
                        <span class="navbar-dropdown-menu-media-title">Chia sẻ biến động số dư</span>
                        <p class="navbar-dropdown-menu-media-desc">Báo lên group chat khi nhận chuyển khoản</p>
                      </div>
                    </div>
                  </a>

                  <div class="dropdown-divider"></div>

                  <a class="navbar-dropdown-menu-media-link" href="cong-thanh-toan-truc-tuyen.html">
                    <div class="d-flex">
                      <div class="flex-shrink-0">
                        <span class="svg-icon svg-icon-sm text-primary">
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path opacity="0.3" d="M18 10V20C18 20.6 18.4 21 19 21C19.6 21 20 20.6 20 20V10H18Z"
                              fill="#035A4B" />
                            <path opacity="0.3"
                              d="M11 10V17H6V10H4V20C4 20.6 4.4 21 5 21H12C12.6 21 13 20.6 13 20V10H11Z"
                              fill="#035A4B" />
                            <path opacity="0.3" d="M10 10C10 11.1 9.1 12 8 12C6.9 12 6 11.1 6 10H10Z" fill="#035A4B" />
                            <path opacity="0.3" d="M18 10C18 11.1 17.1 12 16 12C14.9 12 14 11.1 14 10H18Z"
                              fill="#035A4B" />
                            <path opacity="0.3" d="M14 4H10V10H14V4Z" fill="#035A4B" />
                            <path opacity="0.3" d="M17 4H20L22 10H18L17 4Z" fill="#035A4B" />
                            <path opacity="0.3" d="M7 4H4L2 10H6L7 4Z" fill="#035A4B" />
                            <path
                              d="M6 10C6 11.1 5.1 12 4 12C2.9 12 2 11.1 2 10H6ZM10 10C10 11.1 10.9 12 12 12C13.1 12 14 11.1 14 10H10ZM18 10C18 11.1 18.9 12 20 12C21.1 12 22 11.1 22 10H18ZM19 2H5C4.4 2 4 2.4 4 3V4H20V3C20 2.4 19.6 2 19 2ZM12 17C12 16.4 11.6 16 11 16H6C5.4 16 5 16.4 5 17C5 17.6 5.4 18 6 18H11C11.6 18 12 17.6 12 17Z"
                              fill="#035A4B" />
                          </svg>

                        </span>
                      </div>

                      <div class="flex-grow-1 ms-3">
                        <span class="navbar-dropdown-menu-media-title">Cổng thanh toán trực tuyến</span>
                        <p class="navbar-dropdown-menu-media-desc">Cổng thanh toán QR miễn phí</p>
                      </div>
                    </div>
                  </a>

                  <div class="dropdown-divider"></div>

                  <a class="navbar-dropdown-menu-media-link" href="thong-ke-dong-tien.html">
                    <div class="d-flex">
                      <div class="flex-shrink-0">
                        <span class="svg-icon svg-icon-sm text-primary">
                          <span class="svg-icon svg-icon-sm text-primary">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd"
                                d="M12 17C16.4183 17 20 13.4183 20 9C20 4.58172 16.4183 1 12 1C7.58172 1 4 4.58172 4 9C4 13.4183 7.58172 17 12 17Z"
                                fill="#035A4B" />
                              <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd"
                                d="M6.53819 9L10.568 19.3624L11.976 18.8149L13.3745 19.3674L17.4703 9H6.53819ZM9.46188 11H14.5298L11.9759 17.4645L9.46188 11Z"
                                fill="#035A4B" />
                              <path opacity="0.3"
                                d="M10 22H14V22C14 23.1046 13.1046 24 12 24V24C10.8954 24 10 23.1046 10 22V22Z"
                                fill="#035A4B" />
                              <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M8 17C8 16.4477 8.44772 16 9 16H15C15.5523 16 16 16.4477 16 17C16 17.5523 15.5523 18 15 18C15.5523 18 16 18.4477 16 19C16 19.5523 15.5523 20 15 20C15.5523 20 16 20.4477 16 21C16 21.5523 15.5523 22 15 22H9C8.44772 22 8 21.5523 8 21C8 20.4477 8.44772 20 9 20C8.44772 20 8 19.5523 8 19C8 18.4477 8.44772 18 9 18C8.44772 18 8 17.5523 8 17Z"
                                fill="#035A4B" />
                            </svg>

                          </span>

                        </span>
                      </div>

                      <div class="flex-grow-1 ms-3">
                        <span class="navbar-dropdown-menu-media-title">Thống kê dòng tiền</span>
                        <p class="navbar-dropdown-menu-media-desc">Nắm bắt được dòng tiền tất cả tài khoản</p>
                      </div>
                    </div>
                  </a>

                </div>

              </li>
              <!-- End Features -->



              <li class="nav-item">
                <a class="nav-link" href="why-sepay.html">Why SePay?</a>
              </li>




              <li class="nav-item">
                <a class="nav-link" href="bang-gia.html">Bảng giá</a>
              </li>

              <li class="nav-item">
                <a class="nav-link" href="https://docs.sepay.vn" target="_blank">Hướng dẫn</a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="https://sepay.vn/blog">Blog</a>
              </li>

                <!-- Cooperate -->
                <li class="hs-has-sub-menu nav-item">
                  <a id="cooperateMegaMenu" class="hs-mega-menu-invoker nav-link dropdown-toggle" href="#"
                      role="button"> Hợp tác</a>

                  <!-- Mega Menu -->
                  <div class="hs-sub-menu dropdown-menu" aria-labelledby="cooperateMegaMenu"
                      style="min-width: 20rem;">


                      <a class="navbar-dropdown-menu-media-link" href="sepay-bank-hub.html">
                          <div class="d-flex">
                              <div class="flex-shrink-0">
                                  <span class="svg-icon svg-icon-sm text-primary">
                                      <span class="svg-icon svg-icon-sm text-primary">
                                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                              xmlns="http://www.w3.org/2000/svg">
                                              <path fill-rule="evenodd" clip-rule="evenodd"
                                                  d="M15 19.5229C15 20.265 15.9624 20.5564 16.374 19.9389L22.2227 11.166C22.5549 10.6676 22.1976 10 21.5986 10H17V4.47708C17 3.73503 16.0376 3.44363 15.626 4.06106L9.77735 12.834C9.44507 13.3324 9.80237 14 10.4014 14H15V19.5229Z"
                                                  fill="#035A4B"></path>
                                              <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd"
                                                  d="M3 6.5C3 5.67157 3.67157 5 4.5 5H9.5C10.3284 5 11 5.67157 11 6.5C11 7.32843 10.3284 8 9.5 8H4.5C3.67157 8 3 7.32843 3 6.5ZM3 18.5C3 17.6716 3.67157 17 4.5 17H9.5C10.3284 17 11 17.6716 11 18.5C11 19.3284 10.3284 20 9.5 20H4.5C3.67157 20 3 19.3284 3 18.5ZM2.5 11C1.67157 11 1 11.6716 1 12.5C1 13.3284 1.67157 14 2.5 14H6.5C7.32843 14 8 13.3284 8 12.5C8 11.6716 7.32843 11 6.5 11H2.5Z"
                                                  fill="#035A4B"></path>
                                          </svg>

                                      </span>

                                  </span>
                              </div>

                              <div class="flex-grow-1 ms-3">
                                <span class="navbar-dropdown-menu-media-title">SePay Bank Hub</span>
                                <p class="navbar-dropdown-menu-media-desc"> Hợp nhất API ngân hàng</p>
                              </div>
                          </div>
                      </a>

                      <div class="dropdown-divider"></div>

                      <a class="navbar-dropdown-menu-media-link" href="affiliate.html">
                          <div class="d-flex">
                              <div class="flex-shrink-0">
                                  <span class="svg-icon svg-icon-sm text-primary">
                                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                          xmlns="http://www.w3.org/2000/svg">
                                          <path opacity="0.3"
                                              d="M12.5 22C11.9 22 11.5 21.6 11.5 21V3C11.5 2.4 11.9 2 12.5 2C13.1 2 13.5 2.4 13.5 3V21C13.5 21.6 13.1 22 12.5 22Z"
                                              fill="#035A4B"></path>
                                          <path
                                              d="M17.8 14.7C17.8 15.5 17.6 16.3 17.2 16.9C16.8 17.6 16.2 18.1 15.3 18.4C14.5 18.8 13.5 19 12.4 19C11.1 19 10 18.7 9.10001 18.2C8.50001 17.8 8.00001 17.4 7.60001 16.7C7.20001 16.1 7 15.5 7 14.9C7 14.6 7.09999 14.3 7.29999 14C7.49999 13.8 7.80001 13.6 8.20001 13.6C8.50001 13.6 8.69999 13.7 8.89999 13.9C9.09999 14.1 9.29999 14.4 9.39999 14.7C9.59999 15.1 9.8 15.5 10 15.8C10.2 16.1 10.5 16.3 10.8 16.5C11.2 16.7 11.6 16.8 12.2 16.8C13 16.8 13.7 16.6 14.2 16.2C14.7 15.8 15 15.3 15 14.8C15 14.4 14.9 14 14.6 13.7C14.3 13.4 14 13.2 13.5 13.1C13.1 13 12.5 12.8 11.8 12.6C10.8 12.4 9.99999 12.1 9.39999 11.8C8.69999 11.5 8.19999 11.1 7.79999 10.6C7.39999 10.1 7.20001 9.39998 7.20001 8.59998C7.20001 7.89998 7.39999 7.19998 7.79999 6.59998C8.19999 5.99998 8.80001 5.60005 9.60001 5.30005C10.4 5.00005 11.3 4.80005 12.3 4.80005C13.1 4.80005 13.8 4.89998 14.5 5.09998C15.1 5.29998 15.6 5.60002 16 5.90002C16.4 6.20002 16.7 6.6 16.9 7C17.1 7.4 17.2 7.69998 17.2 8.09998C17.2 8.39998 17.1 8.7 16.9 9C16.7 9.3 16.4 9.40002 16 9.40002C15.7 9.40002 15.4 9.29995 15.3 9.19995C15.2 9.09995 15 8.80002 14.8 8.40002C14.6 7.90002 14.3 7.49995 13.9 7.19995C13.5 6.89995 13 6.80005 12.2 6.80005C11.5 6.80005 10.9 7.00005 10.5 7.30005C10.1 7.60005 9.79999 8.00002 9.79999 8.40002C9.79999 8.70002 9.9 8.89998 10 9.09998C10.1 9.29998 10.4 9.49998 10.6 9.59998C10.8 9.69998 11.1 9.90002 11.4 9.90002C11.7 10 12.1 10.1 12.7 10.3C13.5 10.5 14.2 10.7 14.8 10.9C15.4 11.1 15.9 11.4 16.4 11.7C16.8 12 17.2 12.4 17.4 12.9C17.6 13.4 17.8 14 17.8 14.7Z"
                                              fill="#035A4B"></path>
                                      </svg>
                                  </span>
                              </div>

                              <div class="flex-grow-1 ms-3">
                                  <span class="navbar-dropdown-menu-media-title">Tiếp thị liên kết -
                                      Affiliate</span>
                                  <p class="navbar-dropdown-menu-media-desc"> Hoa hồng lên đến 30%</p>
                              </div>
                          </div>
                      </a>



                  </div>

              </li>
              <!-- End Cooperate -->

               <!-- Company -->
              <li class="hs-has-sub-menu nav-item">
                <a id="accountMegaMenu" class="hs-mega-menu-invoker nav-link dropdown-toggle " href="#" role="button" aria-expanded="false">Công ty</a>

                <!-- Mega Menu -->
                <div class="hs-sub-menu dropdown-menu" aria-labelledby="accountMegaMenu" style="min-width: 14rem;">
                    <a class="dropdown-item " href="./gioi-thieu.html">Giới thiệu</a>
                    <a class="dropdown-item " href="./chung-nhan.html">Chứng nhận</a>

                  <!-- Authentication -->
                  <div class="hs-has-sub-menu nav-item">
                    <a id="authenticationMegaMenu" class="hs-mega-menu-invoker dropdown-item dropdown-toggle " href="#" role="button" aria-expanded="false">Ký kết hợp tác</a>

                    <div class="hs-sub-menu dropdown-menu" aria-labelledby="authenticationMegaMenu" style="min-width: 14rem;">
                      <a class="dropdown-item " href="./kien-long-bank.html">Ngân hàng Kiên Long</a>
                      <a class="dropdown-item " href="./ocb.html">Ngân hàng OCB</a>
                      <a class="dropdown-item " href="./msb.html">Ngân hàng MSB</a>
                      <a class="dropdown-item " href="./mb.html">Ngân hàng MB</a>
                     </div>
                  </div>
                  <!-- End Authentication -->

                  <a class="dropdown-item " href="./ngan-hang.html">Ngân hàng kết nối (22)</a>
 
                </div>
                <!-- End Mega Menu -->
              </li>
              <!-- End Company -->

              <!-- Button -->
              <li class="nav-item">
                <a class="btn btn-primary btn-transition"
                href="https://my.sepay.vn/login"
                target="_blank">Đăng nhập</a>
                <a class="btn btn-light btn-transition" href="https://my.sepay.vn/register" target="_blank">Đăng ký</a>
              </li>
              <!-- End Button -->
            </ul>
          </div>
        </div>
        <!-- End Collapse -->
      </nav>
    </div>


    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@graph": [{
          "@type": "Organization",
          "@id": "https://sepay.vn/#organization",
          "name": "SePay",
          "url": "https://sepay.vn/",
          "logo": {
            "@type": "ImageObject",
            "@id": "https://sepay.vn/#logo",
            "inLanguage": "vi_VN",
            "url": "https://sepay.vn/assets/img/logo/sepay-820x820-blue-icon.png",
            "contentUrl": "https://sepay.vn/assets/img/logo/sepay-820x820-blue-icon.png",
            "width": 820,
            "height": 820,
            "caption": "SePay"
          },
          "image": {
            "@id": "https://sepay.vn/#logo"
          }
        }, {
          "@type": "WebSite",
          "@id": "https://sepay.vn/#website",
          "url": "https://sepay.vn/",
          "name": "SePay",
          "description": "SePay cung cấp công cụ giúp chia sẻ biến động số dư ngân hàng, xác thực thanh toán chuyển khoản, thống kê dòng tiền ngân hàng một cách tự động",
          "publisher": {
            "@id": "https://sepay.vn/#organization"
          },
          "inLanguage": "vi_VN"
        }, {
          "@type": "WebPage",
          "@id": "https://sepay.vn/kien-long-bank.html#webpage",
          "url": "https://sepay.vn/kien-long-bank.html",
          "name": "SePay - Kiên Long Bank",
          "isPartOf": {
            "@id": "https://sepay.vn/#website"
          },
          "primaryImageOfPage": {
            "@id": "https://sepay.vn/kien-long-bank.html#primaryimage"
          },
          "datePublished": "2023-03-19T15:56:22+00:00",
          "dateModified": "2023-03-20T11:06:48+00:00",
          "description": "SePay - KienLongBank: Tự hào là đối tác chính thức của Open Banking. Cung cấp dịch vụ chia sẻ biến động số dư và xác nhận thanh toán tự động.",
          "breadcrumb": {
            "@id": "https://sepay.vn/kien-long-bank.html#breadcrumb"
          },
          "inLanguage": "vi_VN",
          "potentialAction": [{
            "@type": "ReadAction",
            "target": ["https://sepay.vn/kien-long-bank.html"]
          }]
        }, {
          "@type": "BreadcrumbList",
          "@id": "https://sepay.vn/kien-long-bank.html#breadcrumb",
          "itemListElement": [{
            "@type": "ListItem",
            "position": 1,
            "name": "Trang chủ",
            "item": "https://sepay.vn/"
          }, {
            "@type": "ListItem",
            "position": 2,
            "name": "SePay - KienLongBank:  Tự hào là đối tác chính thức"
          }]
        }]
      }
    </script>


  </header>

  <!-- ========== END HEADER ========== -->

  <!-- ========== MAIN CONTENT ========== -->
  <main id="content" role="main">
    <!-- Hero -->
    <div class="position-relative bg-primary overflow-hidden">
      <div class="container position-relative zi-2 content-space-3 content-space-md-5">
        <div class="w-md-75 w-xl-65 text-center mx-md-auto">
          <!-- Heading -->
          <div class="mb-7">
            <h1 class="display-4 text-white mb-4">SePay - KienLongBank</h1>
            <p class="lead text-white mb-4">Tự hào là đối tác chính thức về Open Banking</p>
          </div>
          <!-- End Title & Description -->

          <div class="d-grid d-sm-flex justify-content-center gap-2">
            <a class="btn btn-light btn-transition px-6" href="https://my.sepay.vn/register">Đăng ký ngay</a>
            <a class="btn text-white" href="lien-he.html#contact">Tư vấn tôi trước <i
                class="bi-chevron-right small ms-1"></i></a>
          </div>
        </div>
      </div>

      <!-- Background Shape -->
      <figure class="position-absolute top-0 start-0 w-65">
        <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 1246 1078">
          <g opacity=".4">
            <linearGradient id="doubleEllipseTopLeftID1" gradientUnits="userSpaceOnUse" x1="2073.5078" y1="1.7251"
              x2="2273.4375" y2="1135.5818" gradientTransform="matrix(-1 0 0 1 2600 0)">
              <stop offset="0.4976" style="stop-color:gulpLighten[#377dff,30]" />
              <stop offset="1" style="stop-color:#377dff" />
            </linearGradient>
            <polygon fill="url(#doubleEllipseTopLeftID1)" points="519.8,0.6 0,0.6 0,1078 863.4,1078   " />
            <linearGradient id="doubleEllipseTopLeftID2" gradientUnits="userSpaceOnUse" x1="1717.1648" y1="3.779560e-05"
              x2="1717.1648" y2="644.0417" gradientTransform="matrix(-1 0 0 1 2600 0)">
              <stop offset="1.577052e-06" style="stop-color:gulpLighten[#377dff,30]" />
              <stop offset="1" style="stop-color:#377dff" />
            </linearGradient>
            <polygon fill="url(#doubleEllipseTopLeftID2)" points="519.7,0 1039.4,0.6 1246,639.1 725.2,644   " />
          </g>
        </svg>
      </figure>
      <!-- End Background Shape -->


    </div>
    <!-- End Hero -->

    

    <!-- Features -->
    <div class="container content-space-2 content-space-lg-2">
      <div class="row justify-content-lg-between align-items-lg-center">
        <div class="col-lg-5 mb-9 mb-lg-0">
          <div class="mb-3">
            <h2 class="h1">SePay là đối tác chính thức của KienLongBank</h2>
          </div>

          <p><a href="https://sepay.vn/blog/sepay-hop-tac-chien-luoc-cung-ngan-hang-kien-long-mo-dau-xu-huong-open-banking/">SePay đã ký kết hợp tác với ngân hàng Kiên Long</a>. Khách hàng SePay ngay bây giờ có thể trải nghiệm các lợi ích tuyệt vời từ API Ngân hàng.</p>

          <p>Sử dụng KienLongBank tại SePay, giao dịch sẽ được đồng bộ tức thì, được sử dụng công nghệ tài khoản ảo (VA) hoàn toàn miễn phí.</p>

          <div class="mt-4">
            <a class="btn btn-primary btn-transition px-5" href="https://my.sepay.vn/register">Đăng ký ngay</a>
          </div>
        </div>
        <!-- End Col -->

        <div class="col-lg-6 col-xl-5">
          
          <a href="https://sepay.vn/blog/sepay-hop-tac-chien-luoc-cung-ngan-hang-kien-long-mo-dau-xu-huong-open-banking/"><img src="https://sepay.vn/uploads/hop-tac/ky-ket-KLB-2.jpg" class="img-fluid"></a>
          <p class="mt-2 text-center"><a href="https://sepay.vn/blog/sepay-hop-tac-chien-luoc-cung-ngan-hang-kien-long-mo-dau-xu-huong-open-banking/">Xem thông tin lễ ký kết hợp tác giữa SePay và KienLongBank</a></p>
        </div>
        <!-- End Col -->
      </div>
      <!-- End Row -->
    </div>
    <!-- End Features -->


     <!-- Mockup -->
     <div class="shape-container mb-5">
      <div class="container">
        <!-- Heading -->
        <div class="w-md-75 text-center mx-md-auto mb-5 mb-md-9">
          <h2 class="h1">Lợi ích đặc biệt</h2>
        </div>
        <!-- End Heading -->

        
      </div>
    </div>

    <!-- Icon Blocks -->
    <div class="container content-space-t-2 content-space-t-lg-1">
      <div class="row justify-content-lg-center">
        <div class="col-md-6 col-lg-5 mb-3 mb-md-7">
          <!-- Icon Blocks -->
          <div class="d-flex pe-lg-5">
            <div class="flex-shrink-0">
              <span class="svg-icon text-primary">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path opacity="0.3" d="M21.25 18.525L13.05 21.825C12.35 22.125 11.65 22.125 10.95 21.825L2.75 18.525C1.75 18.125 1.75 16.725 2.75 16.325L4.04999 15.825L10.25 18.325C10.85 18.525 11.45 18.625 12.05 18.625C12.65 18.625 13.25 18.525 13.85 18.325L20.05 15.825L21.35 16.325C22.35 16.725 22.35 18.125 21.25 18.525ZM13.05 16.425L21.25 13.125C22.25 12.725 22.25 11.325 21.25 10.925L13.05 7.62502C12.35 7.32502 11.65 7.32502 10.95 7.62502L2.75 10.925C1.75 11.325 1.75 12.725 2.75 13.125L10.95 16.425C11.65 16.725 12.45 16.725 13.05 16.425Z" fill="#035A4B"></path>
                    <path d="M11.05 11.025L2.84998 7.725C1.84998 7.325 1.84998 5.925 2.84998 5.525L11.05 2.225C11.75 1.925 12.45 1.925 13.15 2.225L21.35 5.525C22.35 5.925 22.35 7.325 21.35 7.725L13.05 11.025C12.45 11.325 11.65 11.325 11.05 11.025Z" fill="#035A4B"></path>
                  </svg>

              </span>
            </div>
            <div class="flex-grow-1 ms-4">
              <h4>Độ ổn định cao</h4>
              <p>API Chính thức từ ngân hàng, độ ổn định và tin cậy rất cao.</p>
            </div>
          </div>
          <!-- End Icon Blocks -->
        </div>
        <!-- End Col -->

        <div class="col-md-6 col-lg-5 mb-3 mb-md-7">
          <!-- Icon Blocks -->
          <div class="d-flex ps-lg-5">
            <div class="flex-shrink-0">
              <span class="svg-icon text-primary">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M15 19.5229C15 20.265 15.9624 20.5564 16.374 19.9389L22.2227 11.166C22.5549 10.6676 22.1976 10 21.5986 10H17V4.47708C17 3.73503 16.0376 3.44363 15.626 4.06106L9.77735 12.834C9.44507 13.3324 9.80237 14 10.4014 14H15V19.5229Z"
                    fill="#035A4B"></path>
                  <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd"
                    d="M3 6.5C3 5.67157 3.67157 5 4.5 5H9.5C10.3284 5 11 5.67157 11 6.5C11 7.32843 10.3284 8 9.5 8H4.5C3.67157 8 3 7.32843 3 6.5ZM3 18.5C3 17.6716 3.67157 17 4.5 17H9.5C10.3284 17 11 17.6716 11 18.5C11 19.3284 10.3284 20 9.5 20H4.5C3.67157 20 3 19.3284 3 18.5ZM2.5 11C1.67157 11 1 11.6716 1 12.5C1 13.3284 1.67157 14 2.5 14H6.5C7.32843 14 8 13.3284 8 12.5C8 11.6716 7.32843 11 6.5 11H2.5Z"
                    fill="#035A4B"></path>
                </svg>

              </span>
            </div>
            <div class="flex-grow-1 ms-4">
              <h4>Đồng bộ tức thì</h4>
              <p>Thông tin giao dịch được đồng bộ qua SePay một cách tức thì.</p>
            </div>
          </div>
          <!-- End Icon Blocks -->
        </div>
        <!-- End Col -->

        <div class="w-100"></div>

        <div class="col-md-6 col-lg-5 mb-3 mb-md-7 mb-lg-0">
          <!-- Icon Blocks -->
          <div class="d-flex pe-lg-5">
            <div class="flex-shrink-0">
              <span class="svg-icon text-primary">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M15.6 5.59998L20.9 10.9C21.3 11.3 21.3 11.9 20.9 12.3L17.6 15.6L11.6 9.59998L15.6 5.59998ZM2.3 12.3L7.59999 17.6L11.6 13.6L5.59999 7.59998L2.3 10.9C1.9 11.3 1.9 11.9 2.3 12.3Z"
                    fill="#035A4B"></path>
                  <path opacity="0.3"
                    d="M17.6 15.6L12.3 20.9C11.9 21.3 11.3 21.3 10.9 20.9L7.59998 17.6L13.6 11.6L17.6 15.6ZM10.9 2.3L5.59998 7.6L9.59998 11.6L15.6 5.6L12.3 2.3C11.9 1.9 11.3 1.9 10.9 2.3Z"
                    fill="#035A4B"></path>
                </svg>



              </span>
            </div>
            <div class="flex-grow-1 ms-4">
              <h4>Online 100%</h4>
              <p>Thao tác liên kết mở API online 100% chỉ trong 1 phút tại giao diện SePay. Không cần thủ tục.</p>
            </div>
          </div>
          <!-- End Icon Blocks -->
        </div>
        <!-- End Col -->

        <div class="col-md-6 col-lg-5">
            <!-- Icon Blocks -->
            <div class="d-flex ps-lg-5">
              <div class="flex-shrink-0">
                <span class="svg-icon text-primary">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path opacity="0.3"
                      d="M12.5 22C11.9 22 11.5 21.6 11.5 21V3C11.5 2.4 11.9 2 12.5 2C13.1 2 13.5 2.4 13.5 3V21C13.5 21.6 13.1 22 12.5 22Z"
                      fill="#035A4B"></path>
                    <path
                      d="M17.8 14.7C17.8 15.5 17.6 16.3 17.2 16.9C16.8 17.6 16.2 18.1 15.3 18.4C14.5 18.8 13.5 19 12.4 19C11.1 19 10 18.7 9.10001 18.2C8.50001 17.8 8.00001 17.4 7.60001 16.7C7.20001 16.1 7 15.5 7 14.9C7 14.6 7.09999 14.3 7.29999 14C7.49999 13.8 7.80001 13.6 8.20001 13.6C8.50001 13.6 8.69999 13.7 8.89999 13.9C9.09999 14.1 9.29999 14.4 9.39999 14.7C9.59999 15.1 9.8 15.5 10 15.8C10.2 16.1 10.5 16.3 10.8 16.5C11.2 16.7 11.6 16.8 12.2 16.8C13 16.8 13.7 16.6 14.2 16.2C14.7 15.8 15 15.3 15 14.8C15 14.4 14.9 14 14.6 13.7C14.3 13.4 14 13.2 13.5 13.1C13.1 13 12.5 12.8 11.8 12.6C10.8 12.4 9.99999 12.1 9.39999 11.8C8.69999 11.5 8.19999 11.1 7.79999 10.6C7.39999 10.1 7.20001 9.39998 7.20001 8.59998C7.20001 7.89998 7.39999 7.19998 7.79999 6.59998C8.19999 5.99998 8.80001 5.60005 9.60001 5.30005C10.4 5.00005 11.3 4.80005 12.3 4.80005C13.1 4.80005 13.8 4.89998 14.5 5.09998C15.1 5.29998 15.6 5.60002 16 5.90002C16.4 6.20002 16.7 6.6 16.9 7C17.1 7.4 17.2 7.69998 17.2 8.09998C17.2 8.39998 17.1 8.7 16.9 9C16.7 9.3 16.4 9.40002 16 9.40002C15.7 9.40002 15.4 9.29995 15.3 9.19995C15.2 9.09995 15 8.80002 14.8 8.40002C14.6 7.90002 14.3 7.49995 13.9 7.19995C13.5 6.89995 13 6.80005 12.2 6.80005C11.5 6.80005 10.9 7.00005 10.5 7.30005C10.1 7.60005 9.79999 8.00002 9.79999 8.40002C9.79999 8.70002 9.9 8.89998 10 9.09998C10.1 9.29998 10.4 9.49998 10.6 9.59998C10.8 9.69998 11.1 9.90002 11.4 9.90002C11.7 10 12.1 10.1 12.7 10.3C13.5 10.5 14.2 10.7 14.8 10.9C15.4 11.1 15.9 11.4 16.4 11.7C16.8 12 17.2 12.4 17.4 12.9C17.6 13.4 17.8 14 17.8 14.7Z"
                      fill="#035A4B"></path>
                  </svg>
  
                </span>
              </div>
              <div class="flex-grow-1 ms-4">
                <h4>Miễn phí</h4>
                <p>Miễn phí 100% API từ ngân hàng KienLongBank.</p>
              </div>
            </div>
            <!-- End Icon Blocks -->
          </div>
          <!-- End Col -->

        <div class="w-100"></div>
 

      
      </div>
      <!-- End Row -->
    </div>
    <!-- End Icon Blocks -->

    <!-- Features -->
    <div class="overflow-hidden">
      <div class="container content-space-2 content-space-lg-3">
        <div class="row justify-content-lg-between align-items-lg-center">

          <div class="col-lg-5">
            <!-- Heading -->
            <div class="mb-5">
              <h2>Liên kết online 100%</h2>
            </div>
            <!-- End Heading -->

            <!-- Step -->
            <ul class="step step-dashed mb-7">
              <li class="step-item">
                <div class="step-content-wrapper">
                  <span class="step-icon step-icon-xs step-icon-soft-primary">1</span>
                  <div class="step-content">
                    <h4 class="step-title">Điền số tài khoản Kiên Long Bank</h4>
                    <p>Điền số tài khoản Kiên Long Bank tại my.sepay.vn để bắt đầu liên kết.</p>
                  </div>
                </div>
              </li>

              <li class="step-item">
                <div class="step-content-wrapper">
                  <span class="step-icon step-icon-xs step-icon-soft-primary">2</span>
                  <div class="step-content">
                    <h4 class="step-title">Nhập mã OTP</h4>
                    <p class="mb-0">Kiên Long Bank sẽ gửi mã OTP về số điện thoại của quý khách, điền mã OTP để xác nhận liên kết.</p>
                  </div>
                </div>
              </li>
              <li class="step-item">
                <div class="step-content-wrapper">
                  <span class="step-icon step-icon-xs step-icon-soft-primary">3</span>
                  <div class="step-content">
                    <h4 class="step-title">Tạo Virtual Account (VA)</h4>
                    <p class="mb-0">Sau khi liên kết thành công. Thực hiện tạo tài khoản ảo với thao tác 1 click chuột.</p>
                  </div>
                </div>
              </li>
              <li class="step-item mb-0">
                <div class="step-content-wrapper">
                  <span class="step-icon step-icon-xs step-icon-soft-primary">4</span>
                  <div class="step-content">
                    <h4 class="step-title">Nhận thanh toán bằng VA</h4>
                    <p class="mb-0">Sử dụng VA để nhận thanh toán, tất cả các giao dịch đều đồng bộ vào SePay theo thời gian thực.</p>
                  </div>
                </div>
              </li>
            </ul>
            <!-- End Step -->

            <a class="btn btn-primary btn-transition" href="https://docs.sepay.vn/ket-noi-kienlongbank-api.html">Xem hướng dẫn</a>
          </div>
          <!-- End Col -->

          <div class="col-lg-6">

             <img src="uploads/chung-nhan/sepay-kienlongbank-chung-nhan.png" class="img-fluid">
             <p class="fw-bold mt-2 text-center">Thông báo xác nhận hợp tác và là đối tác chiến lược giữa Ngân hàng Kiên Long Bank và SePay</p>
          </div>
          <!-- End Col -->


        </div>
        <!-- End Row -->
      </div>
    </div>
    <!-- End Features -->


   


    <!-- Mockup -->
    <div class="shape-container content-space-t-2 content-space-t-lg-3">
      <div class="container">
        <!-- Heading -->
        <div class="w-md-75 text-center mx-md-auto mb-5 mb-md-9">
          <h2 class="h1">Bạn đã sẵn sàng tích hợp?</h2>
          <p>SePay giúp bạn tự động hóa chuyển khoản ngân hàng. Tăng trải nghiệm thanh toán của khách hàng</p>
        </div>
        <!-- End Heading -->

        <!-- Devices -->
        <div class="devices">
          <!-- Mobile Device -->
          <figure class="device-mobile rotated-3d-right">
            <div class="device-mobile-frame">
              <img class="device-mobile-img" src="./assets/img/others/sepay-telegram2.jpg?v=2" alt="Image Description">
            </div>
          </figure>
          <!-- End Mobile Device -->

          <!-- Browser Device -->
          <figure class="device-browser">
            <div class="device-browser-header">
              <div class="device-browser-header-btn-list">
                <span class="device-browser-header-btn-list-btn"></span>
                <span class="device-browser-header-btn-list-btn"></span>
                <span class="device-browser-header-btn-list-btn"></span>
              </div>
              <div class="device-browser-header-browser-bar">my.sepay.vn</div>
            </div>

            <div class="device-browser-frame">
              <img class="device-browser-img" src="./assets/img/others/sepay-dashboard.png" alt="Image Description">
            </div>
          </figure>
          <!-- End Browser Device -->
        </div>
        <!-- End Devices -->
      </div>


    </div>
    <!-- End Mockup -->
    <div class="text-center mt-3">
      <div class="card card-info-link card-sm">
        <div class="card-body">
          Bạn muốn tư vấn? <a class="card-link ms-2" href="https://sepay.vn/lien-he.html#contact"> Liên hệ tư vấn<span class="bi-chevron-right small ms-1"></span></a>
        </div>
      </div>
    </div>


  </main>
  <!-- ========== END MAIN CONTENT ========== -->

  

   <!-- ========== FOOTER ========== -->
   <footer class="container content-space-2 content-space-t-lg-3">
    <!-- Links -->
    <div class="row mb-5">
      <div class="col-md-3 mb-5 mb-md-0">
        <!-- Logo -->
        <div class="mb-3">
          <a class="navbar-brand" href="./index.html" aria-label="Space">
            <img class="navbar-brand-logo" src="./assets/img/logo/sepay-blue-154x50.png" alt="Image Description">
          </a>
        </div>
        <!-- End Logo -->

        <!-- List -->
        <ul class="list-unstyled list-py-1">
          <li><b>Công Ty Cổ Phần SePay</b></li>
          <li><a class="link-sm link-secondary" href="https://goo.gl/maps/HGZVhsbv3NhR7kan8" target="_blank"><i
                class="bi-geo-alt-fill me-1"></i> 168 Đường số 2, Vạn Phúc City, P. Hiệp Bình Phước, Thủ Đức, TP. Hồ Chí
              Minh, Việt Nam</a></li>
          <li><a class="link-sm link-secondary" href="tel:02873059589"><i class="bi-telephone-inbound-fill me-1"></i>
              02873.059.589</a></li>
          <li><span class="text-secondary fs-6"><i class="bi bi-envelope-fill me-2"></i> <EMAIL></span></li>
          <li><span class="text-secondary fs-6">MST: 0317887567, cấp ngày 19/06/2023 tại Sở Kế hoạch và Đầu tư
              TPHCM</span></li>

        </ul>
        <!-- End List -->
        <div class="col-sm-auto">
          <!-- Socials -->
          <ul class="list-inline mb-0">
            <li class="list-inline-item">
              <a class="btn btn-soft-secondary btn-xs btn-icon" href="https://www.facebook.com/sepay.vn">
                <i class="bi-facebook"></i>
              </a>
            </li>
            <li class="list-inline-item">
              <a class="btn btn-soft-secondary btn-xs btn-icon" href="https://www.youtube.com/@SePayVN">
                <i class="bi-youtube"></i>
              </a>
            </li>
            <li class="list-inline-item">
              <a class="btn btn-soft-secondary btn-xs btn-icon" href="https://www.pinterest.com/sepayvn/">
                <i class="bi-pinterest"></i>
              </a>
            </li>




          </ul>
          <!-- End Socials -->
        </div>
      </div>

      <div class="col-sm-2 col-md-2 col-6 mb-3 mb-sm-0">
        <h5 class=" mb-3">THÔNG TIN</h5>

        <!-- List -->
        <ul class="list-unstyled list-py-1">
          <li><a class="text-body" href="gioi-thieu.html">Giới thiệu</a></li>
          <li><a class="text-body" href="bang-gia.html">Bảng giá</a></li>

          <li><a class="text-body" href="https://sepay.vn/blog">Blog</a></li>
          <li><a class="text-body" href="lien-he.html">Liên hệ</a></li>
          <li><a class="text-body" target="_blank" href="https://docs.sepay.vn/tich-hop-webhooks.html">WebHooks</a></li>

          <li><a class="text-body" target="_blank" href="https://docs.sepay.vn/gioi-thieu-api.html">API</a></li>

          <li><a class="text-body" href="faq.html">FAQ</a></li>
          <li><a class="text-body" href="changelog.html">Changelog</a></li>

        </ul>
        <div><img loading="lazy" src="/assets/img/others/RapidSSL-SEAL.gif" class="img-fluid"> <a
            href="https://secure.sitelock.com/public/verify/sepay.vn" target="_blank" rel=nofollow><img
              src="https://shield.sitelock.com/shield/sepay.vn" loading="lazy" class="img-fluid"
              style="max-width: 90px;"></a> <a href="http://online.gov.vn/Home/WebDetails/107586" target="_blank"
            rel=nofollow><img src="https://sepay.vn/assets/img/others/ttbct.png" loading="lazy" class="img-fluid"
              style="max-width: 150px;"></a></div>
        <!-- End List -->
      </div>

      <div class="col-sm-3 col-md-3 col-6 mb-3 mb-sm-0">
        <h5 class=" mb-3">HƯỚNG DẪN & TÍNH NĂNG</h5>

        <!-- List -->
        <ul class="list-unstyled list-py-1">
          <li>
            <a class="text-body" href="https://docs.sepay.vn/">
              Hướng dẫn sử dụng
            </a>
          </li>
          <li><a class="text-body" href="thanh-toan.html">Hướng dẫn thanh toán</a></li>
          <li><a class="text-body" href="terms-of-service.html">Quy định sử dụng</a></li>
          <li><a class="text-body" href="hoan-tien.html">Quy định hoàn tiền</a></li>
          <li><a class="text-body" href="privacy.html">Chính sách bảo mật</a></li>
          <li>
            <a class="text-body" href="ngan-hang.html">
              Ngân hàng kết nối
            </a>
          </li>


          <li>
            <a class="text-body" href="affiliate.html">
              Tiếp thị liên kết
            </a>
          </li>
          <li>
            <a class="text-body" href="chia-se-bien-dong-so-du.html">
              Chia sẻ biến động số dư
            </a>
          </li>
          <li>
            <a class="text-body" href="cong-thanh-toan-truc-tuyen.html">
              Cổng thanh toán trực tuyến
            </a>
          </li>
          <li>
            <a class="text-body" href="thong-ke-dong-tien.html">
              Thống kê dòng tiền
            </a>
          </li>
        </ul>
        <!-- End List -->
      </div>

      <div class="col-sm-4 row">
        <div class="col-6 mb-3 mb-sm-0 mx-auto">
          <h5 class=" mb-3">TÍCH HỢP</h5>
          <!-- List -->
          <ul class="list-unstyled list-py-1">
  
            <li>
              <a class="text-body" href="https://docs.sepay.vn/tich-hop-haravan.html">
                <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/haravan-icon.png"
                  style="width:22px; height: 22px;"> Haravan
              </a>
            </li>
  
            <li>
              <a class="text-body" href="https://docs.sepay.vn/tich-hop-sapo.html">
                <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/sapo-icon.png"
                  style="width:22px; height: 22px;"> Sapo
              </a>
            </li>

            <li>
              <a class="text-body" href="https://docs.sepay.vn/tich-hop-shopify.html">
                <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/shopify-icon.png"
                  style="width:22px; height: 22px;"> Shopify
              </a>
            </li>

            <li>
              <a class="text-body" href="https://docs.sepay.vn/woocommerce.html">
                <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/woocommerce-icon.png"
                  style="width:22px; height: 22px;"> WooCommerce
              </a>
            </li>
            <li>
              <a class="text-body" href="https://docs.sepay.vn/tich-hop-google-sheets.html">
                <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/others/google-sheets-icon.png"
                  style="width:22px; height: 22px;"> Google Sheets
              </a>
            </li>
            <li>
              <a class="text-body" href="https://docs.sepay.vn/tich-hop-lark-messenger.html">
                <img class="me-1" loading="lazy" src="https://docs.sepay.vn/assets/img/lark/lark-icon.png"
                  style="width:22px; height: 22px;"> Lark
              </a>
            </li>
            <li>
              <a class="text-body" href="https://docs.sepay.vn/tich-hop-telegram.html">
                <i class="bi bi-telegram me-1 text-info fs-3"></i> Telegram
              </a>
            </li>
  
  
  
  
            
        </div>
        <div class="col-6 mb-3 mb-sm-0 mx-auto">
          <h5 class=" mb-3">KẾT NỐI VỚI SEPAY</h5>
           <!-- List -->
           <ul class="list-unstyled list-py-1">
  
            <li>
              <a class="text-body" href="https://www.facebook.com/sepay.vn">
                <i class="bi bi-facebook me-1"></i> Facebook
              </a>
            </li>
            <li>
              <a class="text-body" href="https://t.me/s/sepaychannel">
                <i class="bi bi-telegram me-1"></i> Telegram
              </a>
            </li>
            <li>
              <a class="text-body" href="https://www.youtube.com/@SePayVN">
                <i class="bi bi-youtube me-1"></i> Youtube
              </a>
            </li>
  
            <li>
              <a class="text-body" href="https://www.pinterest.com/sepayvn/">
                <i class="bi bi-pinterest me-1"></i> Pinterest
              </a>
            </li>
            <li>
              <a class="text-body" href="https://github.com/sepayvn">
                <i class="bi bi-github me-1"></i> GitHub
              </a>
            </li>
  
  
  
          </ul>
          <!-- End List -->
        </div>
        <div class="col-12">
          <span class="text-cap mt-1">Hợp tác chiến lược:</span>
          <div class="row">
            <div class="col-3 py-3">
              <a href="https://sepay.vn/mb.html"><img class="avatar avatar-lg avatar-4x3" src="https://qr.sepay.vn/assets/img/banklogo/MB.png" alt="MBBank"></a>
            </div>
            <!-- End Col -->
  
            <div class="col-3 py-3">
              <a href="https://sepay.vn/ocb.html"><img class="avatar avatar-lg avatar-4x3" src="https://qr.sepay.vn/assets/img/banklogo/OCB.png" alt="OCB"></a>
            </div>
            <!-- End Col -->
            <div class="col-3 py-3">
              <a href="https://sepay.vn/kien-long-bank.html"><img class="avatar avatar-lg avatar-4x3" src="https://sepay.vn/blog/wp-content/uploads/2024/05/kienlongbank-logo.png" alt="KienlongBank"></a>
            </div>
            <!-- End Col -->
  
            <div class="col-3 py-3">
              <a href="https://sepay.vn/msb.html"><img class="avatar avatar-lg avatar-4x3" src="https://qr.sepay.vn/assets/img/banklogo/MSB.png" alt="MSB"></a>
            </div>
            <!-- End Col -->
          </div>
        </div>
      </div>
    </div>
    <!-- End Links -->
    <div class="border-top border-10 my-5"></div>
    <div class="row align-items-sm-center">
      <div class="col-12 mb-4 mb-sm-0">
        <p class="small mb-0 text-center">&copy; SePay 2023. All rights reserved.</p>
      </div>
      <!-- End Col -->


      <!-- End Col -->
    </div>
    <!-- End Row -->
  </footer>
  <!-- ========== END FOOTER ========== -->
  <!--contact-box-html-->
  <div id="contact-box-overlay"></div>
  <div id="contact-box">
      <div class="popup">
          <a href="https://m.me/***************" target="_blank" class="item">
              <div class="logo">
                  <img src="assets/img/others/fb-messenger.png" width="50%" />
              </div>
              <div class="meta">
                  <p class="title">Facebook Messenger</p>
                  <small class="description">Hỗ trợ live chat 24/7</small>
              </div>
          </a>
          <div class="divide"></div>
          <a href="tel:02873059589" class="item">
              <div class="icon" style="background-color: #22c55e; color: #fff;">
                  <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;"><path d="M5.5 1H2.87785C1.63626 1 0.694688 2.11946 0.907423 3.34268L1.14841 4.72836C1.96878 9.4455 5.51475 13.2235 10.1705 14.3409L12.5333 14.908C13.7909 15.2098 15 14.2566 15 12.9632V10.5L11.75 8.25L9.25 10.75L5.25 6.75L7.75 4.25L5.5 1Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" fill="transparent"></path></svg>
              </div>
              <div class="meta">
                  <p class="title">Hotline</p>
                  <small class="description">Điện thoại hỗ trợ</small>
              </div>
          </a>
          <div class="divide"></div>
          <a href="https://www.youtube.com/@SePayVN" target="_blank" class="item">
              <div class="logo">
                  <img src="assets/img/others/youtube-social.png" />
              </div>
              <div class="meta">
                  <p class="title">Youtube</p>
                  <small class="description">Theo dõi video mới nhất của SePay</small>
              </div>
          </a>
          <div class="divide"></div>
          <a href="https://t.me/s/sepaychannel" target="_blank" class="item">
              <div class="logo">
                  <img src="assets/img/others/telegram-social.png" />
              </div>
              <div class="meta">
                  <p class="title">Telegram</p>
                  <small class="description">Nhận thông tin mới nhất từ SePay</small>
              </div>
          </a>
      </div>
      <div class="container">
          <div class="dot-ping">
              <div class="ping"></div>
              <div class="dot"></div>
          </div>
          <div class="contact-icon">
              <svg data-testid="geist-icon" height="20" stroke-linejoin="round" viewBox="0 0 16 16" width="20" style="color: currentcolor;"><path fill-rule="evenodd" clip-rule="evenodd" d="M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z" fill="currentColor"></path></svg>
          </div>
          <span style="font-weight: bold;">Liên hệ chúng tôi</span>
      </div>
  </div>
  <!--/contact-box-html-->

  <!-- Go To -->
  <a class="js-go-to go-to position-fixed" href="javascript:;" style="visibility: hidden;" data-hs-go-to-options='{
       "offsetTop": 700,
       "position": {
         "init": {
           "right": "2rem"
         },
         "show": {
           "bottom": "2rem"
         },
         "hide": {
           "bottom": "-2rem"
         }
       }
     }'>
    <i class="bi-chevron-up"></i>
  </a>
  <!-- ========== END SECONDARY CONTENTS ========== -->

  <!-- JS Global Compulsory  -->
  <script src="./assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

  <!-- JS Implementing Plugins -->
  <script src="./assets/vendor/hs-header/dist/hs-header.min.js"></script>
  <script src="./assets/vendor/hs-mega-menu/dist/hs-mega-menu.min.js"></script>
  <script src="./assets/vendor/fslightbox/index.js"></script>
  <!-- JS Front -->
  <script src="./assets/js/theme.min.js"></script>

  <!-- contact-box-js -->
  <script src="./assets/js/contact-box.js"></script>
  <!-- /contact-box-js -->

  <!-- JS Plugins Init. -->
  <script>
    (function () {
      // INITIALIZATION OF HEADER
      // =======================================================
      new HSHeader('#header').init()


      // INITIALIZATION OF MEGA MENU
      // =======================================================
      new HSMegaMenu('.js-mega-menu', {
        desktop: {
          position: 'left'
        }
      })


      // INITIALIZATION OF BOOTSTRAP DROPDOWN
      // =======================================================
      HSBsDropdown.init()

    })()
  </script>
</body>

</html>